import React from 'react';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';

const DocumentRepository = ({ documents }) => {

  const downloadTemplate = () => {
    return <i className="pi pi-download download-icon cursor-pointer" />;
  };

  return (
    <div className="document-repo-container">
      <h5 className="document-repo-header">Document Repository</h5>
      <DataTable 
        value={documents} 
        className="document-repo-table"
        paginator
        rows={5}
        rowsPerPageOptions={[5, 10, 20]}
      >
        <Column field="artifact" header="Artifact" className="doc-artifact-col" />
        <Column field="description" header="Description" className="doc-description-col" />
        <Column field="uploader" header="Uploaded by" className="doc-uploader-col" />
        <Column field="date" header="Uploaded on" className="doc-date-col" />
        <Column body={downloadTemplate} header="" className="doc-action-col" style={{ width: '50px' }} />
      </DataTable>
    </div>
  );
};

export default DocumentRepository;