{"version": 3, "sources": ["../../src/utils.ts", "../../src/tags.ts", "../../src/attrs.ts", "../../src/regexp.ts", "../../src/purify.ts"], "names": ["entries", "setPrototypeOf", "isFrozen", "getPrototypeOf", "getOwnPropertyDescriptor", "Object", "freeze", "seal", "create", "apply", "construct", "Reflect", "x", "fun", "thisValue", "args", "Func", "arrayForEach", "unapply", "Array", "prototype", "for<PERSON>ach", "arrayLastIndexOf", "lastIndexOf", "arrayPop", "pop", "arrayPush", "push", "arraySplice", "splice", "stringToLowerCase", "String", "toLowerCase", "stringToString", "toString", "stringMatch", "match", "stringReplace", "replace", "stringIndexOf", "indexOf", "stringTrim", "trim", "objectHasOwnProperty", "hasOwnProperty", "regExpTest", "RegExp", "test", "typeErrorCreate", "unconstruct", "TypeError", "func", "thisArg", "lastIndex", "_len", "arguments", "length", "_key", "_len2", "_key2", "addToSet", "set", "array", "transformCaseFunc", "undefined", "l", "element", "lcElement", "cleanArray", "index", "clone", "object", "newObject", "property", "value", "isArray", "constructor", "lookupGetter", "prop", "desc", "get", "fallback<PERSON><PERSON><PERSON>", "html$1", "svg$1", "svgFilters", "svgDisallowed", "mathMl$1", "mathMlDisallowed", "text", "html", "svg", "mathMl", "xml", "MUSTACHE_EXPR", "ERB_EXPR", "TMPLIT_EXPR", "DATA_ATTR", "ARIA_ATTR", "IS_ALLOWED_URI", "IS_SCRIPT_OR_DATA", "ATTR_WHITESPACE", "DOCTYPE_NAME", "CUSTOM_ELEMENT", "NODE_TYPE", "attribute", "cdataSection", "entityReference", "entityNode", "progressingInstruction", "comment", "document", "documentType", "documentFragment", "notation", "getGlobal", "window", "_createTrustedTypesPolicy", "trustedTypes", "purifyHostElement", "createPolicy", "suffix", "ATTR_NAME", "hasAttribute", "getAttribute", "policyName", "createHTML", "createScriptURL", "scriptUrl", "_", "console", "warn", "_createHooksMap", "afterSanitizeAttributes", "afterSanitizeElements", "afterSanitizeShadowDOM", "beforeSanitizeAttributes", "beforeSanitizeElements", "beforeSanitizeShadowDOM", "uponSanitizeAttribute", "uponSanitizeElement", "uponSanitizeShadowNode", "createDOMPurify", "DOMPurify", "root", "version", "removed", "nodeType", "Element", "isSupported", "originalDocument", "currentScript", "DocumentFragment", "HTMLTemplateElement", "Node", "Node<PERSON><PERSON><PERSON>", "NamedNodeMap", "MozNamedAttrMap", "HTMLFormElement", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ElementPrototype", "cloneNode", "remove", "getNextSibling", "getChildNodes", "getParentNode", "template", "createElement", "content", "ownerDocument", "trustedTypesPolicy", "emptyHTML", "implementation", "createNodeIterator", "createDocumentFragment", "getElementsByTagName", "importNode", "hooks", "createHTMLDocument", "EXPRESSIONS", "IS_ALLOWED_URI$1", "ALLOWED_TAGS", "DEFAULT_ALLOWED_TAGS", "ALLOWED_ATTR", "DEFAULT_ALLOWED_ATTR", "CUSTOM_ELEMENT_HANDLING", "tagNameCheck", "writable", "configurable", "enumerable", "attributeNameCheck", "allowCustomizedBuiltInElements", "FORBID_TAGS", "FORBID_ATTR", "ALLOW_ARIA_ATTR", "ALLOW_DATA_ATTR", "ALLOW_UNKNOWN_PROTOCOLS", "ALLOW_SELF_CLOSE_IN_ATTR", "SAFE_FOR_TEMPLATES", "SAFE_FOR_XML", "WHOLE_DOCUMENT", "SET_CONFIG", "FORCE_BODY", "RETURN_DOM", "RETURN_DOM_FRAGMENT", "RETURN_TRUSTED_TYPE", "SANITIZE_DOM", "SANITIZE_NAMED_PROPS", "SANITIZE_NAMED_PROPS_PREFIX", "KEEP_CONTENT", "IN_PLACE", "USE_PROFILES", "FORBID_CONTENTS", "DEFAULT_FORBID_CONTENTS", "DATA_URI_TAGS", "DEFAULT_DATA_URI_TAGS", "URI_SAFE_ATTRIBUTES", "DEFAULT_URI_SAFE_ATTRIBUTES", "MATHML_NAMESPACE", "SVG_NAMESPACE", "HTML_NAMESPACE", "NAMESPACE", "IS_EMPTY_INPUT", "ALLOWED_NAMESPACES", "DEFAULT_ALLOWED_NAMESPACES", "MATHML_TEXT_INTEGRATION_POINTS", "HTML_INTEGRATION_POINTS", "COMMON_SVG_AND_HTML_ELEMENTS", "PARSER_MEDIA_TYPE", "SUPPORTED_PARSER_MEDIA_TYPES", "DEFAULT_PARSER_MEDIA_TYPE", "CONFIG", "formElement", "isRegexOrFunction", "testValue", "Function", "_parseConfig", "cfg", "ADD_URI_SAFE_ATTR", "ADD_DATA_URI_TAGS", "ALLOWED_URI_REGEXP", "ADD_TAGS", "ADD_ATTR", "table", "tbody", "TRUSTED_TYPES_POLICY", "ALL_SVG_TAGS", "ALL_MATHML_TAGS", "_checkValidNamespace", "parent", "tagName", "namespaceURI", "parentTagName", "Boolean", "_forceRemove", "node", "<PERSON><PERSON><PERSON><PERSON>", "_removeAttribute", "name", "getAttributeNode", "from", "removeAttribute", "setAttribute", "_initDocument", "dirty", "doc", "leadingWhitespace", "matches", "dirtyPayload", "parseFromString", "documentElement", "createDocument", "innerHTML", "body", "insertBefore", "createTextNode", "childNodes", "call", "_createNodeIterator", "SHOW_ELEMENT", "SHOW_COMMENT", "SHOW_TEXT", "SHOW_PROCESSING_INSTRUCTION", "SHOW_CDATA_SECTION", "_isClobbered", "nodeName", "textContent", "attributes", "hasChildNodes", "_isNode", "_executeHooks", "currentNode", "data", "hook", "_sanitizeElements", "allowedTags", "first<PERSON><PERSON><PERSON><PERSON><PERSON>", "_isBasicCustomElement", "parentNode", "i", "child<PERSON>lone", "__removalCount", "expr", "_isValidAttribute", "lcTag", "lcName", "_sanitizeAttributes", "hookEvent", "attrName", "attrValue", "keepAttr", "allowedAttributes", "forceKeepAttr", "attr", "initValue", "getAttributeType", "setAttributeNS", "_sanitizeShadowDOM", "fragment", "shadowNode", "shadowIterator", "nextNode", "sanitize", "importedNode", "returnNode", "append<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "nodeIterator", "shadowroot", "shadowrootmode", "serializedHTML", "outerHTML", "doctype", "setConfig", "clearConfig", "isValidAttribute", "tag", "addHook", "entryPoint", "hookFunction", "removeH<PERSON>", "removeHooks", "removeAllHooks"], "mappings": ";sHAAA,MAAM,QACJA,EAAO,eACPC,EAAc,SACdC,EAAQ,eACRC,EAAc,yBACdC,GACEC,OAEJ,IAAI,OAAEC,EAAM,KAAEC,EAAI,OAAEC,GAAWH,QAC3B,MAAEI,EAAK,UAAEC,GAAiC,qBAAZC,SAA2BA,QAExDL,IACHA,EAAS,SAAUM,GACjB,OAAOA,C,GAINL,IACHA,EAAO,SAAUK,GACf,OAAOA,C,GAINH,IACHA,EAAQ,SAAUI,EAAKC,EAAWC,GAChC,OAAOF,EAAIJ,MAAMK,EAAWC,E,GAI3BL,IACHA,EAAY,SAAUM,EAAMD,GAC1B,OAAO,IAAIC,KAAQD,E,GAIvB,MAAME,EAAeC,EAAQC,MAAMC,UAAUC,SAEvCC,EAAmBJ,EAAQC,MAAMC,UAAUG,aAC3CC,EAAWN,EAAQC,MAAMC,UAAUK,KACnCC,EAAYR,EAAQC,MAAMC,UAAUO,MAEpCC,EAAcV,EAAQC,MAAMC,UAAUS,QAEtCC,EAAoBZ,EAAQa,OAAOX,UAAUY,aAC7CC,EAAiBf,EAAQa,OAAOX,UAAUc,UAC1CC,EAAcjB,EAAQa,OAAOX,UAAUgB,OACvCC,EAAgBnB,EAAQa,OAAOX,UAAUkB,SACzCC,EAAgBrB,EAAQa,OAAOX,UAAUoB,SACzCC,EAAavB,EAAQa,OAAOX,UAAUsB,MAEtCC,EAAuBzB,EAAQb,OAAOe,UAAUwB,gBAEhDC,EAAa3B,EAAQ4B,OAAO1B,UAAU2B,MAEtCC,EAAkBC,EAAYC,WAQpC,SAAShC,EACPiC,GAEA,OAAO,SAACC,GACFA,aAAmBN,SACrBM,EAAQC,UAAY,GACrB,QAAAC,EAAAC,UAAAC,OAHsBzC,EAAW,IAAAI,MAAAmC,EAAA,EAAAA,EAAA,KAAAG,EAAA,EAAAA,EAAAH,EAAAG,IAAX1C,EAAW0C,EAAA,GAAAF,UAAAE,GAKlC,OAAOhD,EAAM0C,EAAMC,EAASrC,E,CAEhC,CAQA,SAASkC,EAAeE,GACtB,OAAO,mBAAAO,EAAAH,UAAAC,OAAIzC,EAAW,IAAAI,MAAAuC,GAAAC,EAAA,EAAAA,EAAAD,EAAAC,IAAX5C,EAAW4C,GAAAJ,UAAAI,GAAA,OAAQjD,EAAUyC,EAAMpC,EAAK,CACrD,CAUA,SAAS6C,EACPC,EACAC,GACyE,IAAzEC,EAAAR,UAAAC,OAAA,QAAAQ,IAAAT,UAAA,GAAAA,UAAA,GAAwDzB,EAEpD7B,GAIFA,EAAe4D,EAAK,MAGtB,IAAII,EAAIH,EAAMN,OACd,KAAOS,KAAK,CACV,IAAIC,EAAUJ,EAAMG,GACpB,GAAuB,kBAAZC,EAAsB,CAC/B,MAAMC,EAAYJ,EAAkBG,GAChCC,IAAcD,IAEXhE,EAAS4D,KACXA,EAAgBG,GAAKE,GAGxBD,EAAUC,EAEd,CAEAN,EAAIK,IAAW,CACjB,CAEA,OAAOL,CACT,CAQA,SAASO,EAAcN,GACrB,IAAK,IAAIO,EAAQ,EAAGA,EAAQP,EAAMN,OAAQa,IAChB1B,EAAqBmB,EAAOO,KAGlDP,EAAMO,GAAS,MAInB,OAAOP,CACT,CAQA,SAASQ,EAAqCC,GAC5C,MAAMC,EAAYhE,EAAO,MAEzB,IAAK,MAAOiE,EAAUC,KAAU1E,EAAQuE,GACd5B,EAAqB4B,EAAQE,KAG/CtD,MAAMwD,QAAQD,GAChBF,EAAUC,GAAYL,EAAWM,GAEjCA,GACiB,kBAAVA,GACPA,EAAME,cAAgBvE,OAEtBmE,EAAUC,GAAYH,EAAMI,GAE5BF,EAAUC,GAAYC,GAK5B,OAAOF,CACT,CASA,SAASK,EACPN,EACAO,GAEA,KAAkB,OAAXP,GAAiB,CACtB,MAAMQ,EAAO3E,EAAyBmE,EAAQO,GAE9C,GAAIC,EAAM,CACR,GAAIA,EAAKC,IACP,OAAO9D,EAAQ6D,EAAKC,KAGtB,GAA0B,oBAAfD,EAAKL,MACd,OAAOxD,EAAQ6D,EAAKL,MAExB,CAEAH,EAASpE,EAAeoE,EAC1B,CAEA,SAASU,IACP,OAAO,IACT,CAEA,OAAOA,CACT,CC3MO,MAAMC,EAAO5E,EAAO,CACzB,IACA,OACA,UACA,UACA,OACA,UACA,QACA,QACA,IACA,MACA,MACA,MACA,QACA,aACA,OACA,KACA,SACA,SACA,UACA,SACA,OACA,OACA,MACA,WACA,UACA,OACA,WACA,KACA,YACA,MACA,UACA,MACA,SACA,MACA,MACA,KACA,KACA,UACA,KACA,WACA,aACA,SACA,OACA,SACA,OACA,KACA,KACA,KACA,KACA,KACA,KACA,OACA,SACA,SACA,KACA,OACA,IACA,MACA,QACA,MACA,MACA,QACA,SACA,KACA,OACA,MACA,OACA,UACA,OACA,WACA,QACA,MACA,OACA,KACA,WACA,SACA,SACA,IACA,UACA,MACA,WACA,IACA,KACA,KACA,OACA,IACA,OACA,UACA,SACA,SACA,QACA,SACA,SACA,OACA,SACA,SACA,QACA,MACA,UACA,MACA,QACA,QACA,KACA,WACA,WACA,QACA,KACA,QACA,OACA,KACA,QACA,KACA,IACA,KACA,MACA,QACA,QAGW6E,EAAM7E,EAAO,CACxB,MACA,IACA,WACA,cACA,eACA,eACA,gBACA,mBACA,SACA,WACA,OACA,OACA,UACA,SACA,OACA,IACA,QACA,WACA,QACA,QACA,OACA,iBACA,SACA,OACA,WACA,QACA,OACA,UACA,UACA,WACA,iBACA,OACA,OACA,QACA,SACA,SACA,OACA,WACA,QACA,OACA,QACA,OACA,UAGW8E,EAAa9E,EAAO,CAC/B,UACA,gBACA,sBACA,cACA,mBACA,oBACA,oBACA,iBACA,eACA,UACA,UACA,UACA,UACA,UACA,iBACA,UACA,UACA,cACA,eACA,WACA,eACA,qBACA,cACA,SACA,iBAOW+E,EAAgB/E,EAAO,CAClC,UACA,gBACA,SACA,UACA,YACA,mBACA,iBACA,gBACA,gBACA,gBACA,QACA,YACA,OACA,eACA,YACA,UACA,gBACA,SACA,MACA,aACA,UACA,QAGWgF,EAAShF,EAAO,CAC3B,OACA,WACA,SACA,UACA,QACA,SACA,KACA,aACA,gBACA,KACA,KACA,QACA,UACA,WACA,QACA,OACA,KACA,SACA,QACA,SACA,OACA,OACA,UACA,SACA,MACA,QACA,MACA,SACA,aACA,gBAKWiF,EAAmBjF,EAAO,CACrC,UACA,cACA,aACA,WACA,YACA,UACA,UACA,SACA,SACA,QACA,YACA,aACA,iBACA,cACA,SAGWkF,EAAOlF,EAAO,CAAC,UCpRfmF,EAAOnF,EAAO,CACzB,SACA,SACA,QACA,MACA,iBACA,eACA,uBACA,WACA,aACA,UACA,SACA,UACA,cACA,cACA,UACA,OACA,QACA,QACA,QACA,OACA,UACA,WACA,eACA,SACA,cACA,WACA,WACA,UACA,MACA,WACA,0BACA,wBACA,WACA,YACA,UACA,eACA,OACA,MACA,UACA,SACA,SACA,OACA,OACA,WACA,KACA,YACA,YACA,QACA,OACA,QACA,OACA,OACA,UACA,OACA,MACA,MACA,YACA,QACA,SACA,MACA,YACA,WACA,QACA,OACA,QACA,UACA,aACA,SACA,OACA,UACA,UACA,cACA,cACA,UACA,gBACA,sBACA,SACA,UACA,UACA,aACA,WACA,MACA,WACA,MACA,WACA,OACA,OACA,UACA,aACA,QACA,WACA,QACA,OACA,QACA,OACA,UACA,QACA,MACA,SACA,OACA,QACA,UACA,WACA,QACA,YACA,OACA,SACA,SACA,QACA,QACA,OACA,QACA,SAGWoF,EAAMpF,EAAO,CACxB,gBACA,aACA,WACA,qBACA,YACA,SACA,gBACA,gBACA,UACA,gBACA,iBACA,QACA,OACA,KACA,QACA,OACA,gBACA,YACA,YACA,QACA,sBACA,8BACA,gBACA,kBACA,KACA,KACA,IACA,KACA,KACA,kBACA,YACA,UACA,UACA,MACA,WACA,YACA,MACA,WACA,OACA,eACA,YACA,SACA,cACA,cACA,gBACA,cACA,YACA,mBACA,eACA,aACA,eACA,cACA,KACA,KACA,KACA,KACA,aACA,WACA,gBACA,oBACA,SACA,OACA,KACA,kBACA,KACA,MACA,YACA,IACA,KACA,KACA,KACA,KACA,UACA,YACA,aACA,WACA,OACA,eACA,iBACA,eACA,mBACA,iBACA,QACA,aACA,aACA,eACA,eACA,cACA,cACA,mBACA,YACA,MACA,OACA,QACA,SACA,OACA,MACA,OACA,aACA,SACA,WACA,UACA,QACA,SACA,cACA,SACA,WACA,cACA,OACA,aACA,sBACA,mBACA,eACA,SACA,gBACA,sBACA,iBACA,IACA,KACA,KACA,SACA,OACA,OACA,cACA,YACA,UACA,SACA,SACA,QACA,OACA,kBACA,QACA,mBACA,mBACA,eACA,cACA,eACA,cACA,aACA,eACA,mBACA,oBACA,iBACA,kBACA,oBACA,iBACA,SACA,eACA,QACA,eACA,iBACA,WACA,cACA,UACA,UACA,YACA,mBACA,cACA,kBACA,iBACA,aACA,OACA,KACA,KACA,UACA,SACA,UACA,aACA,UACA,aACA,gBACA,gBACA,QACA,eACA,OACA,eACA,mBACA,mBACA,IACA,KACA,KACA,QACA,IACA,KACA,KACA,IACA,eAGWqF,EAASrF,EAAO,CAC3B,SACA,cACA,QACA,WACA,QACA,eACA,cACA,aACA,aACA,QACA,MACA,UACA,eACA,WACA,QACA,QACA,SACA,OACA,KACA,UACA,SACA,gBACA,SACA,SACA,iBACA,YACA,WACA,cACA,UACA,UACA,gBACA,WACA,WACA,OACA,WACA,WACA,aACA,UACA,SACA,SACA,cACA,gBACA,uBACA,YACA,YACA,aACA,WACA,iBACA,iBACA,YACA,UACA,QACA,UAGWsF,EAAMtF,EAAO,CACxB,aACA,SACA,cACA,YACA,gBC9WWuF,EAAgBtF,EAAK,6BACrBuF,EAAWvF,EAAK,yBAChBwF,EAAcxF,EAAK,iBACnByF,EAAYzF,EAAK,gCACjB0F,EAAY1F,EAAK,kBACjB2F,EAAiB3F,EAC5B,oGAEW4F,EAAoB5F,EAAK,yBACzB6F,EAAkB7F,EAC7B,+DAEW8F,EAAe9F,EAAK,WACpB+F,EAAiB/F,EAAK,4B,8LCmBnC,MAAMgG,EAAY,CAChBrC,QAAS,EACTsC,UAAW,EACXhB,KAAM,EACNiB,aAAc,EACdC,gBAAiB,EACjBC,WAAY,EACZC,uBAAwB,EACxBC,QAAS,EACTC,SAAU,EACVC,aAAc,GACdC,iBAAkB,GAClBC,SAAU,IAGNC,EAAY,WAChB,MAAyB,qBAAXC,OAAyB,KAAOA,MAChD,EAUMC,GAA4B,SAChCC,EACAC,GAEA,GAC0B,kBAAjBD,GAC8B,oBAA9BA,EAAaE,aAEpB,OAAO,KAMT,IAAIC,EAAS,KACb,MAAMC,EAAY,wBACdH,GAAqBA,EAAkBI,aAAaD,KACtDD,EAASF,EAAkBK,aAAaF,IAG1C,MAAMG,EAAa,aAAeJ,EAAS,IAAMA,EAAS,IAE1D,IACE,OAAOH,EAAaE,aAAaK,EAAY,CAC3CC,WAAWpC,GACFA,EAETqC,gBAAgBC,GACPA,G,CAGX,MAAOC,GAOP,OAHAC,QAAQC,KACN,uBAAyBN,EAAa,0BAEjC,IACT,CACF,EAEMO,GAAkB,WACtB,MAAO,CACLC,wBAAyB,GACzBC,sBAAuB,GACvBC,uBAAwB,GACxBC,yBAA0B,GAC1BC,uBAAwB,GACxBC,wBAAyB,GACzBC,sBAAuB,GACvBC,oBAAqB,GACrBC,uBAAwB,GAE5B,EAEA,SAASC,KAAgD,IAAhC1B,EAAqB5D,UAAAC,OAAA,QAAAQ,IAAAT,UAAA,GAAAA,UAAA,GAAA2D,IAC5C,MAAM4B,EAAwBC,GAAqBF,GAAgBE,GAMnE,GAJAD,EAAUE,QAAU,QAEpBF,EAAUG,QAAU,IAGjB9B,IACAA,EAAOL,UACRK,EAAOL,SAASoC,WAAa3C,EAAUO,WACtCK,EAAOgC,QAMR,OAFAL,EAAUM,aAAc,EAEjBN,EAGT,IAAI,SAAEhC,GAAaK,EAEnB,MAAMkC,EAAmBvC,EACnBwC,EACJD,EAAiBC,eACb,iBACJC,EAAgB,oBAChBC,EAAmB,KACnBC,EAAI,QACJN,EAAO,WACPO,EAAU,aACVC,EAAexC,EAAOwC,cAAiBxC,EAAeyC,gBAAe,gBACrEC,EAAe,UACfC,EAAS,aACTzC,GACEF,EAEE4C,EAAmBZ,EAAQ/H,UAE3B4I,EAAYnF,EAAakF,EAAkB,aAC3CE,EAASpF,EAAakF,EAAkB,UACxCG,EAAiBrF,EAAakF,EAAkB,eAChDI,GAAgBtF,EAAakF,EAAkB,cAC/CK,GAAgBvF,EAAakF,EAAkB,cAQrD,GAAmC,oBAAxBP,EAAoC,CAC7C,MAAMa,EAAWvD,EAASwD,cAAc,YACpCD,EAASE,SAAWF,EAASE,QAAQC,gBACvC1D,EAAWuD,EAASE,QAAQC,cAEhC,CAEA,IAAIC,GACAC,GAAY,GAEhB,MAAM,eACJC,GAAc,mBACdC,GAAkB,uBAClBC,GAAsB,qBACtBC,IACEhE,GACE,WAAEiE,IAAe1B,EAEvB,IAAI2B,GAAQ7C,KAKZW,EAAUM,YACW,oBAAZpJ,GACkB,oBAAlBoK,IACPO,SACsC3G,IAAtC2G,GAAeM,mBAEjB,MAAM,cACJpF,GAAa,SACbC,GAAQ,YACRC,GAAW,UACXC,GAAS,UACTC,GAAS,kBACTE,GAAiB,gBACjBC,GAAe,eACfE,IACE4E,EAEJ,IAAMhF,eAAAiF,IAAmBD,EAQrBE,GAAe,KACnB,MAAMC,GAAuBzH,EAAS,CAAC,EAAG,IACrCsB,KACAC,KACAC,KACAE,KACAE,IAIL,IAAI8F,GAAe,KACnB,MAAMC,GAAuB3H,EAAS,CAAC,EAAG,IACrC6B,KACAC,KACAC,KACAC,IASL,IAAI4F,GAA0BnL,OAAOE,KACnCC,EAAO,KAAM,CACXiL,aAAc,CACZC,UAAU,EACVC,cAAc,EACdC,YAAY,EACZlH,MAAO,MAETmH,mBAAoB,CAClBH,UAAU,EACVC,cAAc,EACdC,YAAY,EACZlH,MAAO,MAEToH,+BAAgC,CAC9BJ,UAAU,EACVC,cAAc,EACdC,YAAY,EACZlH,OAAO,MAMTqH,GAAc,KAGdC,GAAc,KAGdC,IAAkB,EAGlBC,IAAkB,EAGlBC,IAA0B,EAI1BC,IAA2B,EAK3BC,IAAqB,EAKrBC,IAAe,EAGfC,IAAiB,EAGjBC,IAAa,EAIbC,IAAa,EAMbC,IAAa,EAIbC,IAAsB,EAItBC,IAAsB,EAKtBC,IAAe,EAefC,IAAuB,EAC3B,MAAMC,GAA8B,gBAGpC,IAAIC,IAAe,EAIfC,IAAW,EAGXC,GAA0C,CAAC,EAG3CC,GAAkB,KACtB,MAAMC,GAA0BxJ,EAAS,CAAC,EAAG,CAC3C,iBACA,QACA,WACA,OACA,gBACA,OACA,SACA,OACA,KACA,KACA,KACA,KACA,QACA,UACA,WACA,WACA,YACA,SACA,QACA,MACA,WACA,QACA,QACA,QACA,QAIF,IAAIyJ,GAAgB,KACpB,MAAMC,GAAwB1J,EAAS,CAAC,EAAG,CACzC,QACA,QACA,MACA,SACA,QACA,UAIF,IAAI2J,GAAsB,KAC1B,MAAMC,GAA8B5J,EAAS,CAAC,EAAG,CAC/C,MACA,QACA,MACA,KACA,QACA,OACA,UACA,cACA,OACA,UACA,QACA,QACA,QACA,UAGI6J,GAAmB,qCACnBC,GAAgB,6BAChBC,GAAiB,+BAEvB,IAAIC,GAAYD,GACZE,IAAiB,EAGjBC,GAAqB,KACzB,MAAMC,GAA6BnK,EACjC,CAAC,EACD,CAAC6J,GAAkBC,GAAeC,IAClC1L,GAGF,IAAI+L,GAAiCpK,EAAS,CAAC,EAAG,CAChD,KACA,KACA,KACA,KACA,UAGEqK,GAA0BrK,EAAS,CAAC,EAAG,CAAC,mBAM5C,MAAMsK,GAA+BtK,EAAS,CAAC,EAAG,CAChD,QACA,QACA,OACA,IACA,WAIF,IAAIuK,GAAmD,KACvD,MAAMC,GAA+B,CAAC,wBAAyB,aACzDC,GAA4B,YAClC,IAAItK,GAA2D,KAG3DuK,GAAwB,KAK5B,MAAMC,GAAczH,EAASwD,cAAc,QAErCkE,GAAoB,SACxBC,GAEA,OAAOA,aAAqB3L,QAAU2L,aAAqBC,Q,EASvDC,GAAe,WAA0B,IAAhBC,EAAArL,UAAAC,OAAA,QAAAQ,IAAAT,UAAA,GAAAA,UAAA,GAAc,CAAC,EAC5C,IAAI+K,IAAUA,KAAWM,EAAzB,CA6LA,GAxLKA,GAAsB,kBAARA,IACjBA,EAAM,CAAC,GAITA,EAAMtK,EAAMsK,GAEZT,IAEmE,IAAjEC,GAA6B5L,QAAQoM,EAAIT,mBACrCE,GACAO,EAAIT,kBAGVpK,GACwB,0BAAtBoK,GACIlM,EACAH,EAGNsJ,GAAezI,EAAqBiM,EAAK,gBACrChL,EAAS,CAAC,EAAGgL,EAAIxD,aAAcrH,IAC/BsH,GACJC,GAAe3I,EAAqBiM,EAAK,gBACrChL,EAAS,CAAC,EAAGgL,EAAItD,aAAcvH,IAC/BwH,GACJuC,GAAqBnL,EAAqBiM,EAAK,sBAC3ChL,EAAS,CAAC,EAAGgL,EAAId,mBAAoB7L,GACrC8L,GACJR,GAAsB5K,EAAqBiM,EAAK,qBAC5ChL,EACEU,EAAMkJ,IACNoB,EAAIC,kBACJ9K,IAEFyJ,GACJH,GAAgB1K,EAAqBiM,EAAK,qBACtChL,EACEU,EAAMgJ,IACNsB,EAAIE,kBACJ/K,IAEFuJ,GACJH,GAAkBxK,EAAqBiM,EAAK,mBACxChL,EAAS,CAAC,EAAGgL,EAAIzB,gBAAiBpJ,IAClCqJ,GACJrB,GAAcpJ,EAAqBiM,EAAK,eACpChL,EAAS,CAAC,EAAGgL,EAAI7C,YAAahI,IAC9BO,EAAM,CAAC,GACX0H,GAAcrJ,EAAqBiM,EAAK,eACpChL,EAAS,CAAC,EAAGgL,EAAI5C,YAAajI,IAC9BO,EAAM,CAAC,GACX4I,KAAevK,EAAqBiM,EAAK,iBACrCA,EAAI1B,aAERjB,IAA0C,IAAxB2C,EAAI3C,gBACtBC,IAA0C,IAAxB0C,EAAI1C,gBACtBC,GAA0ByC,EAAIzC,0BAA2B,EACzDC,IAA4D,IAAjCwC,EAAIxC,yBAC/BC,GAAqBuC,EAAIvC,qBAAsB,EAC/CC,IAAoC,IAArBsC,EAAItC,aACnBC,GAAiBqC,EAAIrC,iBAAkB,EACvCG,GAAakC,EAAIlC,aAAc,EAC/BC,GAAsBiC,EAAIjC,sBAAuB,EACjDC,GAAsBgC,EAAIhC,sBAAuB,EACjDH,GAAamC,EAAInC,aAAc,EAC/BI,IAAoC,IAArB+B,EAAI/B,aACnBC,GAAuB8B,EAAI9B,uBAAwB,EACnDE,IAAoC,IAArB4B,EAAI5B,aACnBC,GAAW2B,EAAI3B,WAAY,EAC3B9B,GAAiByD,EAAIG,oBAAsB7I,EAC3C0H,GAAYgB,EAAIhB,WAAaD,GAC7BK,GACEY,EAAIZ,gCAAkCA,GACxCC,GACEW,EAAIX,yBAA2BA,GAEjCzC,GAA0BoD,EAAIpD,yBAA2B,CAAC,EAExDoD,EAAIpD,yBACJgD,GAAkBI,EAAIpD,wBAAwBC,gBAE9CD,GAAwBC,aACtBmD,EAAIpD,wBAAwBC,cAI9BmD,EAAIpD,yBACJgD,GAAkBI,EAAIpD,wBAAwBK,sBAE9CL,GAAwBK,mBACtB+C,EAAIpD,wBAAwBK,oBAI9B+C,EAAIpD,yBAEF,mBADKoD,EAAIpD,wBAAwBM,iCAGnCN,GAAwBM,+BACtB8C,EAAIpD,wBAAwBM,gCAG5BO,KACFH,IAAkB,GAGhBS,KACFD,IAAa,GAIXQ,KACF9B,GAAexH,EAAS,CAAC,EAAG4B,GAC5B8F,GAAe,IACW,IAAtB4B,GAAazH,OACf7B,EAASwH,GAAclG,GACvBtB,EAAS0H,GAAc7F,KAGA,IAArByH,GAAaxH,MACf9B,EAASwH,GAAcjG,GACvBvB,EAAS0H,GAAc5F,GACvB9B,EAAS0H,GAAc1F,KAGO,IAA5BsH,GAAa9H,aACfxB,EAASwH,GAAchG,GACvBxB,EAAS0H,GAAc5F,GACvB9B,EAAS0H,GAAc1F,KAGG,IAAxBsH,GAAavH,SACf/B,EAASwH,GAAc9F,GACvB1B,EAAS0H,GAAc3F,GACvB/B,EAAS0H,GAAc1F,KAKvBgJ,EAAII,WACF5D,KAAiBC,KACnBD,GAAe9G,EAAM8G,KAGvBxH,EAASwH,GAAcwD,EAAII,SAAUjL,KAGnC6K,EAAIK,WACF3D,KAAiBC,KACnBD,GAAehH,EAAMgH,KAGvB1H,EAAS0H,GAAcsD,EAAIK,SAAUlL,KAGnC6K,EAAIC,mBACNjL,EAAS2J,GAAqBqB,EAAIC,kBAAmB9K,IAGnD6K,EAAIzB,kBACFA,KAAoBC,KACtBD,GAAkB7I,EAAM6I,KAG1BvJ,EAASuJ,GAAiByB,EAAIzB,gBAAiBpJ,KAI7CiJ,KACF5B,GAAa,UAAW,GAItBmB,IACF3I,EAASwH,GAAc,CAAC,OAAQ,OAAQ,SAItCA,GAAa8D,QACftL,EAASwH,GAAc,CAAC,iBACjBW,GAAYoD,OAGjBP,EAAIQ,qBAAsB,CAC5B,GAAmD,oBAAxCR,EAAIQ,qBAAqBvH,WAClC,MAAM7E,EACJ,+EAIJ,GAAwD,oBAA7C4L,EAAIQ,qBAAqBtH,gBAClC,MAAM9E,EACJ,oFAKJyH,GAAqBmE,EAAIQ,qBAGzB1E,GAAYD,GAAmB5C,WAAW,GAC5C,WAE6B7D,IAAvByG,KACFA,GAAqBrD,GACnBC,EACAiC,IAKuB,OAAvBmB,IAAoD,kBAAdC,KACxCA,GAAYD,GAAmB5C,WAAW,KAM1CvH,GACFA,EAAOsO,GAGTN,GAASM,CAlOT,C,EAwOIS,GAAezL,EAAS,CAAC,EAAG,IAC7BuB,KACAC,KACAC,IAECiK,GAAkB1L,EAAS,CAAC,EAAG,IAChC0B,KACAC,IASCgK,GAAuB,SAAUrL,GACrC,IAAIsL,EAASpF,GAAclG,GAItBsL,GAAWA,EAAOC,UACrBD,EAAS,CACPE,aAAc9B,GACd6B,QAAS,aAIb,MAAMA,EAAU3N,EAAkBoC,EAAQuL,SACpCE,EAAgB7N,EAAkB0N,EAAOC,SAE/C,QAAK3B,GAAmB5J,EAAQwL,gBAI5BxL,EAAQwL,eAAiBhC,GAIvB8B,EAAOE,eAAiB/B,GACP,QAAZ8B,EAMLD,EAAOE,eAAiBjC,GAEZ,QAAZgC,IACmB,mBAAlBE,GACC3B,GAA+B2B,IAM9BC,QAAQP,GAAaI,IAG1BvL,EAAQwL,eAAiBjC,GAIvB+B,EAAOE,eAAiB/B,GACP,SAAZ8B,EAKLD,EAAOE,eAAiBhC,GACP,SAAZ+B,GAAsBxB,GAAwB0B,GAKhDC,QAAQN,GAAgBG,IAG7BvL,EAAQwL,eAAiB/B,KAKzB6B,EAAOE,eAAiBhC,KACvBO,GAAwB0B,OAMzBH,EAAOE,eAAiBjC,KACvBO,GAA+B2B,MAQ/BL,GAAgBG,KAChBvB,GAA6BuB,KAAaJ,GAAaI,MAMpC,0BAAtBtB,KACAL,GAAmB5J,EAAQwL,e,EAiBzBG,GAAe,SAAUC,GAC7BpO,EAAUoH,EAAUG,QAAS,CAAE/E,QAAS4L,IAExC,IAEE1F,GAAc0F,GAAMC,YAAYD,E,CAChC,MAAO9H,GACPiC,EAAO6F,EACT,C,EASIE,GAAmB,SAAUC,EAAc/L,GAC/C,IACExC,EAAUoH,EAAUG,QAAS,CAC3BzC,UAAWtC,EAAQgM,iBAAiBD,GACpCE,KAAMjM,G,CAER,MAAO8D,GACPtG,EAAUoH,EAAUG,QAAS,CAC3BzC,UAAW,KACX2J,KAAMjM,GAEV,CAKA,GAHAA,EAAQkM,gBAAgBH,GAGX,OAATA,EACF,GAAIvD,IAAcC,GAChB,IACEkD,GAAa3L,EACf,CAAE,MAAO8D,GAAI,MAEb,IACE9D,EAAQmM,aAAaJ,EAAM,GAC7B,CAAE,MAAOjI,GAAI,C,EAWbsI,GAAgB,SAAUC,GAE9B,IAAIC,EAAM,KACNC,EAAoB,KAExB,GAAIhE,GACF8D,EAAQ,oBAAsBA,MACzB,CAEL,MAAMG,EAAUvO,EAAYoO,EAAO,eACnCE,EAAoBC,GAAWA,EAAQ,EACzC,CAGwB,0BAAtBvC,IACAP,KAAcD,KAGd4C,EACE,iEACAA,EACA,kBAGJ,MAAMI,EAAelG,GACjBA,GAAmB5C,WAAW0I,GAC9BA,EAKJ,GAAI3C,KAAcD,GAChB,IACE6C,GAAM,IAAI1G,GAAY8G,gBAAgBD,EAAcxC,GACtD,CAAE,MAAOnG,GAAI,CAIf,IAAKwI,IAAQA,EAAIK,gBAAiB,CAChCL,EAAM7F,GAAemG,eAAelD,GAAW,WAAY,MAC3D,IACE4C,EAAIK,gBAAgBE,UAAYlD,GAC5BnD,GACAiG,C,CACJ,MAAO3I,GACP,CAEJ,CAEA,MAAMgJ,EAAOR,EAAIQ,MAAQR,EAAIK,gBAU7B,OARIN,GAASE,GACXO,EAAKC,aACHnK,EAASoK,eAAeT,GACxBO,EAAKG,WAAW,IAAM,MAKtBvD,KAAcD,GACT7C,GAAqBsG,KAC1BZ,EACAjE,GAAiB,OAAS,QAC1B,GAGGA,GAAiBiE,EAAIK,gBAAkBG,C,EAS1CK,GAAsB,SAAUtI,GACpC,OAAO6B,GAAmBwG,KACxBrI,EAAKyB,eAAiBzB,EACtBA,EAEAW,EAAW4H,aACT5H,EAAW6H,aACX7H,EAAW8H,UACX9H,EAAW+H,4BACX/H,EAAWgI,mBACb,K,EAUEC,GAAe,SAAUzN,GAC7B,OACEA,aAAmB2F,IACU,kBAArB3F,EAAQ0N,UACiB,kBAAxB1N,EAAQ2N,aACgB,oBAAxB3N,EAAQ6L,eACb7L,EAAQ4N,sBAAsBnI,IACG,oBAA5BzF,EAAQkM,iBACiB,oBAAzBlM,EAAQmM,cACiB,kBAAzBnM,EAAQwL,cACiB,oBAAzBxL,EAAQ+M,cACkB,oBAA1B/M,EAAQ6N,c,EAUfC,GAAU,SAAUtN,GACxB,MAAuB,oBAAT+E,GAAuB/E,aAAiB+E,C,EAGxD,SAASwI,GAOPjH,EAAYkH,EAA+BC,GAC3ClR,EAAa+J,GAAQoH,IACnBA,EAAKhB,KAAKtI,EAAWoJ,EAAaC,EAAM7D,GAAO,GAEnD,CAWA,MAAM+D,GAAoB,SAAUH,GAClC,IAAI3H,EAAU,KAMd,GAHA0H,GAAcjH,GAAMxC,uBAAwB0J,EAAa,MAGrDP,GAAaO,GAEf,OADArC,GAAaqC,IACN,EAIT,MAAMzC,EAAU1L,GAAkBmO,EAAYN,UAS9C,GANAK,GAAcjH,GAAMrC,oBAAqBuJ,EAAa,CACpDzC,UACA6C,YAAalH,KAKbkB,IACA4F,EAAYH,kBACXC,GAAQE,EAAYK,oBACrB1P,EAAW,WAAYqP,EAAYnB,YACnClO,EAAW,WAAYqP,EAAYL,aAGnC,OADAhC,GAAaqC,IACN,EAIT,GAAIA,EAAYhJ,WAAa3C,EAAUK,uBAErC,OADAiJ,GAAaqC,IACN,EAIT,GACE5F,IACA4F,EAAYhJ,WAAa3C,EAAUM,SACnChE,EAAW,UAAWqP,EAAYC,MAGlC,OADAtC,GAAaqC,IACN,EAIT,IAAK9G,GAAaqE,IAAY1D,GAAY0D,GAAU,CAElD,IAAK1D,GAAY0D,IAAY+C,GAAsB/C,GAAU,CAC3D,GACEjE,GAAwBC,wBAAwB3I,QAChDD,EAAW2I,GAAwBC,aAAcgE,GAEjD,OAAO,EAGT,GACEjE,GAAwBC,wBAAwBiD,UAChDlD,GAAwBC,aAAagE,GAErC,OAAO,CAEX,CAGA,GAAIzC,KAAiBG,GAAgBsC,GAAU,CAC7C,MAAMgD,EAAarI,GAAc8H,IAAgBA,EAAYO,WACvDtB,EAAahH,GAAc+H,IAAgBA,EAAYf,WAE7D,GAAIA,GAAcsB,EAGhB,IAAK,IAAIC,EAFUvB,EAAW3N,OAEJ,EAAGkP,GAAK,IAAKA,EAAG,CACxC,MAAMC,EAAa3I,EAAUmH,EAAWuB,IAAI,GAC5CC,EAAWC,gBAAkBV,EAAYU,gBAAkB,GAAK,EAChEH,EAAWxB,aAAa0B,EAAYzI,EAAegI,GACrD,CAEJ,CAGA,OADArC,GAAaqC,IACN,CACT,CAGA,OAAIA,aAAuB/I,IAAYoG,GAAqB2C,IAC1DrC,GAAaqC,IACN,GAKM,aAAZzC,GACa,YAAZA,GACY,aAAZA,IACF5M,EAAW,8BAA+BqP,EAAYnB,YAOpD1E,IAAsB6F,EAAYhJ,WAAa3C,EAAUf,OAE3D+E,EAAU2H,EAAYL,YAEtB5Q,EAAa,CAAC4E,GAAeC,GAAUC,KAAe8M,IACpDtI,EAAUlI,EAAckI,EAASsI,EAAM,IAAI,IAGzCX,EAAYL,cAAgBtH,IAC9B7I,EAAUoH,EAAUG,QAAS,CAAE/E,QAASgO,EAAYlI,cACpDkI,EAAYL,YAActH,IAK9B0H,GAAcjH,GAAM3C,sBAAuB6J,EAAa,OAEjD,IAtBLrC,GAAaqC,IACN,E,EAiCLY,GAAoB,SACxBC,EACAC,EACAtO,GAGA,GACEmI,KACY,OAAXmG,GAA8B,SAAXA,KACnBtO,KAASoC,GAAYpC,KAAS6J,IAE/B,OAAO,EAOT,GACErC,KACCF,GAAYgH,IACbnQ,EAAWmD,GAAWgN,SAGjB,GAAI/G,IAAmBpJ,EAAWoD,GAAW+M,SAG7C,IAAK1H,GAAa0H,IAAWhH,GAAYgH,IAC9C,KAIGR,GAAsBO,KACnBvH,GAAwBC,wBAAwB3I,QAChDD,EAAW2I,GAAwBC,aAAcsH,IAChDvH,GAAwBC,wBAAwBiD,UAC/ClD,GAAwBC,aAAasH,MACvCvH,GAAwBK,8BAA8B/I,QACtDD,EAAW2I,GAAwBK,mBAAoBmH,IACtDxH,GAAwBK,8BAA8B6C,UACrDlD,GAAwBK,mBAAmBmH,KAGrC,OAAXA,GACCxH,GAAwBM,iCACtBN,GAAwBC,wBAAwB3I,QAChDD,EAAW2I,GAAwBC,aAAc/G,IAChD8G,GAAwBC,wBAAwBiD,UAC/ClD,GAAwBC,aAAa/G,KAK3C,OAAO,OAGJ,GAAI6I,GAAoByF,SAIxB,GACLnQ,EAAWsI,GAAgB9I,EAAcqC,EAAO0B,GAAiB,WAK5D,GACO,QAAX4M,GAA+B,eAAXA,GAAsC,SAAXA,GACtC,WAAVD,GACkC,IAAlCxQ,EAAcmC,EAAO,WACrB2I,GAAc0F,GAMT,GACL5G,KACCtJ,EAAWsD,GAAmB9D,EAAcqC,EAAO0B,GAAiB,WAIhE,GAAI1B,EACT,OAAO,EAMT,OAAO,C,EAWH8N,GAAwB,SAAU/C,GACtC,MAAmB,mBAAZA,GAAgCtN,EAAYsN,EAASnJ,G,EAaxD2M,GAAsB,SAAUf,GAEpCD,GAAcjH,GAAMzC,yBAA0B2J,EAAa,MAE3D,MAAM,WAAEJ,GAAeI,EAGvB,IAAKJ,GAAcH,GAAaO,GAC9B,OAGF,MAAMgB,EAAY,CAChBC,SAAU,GACVC,UAAW,GACXC,UAAU,EACVC,kBAAmBhI,GACnBiI,mBAAevP,GAEjB,IAAIC,EAAI6N,EAAWtO,OAGnB,KAAOS,KAAK,CACV,MAAMuP,EAAO1B,EAAW7N,IAClB,KAAEgM,EAAI,aAAEP,EAAchL,MAAO0O,GAAcI,EAC3CR,EAASjP,GAAkBkM,GAE3BwD,EAAYL,EAClB,IAAI1O,EAAiB,UAATuL,EAAmBwD,EAAYhR,EAAWgR,GAsBtD,GAnBAP,EAAUC,SAAWH,EACrBE,EAAUE,UAAY1O,EACtBwO,EAAUG,UAAW,EACrBH,EAAUK,mBAAgBvP,EAC1BiO,GAAcjH,GAAMtC,sBAAuBwJ,EAAagB,GACxDxO,EAAQwO,EAAUE,WAKdtG,IAAoC,OAAXkG,GAA8B,SAAXA,IAE9ChD,GAAiBC,EAAMiC,GAGvBxN,EAAQqI,GAA8BrI,GAIpC4H,IAAgBzJ,EAAW,gCAAiC6B,GAAQ,CACtEsL,GAAiBC,EAAMiC,GACvB,QACF,CAGA,GAAIgB,EAAUK,cACZ,SAIF,IAAKL,EAAUG,SAAU,CACvBrD,GAAiBC,EAAMiC,GACvB,QACF,CAGA,IAAK9F,IAA4BvJ,EAAW,OAAQ6B,GAAQ,CAC1DsL,GAAiBC,EAAMiC,GACvB,QACF,CAGI7F,IACFpL,EAAa,CAAC4E,GAAeC,GAAUC,KAAe8M,IACpDnO,EAAQrC,EAAcqC,EAAOmO,EAAM,IAAI,IAK3C,MAAME,EAAQhP,GAAkBmO,EAAYN,UAC5C,GAAKkB,GAAkBC,EAAOC,EAAQtO,GAAtC,CAMA,GACE+F,IACwB,kBAAjBpD,GACkC,oBAAlCA,EAAaqM,iBAEpB,GAAIhE,QAGF,OAAQrI,EAAaqM,iBAAiBX,EAAOC,IAC3C,IAAK,cACHtO,EAAQ+F,GAAmB5C,WAAWnD,GACtC,MAGF,IAAK,mBACHA,EAAQ+F,GAAmB3C,gBAAgBpD,GAYnD,GAAIA,IAAU+O,EACZ,IACM/D,EACFwC,EAAYyB,eAAejE,EAAcO,EAAMvL,GAG/CwN,EAAY7B,aAAaJ,EAAMvL,GAG7BiN,GAAaO,GACfrC,GAAaqC,GAEb1Q,EAASsH,EAAUG,Q,CAErB,MAAOjB,GACPgI,GAAiBC,EAAMiC,EACzB,CA9CF,MAFElC,GAAiBC,EAAMiC,EAkD3B,CAGAD,GAAcjH,GAAM5C,wBAAyB8J,EAAa,K,EAQtD0B,GAAqB,SAArBA,EAA+BC,GACnC,IAAIC,EAAa,KACjB,MAAMC,EAAiB1C,GAAoBwC,GAK3C,IAFA5B,GAAcjH,GAAMvC,wBAAyBoL,EAAU,MAE/CC,EAAaC,EAAeC,YAElC/B,GAAcjH,GAAMpC,uBAAwBkL,EAAY,MAGxDzB,GAAkByB,GAGlBb,GAAoBa,GAGhBA,EAAWvJ,mBAAmBhB,GAChCqK,EAAmBE,EAAWvJ,SAKlC0H,GAAcjH,GAAM1C,uBAAwBuL,EAAU,K,EAmOxD,OA/NA/K,EAAUmL,SAAW,SAAU1D,GAAe,IAAR3B,EAAGrL,UAAAC,OAAA,QAAAQ,IAAAT,UAAA,GAAAA,UAAA,GAAG,CAAC,EACvCyN,EAAO,KACPkD,EAAe,KACfhC,EAAc,KACdiC,EAAa,KAUjB,GANAtG,IAAkB0C,EACd1C,KACF0C,EAAQ,eAIW,kBAAVA,IAAuByB,GAAQzB,GAAQ,CAChD,GAA8B,oBAAnBA,EAAMrO,SAMf,MAAMc,EAAgB,8BAJtB,GAAqB,kBADrBuN,EAAQA,EAAMrO,YAEZ,MAAMc,EAAgB,kCAK5B,CAGA,IAAK8F,EAAUM,YACb,OAAOmH,EAgBT,GAZK/D,IACHmC,GAAaC,GAIf9F,EAAUG,QAAU,GAGC,kBAAVsH,IACTtD,IAAW,GAGTA,IAEF,GAAKsD,EAAeqB,SAAU,CAC5B,MAAMnC,EAAU1L,GAAmBwM,EAAeqB,UAClD,IAAKxG,GAAaqE,IAAY1D,GAAY0D,GACxC,MAAMzM,EACJ,0DAGN,OACK,GAAIuN,aAAiB9G,EAG1BuH,EAAOV,GAAc,iBACrB4D,EAAelD,EAAKxG,cAAcO,WAAWwF,GAAO,GAElD2D,EAAahL,WAAa3C,EAAUrC,SACV,SAA1BgQ,EAAatC,UAIsB,SAA1BsC,EAAatC,SADtBZ,EAAOkD,EAKPlD,EAAKoD,YAAYF,OAEd,CAEL,IACGxH,KACAL,KACAE,KAEuB,IAAxBgE,EAAM/N,QAAQ,KAEd,OAAOiI,IAAsBmC,GACzBnC,GAAmB5C,WAAW0I,GAC9BA,EAON,GAHAS,EAAOV,GAAcC,IAGhBS,EACH,OAAOtE,GAAa,KAAOE,GAAsBlC,GAAY,EAEjE,CAGIsG,GAAQvE,IACVoD,GAAamB,EAAKqD,YAIpB,MAAMC,EAAejD,GAAoBpE,GAAWsD,EAAQS,GAG5D,KAAQkB,EAAcoC,EAAaN,YAEjC3B,GAAkBH,GAGlBe,GAAoBf,GAGhBA,EAAY3H,mBAAmBhB,GACjCqK,GAAmB1B,EAAY3H,SAKnC,GAAI0C,GACF,OAAOsD,EAIT,GAAI7D,GAAY,CACd,GAAIC,GAGF,IAFAwH,EAAatJ,GAAuBuG,KAAKJ,EAAKxG,eAEvCwG,EAAKqD,YAEVF,EAAWC,YAAYpD,EAAKqD,iBAG9BF,EAAanD,EAcf,OAXI1F,GAAaiJ,YAAcjJ,GAAakJ,kBAQ1CL,EAAapJ,GAAWqG,KAAK/H,EAAkB8K,GAAY,IAGtDA,CACT,CAEA,IAAIM,EAAiBlI,GAAiByE,EAAK0D,UAAY1D,EAAKD,UAsB5D,OAlBExE,IACAnB,GAAa,aACb4F,EAAKxG,eACLwG,EAAKxG,cAAcmK,SACnB3D,EAAKxG,cAAcmK,QAAQ1E,MAC3BpN,EAAWwD,EAA0B2K,EAAKxG,cAAcmK,QAAQ1E,QAEhEwE,EACE,aAAezD,EAAKxG,cAAcmK,QAAQ1E,KAAO,MAAQwE,GAIzDpI,IACFpL,EAAa,CAAC4E,GAAeC,GAAUC,KAAe8M,IACpD4B,EAAiBpS,EAAcoS,EAAgB5B,EAAM,IAAI,IAItDpI,IAAsBmC,GACzBnC,GAAmB5C,WAAW4M,GAC9BA,C,EAGN3L,EAAU8L,UAAY,WACpBjG,GADiCpL,UAAAC,OAAA,QAAAQ,IAAAT,UAAA,GAAAA,UAAA,GAAG,CAAC,GAErCiJ,IAAa,C,EAGf1D,EAAU+L,YAAc,WACtBvG,GAAS,KACT9B,IAAa,C,EAGf1D,EAAUgM,iBAAmB,SAAUC,EAAKvB,EAAM9O,GAE3C4J,IACHK,GAAa,CAAC,GAGhB,MAAMoE,EAAQhP,GAAkBgR,GAC1B/B,EAASjP,GAAkByP,GACjC,OAAOV,GAAkBC,EAAOC,EAAQtO,E,EAG1CoE,EAAUkM,QAAU,SAAUC,EAAYC,GACZ,oBAAjBA,GAIXxT,EAAUsJ,GAAMiK,GAAaC,E,EAG/BpM,EAAUqM,WAAa,SAAUF,EAAYC,GAC3C,QAAqBlR,IAAjBkR,EAA4B,CAC9B,MAAM7Q,EAAQ/C,EAAiB0J,GAAMiK,GAAaC,GAElD,OAAkB,IAAX7Q,OACHL,EACApC,EAAYoJ,GAAMiK,GAAa5Q,EAAO,GAAG,EAC/C,CAEA,OAAO7C,EAASwJ,GAAMiK,G,EAGxBnM,EAAUsM,YAAc,SAAUH,GAChCjK,GAAMiK,GAAc,E,EAGtBnM,EAAUuM,eAAiB,WACzBrK,GAAQ7C,I,EAGHW,CACT,C,OAEeD,I", "file": "static/js/4.c029bb58.chunk.js", "sourcesContent": ["const {\n  entries,\n  setPrototypeOf,\n  isFrozen,\n  getPrototypeOf,\n  getOwnPropertyDescriptor,\n} = Object;\n\nlet { freeze, seal, create } = Object; // eslint-disable-line import/no-mutable-exports\nlet { apply, construct } = typeof Reflect !== 'undefined' && Reflect;\n\nif (!freeze) {\n  freeze = function (x) {\n    return x;\n  };\n}\n\nif (!seal) {\n  seal = function (x) {\n    return x;\n  };\n}\n\nif (!apply) {\n  apply = function (fun, thisValue, args) {\n    return fun.apply(thisValue, args);\n  };\n}\n\nif (!construct) {\n  construct = function (Func, args) {\n    return new Func(...args);\n  };\n}\n\nconst arrayForEach = unapply(Array.prototype.forEach);\nconst arrayIndexOf = unapply(Array.prototype.indexOf);\nconst arrayLastIndexOf = unapply(Array.prototype.lastIndexOf);\nconst arrayPop = unapply(Array.prototype.pop);\nconst arrayPush = unapply(Array.prototype.push);\nconst arraySlice = unapply(Array.prototype.slice);\nconst arraySplice = unapply(Array.prototype.splice);\n\nconst stringToLowerCase = unapply(String.prototype.toLowerCase);\nconst stringToString = unapply(String.prototype.toString);\nconst stringMatch = unapply(String.prototype.match);\nconst stringReplace = unapply(String.prototype.replace);\nconst stringIndexOf = unapply(String.prototype.indexOf);\nconst stringTrim = unapply(String.prototype.trim);\n\nconst objectHasOwnProperty = unapply(Object.prototype.hasOwnProperty);\n\nconst regExpTest = unapply(RegExp.prototype.test);\n\nconst typeErrorCreate = unconstruct(TypeError);\n\n/**\n * Creates a new function that calls the given function with a specified thisArg and arguments.\n *\n * @param func - The function to be wrapped and called.\n * @returns A new function that calls the given function with a specified thisArg and arguments.\n */\nfunction unapply<T>(\n  func: (thisArg: any, ...args: any[]) => T\n): (thisArg: any, ...args: any[]) => T {\n  return (thisArg: any, ...args: any[]): T => {\n    if (thisArg instanceof RegExp) {\n      thisArg.lastIndex = 0;\n    }\n\n    return apply(func, thisArg, args);\n  };\n}\n\n/**\n * Creates a new function that constructs an instance of the given constructor function with the provided arguments.\n *\n * @param func - The constructor function to be wrapped and called.\n * @returns A new function that constructs an instance of the given constructor function with the provided arguments.\n */\nfunction unconstruct<T>(func: (...args: any[]) => T): (...args: any[]) => T {\n  return (...args: any[]): T => construct(func, args);\n}\n\n/**\n * Add properties to a lookup table\n *\n * @param set - The set to which elements will be added.\n * @param array - The array containing elements to be added to the set.\n * @param transformCaseFunc - An optional function to transform the case of each element before adding to the set.\n * @returns The modified set with added elements.\n */\nfunction addToSet(\n  set: Record<string, any>,\n  array: readonly any[],\n  transformCaseFunc: ReturnType<typeof unapply<string>> = stringToLowerCase\n): Record<string, any> {\n  if (setPrototypeOf) {\n    // Make 'in' and truthy checks like Boolean(set.constructor)\n    // independent of any properties defined on Object.prototype.\n    // Prevent prototype setters from intercepting set as a this value.\n    setPrototypeOf(set, null);\n  }\n\n  let l = array.length;\n  while (l--) {\n    let element = array[l];\n    if (typeof element === 'string') {\n      const lcElement = transformCaseFunc(element);\n      if (lcElement !== element) {\n        // Config presets (e.g. tags.js, attrs.js) are immutable.\n        if (!isFrozen(array)) {\n          (array as any[])[l] = lcElement;\n        }\n\n        element = lcElement;\n      }\n    }\n\n    set[element] = true;\n  }\n\n  return set;\n}\n\n/**\n * Clean up an array to harden against CSPP\n *\n * @param array - The array to be cleaned.\n * @returns The cleaned version of the array\n */\nfunction cleanArray<T>(array: T[]): Array<T | null> {\n  for (let index = 0; index < array.length; index++) {\n    const isPropertyExist = objectHasOwnProperty(array, index);\n\n    if (!isPropertyExist) {\n      array[index] = null;\n    }\n  }\n\n  return array;\n}\n\n/**\n * Shallow clone an object\n *\n * @param object - The object to be cloned.\n * @returns A new object that copies the original.\n */\nfunction clone<T extends Record<string, any>>(object: T): T {\n  const newObject = create(null);\n\n  for (const [property, value] of entries(object)) {\n    const isPropertyExist = objectHasOwnProperty(object, property);\n\n    if (isPropertyExist) {\n      if (Array.isArray(value)) {\n        newObject[property] = cleanArray(value);\n      } else if (\n        value &&\n        typeof value === 'object' &&\n        value.constructor === Object\n      ) {\n        newObject[property] = clone(value);\n      } else {\n        newObject[property] = value;\n      }\n    }\n  }\n\n  return newObject;\n}\n\n/**\n * This method automatically checks if the prop is function or getter and behaves accordingly.\n *\n * @param object - The object to look up the getter function in its prototype chain.\n * @param prop - The property name for which to find the getter function.\n * @returns The getter function found in the prototype chain or a fallback function.\n */\nfunction lookupGetter<T extends Record<string, any>>(\n  object: T,\n  prop: string\n): ReturnType<typeof unapply<any>> | (() => null) {\n  while (object !== null) {\n    const desc = getOwnPropertyDescriptor(object, prop);\n\n    if (desc) {\n      if (desc.get) {\n        return unapply(desc.get);\n      }\n\n      if (typeof desc.value === 'function') {\n        return unapply(desc.value);\n      }\n    }\n\n    object = getPrototypeOf(object);\n  }\n\n  function fallbackValue(): null {\n    return null;\n  }\n\n  return fallbackValue;\n}\n\nexport {\n  // Array\n  arrayForEach,\n  arrayIndexOf,\n  arrayLastIndexOf,\n  arrayPop,\n  arrayPush,\n  arraySlice,\n  arraySplice,\n  // Object\n  entries,\n  freeze,\n  getPrototypeOf,\n  getOwnPropertyDescriptor,\n  isFrozen,\n  setPrototypeOf,\n  seal,\n  clone,\n  create,\n  objectHasOwnProperty,\n  // RegExp\n  regExpTest,\n  // String\n  stringIndexOf,\n  stringMatch,\n  stringReplace,\n  stringToLowerCase,\n  stringToString,\n  stringTrim,\n  // Errors\n  typeErrorCreate,\n  // Other\n  lookupGetter,\n  addToSet,\n  // Reflect\n  unapply,\n  unconstruct,\n};\n", "import { freeze } from './utils.js';\n\nexport const html = freeze([\n  'a',\n  'abbr',\n  'acronym',\n  'address',\n  'area',\n  'article',\n  'aside',\n  'audio',\n  'b',\n  'bdi',\n  'bdo',\n  'big',\n  'blink',\n  'blockquote',\n  'body',\n  'br',\n  'button',\n  'canvas',\n  'caption',\n  'center',\n  'cite',\n  'code',\n  'col',\n  'colgroup',\n  'content',\n  'data',\n  'datalist',\n  'dd',\n  'decorator',\n  'del',\n  'details',\n  'dfn',\n  'dialog',\n  'dir',\n  'div',\n  'dl',\n  'dt',\n  'element',\n  'em',\n  'fieldset',\n  'figcaption',\n  'figure',\n  'font',\n  'footer',\n  'form',\n  'h1',\n  'h2',\n  'h3',\n  'h4',\n  'h5',\n  'h6',\n  'head',\n  'header',\n  'hgroup',\n  'hr',\n  'html',\n  'i',\n  'img',\n  'input',\n  'ins',\n  'kbd',\n  'label',\n  'legend',\n  'li',\n  'main',\n  'map',\n  'mark',\n  'marquee',\n  'menu',\n  'menuitem',\n  'meter',\n  'nav',\n  'nobr',\n  'ol',\n  'optgroup',\n  'option',\n  'output',\n  'p',\n  'picture',\n  'pre',\n  'progress',\n  'q',\n  'rp',\n  'rt',\n  'ruby',\n  's',\n  'samp',\n  'section',\n  'select',\n  'shadow',\n  'small',\n  'source',\n  'spacer',\n  'span',\n  'strike',\n  'strong',\n  'style',\n  'sub',\n  'summary',\n  'sup',\n  'table',\n  'tbody',\n  'td',\n  'template',\n  'textarea',\n  'tfoot',\n  'th',\n  'thead',\n  'time',\n  'tr',\n  'track',\n  'tt',\n  'u',\n  'ul',\n  'var',\n  'video',\n  'wbr',\n] as const);\n\nexport const svg = freeze([\n  'svg',\n  'a',\n  'altglyph',\n  'altglyphdef',\n  'altglyphitem',\n  'animatecolor',\n  'animatemotion',\n  'animatetransform',\n  'circle',\n  'clippath',\n  'defs',\n  'desc',\n  'ellipse',\n  'filter',\n  'font',\n  'g',\n  'glyph',\n  'glyphref',\n  'hkern',\n  'image',\n  'line',\n  'lineargradient',\n  'marker',\n  'mask',\n  'metadata',\n  'mpath',\n  'path',\n  'pattern',\n  'polygon',\n  'polyline',\n  'radialgradient',\n  'rect',\n  'stop',\n  'style',\n  'switch',\n  'symbol',\n  'text',\n  'textpath',\n  'title',\n  'tref',\n  'tspan',\n  'view',\n  'vkern',\n] as const);\n\nexport const svgFilters = freeze([\n  'feBlend',\n  'feColorMatrix',\n  'feComponentTransfer',\n  'feComposite',\n  'feConvolveMatrix',\n  'feDiffuseLighting',\n  'feDisplacementMap',\n  'feDistantLight',\n  'feDropShadow',\n  'feFlood',\n  'feFuncA',\n  'feFuncB',\n  'feFuncG',\n  'feFuncR',\n  'feGaussianBlur',\n  'feImage',\n  'feMerge',\n  'feMergeNode',\n  'feMorphology',\n  'feOffset',\n  'fePointLight',\n  'feSpecularLighting',\n  'feSpotLight',\n  'feTile',\n  'feTurbulence',\n] as const);\n\n// List of SVG elements that are disallowed by default.\n// We still need to know them so that we can do namespace\n// checks properly in case one wants to add them to\n// allow-list.\nexport const svgDisallowed = freeze([\n  'animate',\n  'color-profile',\n  'cursor',\n  'discard',\n  'font-face',\n  'font-face-format',\n  'font-face-name',\n  'font-face-src',\n  'font-face-uri',\n  'foreignobject',\n  'hatch',\n  'hatchpath',\n  'mesh',\n  'meshgradient',\n  'meshpatch',\n  'meshrow',\n  'missing-glyph',\n  'script',\n  'set',\n  'solidcolor',\n  'unknown',\n  'use',\n] as const);\n\nexport const mathMl = freeze([\n  'math',\n  'menclose',\n  'merror',\n  'mfenced',\n  'mfrac',\n  'mglyph',\n  'mi',\n  'mlabeledtr',\n  'mmultiscripts',\n  'mn',\n  'mo',\n  'mover',\n  'mpadded',\n  'mphantom',\n  'mroot',\n  'mrow',\n  'ms',\n  'mspace',\n  'msqrt',\n  'mstyle',\n  'msub',\n  'msup',\n  'msubsup',\n  'mtable',\n  'mtd',\n  'mtext',\n  'mtr',\n  'munder',\n  'munderover',\n  'mprescripts',\n] as const);\n\n// Similarly to SVG, we want to know all MathML elements,\n// even those that we disallow by default.\nexport const mathMlDisallowed = freeze([\n  'maction',\n  'maligngroup',\n  'malignmark',\n  'mlongdiv',\n  'mscarries',\n  'mscarry',\n  'msgroup',\n  'mstack',\n  'msline',\n  'msrow',\n  'semantics',\n  'annotation',\n  'annotation-xml',\n  'mprescripts',\n  'none',\n] as const);\n\nexport const text = freeze(['#text'] as const);\n", "import { freeze } from './utils.js';\n\nexport const html = freeze([\n  'accept',\n  'action',\n  'align',\n  'alt',\n  'autocapitalize',\n  'autocomplete',\n  'autopictureinpicture',\n  'autoplay',\n  'background',\n  'bgcolor',\n  'border',\n  'capture',\n  'cellpadding',\n  'cellspacing',\n  'checked',\n  'cite',\n  'class',\n  'clear',\n  'color',\n  'cols',\n  'colspan',\n  'controls',\n  'controlslist',\n  'coords',\n  'crossorigin',\n  'datetime',\n  'decoding',\n  'default',\n  'dir',\n  'disabled',\n  'disablepictureinpicture',\n  'disableremoteplayback',\n  'download',\n  'draggable',\n  'enctype',\n  'enterkeyhint',\n  'face',\n  'for',\n  'headers',\n  'height',\n  'hidden',\n  'high',\n  'href',\n  'hreflang',\n  'id',\n  'inputmode',\n  'integrity',\n  'ismap',\n  'kind',\n  'label',\n  'lang',\n  'list',\n  'loading',\n  'loop',\n  'low',\n  'max',\n  'maxlength',\n  'media',\n  'method',\n  'min',\n  'minlength',\n  'multiple',\n  'muted',\n  'name',\n  'nonce',\n  'noshade',\n  'novalidate',\n  'nowrap',\n  'open',\n  'optimum',\n  'pattern',\n  'placeholder',\n  'playsinline',\n  'popover',\n  'popovertarget',\n  'popovertargetaction',\n  'poster',\n  'preload',\n  'pubdate',\n  'radiogroup',\n  'readonly',\n  'rel',\n  'required',\n  'rev',\n  'reversed',\n  'role',\n  'rows',\n  'rowspan',\n  'spellcheck',\n  'scope',\n  'selected',\n  'shape',\n  'size',\n  'sizes',\n  'span',\n  'srclang',\n  'start',\n  'src',\n  'srcset',\n  'step',\n  'style',\n  'summary',\n  'tabindex',\n  'title',\n  'translate',\n  'type',\n  'usemap',\n  'valign',\n  'value',\n  'width',\n  'wrap',\n  'xmlns',\n  'slot',\n] as const);\n\nexport const svg = freeze([\n  'accent-height',\n  'accumulate',\n  'additive',\n  'alignment-baseline',\n  'amplitude',\n  'ascent',\n  'attributename',\n  'attributetype',\n  'azimuth',\n  'basefrequency',\n  'baseline-shift',\n  'begin',\n  'bias',\n  'by',\n  'class',\n  'clip',\n  'clippathunits',\n  'clip-path',\n  'clip-rule',\n  'color',\n  'color-interpolation',\n  'color-interpolation-filters',\n  'color-profile',\n  'color-rendering',\n  'cx',\n  'cy',\n  'd',\n  'dx',\n  'dy',\n  'diffuseconstant',\n  'direction',\n  'display',\n  'divisor',\n  'dur',\n  'edgemode',\n  'elevation',\n  'end',\n  'exponent',\n  'fill',\n  'fill-opacity',\n  'fill-rule',\n  'filter',\n  'filterunits',\n  'flood-color',\n  'flood-opacity',\n  'font-family',\n  'font-size',\n  'font-size-adjust',\n  'font-stretch',\n  'font-style',\n  'font-variant',\n  'font-weight',\n  'fx',\n  'fy',\n  'g1',\n  'g2',\n  'glyph-name',\n  'glyphref',\n  'gradientunits',\n  'gradienttransform',\n  'height',\n  'href',\n  'id',\n  'image-rendering',\n  'in',\n  'in2',\n  'intercept',\n  'k',\n  'k1',\n  'k2',\n  'k3',\n  'k4',\n  'kerning',\n  'keypoints',\n  'keysplines',\n  'keytimes',\n  'lang',\n  'lengthadjust',\n  'letter-spacing',\n  'kernelmatrix',\n  'kernelunitlength',\n  'lighting-color',\n  'local',\n  'marker-end',\n  'marker-mid',\n  'marker-start',\n  'markerheight',\n  'markerunits',\n  'markerwidth',\n  'maskcontentunits',\n  'maskunits',\n  'max',\n  'mask',\n  'media',\n  'method',\n  'mode',\n  'min',\n  'name',\n  'numoctaves',\n  'offset',\n  'operator',\n  'opacity',\n  'order',\n  'orient',\n  'orientation',\n  'origin',\n  'overflow',\n  'paint-order',\n  'path',\n  'pathlength',\n  'patterncontentunits',\n  'patterntransform',\n  'patternunits',\n  'points',\n  'preservealpha',\n  'preserveaspectratio',\n  'primitiveunits',\n  'r',\n  'rx',\n  'ry',\n  'radius',\n  'refx',\n  'refy',\n  'repeatcount',\n  'repeatdur',\n  'restart',\n  'result',\n  'rotate',\n  'scale',\n  'seed',\n  'shape-rendering',\n  'slope',\n  'specularconstant',\n  'specularexponent',\n  'spreadmethod',\n  'startoffset',\n  'stddeviation',\n  'stitchtiles',\n  'stop-color',\n  'stop-opacity',\n  'stroke-dasharray',\n  'stroke-dashoffset',\n  'stroke-linecap',\n  'stroke-linejoin',\n  'stroke-miterlimit',\n  'stroke-opacity',\n  'stroke',\n  'stroke-width',\n  'style',\n  'surfacescale',\n  'systemlanguage',\n  'tabindex',\n  'tablevalues',\n  'targetx',\n  'targety',\n  'transform',\n  'transform-origin',\n  'text-anchor',\n  'text-decoration',\n  'text-rendering',\n  'textlength',\n  'type',\n  'u1',\n  'u2',\n  'unicode',\n  'values',\n  'viewbox',\n  'visibility',\n  'version',\n  'vert-adv-y',\n  'vert-origin-x',\n  'vert-origin-y',\n  'width',\n  'word-spacing',\n  'wrap',\n  'writing-mode',\n  'xchannelselector',\n  'ychannelselector',\n  'x',\n  'x1',\n  'x2',\n  'xmlns',\n  'y',\n  'y1',\n  'y2',\n  'z',\n  'zoomandpan',\n] as const);\n\nexport const mathMl = freeze([\n  'accent',\n  'accentunder',\n  'align',\n  'bevelled',\n  'close',\n  'columnsalign',\n  'columnlines',\n  'columnspan',\n  'denomalign',\n  'depth',\n  'dir',\n  'display',\n  'displaystyle',\n  'encoding',\n  'fence',\n  'frame',\n  'height',\n  'href',\n  'id',\n  'largeop',\n  'length',\n  'linethickness',\n  'lspace',\n  'lquote',\n  'mathbackground',\n  'mathcolor',\n  'mathsize',\n  'mathvariant',\n  'maxsize',\n  'minsize',\n  'movablelimits',\n  'notation',\n  'numalign',\n  'open',\n  'rowalign',\n  'rowlines',\n  'rowspacing',\n  'rowspan',\n  'rspace',\n  'rquote',\n  'scriptlevel',\n  'scriptminsize',\n  'scriptsizemultiplier',\n  'selection',\n  'separator',\n  'separators',\n  'stretchy',\n  'subscriptshift',\n  'supscriptshift',\n  'symmetric',\n  'voffset',\n  'width',\n  'xmlns',\n]);\n\nexport const xml = freeze([\n  'xlink:href',\n  'xml:id',\n  'xlink:title',\n  'xml:space',\n  'xmlns:xlink',\n] as const);\n", "import { seal } from './utils.js';\n\n// eslint-disable-next-line unicorn/better-regex\nexport const MUSTACHE_EXPR = seal(/\\{\\{[\\w\\W]*|[\\w\\W]*\\}\\}/gm); // Specify template detection regex for SAFE_FOR_TEMPLATES mode\nexport const ERB_EXPR = seal(/<%[\\w\\W]*|[\\w\\W]*%>/gm);\nexport const TMPLIT_EXPR = seal(/\\$\\{[\\w\\W]*/gm); // eslint-disable-line unicorn/better-regex\nexport const DATA_ATTR = seal(/^data-[\\-\\w.\\u00B7-\\uFFFF]+$/); // eslint-disable-line no-useless-escape\nexport const ARIA_ATTR = seal(/^aria-[\\-\\w]+$/); // eslint-disable-line no-useless-escape\nexport const IS_ALLOWED_URI = seal(\n  /^(?:(?:(?:f|ht)tps?|mailto|tel|callto|sms|cid|xmpp|matrix):|[^a-z]|[a-z+.\\-]+(?:[^a-z+.\\-:]|$))/i // eslint-disable-line no-useless-escape\n);\nexport const IS_SCRIPT_OR_DATA = seal(/^(?:\\w+script|data):/i);\nexport const ATTR_WHITESPACE = seal(\n  /[\\u0000-\\u0020\\u00A0\\u1680\\u180E\\u2000-\\u2029\\u205F\\u3000]/g // eslint-disable-line no-control-regex\n);\nexport const DOCTYPE_NAME = seal(/^html$/i);\nexport const CUSTOM_ELEMENT = seal(/^[a-z][.\\w]*(-[.\\w]+)+$/i);\n", "/* eslint-disable @typescript-eslint/indent */\n\nimport type { TrustedHTML, TrustedTypesWindow } from 'trusted-types/lib';\nimport type { Config, UseProfilesConfig } from './config';\nimport * as TAGS from './tags.js';\nimport * as ATTRS from './attrs.js';\nimport * as EXPRESSIONS from './regexp.js';\nimport {\n  addToSet,\n  clone,\n  entries,\n  freeze,\n  arrayForEach,\n  arrayLastIndexOf,\n  arrayPop,\n  arrayPush,\n  arraySplice,\n  stringMatch,\n  stringReplace,\n  stringToLowerCase,\n  stringToString,\n  stringIndexOf,\n  stringTrim,\n  regExpTest,\n  typeErrorCreate,\n  lookupGetter,\n  create,\n  objectHasOwnProperty,\n} from './utils.js';\n\nexport type { Config } from './config';\n\ndeclare const VERSION: string;\n\n// https://developer.mozilla.org/en-US/docs/Web/API/Node/nodeType\nconst NODE_TYPE = {\n  element: 1,\n  attribute: 2,\n  text: 3,\n  cdataSection: 4,\n  entityReference: 5, // Deprecated\n  entityNode: 6, // Deprecated\n  progressingInstruction: 7,\n  comment: 8,\n  document: 9,\n  documentType: 10,\n  documentFragment: 11,\n  notation: 12, // Deprecated\n};\n\nconst getGlobal = function (): WindowLike {\n  return typeof window === 'undefined' ? null : window;\n};\n\n/**\n * Creates a no-op policy for internal use only.\n * Don't export this function outside this module!\n * @param trustedTypes The policy factory.\n * @param purifyHostElement The Script element used to load DOMPurify (to determine policy name suffix).\n * @return The policy created (or null, if Trusted Types\n * are not supported or creating the policy failed).\n */\nconst _createTrustedTypesPolicy = function (\n  trustedTypes: TrustedTypePolicyFactory,\n  purifyHostElement: HTMLScriptElement\n) {\n  if (\n    typeof trustedTypes !== 'object' ||\n    typeof trustedTypes.createPolicy !== 'function'\n  ) {\n    return null;\n  }\n\n  // Allow the callers to control the unique policy name\n  // by adding a data-tt-policy-suffix to the script element with the DOMPurify.\n  // Policy creation with duplicate names throws in Trusted Types.\n  let suffix = null;\n  const ATTR_NAME = 'data-tt-policy-suffix';\n  if (purifyHostElement && purifyHostElement.hasAttribute(ATTR_NAME)) {\n    suffix = purifyHostElement.getAttribute(ATTR_NAME);\n  }\n\n  const policyName = 'dompurify' + (suffix ? '#' + suffix : '');\n\n  try {\n    return trustedTypes.createPolicy(policyName, {\n      createHTML(html) {\n        return html;\n      },\n      createScriptURL(scriptUrl) {\n        return scriptUrl;\n      },\n    });\n  } catch (_) {\n    // Policy creation failed (most likely another DOMPurify script has\n    // already run). Skip creating the policy, as this will only cause errors\n    // if TT are enforced.\n    console.warn(\n      'TrustedTypes policy ' + policyName + ' could not be created.'\n    );\n    return null;\n  }\n};\n\nconst _createHooksMap = function (): HooksMap {\n  return {\n    afterSanitizeAttributes: [],\n    afterSanitizeElements: [],\n    afterSanitizeShadowDOM: [],\n    beforeSanitizeAttributes: [],\n    beforeSanitizeElements: [],\n    beforeSanitizeShadowDOM: [],\n    uponSanitizeAttribute: [],\n    uponSanitizeElement: [],\n    uponSanitizeShadowNode: [],\n  };\n};\n\nfunction createDOMPurify(window: WindowLike = getGlobal()): DOMPurify {\n  const DOMPurify: DOMPurify = (root: WindowLike) => createDOMPurify(root);\n\n  DOMPurify.version = VERSION;\n\n  DOMPurify.removed = [];\n\n  if (\n    !window ||\n    !window.document ||\n    window.document.nodeType !== NODE_TYPE.document ||\n    !window.Element\n  ) {\n    // Not running in a browser, provide a factory function\n    // so that you can pass your own Window\n    DOMPurify.isSupported = false;\n\n    return DOMPurify;\n  }\n\n  let { document } = window;\n\n  const originalDocument = document;\n  const currentScript: HTMLScriptElement =\n    originalDocument.currentScript as HTMLScriptElement;\n  const {\n    DocumentFragment,\n    HTMLTemplateElement,\n    Node,\n    Element,\n    NodeFilter,\n    NamedNodeMap = window.NamedNodeMap || (window as any).MozNamedAttrMap,\n    HTMLFormElement,\n    DOMParser,\n    trustedTypes,\n  } = window;\n\n  const ElementPrototype = Element.prototype;\n\n  const cloneNode = lookupGetter(ElementPrototype, 'cloneNode');\n  const remove = lookupGetter(ElementPrototype, 'remove');\n  const getNextSibling = lookupGetter(ElementPrototype, 'nextSibling');\n  const getChildNodes = lookupGetter(ElementPrototype, 'childNodes');\n  const getParentNode = lookupGetter(ElementPrototype, 'parentNode');\n\n  // As per issue #47, the web-components registry is inherited by a\n  // new document created via createHTMLDocument. As per the spec\n  // (http://w3c.github.io/webcomponents/spec/custom/#creating-and-passing-registries)\n  // a new empty registry is used when creating a template contents owner\n  // document, so we use that as our parent document to ensure nothing\n  // is inherited.\n  if (typeof HTMLTemplateElement === 'function') {\n    const template = document.createElement('template');\n    if (template.content && template.content.ownerDocument) {\n      document = template.content.ownerDocument;\n    }\n  }\n\n  let trustedTypesPolicy;\n  let emptyHTML = '';\n\n  const {\n    implementation,\n    createNodeIterator,\n    createDocumentFragment,\n    getElementsByTagName,\n  } = document;\n  const { importNode } = originalDocument;\n\n  let hooks = _createHooksMap();\n\n  /**\n   * Expose whether this browser supports running the full DOMPurify.\n   */\n  DOMPurify.isSupported =\n    typeof entries === 'function' &&\n    typeof getParentNode === 'function' &&\n    implementation &&\n    implementation.createHTMLDocument !== undefined;\n\n  const {\n    MUSTACHE_EXPR,\n    ERB_EXPR,\n    TMPLIT_EXPR,\n    DATA_ATTR,\n    ARIA_ATTR,\n    IS_SCRIPT_OR_DATA,\n    ATTR_WHITESPACE,\n    CUSTOM_ELEMENT,\n  } = EXPRESSIONS;\n\n  let { IS_ALLOWED_URI } = EXPRESSIONS;\n\n  /**\n   * We consider the elements and attributes below to be safe. Ideally\n   * don't add any new ones but feel free to remove unwanted ones.\n   */\n\n  /* allowed element names */\n  let ALLOWED_TAGS = null;\n  const DEFAULT_ALLOWED_TAGS = addToSet({}, [\n    ...TAGS.html,\n    ...TAGS.svg,\n    ...TAGS.svgFilters,\n    ...TAGS.mathMl,\n    ...TAGS.text,\n  ]);\n\n  /* Allowed attribute names */\n  let ALLOWED_ATTR = null;\n  const DEFAULT_ALLOWED_ATTR = addToSet({}, [\n    ...ATTRS.html,\n    ...ATTRS.svg,\n    ...ATTRS.mathMl,\n    ...ATTRS.xml,\n  ]);\n\n  /*\n   * Configure how DOMPurify should handle custom elements and their attributes as well as customized built-in elements.\n   * @property {RegExp|Function|null} tagNameCheck one of [null, regexPattern, predicate]. Default: `null` (disallow any custom elements)\n   * @property {RegExp|Function|null} attributeNameCheck one of [null, regexPattern, predicate]. Default: `null` (disallow any attributes not on the allow list)\n   * @property {boolean} allowCustomizedBuiltInElements allow custom elements derived from built-ins if they pass CUSTOM_ELEMENT_HANDLING.tagNameCheck. Default: `false`.\n   */\n  let CUSTOM_ELEMENT_HANDLING = Object.seal(\n    create(null, {\n      tagNameCheck: {\n        writable: true,\n        configurable: false,\n        enumerable: true,\n        value: null,\n      },\n      attributeNameCheck: {\n        writable: true,\n        configurable: false,\n        enumerable: true,\n        value: null,\n      },\n      allowCustomizedBuiltInElements: {\n        writable: true,\n        configurable: false,\n        enumerable: true,\n        value: false,\n      },\n    })\n  );\n\n  /* Explicitly forbidden tags (overrides ALLOWED_TAGS/ADD_TAGS) */\n  let FORBID_TAGS = null;\n\n  /* Explicitly forbidden attributes (overrides ALLOWED_ATTR/ADD_ATTR) */\n  let FORBID_ATTR = null;\n\n  /* Decide if ARIA attributes are okay */\n  let ALLOW_ARIA_ATTR = true;\n\n  /* Decide if custom data attributes are okay */\n  let ALLOW_DATA_ATTR = true;\n\n  /* Decide if unknown protocols are okay */\n  let ALLOW_UNKNOWN_PROTOCOLS = false;\n\n  /* Decide if self-closing tags in attributes are allowed.\n   * Usually removed due to a mXSS issue in jQuery 3.0 */\n  let ALLOW_SELF_CLOSE_IN_ATTR = true;\n\n  /* Output should be safe for common template engines.\n   * This means, DOMPurify removes data attributes, mustaches and ERB\n   */\n  let SAFE_FOR_TEMPLATES = false;\n\n  /* Output should be safe even for XML used within HTML and alike.\n   * This means, DOMPurify removes comments when containing risky content.\n   */\n  let SAFE_FOR_XML = true;\n\n  /* Decide if document with <html>... should be returned */\n  let WHOLE_DOCUMENT = false;\n\n  /* Track whether config is already set on this instance of DOMPurify. */\n  let SET_CONFIG = false;\n\n  /* Decide if all elements (e.g. style, script) must be children of\n   * document.body. By default, browsers might move them to document.head */\n  let FORCE_BODY = false;\n\n  /* Decide if a DOM `HTMLBodyElement` should be returned, instead of a html\n   * string (or a TrustedHTML object if Trusted Types are supported).\n   * If `WHOLE_DOCUMENT` is enabled a `HTMLHtmlElement` will be returned instead\n   */\n  let RETURN_DOM = false;\n\n  /* Decide if a DOM `DocumentFragment` should be returned, instead of a html\n   * string  (or a TrustedHTML object if Trusted Types are supported) */\n  let RETURN_DOM_FRAGMENT = false;\n\n  /* Try to return a Trusted Type object instead of a string, return a string in\n   * case Trusted Types are not supported  */\n  let RETURN_TRUSTED_TYPE = false;\n\n  /* Output should be free from DOM clobbering attacks?\n   * This sanitizes markups named with colliding, clobberable built-in DOM APIs.\n   */\n  let SANITIZE_DOM = true;\n\n  /* Achieve full DOM Clobbering protection by isolating the namespace of named\n   * properties and JS variables, mitigating attacks that abuse the HTML/DOM spec rules.\n   *\n   * HTML/DOM spec rules that enable DOM Clobbering:\n   *   - Named Access on Window (§7.3.3)\n   *   - DOM Tree Accessors (§3.1.5)\n   *   - Form Element Parent-Child Relations (§4.10.3)\n   *   - Iframe srcdoc / Nested WindowProxies (§4.8.5)\n   *   - HTMLCollection (§4.2.10.2)\n   *\n   * Namespace isolation is implemented by prefixing `id` and `name` attributes\n   * with a constant string, i.e., `user-content-`\n   */\n  let SANITIZE_NAMED_PROPS = false;\n  const SANITIZE_NAMED_PROPS_PREFIX = 'user-content-';\n\n  /* Keep element content when removing element? */\n  let KEEP_CONTENT = true;\n\n  /* If a `Node` is passed to sanitize(), then performs sanitization in-place instead\n   * of importing it into a new Document and returning a sanitized copy */\n  let IN_PLACE = false;\n\n  /* Allow usage of profiles like html, svg and mathMl */\n  let USE_PROFILES: UseProfilesConfig | false = {};\n\n  /* Tags to ignore content of when KEEP_CONTENT is true */\n  let FORBID_CONTENTS = null;\n  const DEFAULT_FORBID_CONTENTS = addToSet({}, [\n    'annotation-xml',\n    'audio',\n    'colgroup',\n    'desc',\n    'foreignobject',\n    'head',\n    'iframe',\n    'math',\n    'mi',\n    'mn',\n    'mo',\n    'ms',\n    'mtext',\n    'noembed',\n    'noframes',\n    'noscript',\n    'plaintext',\n    'script',\n    'style',\n    'svg',\n    'template',\n    'thead',\n    'title',\n    'video',\n    'xmp',\n  ]);\n\n  /* Tags that are safe for data: URIs */\n  let DATA_URI_TAGS = null;\n  const DEFAULT_DATA_URI_TAGS = addToSet({}, [\n    'audio',\n    'video',\n    'img',\n    'source',\n    'image',\n    'track',\n  ]);\n\n  /* Attributes safe for values like \"javascript:\" */\n  let URI_SAFE_ATTRIBUTES = null;\n  const DEFAULT_URI_SAFE_ATTRIBUTES = addToSet({}, [\n    'alt',\n    'class',\n    'for',\n    'id',\n    'label',\n    'name',\n    'pattern',\n    'placeholder',\n    'role',\n    'summary',\n    'title',\n    'value',\n    'style',\n    'xmlns',\n  ]);\n\n  const MATHML_NAMESPACE = 'http://www.w3.org/1998/Math/MathML';\n  const SVG_NAMESPACE = 'http://www.w3.org/2000/svg';\n  const HTML_NAMESPACE = 'http://www.w3.org/1999/xhtml';\n  /* Document namespace */\n  let NAMESPACE = HTML_NAMESPACE;\n  let IS_EMPTY_INPUT = false;\n\n  /* Allowed XHTML+XML namespaces */\n  let ALLOWED_NAMESPACES = null;\n  const DEFAULT_ALLOWED_NAMESPACES = addToSet(\n    {},\n    [MATHML_NAMESPACE, SVG_NAMESPACE, HTML_NAMESPACE],\n    stringToString\n  );\n\n  let MATHML_TEXT_INTEGRATION_POINTS = addToSet({}, [\n    'mi',\n    'mo',\n    'mn',\n    'ms',\n    'mtext',\n  ]);\n\n  let HTML_INTEGRATION_POINTS = addToSet({}, ['annotation-xml']);\n\n  // Certain elements are allowed in both SVG and HTML\n  // namespace. We need to specify them explicitly\n  // so that they don't get erroneously deleted from\n  // HTML namespace.\n  const COMMON_SVG_AND_HTML_ELEMENTS = addToSet({}, [\n    'title',\n    'style',\n    'font',\n    'a',\n    'script',\n  ]);\n\n  /* Parsing of strict XHTML documents */\n  let PARSER_MEDIA_TYPE: null | DOMParserSupportedType = null;\n  const SUPPORTED_PARSER_MEDIA_TYPES = ['application/xhtml+xml', 'text/html'];\n  const DEFAULT_PARSER_MEDIA_TYPE = 'text/html';\n  let transformCaseFunc: null | Parameters<typeof addToSet>[2] = null;\n\n  /* Keep a reference to config to pass to hooks */\n  let CONFIG: Config | null = null;\n\n  /* Ideally, do not touch anything below this line */\n  /* ______________________________________________ */\n\n  const formElement = document.createElement('form');\n\n  const isRegexOrFunction = function (\n    testValue: unknown\n  ): testValue is Function | RegExp {\n    return testValue instanceof RegExp || testValue instanceof Function;\n  };\n\n  /**\n   * _parseConfig\n   *\n   * @param cfg optional config literal\n   */\n  // eslint-disable-next-line complexity\n  const _parseConfig = function (cfg: Config = {}): void {\n    if (CONFIG && CONFIG === cfg) {\n      return;\n    }\n\n    /* Shield configuration object from tampering */\n    if (!cfg || typeof cfg !== 'object') {\n      cfg = {};\n    }\n\n    /* Shield configuration object from prototype pollution */\n    cfg = clone(cfg);\n\n    PARSER_MEDIA_TYPE =\n      // eslint-disable-next-line unicorn/prefer-includes\n      SUPPORTED_PARSER_MEDIA_TYPES.indexOf(cfg.PARSER_MEDIA_TYPE) === -1\n        ? DEFAULT_PARSER_MEDIA_TYPE\n        : cfg.PARSER_MEDIA_TYPE;\n\n    // HTML tags and attributes are not case-sensitive, converting to lowercase. Keeping XHTML as is.\n    transformCaseFunc =\n      PARSER_MEDIA_TYPE === 'application/xhtml+xml'\n        ? stringToString\n        : stringToLowerCase;\n\n    /* Set configuration parameters */\n    ALLOWED_TAGS = objectHasOwnProperty(cfg, 'ALLOWED_TAGS')\n      ? addToSet({}, cfg.ALLOWED_TAGS, transformCaseFunc)\n      : DEFAULT_ALLOWED_TAGS;\n    ALLOWED_ATTR = objectHasOwnProperty(cfg, 'ALLOWED_ATTR')\n      ? addToSet({}, cfg.ALLOWED_ATTR, transformCaseFunc)\n      : DEFAULT_ALLOWED_ATTR;\n    ALLOWED_NAMESPACES = objectHasOwnProperty(cfg, 'ALLOWED_NAMESPACES')\n      ? addToSet({}, cfg.ALLOWED_NAMESPACES, stringToString)\n      : DEFAULT_ALLOWED_NAMESPACES;\n    URI_SAFE_ATTRIBUTES = objectHasOwnProperty(cfg, 'ADD_URI_SAFE_ATTR')\n      ? addToSet(\n          clone(DEFAULT_URI_SAFE_ATTRIBUTES),\n          cfg.ADD_URI_SAFE_ATTR,\n          transformCaseFunc\n        )\n      : DEFAULT_URI_SAFE_ATTRIBUTES;\n    DATA_URI_TAGS = objectHasOwnProperty(cfg, 'ADD_DATA_URI_TAGS')\n      ? addToSet(\n          clone(DEFAULT_DATA_URI_TAGS),\n          cfg.ADD_DATA_URI_TAGS,\n          transformCaseFunc\n        )\n      : DEFAULT_DATA_URI_TAGS;\n    FORBID_CONTENTS = objectHasOwnProperty(cfg, 'FORBID_CONTENTS')\n      ? addToSet({}, cfg.FORBID_CONTENTS, transformCaseFunc)\n      : DEFAULT_FORBID_CONTENTS;\n    FORBID_TAGS = objectHasOwnProperty(cfg, 'FORBID_TAGS')\n      ? addToSet({}, cfg.FORBID_TAGS, transformCaseFunc)\n      : clone({});\n    FORBID_ATTR = objectHasOwnProperty(cfg, 'FORBID_ATTR')\n      ? addToSet({}, cfg.FORBID_ATTR, transformCaseFunc)\n      : clone({});\n    USE_PROFILES = objectHasOwnProperty(cfg, 'USE_PROFILES')\n      ? cfg.USE_PROFILES\n      : false;\n    ALLOW_ARIA_ATTR = cfg.ALLOW_ARIA_ATTR !== false; // Default true\n    ALLOW_DATA_ATTR = cfg.ALLOW_DATA_ATTR !== false; // Default true\n    ALLOW_UNKNOWN_PROTOCOLS = cfg.ALLOW_UNKNOWN_PROTOCOLS || false; // Default false\n    ALLOW_SELF_CLOSE_IN_ATTR = cfg.ALLOW_SELF_CLOSE_IN_ATTR !== false; // Default true\n    SAFE_FOR_TEMPLATES = cfg.SAFE_FOR_TEMPLATES || false; // Default false\n    SAFE_FOR_XML = cfg.SAFE_FOR_XML !== false; // Default true\n    WHOLE_DOCUMENT = cfg.WHOLE_DOCUMENT || false; // Default false\n    RETURN_DOM = cfg.RETURN_DOM || false; // Default false\n    RETURN_DOM_FRAGMENT = cfg.RETURN_DOM_FRAGMENT || false; // Default false\n    RETURN_TRUSTED_TYPE = cfg.RETURN_TRUSTED_TYPE || false; // Default false\n    FORCE_BODY = cfg.FORCE_BODY || false; // Default false\n    SANITIZE_DOM = cfg.SANITIZE_DOM !== false; // Default true\n    SANITIZE_NAMED_PROPS = cfg.SANITIZE_NAMED_PROPS || false; // Default false\n    KEEP_CONTENT = cfg.KEEP_CONTENT !== false; // Default true\n    IN_PLACE = cfg.IN_PLACE || false; // Default false\n    IS_ALLOWED_URI = cfg.ALLOWED_URI_REGEXP || EXPRESSIONS.IS_ALLOWED_URI;\n    NAMESPACE = cfg.NAMESPACE || HTML_NAMESPACE;\n    MATHML_TEXT_INTEGRATION_POINTS =\n      cfg.MATHML_TEXT_INTEGRATION_POINTS || MATHML_TEXT_INTEGRATION_POINTS;\n    HTML_INTEGRATION_POINTS =\n      cfg.HTML_INTEGRATION_POINTS || HTML_INTEGRATION_POINTS;\n\n    CUSTOM_ELEMENT_HANDLING = cfg.CUSTOM_ELEMENT_HANDLING || {};\n    if (\n      cfg.CUSTOM_ELEMENT_HANDLING &&\n      isRegexOrFunction(cfg.CUSTOM_ELEMENT_HANDLING.tagNameCheck)\n    ) {\n      CUSTOM_ELEMENT_HANDLING.tagNameCheck =\n        cfg.CUSTOM_ELEMENT_HANDLING.tagNameCheck;\n    }\n\n    if (\n      cfg.CUSTOM_ELEMENT_HANDLING &&\n      isRegexOrFunction(cfg.CUSTOM_ELEMENT_HANDLING.attributeNameCheck)\n    ) {\n      CUSTOM_ELEMENT_HANDLING.attributeNameCheck =\n        cfg.CUSTOM_ELEMENT_HANDLING.attributeNameCheck;\n    }\n\n    if (\n      cfg.CUSTOM_ELEMENT_HANDLING &&\n      typeof cfg.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements ===\n        'boolean'\n    ) {\n      CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements =\n        cfg.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements;\n    }\n\n    if (SAFE_FOR_TEMPLATES) {\n      ALLOW_DATA_ATTR = false;\n    }\n\n    if (RETURN_DOM_FRAGMENT) {\n      RETURN_DOM = true;\n    }\n\n    /* Parse profile info */\n    if (USE_PROFILES) {\n      ALLOWED_TAGS = addToSet({}, TAGS.text);\n      ALLOWED_ATTR = [];\n      if (USE_PROFILES.html === true) {\n        addToSet(ALLOWED_TAGS, TAGS.html);\n        addToSet(ALLOWED_ATTR, ATTRS.html);\n      }\n\n      if (USE_PROFILES.svg === true) {\n        addToSet(ALLOWED_TAGS, TAGS.svg);\n        addToSet(ALLOWED_ATTR, ATTRS.svg);\n        addToSet(ALLOWED_ATTR, ATTRS.xml);\n      }\n\n      if (USE_PROFILES.svgFilters === true) {\n        addToSet(ALLOWED_TAGS, TAGS.svgFilters);\n        addToSet(ALLOWED_ATTR, ATTRS.svg);\n        addToSet(ALLOWED_ATTR, ATTRS.xml);\n      }\n\n      if (USE_PROFILES.mathMl === true) {\n        addToSet(ALLOWED_TAGS, TAGS.mathMl);\n        addToSet(ALLOWED_ATTR, ATTRS.mathMl);\n        addToSet(ALLOWED_ATTR, ATTRS.xml);\n      }\n    }\n\n    /* Merge configuration parameters */\n    if (cfg.ADD_TAGS) {\n      if (ALLOWED_TAGS === DEFAULT_ALLOWED_TAGS) {\n        ALLOWED_TAGS = clone(ALLOWED_TAGS);\n      }\n\n      addToSet(ALLOWED_TAGS, cfg.ADD_TAGS, transformCaseFunc);\n    }\n\n    if (cfg.ADD_ATTR) {\n      if (ALLOWED_ATTR === DEFAULT_ALLOWED_ATTR) {\n        ALLOWED_ATTR = clone(ALLOWED_ATTR);\n      }\n\n      addToSet(ALLOWED_ATTR, cfg.ADD_ATTR, transformCaseFunc);\n    }\n\n    if (cfg.ADD_URI_SAFE_ATTR) {\n      addToSet(URI_SAFE_ATTRIBUTES, cfg.ADD_URI_SAFE_ATTR, transformCaseFunc);\n    }\n\n    if (cfg.FORBID_CONTENTS) {\n      if (FORBID_CONTENTS === DEFAULT_FORBID_CONTENTS) {\n        FORBID_CONTENTS = clone(FORBID_CONTENTS);\n      }\n\n      addToSet(FORBID_CONTENTS, cfg.FORBID_CONTENTS, transformCaseFunc);\n    }\n\n    /* Add #text in case KEEP_CONTENT is set to true */\n    if (KEEP_CONTENT) {\n      ALLOWED_TAGS['#text'] = true;\n    }\n\n    /* Add html, head and body to ALLOWED_TAGS in case WHOLE_DOCUMENT is true */\n    if (WHOLE_DOCUMENT) {\n      addToSet(ALLOWED_TAGS, ['html', 'head', 'body']);\n    }\n\n    /* Add tbody to ALLOWED_TAGS in case tables are permitted, see #286, #365 */\n    if (ALLOWED_TAGS.table) {\n      addToSet(ALLOWED_TAGS, ['tbody']);\n      delete FORBID_TAGS.tbody;\n    }\n\n    if (cfg.TRUSTED_TYPES_POLICY) {\n      if (typeof cfg.TRUSTED_TYPES_POLICY.createHTML !== 'function') {\n        throw typeErrorCreate(\n          'TRUSTED_TYPES_POLICY configuration option must provide a \"createHTML\" hook.'\n        );\n      }\n\n      if (typeof cfg.TRUSTED_TYPES_POLICY.createScriptURL !== 'function') {\n        throw typeErrorCreate(\n          'TRUSTED_TYPES_POLICY configuration option must provide a \"createScriptURL\" hook.'\n        );\n      }\n\n      // Overwrite existing TrustedTypes policy.\n      trustedTypesPolicy = cfg.TRUSTED_TYPES_POLICY;\n\n      // Sign local variables required by `sanitize`.\n      emptyHTML = trustedTypesPolicy.createHTML('');\n    } else {\n      // Uninitialized policy, attempt to initialize the internal dompurify policy.\n      if (trustedTypesPolicy === undefined) {\n        trustedTypesPolicy = _createTrustedTypesPolicy(\n          trustedTypes,\n          currentScript\n        );\n      }\n\n      // If creating the internal policy succeeded sign internal variables.\n      if (trustedTypesPolicy !== null && typeof emptyHTML === 'string') {\n        emptyHTML = trustedTypesPolicy.createHTML('');\n      }\n    }\n\n    // Prevent further manipulation of configuration.\n    // Not available in IE8, Safari 5, etc.\n    if (freeze) {\n      freeze(cfg);\n    }\n\n    CONFIG = cfg;\n  };\n\n  /* Keep track of all possible SVG and MathML tags\n   * so that we can perform the namespace checks\n   * correctly. */\n  const ALL_SVG_TAGS = addToSet({}, [\n    ...TAGS.svg,\n    ...TAGS.svgFilters,\n    ...TAGS.svgDisallowed,\n  ]);\n  const ALL_MATHML_TAGS = addToSet({}, [\n    ...TAGS.mathMl,\n    ...TAGS.mathMlDisallowed,\n  ]);\n\n  /**\n   * @param element a DOM element whose namespace is being checked\n   * @returns Return false if the element has a\n   *  namespace that a spec-compliant parser would never\n   *  return. Return true otherwise.\n   */\n  const _checkValidNamespace = function (element: Element): boolean {\n    let parent = getParentNode(element);\n\n    // In JSDOM, if we're inside shadow DOM, then parentNode\n    // can be null. We just simulate parent in this case.\n    if (!parent || !parent.tagName) {\n      parent = {\n        namespaceURI: NAMESPACE,\n        tagName: 'template',\n      };\n    }\n\n    const tagName = stringToLowerCase(element.tagName);\n    const parentTagName = stringToLowerCase(parent.tagName);\n\n    if (!ALLOWED_NAMESPACES[element.namespaceURI]) {\n      return false;\n    }\n\n    if (element.namespaceURI === SVG_NAMESPACE) {\n      // The only way to switch from HTML namespace to SVG\n      // is via <svg>. If it happens via any other tag, then\n      // it should be killed.\n      if (parent.namespaceURI === HTML_NAMESPACE) {\n        return tagName === 'svg';\n      }\n\n      // The only way to switch from MathML to SVG is via`\n      // svg if parent is either <annotation-xml> or MathML\n      // text integration points.\n      if (parent.namespaceURI === MATHML_NAMESPACE) {\n        return (\n          tagName === 'svg' &&\n          (parentTagName === 'annotation-xml' ||\n            MATHML_TEXT_INTEGRATION_POINTS[parentTagName])\n        );\n      }\n\n      // We only allow elements that are defined in SVG\n      // spec. All others are disallowed in SVG namespace.\n      return Boolean(ALL_SVG_TAGS[tagName]);\n    }\n\n    if (element.namespaceURI === MATHML_NAMESPACE) {\n      // The only way to switch from HTML namespace to MathML\n      // is via <math>. If it happens via any other tag, then\n      // it should be killed.\n      if (parent.namespaceURI === HTML_NAMESPACE) {\n        return tagName === 'math';\n      }\n\n      // The only way to switch from SVG to MathML is via\n      // <math> and HTML integration points\n      if (parent.namespaceURI === SVG_NAMESPACE) {\n        return tagName === 'math' && HTML_INTEGRATION_POINTS[parentTagName];\n      }\n\n      // We only allow elements that are defined in MathML\n      // spec. All others are disallowed in MathML namespace.\n      return Boolean(ALL_MATHML_TAGS[tagName]);\n    }\n\n    if (element.namespaceURI === HTML_NAMESPACE) {\n      // The only way to switch from SVG to HTML is via\n      // HTML integration points, and from MathML to HTML\n      // is via MathML text integration points\n      if (\n        parent.namespaceURI === SVG_NAMESPACE &&\n        !HTML_INTEGRATION_POINTS[parentTagName]\n      ) {\n        return false;\n      }\n\n      if (\n        parent.namespaceURI === MATHML_NAMESPACE &&\n        !MATHML_TEXT_INTEGRATION_POINTS[parentTagName]\n      ) {\n        return false;\n      }\n\n      // We disallow tags that are specific for MathML\n      // or SVG and should never appear in HTML namespace\n      return (\n        !ALL_MATHML_TAGS[tagName] &&\n        (COMMON_SVG_AND_HTML_ELEMENTS[tagName] || !ALL_SVG_TAGS[tagName])\n      );\n    }\n\n    // For XHTML and XML documents that support custom namespaces\n    if (\n      PARSER_MEDIA_TYPE === 'application/xhtml+xml' &&\n      ALLOWED_NAMESPACES[element.namespaceURI]\n    ) {\n      return true;\n    }\n\n    // The code should never reach this place (this means\n    // that the element somehow got namespace that is not\n    // HTML, SVG, MathML or allowed via ALLOWED_NAMESPACES).\n    // Return false just in case.\n    return false;\n  };\n\n  /**\n   * _forceRemove\n   *\n   * @param node a DOM node\n   */\n  const _forceRemove = function (node: Node): void {\n    arrayPush(DOMPurify.removed, { element: node });\n\n    try {\n      // eslint-disable-next-line unicorn/prefer-dom-node-remove\n      getParentNode(node).removeChild(node);\n    } catch (_) {\n      remove(node);\n    }\n  };\n\n  /**\n   * _removeAttribute\n   *\n   * @param name an Attribute name\n   * @param element a DOM node\n   */\n  const _removeAttribute = function (name: string, element: Element): void {\n    try {\n      arrayPush(DOMPurify.removed, {\n        attribute: element.getAttributeNode(name),\n        from: element,\n      });\n    } catch (_) {\n      arrayPush(DOMPurify.removed, {\n        attribute: null,\n        from: element,\n      });\n    }\n\n    element.removeAttribute(name);\n\n    // We void attribute values for unremovable \"is\" attributes\n    if (name === 'is') {\n      if (RETURN_DOM || RETURN_DOM_FRAGMENT) {\n        try {\n          _forceRemove(element);\n        } catch (_) {}\n      } else {\n        try {\n          element.setAttribute(name, '');\n        } catch (_) {}\n      }\n    }\n  };\n\n  /**\n   * _initDocument\n   *\n   * @param dirty - a string of dirty markup\n   * @return a DOM, filled with the dirty markup\n   */\n  const _initDocument = function (dirty: string): Document {\n    /* Create a HTML document */\n    let doc = null;\n    let leadingWhitespace = null;\n\n    if (FORCE_BODY) {\n      dirty = '<remove></remove>' + dirty;\n    } else {\n      /* If FORCE_BODY isn't used, leading whitespace needs to be preserved manually */\n      const matches = stringMatch(dirty, /^[\\r\\n\\t ]+/);\n      leadingWhitespace = matches && matches[0];\n    }\n\n    if (\n      PARSER_MEDIA_TYPE === 'application/xhtml+xml' &&\n      NAMESPACE === HTML_NAMESPACE\n    ) {\n      // Root of XHTML doc must contain xmlns declaration (see https://www.w3.org/TR/xhtml1/normative.html#strict)\n      dirty =\n        '<html xmlns=\"http://www.w3.org/1999/xhtml\"><head></head><body>' +\n        dirty +\n        '</body></html>';\n    }\n\n    const dirtyPayload = trustedTypesPolicy\n      ? trustedTypesPolicy.createHTML(dirty)\n      : dirty;\n    /*\n     * Use the DOMParser API by default, fallback later if needs be\n     * DOMParser not work for svg when has multiple root element.\n     */\n    if (NAMESPACE === HTML_NAMESPACE) {\n      try {\n        doc = new DOMParser().parseFromString(dirtyPayload, PARSER_MEDIA_TYPE);\n      } catch (_) {}\n    }\n\n    /* Use createHTMLDocument in case DOMParser is not available */\n    if (!doc || !doc.documentElement) {\n      doc = implementation.createDocument(NAMESPACE, 'template', null);\n      try {\n        doc.documentElement.innerHTML = IS_EMPTY_INPUT\n          ? emptyHTML\n          : dirtyPayload;\n      } catch (_) {\n        // Syntax error if dirtyPayload is invalid xml\n      }\n    }\n\n    const body = doc.body || doc.documentElement;\n\n    if (dirty && leadingWhitespace) {\n      body.insertBefore(\n        document.createTextNode(leadingWhitespace),\n        body.childNodes[0] || null\n      );\n    }\n\n    /* Work on whole document or just its body */\n    if (NAMESPACE === HTML_NAMESPACE) {\n      return getElementsByTagName.call(\n        doc,\n        WHOLE_DOCUMENT ? 'html' : 'body'\n      )[0];\n    }\n\n    return WHOLE_DOCUMENT ? doc.documentElement : body;\n  };\n\n  /**\n   * Creates a NodeIterator object that you can use to traverse filtered lists of nodes or elements in a document.\n   *\n   * @param root The root element or node to start traversing on.\n   * @return The created NodeIterator\n   */\n  const _createNodeIterator = function (root: Node): NodeIterator {\n    return createNodeIterator.call(\n      root.ownerDocument || root,\n      root,\n      // eslint-disable-next-line no-bitwise\n      NodeFilter.SHOW_ELEMENT |\n        NodeFilter.SHOW_COMMENT |\n        NodeFilter.SHOW_TEXT |\n        NodeFilter.SHOW_PROCESSING_INSTRUCTION |\n        NodeFilter.SHOW_CDATA_SECTION,\n      null\n    );\n  };\n\n  /**\n   * _isClobbered\n   *\n   * @param element element to check for clobbering attacks\n   * @return true if clobbered, false if safe\n   */\n  const _isClobbered = function (element: Element): boolean {\n    return (\n      element instanceof HTMLFormElement &&\n      (typeof element.nodeName !== 'string' ||\n        typeof element.textContent !== 'string' ||\n        typeof element.removeChild !== 'function' ||\n        !(element.attributes instanceof NamedNodeMap) ||\n        typeof element.removeAttribute !== 'function' ||\n        typeof element.setAttribute !== 'function' ||\n        typeof element.namespaceURI !== 'string' ||\n        typeof element.insertBefore !== 'function' ||\n        typeof element.hasChildNodes !== 'function')\n    );\n  };\n\n  /**\n   * Checks whether the given object is a DOM node.\n   *\n   * @param value object to check whether it's a DOM node\n   * @return true is object is a DOM node\n   */\n  const _isNode = function (value: unknown): value is Node {\n    return typeof Node === 'function' && value instanceof Node;\n  };\n\n  function _executeHooks<\n    T extends\n      | NodeHook\n      | ElementHook\n      | DocumentFragmentHook\n      | UponSanitizeElementHook\n      | UponSanitizeAttributeHook\n  >(hooks: T[], currentNode: Parameters<T>[0], data: Parameters<T>[1]): void {\n    arrayForEach(hooks, (hook) => {\n      hook.call(DOMPurify, currentNode, data, CONFIG);\n    });\n  }\n\n  /**\n   * _sanitizeElements\n   *\n   * @protect nodeName\n   * @protect textContent\n   * @protect removeChild\n   * @param currentNode to check for permission to exist\n   * @return true if node was killed, false if left alive\n   */\n  const _sanitizeElements = function (currentNode: any): boolean {\n    let content = null;\n\n    /* Execute a hook if present */\n    _executeHooks(hooks.beforeSanitizeElements, currentNode, null);\n\n    /* Check if element is clobbered or can clobber */\n    if (_isClobbered(currentNode)) {\n      _forceRemove(currentNode);\n      return true;\n    }\n\n    /* Now let's check the element's type and name */\n    const tagName = transformCaseFunc(currentNode.nodeName);\n\n    /* Execute a hook if present */\n    _executeHooks(hooks.uponSanitizeElement, currentNode, {\n      tagName,\n      allowedTags: ALLOWED_TAGS,\n    });\n\n    /* Detect mXSS attempts abusing namespace confusion */\n    if (\n      SAFE_FOR_XML &&\n      currentNode.hasChildNodes() &&\n      !_isNode(currentNode.firstElementChild) &&\n      regExpTest(/<[/\\w!]/g, currentNode.innerHTML) &&\n      regExpTest(/<[/\\w!]/g, currentNode.textContent)\n    ) {\n      _forceRemove(currentNode);\n      return true;\n    }\n\n    /* Remove any occurrence of processing instructions */\n    if (currentNode.nodeType === NODE_TYPE.progressingInstruction) {\n      _forceRemove(currentNode);\n      return true;\n    }\n\n    /* Remove any kind of possibly harmful comments */\n    if (\n      SAFE_FOR_XML &&\n      currentNode.nodeType === NODE_TYPE.comment &&\n      regExpTest(/<[/\\w]/g, currentNode.data)\n    ) {\n      _forceRemove(currentNode);\n      return true;\n    }\n\n    /* Remove element if anything forbids its presence */\n    if (!ALLOWED_TAGS[tagName] || FORBID_TAGS[tagName]) {\n      /* Check if we have a custom element to handle */\n      if (!FORBID_TAGS[tagName] && _isBasicCustomElement(tagName)) {\n        if (\n          CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof RegExp &&\n          regExpTest(CUSTOM_ELEMENT_HANDLING.tagNameCheck, tagName)\n        ) {\n          return false;\n        }\n\n        if (\n          CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof Function &&\n          CUSTOM_ELEMENT_HANDLING.tagNameCheck(tagName)\n        ) {\n          return false;\n        }\n      }\n\n      /* Keep content except for bad-listed elements */\n      if (KEEP_CONTENT && !FORBID_CONTENTS[tagName]) {\n        const parentNode = getParentNode(currentNode) || currentNode.parentNode;\n        const childNodes = getChildNodes(currentNode) || currentNode.childNodes;\n\n        if (childNodes && parentNode) {\n          const childCount = childNodes.length;\n\n          for (let i = childCount - 1; i >= 0; --i) {\n            const childClone = cloneNode(childNodes[i], true);\n            childClone.__removalCount = (currentNode.__removalCount || 0) + 1;\n            parentNode.insertBefore(childClone, getNextSibling(currentNode));\n          }\n        }\n      }\n\n      _forceRemove(currentNode);\n      return true;\n    }\n\n    /* Check whether element has a valid namespace */\n    if (currentNode instanceof Element && !_checkValidNamespace(currentNode)) {\n      _forceRemove(currentNode);\n      return true;\n    }\n\n    /* Make sure that older browsers don't get fallback-tag mXSS */\n    if (\n      (tagName === 'noscript' ||\n        tagName === 'noembed' ||\n        tagName === 'noframes') &&\n      regExpTest(/<\\/no(script|embed|frames)/i, currentNode.innerHTML)\n    ) {\n      _forceRemove(currentNode);\n      return true;\n    }\n\n    /* Sanitize element content to be template-safe */\n    if (SAFE_FOR_TEMPLATES && currentNode.nodeType === NODE_TYPE.text) {\n      /* Get the element's text content */\n      content = currentNode.textContent;\n\n      arrayForEach([MUSTACHE_EXPR, ERB_EXPR, TMPLIT_EXPR], (expr) => {\n        content = stringReplace(content, expr, ' ');\n      });\n\n      if (currentNode.textContent !== content) {\n        arrayPush(DOMPurify.removed, { element: currentNode.cloneNode() });\n        currentNode.textContent = content;\n      }\n    }\n\n    /* Execute a hook if present */\n    _executeHooks(hooks.afterSanitizeElements, currentNode, null);\n\n    return false;\n  };\n\n  /**\n   * _isValidAttribute\n   *\n   * @param lcTag Lowercase tag name of containing element.\n   * @param lcName Lowercase attribute name.\n   * @param value Attribute value.\n   * @return Returns true if `value` is valid, otherwise false.\n   */\n  // eslint-disable-next-line complexity\n  const _isValidAttribute = function (\n    lcTag: string,\n    lcName: string,\n    value: string\n  ): boolean {\n    /* Make sure attribute cannot clobber */\n    if (\n      SANITIZE_DOM &&\n      (lcName === 'id' || lcName === 'name') &&\n      (value in document || value in formElement)\n    ) {\n      return false;\n    }\n\n    /* Allow valid data-* attributes: At least one character after \"-\"\n        (https://html.spec.whatwg.org/multipage/dom.html#embedding-custom-non-visible-data-with-the-data-*-attributes)\n        XML-compatible (https://html.spec.whatwg.org/multipage/infrastructure.html#xml-compatible and http://www.w3.org/TR/xml/#d0e804)\n        We don't need to check the value; it's always URI safe. */\n    if (\n      ALLOW_DATA_ATTR &&\n      !FORBID_ATTR[lcName] &&\n      regExpTest(DATA_ATTR, lcName)\n    ) {\n      // This attribute is safe\n    } else if (ALLOW_ARIA_ATTR && regExpTest(ARIA_ATTR, lcName)) {\n      // This attribute is safe\n      /* Otherwise, check the name is permitted */\n    } else if (!ALLOWED_ATTR[lcName] || FORBID_ATTR[lcName]) {\n      if (\n        // First condition does a very basic check if a) it's basically a valid custom element tagname AND\n        // b) if the tagName passes whatever the user has configured for CUSTOM_ELEMENT_HANDLING.tagNameCheck\n        // and c) if the attribute name passes whatever the user has configured for CUSTOM_ELEMENT_HANDLING.attributeNameCheck\n        (_isBasicCustomElement(lcTag) &&\n          ((CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof RegExp &&\n            regExpTest(CUSTOM_ELEMENT_HANDLING.tagNameCheck, lcTag)) ||\n            (CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof Function &&\n              CUSTOM_ELEMENT_HANDLING.tagNameCheck(lcTag))) &&\n          ((CUSTOM_ELEMENT_HANDLING.attributeNameCheck instanceof RegExp &&\n            regExpTest(CUSTOM_ELEMENT_HANDLING.attributeNameCheck, lcName)) ||\n            (CUSTOM_ELEMENT_HANDLING.attributeNameCheck instanceof Function &&\n              CUSTOM_ELEMENT_HANDLING.attributeNameCheck(lcName)))) ||\n        // Alternative, second condition checks if it's an `is`-attribute, AND\n        // the value passes whatever the user has configured for CUSTOM_ELEMENT_HANDLING.tagNameCheck\n        (lcName === 'is' &&\n          CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements &&\n          ((CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof RegExp &&\n            regExpTest(CUSTOM_ELEMENT_HANDLING.tagNameCheck, value)) ||\n            (CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof Function &&\n              CUSTOM_ELEMENT_HANDLING.tagNameCheck(value))))\n      ) {\n        // If user has supplied a regexp or function in CUSTOM_ELEMENT_HANDLING.tagNameCheck, we need to also allow derived custom elements using the same tagName test.\n        // Additionally, we need to allow attributes passing the CUSTOM_ELEMENT_HANDLING.attributeNameCheck user has configured, as custom elements can define these at their own discretion.\n      } else {\n        return false;\n      }\n      /* Check value is safe. First, is attr inert? If so, is safe */\n    } else if (URI_SAFE_ATTRIBUTES[lcName]) {\n      // This attribute is safe\n      /* Check no script, data or unknown possibly unsafe URI\n        unless we know URI values are safe for that attribute */\n    } else if (\n      regExpTest(IS_ALLOWED_URI, stringReplace(value, ATTR_WHITESPACE, ''))\n    ) {\n      // This attribute is safe\n      /* Keep image data URIs alive if src/xlink:href is allowed */\n      /* Further prevent gadget XSS for dynamically built script tags */\n    } else if (\n      (lcName === 'src' || lcName === 'xlink:href' || lcName === 'href') &&\n      lcTag !== 'script' &&\n      stringIndexOf(value, 'data:') === 0 &&\n      DATA_URI_TAGS[lcTag]\n    ) {\n      // This attribute is safe\n      /* Allow unknown protocols: This provides support for links that\n        are handled by protocol handlers which may be unknown ahead of\n        time, e.g. fb:, spotify: */\n    } else if (\n      ALLOW_UNKNOWN_PROTOCOLS &&\n      !regExpTest(IS_SCRIPT_OR_DATA, stringReplace(value, ATTR_WHITESPACE, ''))\n    ) {\n      // This attribute is safe\n      /* Check for binary attributes */\n    } else if (value) {\n      return false;\n    } else {\n      // Binary attributes are safe at this point\n      /* Anything else, presume unsafe, do not add it back */\n    }\n\n    return true;\n  };\n\n  /**\n   * _isBasicCustomElement\n   * checks if at least one dash is included in tagName, and it's not the first char\n   * for more sophisticated checking see https://github.com/sindresorhus/validate-element-name\n   *\n   * @param tagName name of the tag of the node to sanitize\n   * @returns Returns true if the tag name meets the basic criteria for a custom element, otherwise false.\n   */\n  const _isBasicCustomElement = function (tagName: string): RegExpMatchArray {\n    return tagName !== 'annotation-xml' && stringMatch(tagName, CUSTOM_ELEMENT);\n  };\n\n  /**\n   * _sanitizeAttributes\n   *\n   * @protect attributes\n   * @protect nodeName\n   * @protect removeAttribute\n   * @protect setAttribute\n   *\n   * @param currentNode to sanitize\n   */\n  const _sanitizeAttributes = function (currentNode: Element): void {\n    /* Execute a hook if present */\n    _executeHooks(hooks.beforeSanitizeAttributes, currentNode, null);\n\n    const { attributes } = currentNode;\n\n    /* Check if we have attributes; if not we might have a text node */\n    if (!attributes || _isClobbered(currentNode)) {\n      return;\n    }\n\n    const hookEvent = {\n      attrName: '',\n      attrValue: '',\n      keepAttr: true,\n      allowedAttributes: ALLOWED_ATTR,\n      forceKeepAttr: undefined,\n    };\n    let l = attributes.length;\n\n    /* Go backwards over all attributes; safely remove bad ones */\n    while (l--) {\n      const attr = attributes[l];\n      const { name, namespaceURI, value: attrValue } = attr;\n      const lcName = transformCaseFunc(name);\n\n      const initValue = attrValue;\n      let value = name === 'value' ? initValue : stringTrim(initValue);\n\n      /* Execute a hook if present */\n      hookEvent.attrName = lcName;\n      hookEvent.attrValue = value;\n      hookEvent.keepAttr = true;\n      hookEvent.forceKeepAttr = undefined; // Allows developers to see this is a property they can set\n      _executeHooks(hooks.uponSanitizeAttribute, currentNode, hookEvent);\n      value = hookEvent.attrValue;\n\n      /* Full DOM Clobbering protection via namespace isolation,\n       * Prefix id and name attributes with `user-content-`\n       */\n      if (SANITIZE_NAMED_PROPS && (lcName === 'id' || lcName === 'name')) {\n        // Remove the attribute with this value\n        _removeAttribute(name, currentNode);\n\n        // Prefix the value and later re-create the attribute with the sanitized value\n        value = SANITIZE_NAMED_PROPS_PREFIX + value;\n      }\n\n      /* Work around a security issue with comments inside attributes */\n      if (SAFE_FOR_XML && regExpTest(/((--!?|])>)|<\\/(style|title)/i, value)) {\n        _removeAttribute(name, currentNode);\n        continue;\n      }\n\n      /* Did the hooks approve of the attribute? */\n      if (hookEvent.forceKeepAttr) {\n        continue;\n      }\n\n      /* Did the hooks approve of the attribute? */\n      if (!hookEvent.keepAttr) {\n        _removeAttribute(name, currentNode);\n        continue;\n      }\n\n      /* Work around a security issue in jQuery 3.0 */\n      if (!ALLOW_SELF_CLOSE_IN_ATTR && regExpTest(/\\/>/i, value)) {\n        _removeAttribute(name, currentNode);\n        continue;\n      }\n\n      /* Sanitize attribute content to be template-safe */\n      if (SAFE_FOR_TEMPLATES) {\n        arrayForEach([MUSTACHE_EXPR, ERB_EXPR, TMPLIT_EXPR], (expr) => {\n          value = stringReplace(value, expr, ' ');\n        });\n      }\n\n      /* Is `value` valid for this attribute? */\n      const lcTag = transformCaseFunc(currentNode.nodeName);\n      if (!_isValidAttribute(lcTag, lcName, value)) {\n        _removeAttribute(name, currentNode);\n        continue;\n      }\n\n      /* Handle attributes that require Trusted Types */\n      if (\n        trustedTypesPolicy &&\n        typeof trustedTypes === 'object' &&\n        typeof trustedTypes.getAttributeType === 'function'\n      ) {\n        if (namespaceURI) {\n          /* Namespaces are not yet supported, see https://bugs.chromium.org/p/chromium/issues/detail?id=1305293 */\n        } else {\n          switch (trustedTypes.getAttributeType(lcTag, lcName)) {\n            case 'TrustedHTML': {\n              value = trustedTypesPolicy.createHTML(value);\n              break;\n            }\n\n            case 'TrustedScriptURL': {\n              value = trustedTypesPolicy.createScriptURL(value);\n              break;\n            }\n\n            default: {\n              break;\n            }\n          }\n        }\n      }\n\n      /* Handle invalid data-* attribute set by try-catching it */\n      if (value !== initValue) {\n        try {\n          if (namespaceURI) {\n            currentNode.setAttributeNS(namespaceURI, name, value);\n          } else {\n            /* Fallback to setAttribute() for browser-unrecognized namespaces e.g. \"x-schema\". */\n            currentNode.setAttribute(name, value);\n          }\n\n          if (_isClobbered(currentNode)) {\n            _forceRemove(currentNode);\n          } else {\n            arrayPop(DOMPurify.removed);\n          }\n        } catch (_) {\n          _removeAttribute(name, currentNode);\n        }\n      }\n    }\n\n    /* Execute a hook if present */\n    _executeHooks(hooks.afterSanitizeAttributes, currentNode, null);\n  };\n\n  /**\n   * _sanitizeShadowDOM\n   *\n   * @param fragment to iterate over recursively\n   */\n  const _sanitizeShadowDOM = function (fragment: DocumentFragment): void {\n    let shadowNode = null;\n    const shadowIterator = _createNodeIterator(fragment);\n\n    /* Execute a hook if present */\n    _executeHooks(hooks.beforeSanitizeShadowDOM, fragment, null);\n\n    while ((shadowNode = shadowIterator.nextNode())) {\n      /* Execute a hook if present */\n      _executeHooks(hooks.uponSanitizeShadowNode, shadowNode, null);\n\n      /* Sanitize tags and elements */\n      _sanitizeElements(shadowNode);\n\n      /* Check attributes next */\n      _sanitizeAttributes(shadowNode);\n\n      /* Deep shadow DOM detected */\n      if (shadowNode.content instanceof DocumentFragment) {\n        _sanitizeShadowDOM(shadowNode.content);\n      }\n    }\n\n    /* Execute a hook if present */\n    _executeHooks(hooks.afterSanitizeShadowDOM, fragment, null);\n  };\n\n  // eslint-disable-next-line complexity\n  DOMPurify.sanitize = function (dirty, cfg = {}) {\n    let body = null;\n    let importedNode = null;\n    let currentNode = null;\n    let returnNode = null;\n    /* Make sure we have a string to sanitize.\n      DO NOT return early, as this will return the wrong type if\n      the user has requested a DOM object rather than a string */\n    IS_EMPTY_INPUT = !dirty;\n    if (IS_EMPTY_INPUT) {\n      dirty = '<!-->';\n    }\n\n    /* Stringify, in case dirty is an object */\n    if (typeof dirty !== 'string' && !_isNode(dirty)) {\n      if (typeof dirty.toString === 'function') {\n        dirty = dirty.toString();\n        if (typeof dirty !== 'string') {\n          throw typeErrorCreate('dirty is not a string, aborting');\n        }\n      } else {\n        throw typeErrorCreate('toString is not a function');\n      }\n    }\n\n    /* Return dirty HTML if DOMPurify cannot run */\n    if (!DOMPurify.isSupported) {\n      return dirty;\n    }\n\n    /* Assign config vars */\n    if (!SET_CONFIG) {\n      _parseConfig(cfg);\n    }\n\n    /* Clean up removed elements */\n    DOMPurify.removed = [];\n\n    /* Check if dirty is correctly typed for IN_PLACE */\n    if (typeof dirty === 'string') {\n      IN_PLACE = false;\n    }\n\n    if (IN_PLACE) {\n      /* Do some early pre-sanitization to avoid unsafe root nodes */\n      if ((dirty as Node).nodeName) {\n        const tagName = transformCaseFunc((dirty as Node).nodeName);\n        if (!ALLOWED_TAGS[tagName] || FORBID_TAGS[tagName]) {\n          throw typeErrorCreate(\n            'root node is forbidden and cannot be sanitized in-place'\n          );\n        }\n      }\n    } else if (dirty instanceof Node) {\n      /* If dirty is a DOM element, append to an empty document to avoid\n         elements being stripped by the parser */\n      body = _initDocument('<!---->');\n      importedNode = body.ownerDocument.importNode(dirty, true);\n      if (\n        importedNode.nodeType === NODE_TYPE.element &&\n        importedNode.nodeName === 'BODY'\n      ) {\n        /* Node is already a body, use as is */\n        body = importedNode;\n      } else if (importedNode.nodeName === 'HTML') {\n        body = importedNode;\n      } else {\n        // eslint-disable-next-line unicorn/prefer-dom-node-append\n        body.appendChild(importedNode);\n      }\n    } else {\n      /* Exit directly if we have nothing to do */\n      if (\n        !RETURN_DOM &&\n        !SAFE_FOR_TEMPLATES &&\n        !WHOLE_DOCUMENT &&\n        // eslint-disable-next-line unicorn/prefer-includes\n        dirty.indexOf('<') === -1\n      ) {\n        return trustedTypesPolicy && RETURN_TRUSTED_TYPE\n          ? trustedTypesPolicy.createHTML(dirty)\n          : dirty;\n      }\n\n      /* Initialize the document to work on */\n      body = _initDocument(dirty);\n\n      /* Check we have a DOM node from the data */\n      if (!body) {\n        return RETURN_DOM ? null : RETURN_TRUSTED_TYPE ? emptyHTML : '';\n      }\n    }\n\n    /* Remove first element node (ours) if FORCE_BODY is set */\n    if (body && FORCE_BODY) {\n      _forceRemove(body.firstChild);\n    }\n\n    /* Get node iterator */\n    const nodeIterator = _createNodeIterator(IN_PLACE ? dirty : body);\n\n    /* Now start iterating over the created document */\n    while ((currentNode = nodeIterator.nextNode())) {\n      /* Sanitize tags and elements */\n      _sanitizeElements(currentNode);\n\n      /* Check attributes next */\n      _sanitizeAttributes(currentNode);\n\n      /* Shadow DOM detected, sanitize it */\n      if (currentNode.content instanceof DocumentFragment) {\n        _sanitizeShadowDOM(currentNode.content);\n      }\n    }\n\n    /* If we sanitized `dirty` in-place, return it. */\n    if (IN_PLACE) {\n      return dirty;\n    }\n\n    /* Return sanitized string or DOM */\n    if (RETURN_DOM) {\n      if (RETURN_DOM_FRAGMENT) {\n        returnNode = createDocumentFragment.call(body.ownerDocument);\n\n        while (body.firstChild) {\n          // eslint-disable-next-line unicorn/prefer-dom-node-append\n          returnNode.appendChild(body.firstChild);\n        }\n      } else {\n        returnNode = body;\n      }\n\n      if (ALLOWED_ATTR.shadowroot || ALLOWED_ATTR.shadowrootmode) {\n        /*\n          AdoptNode() is not used because internal state is not reset\n          (e.g. the past names map of a HTMLFormElement), this is safe\n          in theory but we would rather not risk another attack vector.\n          The state that is cloned by importNode() is explicitly defined\n          by the specs.\n        */\n        returnNode = importNode.call(originalDocument, returnNode, true);\n      }\n\n      return returnNode;\n    }\n\n    let serializedHTML = WHOLE_DOCUMENT ? body.outerHTML : body.innerHTML;\n\n    /* Serialize doctype if allowed */\n    if (\n      WHOLE_DOCUMENT &&\n      ALLOWED_TAGS['!doctype'] &&\n      body.ownerDocument &&\n      body.ownerDocument.doctype &&\n      body.ownerDocument.doctype.name &&\n      regExpTest(EXPRESSIONS.DOCTYPE_NAME, body.ownerDocument.doctype.name)\n    ) {\n      serializedHTML =\n        '<!DOCTYPE ' + body.ownerDocument.doctype.name + '>\\n' + serializedHTML;\n    }\n\n    /* Sanitize final string template-safe */\n    if (SAFE_FOR_TEMPLATES) {\n      arrayForEach([MUSTACHE_EXPR, ERB_EXPR, TMPLIT_EXPR], (expr) => {\n        serializedHTML = stringReplace(serializedHTML, expr, ' ');\n      });\n    }\n\n    return trustedTypesPolicy && RETURN_TRUSTED_TYPE\n      ? trustedTypesPolicy.createHTML(serializedHTML)\n      : serializedHTML;\n  };\n\n  DOMPurify.setConfig = function (cfg = {}) {\n    _parseConfig(cfg);\n    SET_CONFIG = true;\n  };\n\n  DOMPurify.clearConfig = function () {\n    CONFIG = null;\n    SET_CONFIG = false;\n  };\n\n  DOMPurify.isValidAttribute = function (tag, attr, value) {\n    /* Initialize shared config vars if necessary. */\n    if (!CONFIG) {\n      _parseConfig({});\n    }\n\n    const lcTag = transformCaseFunc(tag);\n    const lcName = transformCaseFunc(attr);\n    return _isValidAttribute(lcTag, lcName, value);\n  };\n\n  DOMPurify.addHook = function (entryPoint, hookFunction) {\n    if (typeof hookFunction !== 'function') {\n      return;\n    }\n\n    arrayPush(hooks[entryPoint], hookFunction);\n  };\n\n  DOMPurify.removeHook = function (entryPoint, hookFunction) {\n    if (hookFunction !== undefined) {\n      const index = arrayLastIndexOf(hooks[entryPoint], hookFunction);\n\n      return index === -1\n        ? undefined\n        : arraySplice(hooks[entryPoint], index, 1)[0];\n    }\n\n    return arrayPop(hooks[entryPoint]);\n  };\n\n  DOMPurify.removeHooks = function (entryPoint) {\n    hooks[entryPoint] = [];\n  };\n\n  DOMPurify.removeAllHooks = function () {\n    hooks = _createHooksMap();\n  };\n\n  return DOMPurify;\n}\n\nexport default createDOMPurify();\n\nexport interface DOMPurify {\n  /**\n   * Creates a DOMPurify instance using the given window-like object. Defaults to `window`.\n   */\n  (root?: WindowLike): DOMPurify;\n\n  /**\n   * Version label, exposed for easier checks\n   * if DOMPurify is up to date or not\n   */\n  version: string;\n\n  /**\n   * Array of elements that DOMPurify removed during sanitation.\n   * Empty if nothing was removed.\n   */\n  removed: Array<RemovedElement | RemovedAttribute>;\n\n  /**\n   * Expose whether this browser supports running the full DOMPurify.\n   */\n  isSupported: boolean;\n\n  /**\n   * Set the configuration once.\n   *\n   * @param cfg configuration object\n   */\n  setConfig(cfg?: Config): void;\n\n  /**\n   * Removes the configuration.\n   */\n  clearConfig(): void;\n\n  /**\n   * Provides core sanitation functionality.\n   *\n   * @param dirty string or DOM node\n   * @param cfg object\n   * @returns Sanitized TrustedHTML.\n   */\n  sanitize(\n    dirty: string | Node,\n    cfg: Config & { RETURN_TRUSTED_TYPE: true }\n  ): TrustedHTML;\n\n  /**\n   * Provides core sanitation functionality.\n   *\n   * @param dirty DOM node\n   * @param cfg object\n   * @returns Sanitized DOM node.\n   */\n  sanitize(dirty: Node, cfg: Config & { IN_PLACE: true }): Node;\n\n  /**\n   * Provides core sanitation functionality.\n   *\n   * @param dirty string or DOM node\n   * @param cfg object\n   * @returns Sanitized DOM node.\n   */\n  sanitize(dirty: string | Node, cfg: Config & { RETURN_DOM: true }): Node;\n\n  /**\n   * Provides core sanitation functionality.\n   *\n   * @param dirty string or DOM node\n   * @param cfg object\n   * @returns Sanitized document fragment.\n   */\n  sanitize(\n    dirty: string | Node,\n    cfg: Config & { RETURN_DOM_FRAGMENT: true }\n  ): DocumentFragment;\n\n  /**\n   * Provides core sanitation functionality.\n   *\n   * @param dirty string or DOM node\n   * @param cfg object\n   * @returns Sanitized string.\n   */\n  sanitize(dirty: string | Node, cfg?: Config): string;\n\n  /**\n   * Checks if an attribute value is valid.\n   * Uses last set config, if any. Otherwise, uses config defaults.\n   *\n   * @param tag Tag name of containing element.\n   * @param attr Attribute name.\n   * @param value Attribute value.\n   * @returns Returns true if `value` is valid. Otherwise, returns false.\n   */\n  isValidAttribute(tag: string, attr: string, value: string): boolean;\n\n  /**\n   * Adds a DOMPurify hook.\n   *\n   * @param entryPoint entry point for the hook to add\n   * @param hookFunction function to execute\n   */\n  addHook(entryPoint: BasicHookName, hookFunction: NodeHook): void;\n\n  /**\n   * Adds a DOMPurify hook.\n   *\n   * @param entryPoint entry point for the hook to add\n   * @param hookFunction function to execute\n   */\n  addHook(entryPoint: ElementHookName, hookFunction: ElementHook): void;\n\n  /**\n   * Adds a DOMPurify hook.\n   *\n   * @param entryPoint entry point for the hook to add\n   * @param hookFunction function to execute\n   */\n  addHook(\n    entryPoint: DocumentFragmentHookName,\n    hookFunction: DocumentFragmentHook\n  ): void;\n\n  /**\n   * Adds a DOMPurify hook.\n   *\n   * @param entryPoint entry point for the hook to add\n   * @param hookFunction function to execute\n   */\n  addHook(\n    entryPoint: 'uponSanitizeElement',\n    hookFunction: UponSanitizeElementHook\n  ): void;\n\n  /**\n   * Adds a DOMPurify hook.\n   *\n   * @param entryPoint entry point for the hook to add\n   * @param hookFunction function to execute\n   */\n  addHook(\n    entryPoint: 'uponSanitizeAttribute',\n    hookFunction: UponSanitizeAttributeHook\n  ): void;\n\n  /**\n   * Remove a DOMPurify hook at a given entryPoint\n   * (pops it from the stack of hooks if hook not specified)\n   *\n   * @param entryPoint entry point for the hook to remove\n   * @param hookFunction optional specific hook to remove\n   * @returns removed hook\n   */\n  removeHook(\n    entryPoint: BasicHookName,\n    hookFunction?: NodeHook\n  ): NodeHook | undefined;\n\n  /**\n   * Remove a DOMPurify hook at a given entryPoint\n   * (pops it from the stack of hooks if hook not specified)\n   *\n   * @param entryPoint entry point for the hook to remove\n   * @param hookFunction optional specific hook to remove\n   * @returns removed hook\n   */\n  removeHook(\n    entryPoint: ElementHookName,\n    hookFunction?: ElementHook\n  ): ElementHook | undefined;\n\n  /**\n   * Remove a DOMPurify hook at a given entryPoint\n   * (pops it from the stack of hooks if hook not specified)\n   *\n   * @param entryPoint entry point for the hook to remove\n   * @param hookFunction optional specific hook to remove\n   * @returns removed hook\n   */\n  removeHook(\n    entryPoint: DocumentFragmentHookName,\n    hookFunction?: DocumentFragmentHook\n  ): DocumentFragmentHook | undefined;\n\n  /**\n   * Remove a DOMPurify hook at a given entryPoint\n   * (pops it from the stack of hooks if hook not specified)\n   *\n   * @param entryPoint entry point for the hook to remove\n   * @param hookFunction optional specific hook to remove\n   * @returns removed hook\n   */\n  removeHook(\n    entryPoint: 'uponSanitizeElement',\n    hookFunction?: UponSanitizeElementHook\n  ): UponSanitizeElementHook | undefined;\n\n  /**\n   * Remove a DOMPurify hook at a given entryPoint\n   * (pops it from the stack of hooks if hook not specified)\n   *\n   * @param entryPoint entry point for the hook to remove\n   * @param hookFunction optional specific hook to remove\n   * @returns removed hook\n   */\n  removeHook(\n    entryPoint: 'uponSanitizeAttribute',\n    hookFunction?: UponSanitizeAttributeHook\n  ): UponSanitizeAttributeHook | undefined;\n\n  /**\n   * Removes all DOMPurify hooks at a given entryPoint\n   *\n   * @param entryPoint entry point for the hooks to remove\n   */\n  removeHooks(entryPoint: HookName): void;\n\n  /**\n   * Removes all DOMPurify hooks.\n   */\n  removeAllHooks(): void;\n}\n\n/**\n * An element removed by DOMPurify.\n */\nexport interface RemovedElement {\n  /**\n   * The element that was removed.\n   */\n  element: Node;\n}\n\n/**\n * An element removed by DOMPurify.\n */\nexport interface RemovedAttribute {\n  /**\n   * The attribute that was removed.\n   */\n  attribute: Attr | null;\n\n  /**\n   * The element that the attribute was removed.\n   */\n  from: Node;\n}\n\ntype BasicHookName =\n  | 'beforeSanitizeElements'\n  | 'afterSanitizeElements'\n  | 'uponSanitizeShadowNode';\ntype ElementHookName = 'beforeSanitizeAttributes' | 'afterSanitizeAttributes';\ntype DocumentFragmentHookName =\n  | 'beforeSanitizeShadowDOM'\n  | 'afterSanitizeShadowDOM';\ntype UponSanitizeElementHookName = 'uponSanitizeElement';\ntype UponSanitizeAttributeHookName = 'uponSanitizeAttribute';\n\ninterface HooksMap {\n  beforeSanitizeElements: NodeHook[];\n  afterSanitizeElements: NodeHook[];\n  beforeSanitizeShadowDOM: DocumentFragmentHook[];\n  uponSanitizeShadowNode: NodeHook[];\n  afterSanitizeShadowDOM: DocumentFragmentHook[];\n  beforeSanitizeAttributes: ElementHook[];\n  afterSanitizeAttributes: ElementHook[];\n  uponSanitizeElement: UponSanitizeElementHook[];\n  uponSanitizeAttribute: UponSanitizeAttributeHook[];\n}\n\nexport type HookName =\n  | BasicHookName\n  | ElementHookName\n  | DocumentFragmentHookName\n  | UponSanitizeElementHookName\n  | UponSanitizeAttributeHookName;\n\nexport type NodeHook = (\n  this: DOMPurify,\n  currentNode: Node,\n  hookEvent: null,\n  config: Config\n) => void;\n\nexport type ElementHook = (\n  this: DOMPurify,\n  currentNode: Element,\n  hookEvent: null,\n  config: Config\n) => void;\n\nexport type DocumentFragmentHook = (\n  this: DOMPurify,\n  currentNode: DocumentFragment,\n  hookEvent: null,\n  config: Config\n) => void;\n\nexport type UponSanitizeElementHook = (\n  this: DOMPurify,\n  currentNode: Node,\n  hookEvent: UponSanitizeElementHookEvent,\n  config: Config\n) => void;\n\nexport type UponSanitizeAttributeHook = (\n  this: DOMPurify,\n  currentNode: Element,\n  hookEvent: UponSanitizeAttributeHookEvent,\n  config: Config\n) => void;\n\nexport interface UponSanitizeElementHookEvent {\n  tagName: string;\n  allowedTags: Record<string, boolean>;\n}\n\nexport interface UponSanitizeAttributeHookEvent {\n  attrName: string;\n  attrValue: string;\n  keepAttr: boolean;\n  allowedAttributes: Record<string, boolean>;\n  forceKeepAttr: boolean | undefined;\n}\n\n/**\n * A `Window`-like object containing the properties and types that DOMPurify requires.\n */\nexport type WindowLike = Pick<\n  typeof globalThis,\n  | 'DocumentFragment'\n  | 'HTMLTemplateElement'\n  | 'Node'\n  | 'Element'\n  | 'NodeFilter'\n  | 'NamedNodeMap'\n  | 'HTMLFormElement'\n  | 'DOMParser'\n> & {\n  document?: Document;\n  MozNamedAttrMap?: typeof window.NamedNodeMap;\n} & Pick<TrustedTypesWindow, 'trustedTypes'>;\n"], "sourceRoot": ""}