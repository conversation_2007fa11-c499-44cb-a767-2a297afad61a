import React, { useEffect, useState } from 'react';
import APIServices from '../../../../service/APIService';
import { API } from '../../../../constants/api_url';
import SupplierReport from './SupplierReport';

const CryptoJS = require("crypto-js");

const PublicSupplierReportView = () => {
    const urlParams = new URLSearchParams(window.location.search);
    const token = decodeURIComponent(urlParams.get('token'));

    const SECRET_KEY = "e!sq6kee4supassid";

    const [show, setShow] = useState(false);
    const [load, setLoad] = useState(true);
    const [auditreport, setAuditReport] = useState({});

    useEffect(() => {
        if (!token) {
            setLoad(false);
            return;
        }

        const fetchData = async () => {
            const decryptedId = decryptToken(token, SECRET_KEY);

            if (!decryptedId) {
                console.error("Failed to decrypt token.");
                setLoad(false);
                return;
            }

            const supplierAssUri = {
                include: [
                    {
                        relation: "supplierAssignmentSubmission",
                        scope: {
                            fields: {
                                type: true,
                                supplierMSIScore: true,
                                submitted_on: true,
                                modified_on: true
                            }
                        }
                    },
                    {
                        relation: "auditorAssignmentSubmission",
                        scope: {
                            fields: {
                                type: true,
                                auditorMSIScore: true,
                                submitted_on: true,
                                modified_on: true,
                                id: true
                            }
                        }
                    },
                    { relation: "vendor" },
                    {
                        relation: "supplierActions",
                        scope: {
                            include: [
                                {
                                    relation: "supplierActionHistories"
                                }
                            ]
                        }
                    }
                ]
            }


            try {


                const response = await APIServices.get(
                    API.SupplierAssessmentAss_Edit(decryptedId) +
                    `?filter=${encodeURIComponent(JSON.stringify(supplierAssUri))}`
                );
                console.log(response)
                if (response.status === 200) {
                    setAuditReport(response.data);
                    setShow(true);
                } else {
                    setShow(false);
                }
            } catch (e) {
                console.error("API Error:", e);
                setShow(false);
            } finally {
                setLoad(false);
            }
        };

        fetchData();
    }, [token]);

    function decryptToken(encryptedText, secret) {
        try {
            const bytes = CryptoJS.AES.decrypt(encryptedText, secret);
            return bytes.toString(CryptoJS.enc.Utf8);
        } catch (error) {
            console.error("Decryption failed:", error);
            return '';
        }
    }

    return (
        <div>
            {load ? (
                <div className="col-12 flex align-items-center justify-content-center">
                    <div> <span className="loader"></span> </div>
                    <label className="col-12 fs-16 fw-5" style={{ display: 'flex' }}>
                        Generating Report Preview, please wait...
                    </label>
                </div>
            ) : (
                show ? <div className='flex justify-content-center'> <div style={{ width: '90%' }}> <SupplierReport report={auditreport} /> </div>  </div> : <div>Something went wrong while previewing the report. Report this error with MSI <NAME_EMAIL> </div>
            )}
        </div>
    );
};

export default PublicSupplierReportView;
