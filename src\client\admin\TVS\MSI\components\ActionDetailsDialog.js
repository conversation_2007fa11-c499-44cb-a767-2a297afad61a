import React from 'react';
import { Dialog } from 'primereact/dialog';
import ActionInformationCard from './ActionInformationCard';
import SupplierInformationCard from './SupplierInformationCard';
import ActionHistoryCard from './ActionHistoryCard';

const ActionDetailsDialog = ({ 
    visible, 
    onHide, 
    action, 
    getCategoryLabel, 
    getNonComplianceLabel, 
    getStatusLabel, 
    getUserName 
}) => {
    if (!action) return null;

    return (
        <Dialog
            header={
                <div className="flex align-items-center">
                    <i className="pi pi-exclamation-circle mr-2" style={{ fontSize: '1.5rem', color: '#315975' }}></i>
                    <span className="font-bold">Action Details</span>
                </div>
            }
            visible={visible}
            onHide={onHide}
            style={{ width: '95vw', maxWidth: '1400px' }}
            breakpoints={{ '960px': '98vw' }}
            className="action-details-dialog"
        >
            <div className="grid">
                {/* Main Action Details Card */}
                <div className="col-6">
                    <ActionInformationCard 
                        action={action} 
                        getCategoryLabel={getCategoryLabel}
                        getNonComplianceLabel={getNonComplianceLabel}
                        getStatusLabel={getStatusLabel}
                        getUserName={getUserName}
                    />
                </div>

                {/* Supplier Info Card */}
                <div className="col-6">
                    <SupplierInformationCard action={action} />
                </div>

                {/* Action History Card */}
                {action.supplierActionHistories?.length > 0 && (
                    <div className="col-12">
                        <ActionHistoryCard 
                            histories={action.supplierActionHistories} 
                            getUserName={getUserName} 
                        />
                    </div>
                )}
            </div>
        </Dialog>
    );
};

export default ActionDetailsDialog;
