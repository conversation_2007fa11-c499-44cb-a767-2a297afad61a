import React, { useState, useEffect } from "react";
import "../reports/toc_style.css";
import { useHeadsObserver } from "./hooks";
import * as XLSX from "xlsx";
import useForceUpdate from "use-force-update";
import $ from "jquery";
import Axios from "axios";
import { API } from "../../constants/api_url";
import { useSelector } from "react-redux";
import { Button } from "primereact/button";
import * as XlsxPopulate from "xlsx-populate/browser/xlsx-populate";
import { saveAs } from "file-saver";
import moment from "moment";
import pdfMake from "pdfmake/build/pdfmake";
import pdfFonts from "pdfmake/build/vfs_fonts";
import { Dropdown } from 'primereact/dropdown'
import APIServices from "../../service/APIService";


// pdfMake.vfs = pdfFonts.pdfMake.vfs;
// pdfMake.fonts = {
//     Roboto: {
//         normal: 'https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.66/fonts/Roboto/Roboto-Regular.ttf',
//         bold: 'https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.66/fonts/Roboto/Roboto-Medium.ttf',
//         italics: 'https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.66/fonts/Roboto/Roboto-Italic.ttf',
//         bolditalics: 'https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.66/fonts/Roboto/Roboto-MediumItalic.ttf'
//       }
// }
const { DateTime } = require("luxon");

window.jQuery = $;
window.$ = $;
const dcf_id = [11, 10, 15, 72, 16, 36];
const Ecovadis = () => {
    const { fymonth } = useSelector((state) => state.user.fyStartMonth);
    const [headings, setHeadings] = useState([]);
    const { activeId } = useHeadsObserver();
    const [tableData, setTableData] = useState([]);
    const [workbook, setWorkbook] = useState(null);
    const [firstSheet, setFirstSheet] = useState(null);
    const [year, setYear] = useState(2022);
    const [rfData, setRFData] = useState({});
    const admin_data = useSelector((state) => state.user.admindetail);
    const emissionFactor = useSelector((state) => state.emissionfactor.emissionFactor);
    const locationList = useSelector((state) => state.sitelist.locationList);
    const siteList = useSelector((state) => state.sitelist.siteList);
    // const rflibrary = useSelector((state) => state.library.rf)

    const [dcfass, setDCFAss] = useState([]);
    const [dcflist, setDcfList] = useState([]);
    const [response, setResponse] = useState([]);
    const [report, setReport] = useState([]);
    const [reportEF, setReportEF] = useState([]);
    const [dpreport, setDpReport] = useState([]);
    let months_ = ["JAN", "FEB", "MAR", "APR", "MAY", "JUN", "JUL", "AUG", "SEP", "OCT", "NOV", "DEC"];
    const forceUpdate = useForceUpdate();
    function formatSubscript__(inputString, findArray, replaceArray) {
        let result = [];
        let currentIndex = 0;

        for (let i = 0; i < findArray.length; i++) {
            const findText = findArray[i];
            const replaceValue = replaceArray[i];
            const index = inputString.toLowerCase().indexOf(findText, currentIndex);

            if (index === -1) {
                // If the findText is not found, add the remaining text and break
                result.push(inputString.substring(currentIndex));
                break;
            }

            // Add the text before the found substring
            result.push(inputString.substring(currentIndex, index));

            // Add the subscripted replaceValue as an object
            result.push(...getResult(findText, replaceValue));

            // Update the currentIndex to continue searching
            currentIndex = index + findText.length;
        }

        // Add any remaining text after the last replacement
        if (currentIndex < inputString.length) {
            result.push(inputString.substring(currentIndex));
        }

        // Filter out empty strings
        result = result.filter((item) => item !== "");

        return result;
    }
    const handleNaNAndInfinity = (value) => {
        if (typeof value === 'number' && isFinite(value)) {
            return value.toFixed(0); // Return the original value if it's a valid number
        } else {
            return 0; // Return 0 for NaN or Infinity
        }
    }
    const getDataByDPArray = (dparr, yr) => {
        let dpreport_ = JSON.parse(JSON.stringify(dpreport))
        let d = 0
        dparr.forEach((dpid) => {
            dpreport_.filter((i) => { return i.dp === dpid }).forEach((k) => {


                if (checkYear(k.rp, yr) !== 0) {
                    d = d + (typeof k.value === 'number' ? k.value : parseFloat(k.value) >= 0 ? parseFloat(k.value) : 0)

                }

            })
        })

        return isNaN(d) ? 0 : d.toFixed(0)
    }
    function formatSubscript(inputString, findArray, replaceArray) {
        return inputString;
    }
    function getResult(str, str2) {
        let arr = str.split(str2.toString());
        arr.splice(1, 0, { text: str2, fontSize: 7, baseline: -5 });
        return arr;
    }
    function ulOrOlToPdfMake(element) {
        const result = [];
        const listItems = Array.from(element.querySelectorAll("li"));

        listItems.forEach((li) => {
            const text = li.textContent.trim();
            if (text !== "") {
                result.push({ text });
            }
        });

        return result;
    }
    function customOlToPdfMake(element) {
        const result = [];
        const listItems = Array.from(element.querySelectorAll("li"));

        listItems.forEach((li) => {
            const text = li.textContent.trim();
            if (text !== "") {
                result.push({ text });
            }
        });

        return result;
    }
    async function exportTable2Excel(type) {
        let initialData = [
            {
                alignment: "center", // Center the text horizontally
                margin: [0, 250], // Adjust the top margin to vertically center the text
                text: [
                    { text: "EcoVadis REPORT" + "\n", fontSize: 40, color: "#315874", bold: true },
                    { text: "FY " + year + " - " + (year + 1).toString().substr(2, 3) + "\n", fontSize: 20, color: "#315874" },
                    { text: DateTime.local().toFormat("MMMM dd, yyyy"), fontSize: 20, color: "blue" }, // Customize the font size and color
                ],
                pageBreak: "after",
            },
            {
                toc: {
                    id: "sectionHeader",
                    title: { text: "Table of Content", style: "tdHead" },
                },
                pageBreak: "after",
            },
        ],
            data = [];
        const div = document.getElementById("main");
        for (let i = 0; i < div.children.length; i++) {
            if (div.childNodes[i].tagName.toLowerCase() === "sectionheader") {
                data.push({
                    table: {
                        widths: ["*"],
                        body: [[{ tocItem: "sectionHeader", text: formatSubscript(div.childNodes[i].textContent, ["tco2e", "n2o", "ch4", "co2"], ["2", "2", "4", "2"]), style: "secHead", border: [false, false, false, false] }]],
                    },
                });
                data.push({
                    text: "", // Empty text

                    margin: [10, 10], // Adjust the margin for horizontal space
                });
            } else if (div.childNodes[i].tagName.toLowerCase() === "sectionheader1") {
                data.push({ text: formatSubscript(div.childNodes[i].textContent, ["tco2e", "n2o", "ch4", "co2"], ["2", "2", "4", "2"]), style: "text-under" });
                data.push({
                    text: "", // Empty text

                    margin: [5, 5], // Adjust the margin for horizontal space
                });
            } else {
                if (div.childNodes[i].children.length !== 0) {
                    for (let child = 0; child < div.childNodes[i].children.length; child++) {
                        let tag = div.childNodes[i].childNodes[child].tagName;

                        if (tag) {
                            if (tag === "OL") {
                                data.push({ ol: ulOrOlToPdfMake(div.childNodes[i].childNodes[child]) });
                                data.push({
                                    text: "", // Empty text

                                    margin: [0, 5], // Adjust the margin for horizontal space
                                });
                            } else if (tag === "UL") {
                                data.push({ ul: ulOrOlToPdfMake(div.childNodes[i].childNodes[child]) });
                                data.push({
                                    text: "", // Empty text

                                    margin: [0, 5], // Adjust the margin for horizontal space
                                });
                            } else if (tag === "TABLE") {
                                let content = generatePdfMakeContentFromTable(div.childNodes[i].childNodes[child]);

                                data.push({
                                    table: {
                                        headerRows: 1,
                                        widths: JSON.parse(JSON.stringify(content))[0].map((a, b) => {
                                            return b == 0 ? "*" : "auto";
                                        }),
                                        body: content,
                                        style: "tableStyle",
                                    },
                                });
                                data.push({
                                    text: "", // Empty text

                                    margin: [0, 10], // Adjust the margin for horizontal space
                                });
                            } else if (tag === "IMG") {
                                data.push({ image: div.childNodes[i].childNodes[child].src });

                                data.push({
                                    text: "", // Empty text

                                    margin: [0, 5], // Adjust the margin for horizontal space
                                });
                            } else if (tag === "BR") {
                                let txt = `Definition: Operational Boundaries requires choosing the scope of emissions that will be reported. There are three scopes of emissions that can be reported:
                       \n Scope 1: Direct GHG Emissions from company owned or controlled sources
                       \n Scope 2: Indirect GHG Emissions from purchased electricity or steam.
                       \n According the GHG Protocol Corporate Reporting Standard, Scope 1 and Scope 2 emissions must be reported. Scope 3 emissions are voluntary`;
                                data.push({ text: txt });
                                data.push({
                                    text: "", // Empty text

                                    margin: [0, 5], // Adjust the margin for horizontal space
                                });
                            } else {
                                data.push({ text: formatSubscript(div.childNodes[i].childNodes[child].textContent, ["tco2e", "n2o", "ch4", "co2"], ["2", "2", "4", "2"]) });
                                data.push({
                                    text: "", // Empty text

                                    margin: [0, 5], // Adjust the margin for horizontal space
                                });
                            }
                        }
                    }
                } else {
                    data.push({ text: formatSubscript(div.childNodes[i].textContent, ["tco2e", "n2o", "ch4", "co2"], ["2", "2", "4", "2"]) });
                    data.push({
                        text: "", // Empty text

                        margin: [5, 5], // Adjust the margin for horizontal space
                    });
                }
            }
        }

        data.forEach((k) => {
            if (k.table) {
                if (!haveSameSubarrayLengths(k.table.body)) {
                    console.log(k.table.body);
                }
            }
        });

        let images = {
            clientlogo: document.getElementById("clientlogo").src,
        };

        const header = (currentPage, pageCount, pageSize) => {

            return {
                columns: [
                    {
                        text: `EcoVadis Report FY` + year.toString().substr(2, 3),
                        style: "headerText",
                        margin: [30, 20],
                        fit: [40, 40],
                        alignment: "left",
                    },
                    {
                        image: "clientlogo",
                        fit: [40, 40],
                        margin: [0, 5, 15, 0],
                        alignment: "right",
                    },
                ],
                // Add margins to the header
            };

        };
        const documentDefinition = {
            info: {
                title: "EcoVadis Report - " + DateTime.local().toFormat("MMMM dd, yyyy"),
                author: "Navigos",
                subject: "EcoVadis Report",
                keywords: "Dont share unless people within same organization",
                producer: "EiSqr",
            },
            // userPassword: 'Report@',
            // ownerPassword: '123456',
            permissions: {
                printing: "highResolution", //'lowResolution'
                modifying: false,
                copying: false,
                annotating: true,
                fillingForms: true,
                contentAccessibility: true,
                documentAssembly: true,
            },
            pageSize: "A4",
            pageMargins: [30, 70, 30, 30], // [left, top, right, bottom] margins
            header,
            footer: function (currentPage, pageCount) {
                return {
                    text: "Page " + currentPage + " of " + pageCount,
                    alignment: "center",
                    fontSize: 8,
                };
            },
            content: [...initialData, ...data],
            // defaultStyle: {
            //     font: 'Roboto'
            //   },

            images,
            styles: {
                tdHead: {
                    bold: true,
                    alignment: "center",
                    valign: "middle",
                    fillColor: "#315874",
                    color: "white",
                },

                secHead: {
                    bold: true,
                    fillColor: "#315874",
                    alignment: "center",
                    padding: [10, 10],
                    color: "white",
                },
                headerText: {
                    fontSize: 14,
                    bold: true,
                    color: "para", // Text color
                },
                "text-under": {
                    decoration: "underline",
                    color: "#315874",
                    bold: true,
                },
                boldBlue: {
                    color: "#315874",
                    bold: true,
                },
            },
        };
        console.log([...initialData, ...data]);


        if (type === 0) {
            const pdf = pdfMake.createPdf(documentDefinition);

            pdf.download("EcoVadisReport.pdf");

        } else {
            pdfMake.createPdf(documentDefinition).open({}, window.open('', '_blank'));

        }
    }
    function haveSameSubarrayLengths(data) {
        if (data.length < 2) {
            // If there are fewer than 2 subarrays, they are considered to have the same length.
            return true;
        }

        const firstSubarrayLength = data[0].length;

        for (let i = 1; i < data.length; i++) {
            if (data[i].length !== firstSubarrayLength) {
                return false;
            }
        }

        return true;
    }
    function generatePdfMakeContentFromTable(table) {
        if (!table) {
            console.error(`Table  not found.`);
            return [];
        }

        let contentArray = [],
            maxCol = 0;
        for (let i = 0; i < table.rows.length; i++) {
            if (maxCol <= table.rows[i].cells.length) {
                maxCol = table.rows[i].cells.length;
            }
        }

        for (let i = 0; i < table.rows.length; i++) {
            const rowArray = [];

            for (let j = 0; j < table.rows[i].cells.length; j++) {
                const cell = table.rows[i].cells[j];
                const colSpan = cell.getAttribute("colspan");
                const rowSpan = cell.getAttribute("rowspan");
                const cellText = cell.textContent.trim();

                const cellObject = { text: cellText };
                cellObject.colSpan = parseInt(colSpan);
                cellObject.rowSpan = parseInt(rowSpan);
                cellObject.style = cell.getAttribute("class");
                rowArray.push(cellObject);
                if (parseInt(colSpan) > 1) {
                    for (let j = 0; j < parseInt(colSpan) - 1; j++) {
                        rowArray.push({});
                    }
                }
            }

            contentArray.push(rowArray);
        }
        contentArray.forEach((i, index) => {
            if (i.length !== maxCol) {
                if (contentArray[index - 1]) {
                    contentArray[index - 1].forEach((k, ind) => {
                        if (k.rowSpan) {
                            if (k.rowSpan > 1) {
                                if (k.colSpan === 1) {
                                    i.splice(ind, 0, { text: "", colSpan: k.colSpan, rowSpan: k.rowSpan - 1 });
                                } else {
                                    let newind = ind;
                                    for (let j = 0; j < parseInt(k.colSpan); j++) {
                                        i.splice(newind, 0, {});
                                        newind++;
                                    }
                                }
                            }
                        } else {
                            for (let j = 0; j < maxCol - Object.keys(i).length; j++) {
                                // i.push({id:1});
                            }
                        }
                    });
                }
            }
        });
        contentArray.forEach((i, index) => {
            if (i.length !== maxCol) {
                let len = Object.keys(i).length;
                for (let j = 0; j < maxCol - len; j++) {
                    i.push({});
                }
            }
        });
        return contentArray;
    }

    const isMergedCell = (merge, rowIndex, colIndex) => {
        return merge.some((range) => rowIndex >= range.s.r && rowIndex <= range.e.r && colIndex >= range.s.c && colIndex <= range.e.c);
    };
    const getSum = (subset) => {
        let i = 0;
        subset.forEach((item) => {
            i = i + item[1];
        });
        return i;
    };
    const checkReportingPeriod = (rp, filter) => {
        let count = 0,
            rps = [];
        filter.forEach((item) => {
            if (rp.includes(item)) {
                count = count + 1;
                rps.push(item);
            }
        });
        return { result: count === rp.length, count: count, rps };
    };
    function getObjectsWithEmptyString(arr) {
        return arr
            .map((obj) => {
                const updatedObj = { ...obj };
                Object.keys(updatedObj).forEach((key) => {
                    if (typeof updatedObj[key] === "string" && updatedObj[key].trim() === "") {
                        delete updatedObj[key];
                    }
                });
                return updatedObj;
            })
            .filter((obj) => Object.values(obj).some((value) => value !== ""));
    }
    const getCellColSpan = (rowIndex, colIndex) => {
        const merge = workbook.Sheets[firstSheet]["!merges"] || [];
        for (const range of merge) {
            if (rowIndex >= range.s.r && rowIndex <= range.e.r && colIndex >= range.s.c && colIndex <= range.e.c) {
                return range.e.c - range.s.c + 1;
            }
        }
        return 1;
    };

    const getCellRowSpan = (rowIndex, colIndex) => {
        const merge = workbook.Sheets[firstSheet]["!merges"] || [];
        for (const range of merge) {
            if (rowIndex >= range.s.r && rowIndex <= range.e.r && colIndex >= range.s.c && colIndex <= range.e.c) {
                return range.e.r - range.s.r + 1;
            }
        }
        return 1;
    };
    function findValueByKey(object, key) {
        if (key in object) {
            return object[key];
        } else {
            return null; // or any other default value you want to return if the key is not found
        }
    }

    const getClassNameByRFResponse = (key, uid) => {
        let locData = JSON.parse(JSON.stringify(rfData));
        let result = findValueByKey(locData, key);
        if (key !== 0) {

        }
        return "";
    };

    const getAttachmentByRFResponse = (key, uid) => {
        let locData = JSON.parse(JSON.stringify(rfData));
        let result = findValueByKey(locData, key);

        if (result) {
            if (result[0].type === 0) {
                if (typeof uid === "string") {
                    let index = result[0].response.findIndex((k) => {
                        return k.name === uid;
                    });
                    if (index !== -1) {

                        return result[0].response[index].value === undefined ? [] : result[0].response[index].value;


                    } else {
                        return [];
                    }
                } else {
                    let str = "";
                    uid.forEach((id) => {
                        let index = result[0].response.findIndex((k) => {
                            return k.name === id;
                        });
                        if (index !== -1) {

                            str = str + " " + result[0].response[index].value;
                        }
                    });
                    if (str.trim().length !== 0) {
                        return str;
                    } else {
                        return 'NA';
                    }
                }
            } else {
                return []
            }
        }
        return [];
    };
    const updateDataByYear = (val) => {
        setYear(val);
        forceUpdate();
    };

    useEffect(() => {
        const elements = Array.from(document.querySelectorAll("sectionheader,sectionheader1,sectionheader2")).map((elem) => ({
            id: elem.id,
            text: elem.childNodes[0].textContent.trim(),
            level: Number(elem.nodeName.charAt(13)),
        }));

        let as = [],
            indx = 0;
        elements.forEach((item, ind) => {
            if (item.level === 0) {
                as[indx] = item;
                indx = indx + 1;
            } else if (elements[ind - 1].level === 0) {
                as[indx] = { item: [item], level: 1 };
                if (elements[ind + 1] !== undefined && elements[ind + 1].level === 0) {
                    indx = indx + 1;
                }
            } else {
                as[indx].item.push(item);
                if (elements[ind + 1] !== undefined && elements[ind + 1].level === 0) {
                    indx = indx + 1;
                }
            }
        });

        setHeadings(as);
    }, []);
    const groupArrayObject = (array, obj) => {
        return array.reduce(
            (group, arr) => {
                let key = arr[obj];

                group[key] = group[key] ?? [];

                group[key].push(arr);

                return group;
            },

            {}
        );
    };
    // useEffect(() => {
    //     let gtaString = {
    //         include: ["newTargetsTwos", "newIndicatorTwos", "newInitiatives"],
    //     };
    //     let dcf_list = [],
    //         dcf_submitted = [],
    //         locloc = [];
    //     let category_string = {
    //         include: [{ relation: "newTopics", scope: { include: [{ relation: "newMetrics", scope: { include: [{ relation: "newDataPoints" }] } }] } }],
    //     };

    //     const promise1 = APIServices.get(API.DCF);
    //     const promise2 = APIServices.get(API.DCF_Submit_UP(admin_data.id));
    //     const promise3 = APIServices.get(API.QNDP_Report_UP(admin_data.id))
    //     const promise4 = APIServices.get(API.RF_Submit_UP(admin_data.id));
    //     const promise5 = APIServices.get(API.AssignDCFUser_UP(admin_data.id));
    //     Promise.all([promise1, promise2, promise3, promise4, promise5]).then(function (values) {
    //         setDCFAss(
    //             values[4].data
    //                 .filter((k) => {
    //                     return dcf_id.includes(k.dcfId) && k.type === 0;
    //                 })
    //                 .map((k) => {
    //                     return { dcfId: k.dcfId, site: k.site[0] };
    //                 })
    //         );
    //         dcf_list = values[0].data;
    //         setDcfList(values[0].data);
    //         dcf_submitted = values[1].data;
    //         setDpReport(values[2].data);
    //         let val = [],
    //             filterarr = groupArrayObject(values[2].data, "submitId");
    //         Object.keys(filterarr).forEach((item) => {
    //             val.push({ id: filterarr[item][0].submitId, rp: filterarr[item][0].rp, rp_: getRP_(filterarr[item][0].rp), year: filterarr[item][0].year, dcf: filterarr[item][0].dcfId, site: filterarr[item][0].site, response: Object.values(groupArrayObject(filterarr[item], "form_id")) });
    //         });

    //         setResponse(val);
    //         let rfid_group = groupArrayObject(values[3].data, "rfid");
    //         Object.keys(rfid_group).forEach((key) => {
    //             rfid_group[key].sort((a, b) => {
    //                 return b.id - a.id;
    //             });
    //         });

    //         setRFData(rfid_group);
    //         forceUpdate();
    //     });
    // }, []);
    // useEffect(() => {
    //     if (response.length !== 0) {
    //         let report_ = renderData(
    //             { location: { a: { name: "All", id: 0 }, b: { name: "All", id: 0 }, c: { name: "All", id: 0 } }, year: { name: "All", id: 0 }, month: null, indicator: { id: 0 }, timeline: [], from: getDateObjectByMonth_Year(4, year), to: getDateObjectByMonth_Year(3, year + 1) },
    //             1
    //         );
    //         let reportEF_ = renderDataEF(
    //             { location: { a: { name: "All", id: 0 }, b: { name: "All", id: 0 }, c: { name: "All", id: 0 } }, year: { name: "All", id: 0 }, month: null, indicator: { id: 0 }, timeline: [], from: getDateObjectByMonth_Year(4, year), to: getDateObjectByMonth_Year(3, year + 1) },
    //             1
    //         );

    //         // report_[`${year - 1}`] = renderData({ location: { a: { name: 'All', id: 0 }, b: { name: 'All', id: 0 }, c: { name: 'All', id: 0 } }, year: { name: 'All', id: 0 }, month: null, indicator: { id: 0 }, timeline: [], from: getDateObjectByMonth_Year(4, year - 1), to: getDateObjectByMonth_Year(3, year) }, 1)
    //         setReport(report_);
    //         setReportEF(reportEF_);
    //         forceUpdate();
    //     }
    // }, [response, year]);

    const getScopeData = (year, area) => {
        if (report[year]) {
            return report[year]
                .filter((k) => {
                    return k.scope === area;
                })
                .map((j) => {
                    return j.ghg;
                })
                .reduce((a, b) => {
                    return a + b;
                }, 0)
                .toFixed(2);
        }
        return 0;
    };
    const checkYear = (rp, yr) => {
        let betweenMonths = [];

        let endDate = moment.utc(getDateObjectByMonth_Year(2, yr + 1)).local();
        let startDate = moment.utc(getDateObjectByMonth_Year(2, yr)).local();
        while (startDate.startOf("month") <= endDate.startOf("month")) {
            betweenMonths.push(startDate.format("MM-YYYY"));
            startDate.add(1, "month");
        }
        return betweenMonths.filter((i) => {
            return rp.includes(i);
        }).length === rp.length
            ? 1
            : betweenMonths.filter((i) => {
                return rp.includes(i);
            }).length;
    };
    const getDataByDP = (dpid, yr) => {
        let dpreport_ = JSON.parse(JSON.stringify(dpreport));
        let d = 0;

        dpreport_
            .filter((i) => {
                return i.dp === dpid;
            })
            .forEach((k) => {
                if (checkYear(k.rp, yr) !== 0) {
                    d = d + (typeof k.value === 'number' ? k.value : parseFloat(k.value) >= 0 ? parseFloat(k.value) : 0)
                        ;
                }
            });

        return d;
    };
    const checkScope = (arr) => {
        let index = dcfass.findIndex((l) => {
            return arr.includes(l.dcfId);
        });
        return index !== -1 ? true : false;
    };
    function concatenateArrayWithAnd(array) {
        if (array.length === 1) {
            return array[0];
        } else if (array.length > 1) {
            const lastElement = array.pop(); // Remove the last element
            return `${array.join(", ")} and ${lastElement}`;
        } else {
            return "NA"; // Return an empty string if the array is empty
        }
    }
    const getMCFuelUsed = () => {
        let result = [];

        report
            .filter((l) => {
                return l.dcfId === 15;
            })
            .forEach((i, j) => {
                !result.includes(i.mode) && result.push(i.mode);
            });

        return concatenateArrayWithAnd(result);
    };
    const getSCFuelUsed = () => {
        let result = [];

        report
            .filter((l) => {
                return l.dcfId === 11;
            })
            .forEach((i, j) => {
                !result.includes(i.fuel_type) && result.push(i.fuel_type);
            });
        return concatenateArrayWithAnd(result);
    };
    const getFEGasUsed = () => {
        let result = [];

        report
            .filter((l) => {
                return l.dcfId === 10;
            })
            .forEach((i, j) => {
                result.push(i.gastype);
            });
        return concatenateArrayWithAnd(result);
    };
    function removeDuplicatesByProperties(arr, keys) {
        const seen = new Set();
        return arr.filter((item) => {
            const key = JSON.stringify(keys.map((key) => item[key]));
            if (!seen.has(key)) {
                seen.add(key);
                return true;
            }
            return false;
        });
    }
    const renderFEGas = () => {
        let result = [];

        reportEF
            .filter((l) => {
                return l.dcfId === 10;
            })
            .forEach((i, j) => {
                result.push({ type: i.gastype, ghg: i.co2e_.toFixed(2) + " kg CO2e/kg" });
            });

        return result.length === 0 ? [{ type: "Not Found", ghg: 0 }] : result;
    };
    const renderSCFuel = () => {
        let result = [];

        reportEF
            .filter((l) => {
                return l.dcfId === 11;
            })
            .forEach((i, j) => {
                result.push({ type: i.fuel_type + "-" + i.unit, co2_: i.co2_.toFixed(2) + " kg CO2e/kg", n2o_: i.n2o_.toFixed(2) + " kg CO2e/kg", ch4_: i.ch4_.toFixed(2) + " kg CO2e/kg" });
            });
        return result.length === 0 ? [{ type: "Not Found", co2_: 0, ch4_: 0, n2o_: 0 }] : removeDuplicatesByProperties(result, ["co2_", "n2o_", "ch4", "unit", "fuel_type"]);
    };

    const renderMCFuel = () => {
        let result = [];

        reportEF
            .filter((l) => {
                return l.dcfId === 15;
            })
            .forEach((i, j) => {
                console.log("i", i);
                result.push({ type: i.mode + " - " + i.fuel_cat, ghg: i.co2e_.toFixed(2) + " kg CO2e /litre" });
            });

        return result.length === 0 ? [{ type: "Not Found", ghg: 0 }] : result;
    };
    const getScopeDataByDCF = (id) => {
        console.log(
            JSON.parse(JSON.stringify(report)).filter((i) => {
                return id.includes(i.dcfId);
            }),
            "report",
            id
        );
        let report_ = JSON.parse(JSON.stringify(report))
            .filter((i) => {
                return id.includes(i.dcfId);
            })
            .map((i) => {
                return i.ghg;
            })
            .reduce((a, b) => {
                return a + b;
            }, 0);
        return report_.toFixed(2);
    };
    const renderEmissionScope1 = (text) => {
        return (
            <>
                {checkScope([11, 10, 15]) && (
                    <>
                        {checkScope([11]) && (
                            <tr>
                                <td colspan="1" rowspan="1">
                                    Stationary Combustion Emission
                                </td>
                                <td colspan="1" rowspan="1">
                                    {getScopeDataByDCF([11])}
                                </td>
                                <td colSpan="1" className="text-center"></td>
                            </tr>
                        )}
                        {checkScope([15]) && (
                            <tr>
                                <td colspan="1" rowspan="1">
                                    Mobile Combustion Emission
                                </td>
                                <td colspan="1" rowspan="1">
                                    {getScopeDataByDCF([15])}
                                </td>
                                <td colSpan="1" className="text-center"></td>
                            </tr>
                        )}
                        {checkScope([10]) && (
                            <tr>
                                <td colspan="1" rowspan="1">
                                    Fugitive Emissions
                                </td>
                                <td colspan="1" rowspan="1">
                                    {getScopeDataByDCF([10])}
                                </td>
                                <td colSpan="1" className="text-center"></td>
                            </tr>
                        )}
                        <tr>
                            <td colspan="1" rowspan="1">
                                Subtotal Scope 1 Emissions
                            </td>
                            <td colspan="1" rowspan="1"></td>
                            <td colSpan="1" className="text-center">
                                {getScopeDataByDCF([10, 11, 15])}
                            </td>
                        </tr>
                    </>
                )}
            </>
        );
    };
    const renderEmissionScope3 = (text) => {
        return (
            <>
                {checkScope([16, 36]) && (
                    <>
                        {checkScope([16]) && (
                            <tr>
                                <td colspan="1" rowspan="1">
                                    Category 1 - Purchased goods and services
                                </td>
                                <td colspan="1" rowspan="1">
                                    {getScopeDataByDCF([16])}
                                </td>
                                <td colSpan="1" className="text-center"></td>
                            </tr>
                        )}
                        {checkScope([36]) && (
                            <tr>
                                <td colspan="1" rowspan="1">
                                    Category 6 - Business travel
                                </td>
                                <td colspan="1" rowspan="1">
                                    {getScopeDataByDCF([36])}
                                </td>
                                <td colSpan="1" className="text-center"></td>
                            </tr>
                        )}
                        <tr>
                            <td colspan="1" rowspan="1">
                                Subtotal Scope 3 Emissions
                            </td>
                            <td colspan="1" rowspan="1"></td>
                            <td colSpan="1" className="text-center">
                                {getScopeDataByDCF([36, 16])}
                            </td>
                        </tr>
                    </>
                )}
            </>
        );
    };
    const renderEmissionScope1_ = (text) => {
        return (
            <>
                {checkScope([11, 10, 15]) && (
                    <>
                        {checkScope([11]) && (
                            <tr>
                                <td colspan="1" rowspan="1">
                                    Stationary Combustion Emission
                                </td>
                                <td colspan="1" rowspan="1">
                                    {getScopeDataByDCF([11])}
                                </td>
                            </tr>
                        )}
                        {checkScope([15]) && (
                            <tr>
                                <td colspan="1" rowspan="1">
                                    Mobile Combustion Emission
                                </td>
                                <td colspan="1" rowspan="1">
                                    {getScopeDataByDCF([15])}
                                </td>
                            </tr>
                        )}
                        {checkScope([10]) && (
                            <tr>
                                <td colspan="1" rowspan="1">
                                    Fugitive Emissions
                                </td>
                                <td colspan="1" rowspan="1">
                                    {getScopeDataByDCF([10])}
                                </td>
                            </tr>
                        )}
                    </>
                )}
            </>
        );
    };
    const renderEmissionScope3_ = (text) => {
        return (
            <>
                {checkScope([16, 36]) && (
                    <>
                        {checkScope([16]) && (
                            <tr>
                                <td colspan="1" rowspan="1">
                                    Category 1 - Purchased goods and services
                                </td>
                                <td colspan="1" rowspan="1">
                                    {getScopeDataByDCF([16])}
                                </td>
                            </tr>
                        )}
                        {checkScope([36]) && (
                            <tr>
                                <td colspan="1" rowspan="1">
                                    Category 6 - Business travel
                                </td>
                                <td colspan="1" rowspan="1">
                                    {getScopeDataByDCF([36])}
                                </td>
                            </tr>
                        )}
                    </>
                )}
            </>
        );
    };
    const renderEmissionScope1_1 = (text) => {
        return (
            <>
                {checkScope([11, 10, 15]) && (
                    <>
                        {checkScope([11]) && (
                            <tr>
                                <td colspan="1" rowspan="1">
                                    Stationary Combustion Emission
                                </td>
                                <td colspan="1" rowspan="1"></td>
                                <td colspan="1" rowspan="1"></td>
                                <td colspan="1" rowspan="1"></td>
                            </tr>
                        )}
                        {checkScope([15]) && (
                            <tr>
                                <td colspan="1" rowspan="1">
                                    Mobile Combustion Emission
                                </td>
                                <td colspan="1" rowspan="1"></td>
                                <td colspan="1" rowspan="1"></td>
                                <td colspan="1" rowspan="1"></td>
                            </tr>
                        )}
                        {checkScope([10]) && (
                            <tr>
                                <td colspan="1" rowspan="1">
                                    Fugitive Emissions
                                </td>
                                <td colspan="1" rowspan="1"></td>
                                <td colspan="1" rowspan="1"></td>
                                <td colspan="1" rowspan="1"></td>
                            </tr>
                        )}
                    </>
                )}
            </>
        );
    };
    const renderEmissionScope3_1 = (text) => {
        return (
            <>
                {checkScope([16, 36]) && (
                    <>
                        {checkScope([16]) && (
                            <tr>
                                <td colspan="1" rowspan="1">
                                    Category 1 - Purchased goods and services
                                </td>
                                <td colspan="1" rowspan="1"></td>
                                <td colspan="1" rowspan="1"></td>
                                <td colspan="1" rowspan="1"></td>
                            </tr>
                        )}
                        {checkScope([36]) && (
                            <tr>
                                <td colspan="1" rowspan="1">
                                    Category 6 - Business travel
                                </td>
                                <td colspan="1" rowspan="1"></td>
                                <td colspan="1" rowspan="1"></td>
                                <td colspan="1" rowspan="1"></td>
                            </tr>
                        )}
                    </>
                )}
            </>
        );
    };
    const renderEmissionByScopeTable = (text) => {
        return (
            <div class="gridlines-container">
                <table class="gridlines">
                    <tbody>
                        <tr>
                            <td colspan="1" rowspan="1" className="tdHead">
                                Scope of Emission
                            </td>
                            <td colspan="1" rowspan="1" className="tdHead">
                                tCO2e
                            </td>
                        </tr>
                        {checkScope([11, 10, 15]) && (
                            <>
                                <tr>
                                    <td colspan="1" rowspan="1">
                                        Scope 1- Direct Emissions
                                    </td>
                                    <td colspan="1" rowspan="1">
                                        {getScopeDataByDCF([11, 10, 15])}
                                    </td>
                                </tr>
                            </>
                        )}
                        {checkScope([72]) && (
                            <tr>
                                <td colspan="1" rowspan="1">
                                    Scope 2- Indirect Emission
                                </td>
                                <td colspan="1" rowspan="1">
                                    {getScopeDataByDCF([72])}
                                </td>
                            </tr>
                        )}
                        {checkScope([16, 36]) && (
                            <tr>
                                <td colspan="1" rowspan="1">
                                    Scope 3- Indirect emissions
                                </td>
                                <td colspan="1" rowspan="1">
                                    {getScopeDataByDCF([16, 36])}
                                </td>
                            </tr>
                        )}
                    </tbody>
                </table>
            </div>
        );
    };
    const renderEmissionByCategoryTable = () => {
        return (
            <div class="gridlines-container">
                <table class="gridlines">
                    <tbody>
                        <tr>
                            <td colspan="1" rowspan="1" className="tdHead">
                                Emission Profile
                            </td>
                            <td colspan="1" rowspan="1" className="tdHead">
                                tCO2e
                            </td>
                        </tr>
                        {checkScope([11, 10, 15]) && (
                            <>
                                {checkScope([11]) && (
                                    <tr>
                                        <td colspan="1" rowspan="1">
                                            Stationary Combustion (Fuel Used)
                                        </td>
                                        <td colspan="1" rowspan="1">
                                            {getScopeDataByDCF([11])}
                                        </td>
                                    </tr>
                                )}
                                {checkScope([15]) && (
                                    <tr>
                                        <td colspan="1" rowspan="1">
                                            Mobile Combustion (Owned Vehicles)
                                        </td>
                                        <td colspan="1" rowspan="1">
                                            {getScopeDataByDCF([15])}
                                        </td>
                                    </tr>
                                )}
                                {checkScope([10]) && (
                                    <tr>
                                        <td colspan="1" rowspan="1">
                                            Fugitive Emissions (Refrigerants)
                                        </td>
                                        <td colspan="1" rowspan="1">
                                            {getScopeDataByDCF([10])}
                                        </td>
                                    </tr>
                                )}
                            </>
                        )}
                        {checkScope([72]) && (
                            <>
                                <tr>
                                    <td colspan="1" rowspan="1">
                                        Emissions from purchased energy (Grid Electricity)
                                    </td>
                                    <td colspan="1" rowspan="1">
                                        {getScopeDataByDCF([72])}
                                    </td>
                                </tr>
                            </>
                        )}
                        {checkScope([16, 36]) && (
                            <>
                                {checkScope([16]) && (
                                    <tr>
                                        <td colspan="1" rowspan="1">
                                            Purchased Goods and Services
                                        </td>
                                        <td colspan="1" rowspan="1">
                                            {getScopeDataByDCF([16])}
                                        </td>
                                    </tr>
                                )}
                                {checkScope([36]) && (
                                    <tr>
                                        <td colspan="1" rowspan="1">
                                            Business Travel
                                        </td>
                                        <td colspan="1" rowspan="1">
                                            {getScopeDataByDCF([36])}
                                        </td>
                                    </tr>
                                )}
                                {/* <tr>
                            <td colspan="1" rowspan="1" >Employee Commute
                            </td>
                            <td colspan="1" rowspan="1" >
                            </td>
                        </tr> */}
                            </>
                        )}
                    </tbody>
                </table>
            </div>
        );
    };
    const getDataByDCFDPID = (dcfid, dpid, yr) => {
        let response_ = JSON.parse(JSON.stringify(response));
        let d = 0;

        response_.forEach((k) => {
            if (k.dcf === dcfid) {
                if (checkYear(k.rp, yr) !== 0) {
                    let result = k.response.filter((k) => {
                        return k.name === dpid;
                    });
                    if (result.length !== 0) {
                        d = d + parseInt(result[0].value.match(/\d+/)[0]);
                    }
                }
            }
        });

        return d;
    };

    function getDateObjectByMonth_Year(month, year) {
        if (isNaN(month) || isNaN(year)) {
            throw new Error("Invalid month or year");
        }

        const normalizedMonth = Math.max(1, Math.min(12, month));

        const date = DateTime.fromObject({ year, month: normalizedMonth, day: 1 });

        return date.toJSDate();
    }
    // const getRP_ = (rp) => {
    //     if (rp.length === 1) {
    //         return months_[parseInt(rp[0].split("-")[0]) - 1] + "-" + rp[0].split("-")[1].slice(-2);
    //     } else {
    //         return months_[parseInt(rp[0].split("-")[0]) - 1] + "-" + rp[0].split("-")[1].slice(-2) + "to" + months_[parseInt(rp[rp.length - 1].split("-")[0]) - 1] + "-" + rp[rp.length - 1].split("-")[1].slice(-2);
    //     }
    // };
    const getClassName = (level) => {
        switch (level) {
            case 1:
                return "head1";
            case 2:
                return "head2";
            case 3:
                return "head3";
            default:
                return null;
        }
    };

    const checkSite = (id, filter) => {
        let idlist = [];

        siteList.forEach((country) => {
            if (filter.a.id === 0 || filter.a.id === country.id) {
                country.locationTwos.forEach((city) => {
                    if (filter.b.id === 0 || filter.b.id === city.id) {
                        city.locationThrees.forEach((loc) => {
                            if (filter.c.id == 0 || filter.c.id === loc.id) {
                                idlist.push(loc.id);
                            }
                        });
                    }
                });
            }
        });

        return idlist.includes(id);
    };
    const groupArrayObject_3_Keys = (array, obj1, obj2, obj3) => {
        return array.reduce((result, arr) => {
            let key1 = arr[obj1];
            let key2 = arr[obj2];
            let key3 = arr[obj3];

            const key = `${key1}-${key2}-${key3}`;

            if (!result[key]) {
                result[key] = [];
            }

            result[key].push(arr);

            return result;
        }, {});
    };
    const renderData = (search) => {
        let betweenMonths = [],
            betweenYears = [];
        let monthly_fg = [];

        let endDate = moment.utc(search.to).local();
        let startDate = moment.utc(search.from).local();
        let year = moment(moment.utc()).format("YYYY");

        if (search.to !== null && search.from !== null) {
            while (startDate.startOf("month") <= endDate.startOf("month")) {
                betweenMonths.push(startDate.format("MM-YYYY"));
                !betweenYears.includes(startDate.format("YYYY")) && betweenYears.push(startDate.format("YYYY"));

                startDate.add(1, "month");
            }

            let res = JSON.parse(JSON.stringify(response));

            res.forEach((report, rind) => {
                let sc_total = 0,
                    fg_total = 0;

                if (checkSite(report.site, search.location)) {
                    report.sitename = locationList.filter((loc) => {
                        return loc.id === report.site;
                    })[0].name;
                    if (report.dcf === 16 && (search.indicator.id === 0 || search.indicator.id === 122)) {
                        report.response.forEach((fg, ind) => {
                            let date = fg.filter((i) => {
                                return i.dp === "DPA0285";
                            })[0].value;
                            let yearind = monthly_fg.findIndex((i) => {
                                return i.year === moment(date).format("YYYY");
                            });
                            let monthind = monthly_fg.findIndex((i) => {
                                return i.year === moment(date).format("YYYY") && i.month === moment(date).format("MMM-YYYY");
                            });
                            let dcfind = monthly_fg.findIndex((i) => {
                                return i.year === moment(date).format("YYYY") && i.month === moment(date).format("MMM-YYYY") && i.dcf === report.dcf;
                            });
                            let siteind = monthly_fg.findIndex((i) => {
                                return i.year === moment(date).format("YYYY") && i.month === moment(date).format("MMM-YYYY") && i.dcf === report.dcf && i.site === report.site;
                            });

                            if (checkReportingPeriod([moment(date).format("MM-YYYY")], betweenMonths).result) {
                                let item_class = fg.filter((i) => {
                                    return i.dp === "DPA0287";
                                })[0].value.name;
                                let item_qty = fg.filter((i) => {
                                    return i.dp === "DPA0288";
                                })[0].value;
                                let price_per_item = fg.filter((i) => {
                                    return i.dp === "DPA0289";
                                })[0].value;

                                let total =
                                    (emissionFactor[0].stdYears[0].stdNames[0].stdScopes[2].stdTopics[0].data1[0].importedData.filter((k) => {
                                        return k.item_classification === item_class;
                                    })[0]["co2e_in_kg"] /
                                        1000) *
                                    item_qty *
                                    price_per_item;

                                fg_total = total + fg_total;

                                let updateind = monthly_fg.findIndex((i) => {
                                    return i.year === moment(date).format("YYYY") && i.month === moment(date).format("MMM-YYYY") && i.dcf === report.dcf && i.site === report.site && i.item_class === item_class;
                                });

                                if (updateind === -1) {
                                    monthly_fg.push({ scope: 3, year: moment(date).format("YYYY"), month: moment(date).format("MMM-YYYY"), ghg: total, dcf: report.dcf, site: report.site, item_class: item_class });
                                } else {
                                    monthly_fg[updateind].ghg = monthly_fg[updateind].ghg + total;
                                }
                            }
                        });
                    }
                    if (report.dcf === 36 && (search.indicator.id === 0 || search.indicator.id === 123)) {
                        report.response.forEach((fg, ind) => {
                            let date = fg.filter((i) => {
                                return i.dp === "DPA0290";
                            })[0].value;
                            let yearind = monthly_fg.findIndex((i) => {
                                return i.year === moment(date).format("YYYY");
                            });
                            let monthind = monthly_fg.findIndex((i) => {
                                return i.year === moment(date).format("YYYY") && i.month === moment(date).format("MMM-YYYY");
                            });
                            let dcfind = monthly_fg.findIndex((i) => {
                                return i.year === moment(date).format("YYYY") && i.month === moment(date).format("MMM-YYYY") && i.dcf === report.dcf;
                            });
                            let siteind = monthly_fg.findIndex((i) => {
                                return i.year === moment(date).format("YYYY") && i.month === moment(date).format("MMM-YYYY") && i.dcf === report.dcf && i.site === report.site;
                            });

                            if (checkReportingPeriod([moment(date).format("MM-YYYY")], betweenMonths).result) {
                                let total = 0;

                                let travel_mode = fg.filter((i) => {
                                    return i.dp === "DPA0291";
                                })[0].value.name;
                                let passenger = fg.filter((i) => {
                                    return i.dp === "DPA0292";
                                })[0].value;

                                if (travel_mode.toLowerCase() === "air") {
                                    total =
                                        fg.filter((i) => {
                                            return i.dp === "DP_co2e_mt";
                                        })[0].value * passenger;

                                    let updateind = monthly_fg.findIndex((i) => {
                                        return i.year === moment(date).format("YYYY") && i.month === moment(date).format("MMM-YYYY") && i.dcf === report.dcf && i.site === report.site && i.travel_mode === travel_mode;
                                    });

                                    if (updateind === -1) {
                                        monthly_fg.push({ scope: 3, year: moment(date).format("YYYY"), month: moment(date).format("MMM-YYYY"), ghg: total, dcf: report.dcf, site: report.site, travel_mode: travel_mode });
                                    } else {
                                        monthly_fg[updateind].ghg = monthly_fg[updateind].ghg + total;
                                    }

                                    fg_total = total + fg_total;
                                } else if (travel_mode.toLowerCase() === "road") {
                                    let veh_cat = fg.filter((i) => {
                                        return i.dp === "DPA0295";
                                    })[0].value.name;
                                    let veh_type = fg.filter((i) => {
                                        return i.dp === "DPA0337";
                                    })[0].value;
                                    let fuel = fg.filter((i) => {
                                        return i.dp === "DPA0338";
                                    })[0].value;
                                    let km = fg.filter((i) => {
                                        return i.dp === "DP_KM";
                                    })[0].value;
                                    if (veh_cat.includes("Cars") || veh_cat.includes("Motor")) {
                                        if (veh_cat.includes("Cars")) {
                                            total =
                                                (emissionFactor[0].stdYears[0].stdNames[0].stdScopes[2].stdTopics[1].data1[0].importedData.filter((k) => {
                                                    return k.vehicle_category === veh_cat && k.vehicle_type === veh_type.name && k.fuel_type === fuel.name && k.unit === "km";
                                                })[0]["co2e_in_kg"] /
                                                    1000) *
                                                km *
                                                passenger;
                                        } else {
                                            total =
                                                (emissionFactor[0].stdYears[0].stdNames[0].stdScopes[2].stdTopics[1].data1[0].importedData.filter((k) => {
                                                    return k.vehicle_category === veh_cat && k.vehicle_type === veh_type.name && k.unit === "km";
                                                })[0]["co2e_in_kg"] /
                                                    1000) *
                                                km *
                                                passenger;
                                        }
                                    } else {
                                        total =
                                            (emissionFactor[0].stdYears[0].stdNames[0].stdScopes[2].stdTopics[1].data1[0].importedData.filter((k) => {
                                                return k.vehicle_category === veh_cat && k.unit === "passenger.km";
                                            })[0]["co2e_in_kg"] /
                                                1000) *
                                            km *
                                            passenger;
                                    }

                                    let updateind = monthly_fg.findIndex((i) => {
                                        return i.year === moment(date).format("YYYY") && i.month === moment(date).format("MMM-YYYY") && i.dcf === report.dcf && i.site === report.site && i.travel_mode === travel_mode;
                                    });

                                    if (updateind === -1) {
                                        monthly_fg.push({ scope: 3, year: moment(date).format("YYYY"), month: moment(date).format("MMM-YYYY"), ghg: total, dcf: report.dcf, site: report.site, travel_mode: travel_mode });
                                    } else {
                                        monthly_fg[updateind].ghg = monthly_fg[updateind].ghg + total;
                                    }

                                    fg_total = total + fg_total;
                                } else if (travel_mode.toLowerCase() === "rail") {
                                    total =
                                        fg.filter((i) => {
                                            return i.dp === "DP_KM";
                                        })[0].value *
                                        passenger *
                                        0.00116;

                                    let updateind = monthly_fg.findIndex((i) => {
                                        return i.year === moment(date).format("YYYY") && i.month === moment(date).format("MMM-YYYY") && i.dcf === report.dcf && i.site === report.site && i.travel_mode === travel_mode;
                                    });

                                    if (updateind === -1) {
                                        monthly_fg.push({ scope: 3, year: moment(date).format("YYYY"), month: moment(date).format("MMM-YYYY"), ghg: total, dcf: report.dcf, site: report.site, travel_mode: travel_mode });
                                    } else {
                                        monthly_fg[updateind].ghg = monthly_fg[updateind].ghg + total;
                                    }

                                    fg_total = total + fg_total;
                                }
                            }
                        });
                    }

                    if (report.dcf === 11 && (search.indicator.id === 0 || search.indicator.id === 93)) {
                        let addedMonth = checkReportingPeriod(report.rp, betweenMonths);

                        let total = 0,
                            co2 = 0,
                            ch4 = 0,
                            n2o = 0,
                            sc_data = [];
                        report.response.forEach((fg, ind) => {
                            let fuel_cat = fg.filter((i) => {
                                return i.dp === "DPA0130";
                            })[0].value.name;
                            let fuel_type = fg.filter((i) => {
                                return i.dp === "DPA0131";
                            })[0].value.name;
                            let unit = fg.filter((i) => {
                                return i.dp === "DPA0132";
                            })[0].value.name;
                            let consumed = fg.filter((i) => {
                                return i.dp === "DPA0336";
                            })[0].value;
                            let fuel_cat_ind = sc_data.findIndex((k) => {
                                return k.fuel_type === fuel_type;
                            });

                            if (fuel_cat === "Solid Fuels" || fuel_cat === "Biomass") {
                                let carbon = fg.filter((i) => {
                                    return i.dp === "DPA0134";
                                })[0].value;
                                let cv = fg.filter((i) => {
                                    return i.dp === "DPA0133";
                                })[0].value;
                                if (carbon > 0 && cv > 0) {
                                    let gj = (carbon * 3.664 * 1000) / cv;
                                    total = (gj * 0.000004184 * consumed) / 1000;
                                    fg["value"] = ["Emission Factor-", 3.664, " ,EnergyProduced-", 0.000004184];
                                    co2 = 0;
                                    ch4 = 0;
                                    n2o = 0;
                                    if (fuel_cat_ind === -1) {
                                        sc_data.push({ fuel_type: fuel_type, ghg: total, co2, n2o, ch4 });
                                    } else {
                                        sc_data[fuel_cat_ind].ghg = sc_data[fuel_cat_ind].ghg + total;
                                        sc_data[fuel_cat_ind].co2 = sc_data[fuel_cat_ind].co2 + co2;
                                        sc_data[fuel_cat_ind].ch4 = sc_data[fuel_cat_ind].ch4 + ch4;
                                        sc_data[fuel_cat_ind].n2o = sc_data[fuel_cat_ind].n2o + n2o;
                                    }
                                } else {
                                    total =
                                        (emissionFactor[0].stdYears[0].stdNames[0].stdScopes[0].stdTopics[0].data1[0].importedData.filter((k) => {
                                            return k.fuel_type === fuel_cat && k.fuel === fuel_type && k.unit === unit;
                                        })[0]["co2e_in_kg"] /
                                            1000) *
                                        consumed;
                                    co2 =
                                        (emissionFactor[0].stdYears[0].stdNames[0].stdScopes[0].stdTopics[0].data1[0].importedData.filter((k) => {
                                            return k.fuel_type === fuel_cat && k.fuel === fuel_type && k.unit === unit;
                                        })[0]["co2_in_kg"] /
                                            1000) *
                                        consumed;
                                    ch4 =
                                        (emissionFactor[0].stdYears[0].stdNames[0].stdScopes[0].stdTopics[0].data1[0].importedData.filter((k) => {
                                            return k.fuel_type === fuel_cat && k.fuel === fuel_type && k.unit === unit;
                                        })[0]["ch4_in_kg"] /
                                            1000) *
                                        consumed;
                                    n2o =
                                        (emissionFactor[0].stdYears[0].stdNames[0].stdScopes[0].stdTopics[0].data1[0].importedData.filter((k) => {
                                            return k.fuel_type === fuel_cat && k.fuel === fuel_type && k.unit === unit;
                                        })[0]["n2o_in_kg"] /
                                            1000) *
                                        consumed;
                                    fg["value"] = ["co2-", co2 / consumed, " ,ch4-", ch4 / consumed, " ,n2o-", n2o / consumed];
                                    if (fuel_cat_ind === -1) {
                                        sc_data.push({ fuel_type: fuel_type, ghg: total, co2, n2o, ch4 });
                                    } else {
                                        sc_data[fuel_cat_ind].ghg = sc_data[fuel_cat_ind].ghg + total;
                                        sc_data[fuel_cat_ind].co2 = sc_data[fuel_cat_ind].co2 + co2;
                                        sc_data[fuel_cat_ind].ch4 = sc_data[fuel_cat_ind].ch4 + ch4;
                                        sc_data[fuel_cat_ind].n2o = sc_data[fuel_cat_ind].n2o + n2o;
                                    }
                                }
                            } else {
                                total =
                                    (emissionFactor[0].stdYears[0].stdNames[0].stdScopes[0].stdTopics[0].data1[0].importedData.filter((k) => {
                                        return k.fuel_type === fuel_cat && k.fuel === fuel_type && k.unit === unit;
                                    })[0]["co2e_in_kg"] /
                                        1000) *
                                    consumed;
                                co2 =
                                    (emissionFactor[0].stdYears[0].stdNames[0].stdScopes[0].stdTopics[0].data1[0].importedData.filter((k) => {
                                        return k.fuel_type === fuel_cat && k.fuel === fuel_type && k.unit === unit;
                                    })[0]["co2_in_kg"] /
                                        1000) *
                                    consumed;
                                ch4 =
                                    (emissionFactor[0].stdYears[0].stdNames[0].stdScopes[0].stdTopics[0].data1[0].importedData.filter((k) => {
                                        return k.fuel_type === fuel_cat && k.fuel === fuel_type && k.unit === unit;
                                    })[0]["ch4_in_kg"] /
                                        1000) *
                                    consumed;
                                n2o =
                                    (emissionFactor[0].stdYears[0].stdNames[0].stdScopes[0].stdTopics[0].data1[0].importedData.filter((k) => {
                                        return k.fuel_type === fuel_cat && k.fuel === fuel_type && k.unit === unit;
                                    })[0]["n2o_in_kg"] /
                                        1000) *
                                    consumed;
                                fg["value"] = ["co2-", co2 / consumed, " ,ch4-", ch4 / consumed, " ,n2o-", n2o / consumed];
                                if (fuel_cat_ind === -1) {
                                    sc_data.push({ fuel_type: fuel_type, ghg: total, co2, n2o, ch4 });
                                } else {
                                    sc_data[fuel_cat_ind].ghg = sc_data[fuel_cat_ind].ghg + total;
                                    sc_data[fuel_cat_ind].co2 = sc_data[fuel_cat_ind].co2 + co2;
                                    sc_data[fuel_cat_ind].ch4 = sc_data[fuel_cat_ind].ch4 + ch4;
                                    sc_data[fuel_cat_ind].n2o = sc_data[fuel_cat_ind].n2o + n2o;
                                }
                            }
                        });
                        addedMonth.rps.forEach((fm) => {
                            let date = moment(fm, "MM-YYYY").toDate();
                            let yearind = monthly_fg.findIndex((i) => {
                                return i.year === moment(date).format("YYYY");
                            });
                            let monthind = monthly_fg.findIndex((i) => {
                                return i.year === moment(date).format("YYYY") && i.month === moment(date).format("MMM-YYYY");
                            });
                            let dcfind = monthly_fg.findIndex((i) => {
                                return i.year === moment(date).format("YYYY") && i.month === moment(date).format("MMM-YYYY") && i.dcf === report.dcf;
                            });
                            let siteind = monthly_fg.findIndex((i) => {
                                return i.year === moment(date).format("YYYY") && i.month === moment(date).format("MMM-YYYY") && i.dcf === report.dcf && i.site === report.site;
                            });

                            sc_data.forEach((item) => {
                                let updateind = monthly_fg.findIndex((i) => {
                                    return i.year === moment(date).format("YYYY") && i.month === moment(date).format("MMM-YYYY") && i.dcf === report.dcf && i.site === report.site && i.fuel_type === item.fuel_type;
                                });

                                if (updateind === -1) {
                                    monthly_fg.push({
                                        scope: 1,
                                        year: moment(date).format("YYYY"),
                                        month: moment(date).format("MMM-YYYY"),
                                        ghg: item.ghg / addedMonth.count,
                                        dcf: report.dcf,
                                        site: report.site,
                                        fuel_type: item.fuel_type,
                                        co2: (item.co2 * 1000) / addedMonth.count,
                                        n2o: (item.n20 * 1000) / addedMonth.count,
                                        ch4: (item.ch4 * 1000) / addedMonth.count,
                                    });
                                } else {
                                    monthly_fg[updateind].co2 = ((item.co2 * 1000) / addedMonth.count) * 1000 + monthly_fg[updateind].co2;
                                    monthly_fg[updateind].n2o = ((item.n2o * 1000) / addedMonth.count) * 1000 + monthly_fg[updateind].n2o;
                                    monthly_fg[updateind].ch4 = ((item.ch4 * 1000) / addedMonth.count) * 1000 + monthly_fg[updateind].ch4;
                                    monthly_fg[updateind].ghg = monthly_fg[updateind].ghg + item.ghg / addedMonth.count;
                                }
                            });
                        });
                    }
                    if (report.dcf === 10 && (search.indicator.id === 0 || search.indicator.id === 116)) {
                        let add = [];

                        report.response.forEach((fg, ind) => {
                            let date = fg.filter((i) => {
                                return i.dp === "DPA0137";
                            })[0].value;
                            let yearind = monthly_fg.findIndex((i) => {
                                return i.year === moment(date).format("YYYY");
                            });
                            let monthind = monthly_fg.findIndex((i) => {
                                return i.year === moment(date).format("YYYY") && i.month === moment(date).format("MMM-YYYY");
                            });
                            let dcfind = monthly_fg.findIndex((i) => {
                                return i.year === moment(date).format("YYYY") && i.month === moment(date).format("MMM-YYYY") && i.dcf === report.dcf;
                            });
                            let siteind = monthly_fg.findIndex((i) => {
                                return i.year === moment(date).format("YYYY") && i.month === moment(date).format("MMM-YYYY") && i.dcf === report.dcf && i.site === report.site;
                            });

                            if (checkReportingPeriod([moment(date).format("MM-YYYY")], betweenMonths).result) {
                                let gastype = fg.filter((i) => {
                                    return i.dp === "DPA0136";
                                })[0].value.name;
                                let gasrefilled = fg.filter((i) => {
                                    return i.dp === "DPA0138";
                                })[0].value;
                                let total =
                                    (emissionFactor[0].stdYears[0].stdNames[0].stdScopes[0].stdTopics[2].data1[0].importedData.filter((k) => {
                                        return k.gas_type === gastype;
                                    })[0]["co2e_in_kg"] /
                                        1000) *
                                    gasrefilled;

                                fg_total = total + fg_total;

                                fg["gas"] = gastype;
                                fg["gasfilled"] = gasrefilled;
                                fg["value"] = ["EmissionFactor-", total / gasrefilled];
                                let updateind = monthly_fg.findIndex((i) => {
                                    return i.year === moment(date).format("YYYY") && i.month === moment(date).format("MMM-YYYY") && i.dcf === report.dcf && i.site === report.site && i.gastype === gastype;
                                });

                                if (updateind === -1) {
                                    monthly_fg.push({ scope: 1, year: moment(date).format("YYYY"), month: moment(date).format("MMM-YYYY"), ghg: 0, dcf: report.dcf, site: report.site, gastype: gastype, ghg: total });
                                } else {
                                    monthly_fg[updateind].ghg = monthly_fg[updateind].ghg + total;
                                }
                            }
                        });
                    }
                    if (report.dcf === 72 && (search.indicator.id === 0 || search.indicator.id === 121)) {
                        report.rp.forEach((i) => {
                            if (checkReportingPeriod([i], betweenMonths).result) {
                                let yearind = monthly_fg.findIndex((i) => {
                                    return i.year === moment(i, "MM-YYYY").format("YYYY");
                                });
                                let monthind = monthly_fg.findIndex((i) => {
                                    return i.year === moment(i, "MM-YYYY").format("YYYY") && i.month === moment(i, "MM-YYYY").format("MMM-YYYY");
                                });
                                let dcfind = monthly_fg.findIndex((i) => {
                                    return i.year === moment(i, "MM-YYYY").format("YYYY") && i.month === moment(i, "MM-YYYY").format("MMM-YYYY") && i.dcf === report.dcf;
                                });
                                let siteind = monthly_fg.findIndex((i) => {
                                    return i.year === moment(i, "MM-YYYY").format("YYYY") && i.month === moment(i, "MM-YYYY").format("MMM-YYYY") && i.dcf === report.dcf && i.site === report.site;
                                });

                                let ef = emissionFactor[0].stdYears[0].stdNames[0].stdScopes[1].stdTopics[0].data1[0].importedData[0]["kwh_in_tco2e"];

                                let renewable =
                                    report.response[0].filter((i) => {
                                        return i.dp === "DPA0156";
                                    })[0].value / report.rp.length;
                                let nonrenewable =
                                    report.response[0].filter((i) => {
                                        return i.dp === "DPA0157";
                                    })[0].value / report.rp.length;

                                if (yearind === -1) {
                                    monthly_fg.push({ scope: 2, year: moment(i, "MM-YYYY").format("YYYY"), month: moment(i, "MM-YYYY").format("MMM-YYYY"), ghg: nonrenewable * ef, dcf: report.dcf, site: report.site, renewable, nonrenewable });
                                } else if (monthind === -1) {
                                    monthly_fg.push({ scope: 2, year: moment(i, "MM-YYYY").format("YYYY"), month: moment(i, "MM-YYYY").format("MMM-YYYY"), ghg: nonrenewable * ef, dcf: report.dcf, site: report.site, renewable, nonrenewable });
                                } else if (dcfind === -1) {
                                    monthly_fg.push({ scope: 2, year: moment(i, "MM-YYYY").format("YYYY"), month: moment(i, "MM-YYYY").format("MMM-YYYY"), ghg: nonrenewable * ef, dcf: report.dcf, site: report.site, renewable, nonrenewable });
                                } else if (siteind === -1) {
                                    monthly_fg.push({ scope: 2, year: moment(i, "MM-YYYY").format("YYYY"), month: moment(i, "MM-YYYY").format("MMM-YYYY"), ghg: nonrenewable * ef, dcf: report.dcf, site: report.site, renewable, nonrenewable });
                                }
                            }
                        });
                    }
                    if (report.dcf === 15 && (search.indicator.id === 0 || search.indicator.id === 118)) {
                        let addedMonth = checkReportingPeriod(report.rp, betweenMonths);

                        let ef_by_fuel = emissionFactor[0].stdYears[0].stdNames[0].stdScopes[0].stdTopics[4].data1[0].importedData;
                        let ef_by_distance = emissionFactor[0].stdYears[0].stdNames[0].stdScopes[0].stdTopics[1].data1[0].importedData;

                        let total = 0,
                            co2 = 0,
                            ch4 = 0,
                            n2o = 0,
                            mc_data = [];

                        report.response.forEach((fg, ind) => {
                            let mode = fg.filter((i) => {
                                return i.dp === "DP_MODE";
                            })[0].value
                                ? "by distance"
                                : "by fuel";
                            let fuel_cat = fg.filter((i) => {
                                return i.dp === "DPA0140";
                            })[0].value;
                            let fuel_type = fg.filter((i) => {
                                return i.dp === "DPA0139";
                            })[0].value;
                            let fuel = fg.filter((i) => {
                                return i.dp === "DPA0141";
                            })[0].value.name;
                            let unit = fg.filter((i) => {
                                return i.dp === "DPA0339";
                            })[0].value;
                            let km = fg.filter((i) => {
                                return i.dp === "DPA0144";
                            })[0].value;
                            let fuel_filled = fg.filter((i) => {
                                return i.dp === "DPA0143";
                            })[0].value;
                            let fuel_cat_ind = mc_data.findIndex((k) => {
                                return k.fuel === fuel;
                            });

                            if (mode === "by distance") {
                                total = ef_by_distance.filter((k) => {
                                    return k.vehicle_category === fuel_cat.name && k.vehicle_type === fuel_type.name && k.fuel_type === fuel && k.unit === unit.name;
                                })[0]["co2e_in_kg"];

                                if (fuel_cat_ind === -1) {
                                    mc_data.push({ mode: fuel, ghg: total * km, fuel_cat: fuel_cat.name });
                                } else {
                                    mc_data[fuel_cat_ind].ghg = mc_data[fuel_cat_ind].ghg + total;
                                }
                            } else {
                                total = ef_by_fuel.filter((k) => {
                                    return k.fuel.trim() === fuel.trim();
                                })[0]["co2e_in_kg"];

                                if (fuel_cat_ind === -1) {
                                    mc_data.push({ mode: fuel, ghg: total * fuel_filled });
                                } else {
                                    mc_data[fuel_cat_ind].ghg = mc_data[fuel_cat_ind].ghg + total;
                                }
                            }
                        });
                        addedMonth.rps.forEach((fm) => {
                            let date = moment(fm, "MM-YYYY").toDate();

                            mc_data.forEach((item) => {
                                let updateind = monthly_fg.findIndex((i) => {
                                    return i.year === moment(date).format("YYYY") && i.month === moment(date).format("MMM-YYYY") && i.dcf === report.dcf && i.site === report.site && i.mode === item.mode;
                                });

                                if (updateind === -1) {
                                    let updateind2 = monthly_fg.findIndex((i) => {
                                        return i.year === moment(date).format("YYYY") && i.month === moment(date).format("MMM-YYYY") && i.dcf === report.dcf && i.site === report.site && i.mode === item.mode;
                                    });
                                    if (updateind2 !== -1) {
                                        monthly_fg[updateind2].ghg = item.ghg / addedMonth.count + monthly_fg[updateind2].ghg;
                                    } else {
                                        monthly_fg.push({ scope: 1, year: moment(date).format("YYYY"), month: moment(date).format("MMM-YYYY"), ghg: 0, dcf: report.dcf, site: report.site, ghg: item.ghg / addedMonth.count, mode: item.mode, fuel_cat: item.fuel_cat });
                                    }
                                } else {
                                    monthly_fg[updateind].ghg = monthly_fg[updateind].ghg + item.ghg / addedMonth.count;
                                }
                            });
                        });
                    }
                }
            });

            let scope12_array = groupArrayObject(
                monthly_fg.filter((i) => {
                    return i.scope === 1 || i.scope === 2;
                }),
                "month"
            );
            let scope3_array = groupArrayObject(
                monthly_fg.filter((i) => {
                    return i.scope === 3;
                }),
                "month"
            );
            let scope1_array = groupArrayObject(
                monthly_fg.filter((i) => {
                    return i.scope === 1;
                }),
                "month"
            );
            let scope2_array = groupArrayObject(
                monthly_fg.filter((i) => {
                    return i.scope === 2;
                }),
                "month"
            );
            let pie = [];
            monthly_fg
                .filter((i) => {
                    return i.scope === 1;
                })
                .forEach((item) => {
                    let index = pie.findIndex((j) => {
                        return j.name === "Scope " + item.scope;
                    });
                    if (index === -1) {
                        pie.push({ name: "Scope " + item.scope, y: item.ghg });
                    } else {
                        pie[index].y = pie[index].y + item.ghg;
                    }
                });
            monthly_fg
                .filter((i) => {
                    return i.scope === 2;
                })
                .forEach((item) => {
                    let index = pie.findIndex((j) => {
                        return j.name === "Scope " + item.scope;
                    });
                    if (index === -1) {
                        pie.push({ name: "Scope " + item.scope, y: item.ghg });
                    } else {
                        pie[index].y = pie[index].y + item.ghg;
                    }
                });
            monthly_fg
                .filter((i) => {
                    return i.scope === 3;
                })
                .forEach((item) => {
                    let index = pie.findIndex((j) => {
                        return j.name === "Scope " + item.scope;
                    });
                    if (index === -1) {
                        pie.push({ name: "Scope " + item.scope, y: item.ghg });
                    } else {
                        pie[index].y = pie[index].y + item.ghg;
                    }
                });

            let index1 = pie.findIndex((j) => {
                return j.name === "Scope 1";
            });
            let index2 = pie.findIndex((j) => {
                return j.name === "Scope 2";
            });
            let index3 = pie.findIndex((j) => {
                return j.name === "Scope 3";
            });

            if (index1 === -1) {
                pie.push({ name: "Scope 1", y: 0 });
            }
            if (index2 === -1) {
                pie.push({ name: "Scope 2", y: 0 });
            }
            if (index3 === -1) {
                pie.push({ name: "Scope 3", y: 0 });
            }

            if (scope1_array.length !== 0) {
                let scope1_tier2 = [],
                    final = [];
                Object.keys(scope1_array).forEach((key) => {
                    scope1_array[key].forEach((item) => {
                        item.dcfId = item.dcf;
                        item.dcf =
                            dcflist.filter((l) => {
                                return l.id === item.dcf;
                            }).length === 0
                                ? item.dcf
                                : dcflist.filter((l) => {
                                    return l.id === item.dcf;
                                })[0].title;

                        if (
                            scope1_tier2.findIndex((i) => {
                                return i.name === item.dcf;
                            }) === -1
                        ) {
                            scope1_tier2.push({
                                name: item.dcf,
                                drilldown: true,
                                color: "red",
                                y: item.ghg,

                                subset: [[key, item.ghg]],
                            });
                        } else {
                            let ind = scope1_tier2.findIndex((i) => {
                                return i.name === item.dcf;
                            });
                            let subind = scope1_tier2[ind].subset.findIndex((j) => {
                                return j[0] === key;
                            });
                            scope1_tier2[ind].y = scope1_tier2[ind].y + item.ghg;
                            if (subind === -1) {
                                scope1_tier2[ind].subset.push([key, item.ghg]);
                            } else {
                                scope1_tier2[ind].subset[subind][1] = scope1_tier2[ind].subset[subind][1] + item.ghg;
                            }
                        }
                    });
                });

                betweenYears.forEach((j) => {
                    let dataset = JSON.parse(JSON.stringify(scope1_tier2)).map((i) => {
                        i.subset.sort((a, b) => {
                            return moment(a[0], "MMM-YYYY").toDate() - moment(b[0], "MMM-YYYY").toDate();
                        });
                        i.subset = i.subset.filter((k) => {
                            return k[0].split("-")[1] === j;
                        });
                        i.y = i.subset.length === 0 ? 0 : getSum(i.subset);
                        i.incomplete = false;
                        return i;
                    });

                    final.push(dataset);
                });
            }
            if (scope2_array.length !== 0) {
                let scope2_tier2 = [],
                    final = [];
                Object.keys(scope2_array).forEach((key) => {
                    scope2_array[key].forEach((item) => {
                        item.dcfId = item.dcf;
                        item.dcf =
                            dcflist.filter((l) => {
                                return l.id === item.dcf;
                            }).length === 0
                                ? item.dcf
                                : dcflist.filter((l) => {
                                    return l.id === item.dcf;
                                })[0].title;
                        if (
                            scope2_tier2.findIndex((i) => {
                                return i.name === item.dcf;
                            }) === -1
                        ) {
                            scope2_tier2.push({
                                name: item.dcf,
                                drilldown: true,
                                color: "red",
                                y: item.ghg,
                                subset: [[key, item.ghg]],
                            });
                        } else {
                            let ind = scope2_tier2.findIndex((i) => {
                                return i.name === item.dcf;
                            });
                            let subind = scope2_tier2[ind].subset.findIndex((j) => {
                                return j[0] === key;
                            });
                            scope2_tier2[ind].y = scope2_tier2[ind].y + item.ghg;
                            if (subind === -1) {
                                scope2_tier2[ind].subset.push([key, item.ghg]);
                            } else {
                                scope2_tier2[ind].subset[subind][1] = scope2_tier2[ind].subset[subind][1] + item.ghg;
                            }
                        }
                    });
                });
                betweenYears.forEach((j) => {
                    let dataset = JSON.parse(JSON.stringify(scope2_tier2)).map((i) => {
                        i.subset.sort((a, b) => {
                            return moment(a[0], "MMM-YYYY").toDate() - moment(b[0], "MMM-YYYY").toDate();
                        });
                        i.subset = i.subset.filter((k) => {
                            return k[0].split("-")[1] === j;
                        });
                        i.y = i.subset.length === 0 ? 0 : getSum(i.subset);
                        i.incomplete = false;
                        return i;
                    });

                    final.push(dataset);
                });
            }
            if (scope3_array.length !== 0) {
                let scope3_tier2 = [],
                    final = [];
                Object.keys(scope3_array).forEach((key) => {
                    scope3_array[key].forEach((item) => {
                        item.dcfId = item.dcf;
                        item.dcf =
                            dcflist.filter((l) => {
                                return l.id === item.dcf;
                            }).length === 0
                                ? item.dcf
                                : dcflist.filter((l) => {
                                    return l.id === item.dcf;
                                })[0].title;
                        if (
                            scope3_tier2.findIndex((i) => {
                                return i.name === item.dcf;
                            }) === -1
                        ) {
                            scope3_tier2.push({
                                name: item.dcf,
                                drilldown: true,
                                color: "red",
                                y: item.ghg,
                                subset: [[key, item.ghg]],
                            });
                        } else {
                            let ind = scope3_tier2.findIndex((i) => {
                                return i.name === item.dcf;
                            });
                            let subind = scope3_tier2[ind].subset.findIndex((j) => {
                                return j[0] === key;
                            });
                            scope3_tier2[ind].y = scope3_tier2[ind].y + item.ghg;
                            if (subind === -1) {
                                scope3_tier2[ind].subset.push([key, item.ghg]);
                            } else {
                                scope3_tier2[ind].subset[subind][1] = scope3_tier2[ind].subset[subind][1] + item.ghg;
                            }
                        }
                    });
                });
                betweenYears.forEach((j) => {
                    let dataset = JSON.parse(JSON.stringify(scope3_tier2)).map((i) => {
                        i.subset.sort((a, b) => {
                            return moment(a[0], "MMM-YYYY").toDate() - moment(b[0], "MMM-YYYY").toDate();
                        });
                        i.subset = i.subset.filter((k) => {
                            return k[0].split("-")[1] === j;
                        });
                        i.y = i.subset.length === 0 ? 0 : getSum(i.subset);
                        i.incomplete = false;
                        return i;
                    });

                    final.push(dataset);
                });
            }
        }

        return monthly_fg;
    };
    const renderDataEF = (search) => {
        let betweenMonths = [],
            betweenYears = [];
        let monthly_fg = [];

        let endDate = moment.utc(search.to).local();
        let startDate = moment.utc(search.from).local();
        let year = moment(moment.utc()).format("YYYY");

        if (search.to !== null && search.from !== null) {
            while (startDate.startOf("month") <= endDate.startOf("month")) {
                betweenMonths.push(startDate.format("MM-YYYY"));
                !betweenYears.includes(startDate.format("YYYY")) && betweenYears.push(startDate.format("YYYY"));

                startDate.add(1, "month");
            }

            let res = JSON.parse(JSON.stringify(response));

            res.forEach((report, rind) => {
                let sc_total = 0,
                    fg_total = 0;

                if (checkSite(report.site, search.location)) {
                    report.sitename = locationList.filter((loc) => {
                        return loc.id === report.site;
                    })[0].name;
                    if (report.dcf === 16 && (search.indicator.id === 0 || search.indicator.id === 122)) {
                        report.response.forEach((fg, ind) => {
                            let date = fg.filter((i) => {
                                return i.dp === "DPA0285";
                            })[0].value;
                            let yearind = monthly_fg.findIndex((i) => {
                                return i.year === moment(date).format("YYYY");
                            });
                            let monthind = monthly_fg.findIndex((i) => {
                                return i.year === moment(date).format("YYYY") && i.month === moment(date).format("MMM-YYYY");
                            });
                            let dcfind = monthly_fg.findIndex((i) => {
                                return i.year === moment(date).format("YYYY") && i.month === moment(date).format("MMM-YYYY") && i.dcf === report.dcf;
                            });
                            let siteind = monthly_fg.findIndex((i) => {
                                return i.year === moment(date).format("YYYY") && i.month === moment(date).format("MMM-YYYY") && i.dcf === report.dcf && i.site === report.site;
                            });

                            if (checkReportingPeriod([moment(date).format("MM-YYYY")], betweenMonths).result) {
                                let item_class = fg.filter((i) => {
                                    return i.dp === "DPA0287";
                                })[0].value.name;
                                let item_qty = fg.filter((i) => {
                                    return i.dp === "DPA0288";
                                })[0].value;
                                let price_per_item = fg.filter((i) => {
                                    return i.dp === "DPA0289";
                                })[0].value;

                                let total =
                                    (emissionFactor[0].stdYears[0].stdNames[0].stdScopes[2].stdTopics[0].data1[0].importedData.filter((k) => {
                                        return k.item_classification === item_class;
                                    })[0]["co2e_in_kg"] /
                                        1000) *
                                    item_qty *
                                    price_per_item;

                                fg_total = total + fg_total;

                                let updateind = monthly_fg.findIndex((i) => {
                                    return i.year === moment(date).format("YYYY") && i.month === moment(date).format("MMM-YYYY") && i.dcf === report.dcf && i.site === report.site && i.item_class === item_class;
                                });

                                if (updateind === -1) {
                                    monthly_fg.push({ scope: 3, year: moment(date).format("YYYY"), month: moment(date).format("MMM-YYYY"), ghg: total, dcf: report.dcf, site: report.site, item_class: item_class });
                                } else {
                                    monthly_fg[updateind].ghg = monthly_fg[updateind].ghg + total;
                                }
                            }
                        });
                    }
                    if (report.dcf === 36 && (search.indicator.id === 0 || search.indicator.id === 123)) {
                        report.response.forEach((fg, ind) => {
                            let date = fg.filter((i) => {
                                return i.dp === "DPA0290";
                            })[0].value;
                            let yearind = monthly_fg.findIndex((i) => {
                                return i.year === moment(date).format("YYYY");
                            });
                            let monthind = monthly_fg.findIndex((i) => {
                                return i.year === moment(date).format("YYYY") && i.month === moment(date).format("MMM-YYYY");
                            });
                            let dcfind = monthly_fg.findIndex((i) => {
                                return i.year === moment(date).format("YYYY") && i.month === moment(date).format("MMM-YYYY") && i.dcf === report.dcf;
                            });
                            let siteind = monthly_fg.findIndex((i) => {
                                return i.year === moment(date).format("YYYY") && i.month === moment(date).format("MMM-YYYY") && i.dcf === report.dcf && i.site === report.site;
                            });

                            if (checkReportingPeriod([moment(date).format("MM-YYYY")], betweenMonths).result) {
                                let total = 0;

                                let travel_mode = fg.filter((i) => {
                                    return i.dp === "DPA0291";
                                })[0].value.name;
                                let passenger = fg.filter((i) => {
                                    return i.dp === "DPA0292";
                                })[0].value;

                                if (travel_mode.toLowerCase() === "air") {
                                    total =
                                        fg.filter((i) => {
                                            return i.dp === "DP_co2e_mt";
                                        })[0].value * passenger;

                                    let updateind = monthly_fg.findIndex((i) => {
                                        return i.year === moment(date).format("YYYY") && i.month === moment(date).format("MMM-YYYY") && i.dcf === report.dcf && i.site === report.site && i.travel_mode === travel_mode;
                                    });

                                    if (updateind === -1) {
                                        monthly_fg.push({ scope: 3, year: moment(date).format("YYYY"), month: moment(date).format("MMM-YYYY"), ghg: total, dcf: report.dcf, site: report.site, travel_mode: travel_mode });
                                    } else {
                                        monthly_fg[updateind].ghg = monthly_fg[updateind].ghg + total;
                                    }

                                    fg_total = total + fg_total;
                                } else if (travel_mode.toLowerCase() === "road") {
                                    let veh_cat = fg.filter((i) => {
                                        return i.dp === "DPA0295";
                                    })[0].value.name;
                                    let veh_type = fg.filter((i) => {
                                        return i.dp === "DPA0337";
                                    })[0].value;
                                    let fuel = fg.filter((i) => {
                                        return i.dp === "DPA0338";
                                    })[0].value;
                                    let km = fg.filter((i) => {
                                        return i.dp === "DP_KM";
                                    })[0].value;
                                    if (veh_cat.includes("Cars") || veh_cat.includes("Motor")) {
                                        if (veh_cat.includes("Cars")) {
                                            total =
                                                (emissionFactor[0].stdYears[0].stdNames[0].stdScopes[2].stdTopics[1].data1[0].importedData.filter((k) => {
                                                    return k.vehicle_category === veh_cat && k.vehicle_type === veh_type.name && k.fuel_type === fuel.name && k.unit === "km";
                                                })[0]["co2e_in_kg"] /
                                                    1000) *
                                                km *
                                                passenger;
                                        } else {
                                            total =
                                                (emissionFactor[0].stdYears[0].stdNames[0].stdScopes[2].stdTopics[1].data1[0].importedData.filter((k) => {
                                                    return k.vehicle_category === veh_cat && k.vehicle_type === veh_type.name && k.unit === "km";
                                                })[0]["co2e_in_kg"] /
                                                    1000) *
                                                km *
                                                passenger;
                                        }
                                    } else {
                                        total =
                                            (emissionFactor[0].stdYears[0].stdNames[0].stdScopes[2].stdTopics[1].data1[0].importedData.filter((k) => {
                                                return k.vehicle_category === veh_cat && k.unit === "passenger.km";
                                            })[0]["co2e_in_kg"] /
                                                1000) *
                                            km *
                                            passenger;
                                    }

                                    let updateind = monthly_fg.findIndex((i) => {
                                        return i.year === moment(date).format("YYYY") && i.month === moment(date).format("MMM-YYYY") && i.dcf === report.dcf && i.site === report.site && i.travel_mode === travel_mode;
                                    });

                                    if (updateind === -1) {
                                        monthly_fg.push({ scope: 3, year: moment(date).format("YYYY"), month: moment(date).format("MMM-YYYY"), ghg: total, dcf: report.dcf, site: report.site, travel_mode: travel_mode });
                                    } else {
                                        monthly_fg[updateind].ghg = monthly_fg[updateind].ghg + total;
                                    }

                                    fg_total = total + fg_total;
                                } else if (travel_mode.toLowerCase() === "rail") {
                                    total =
                                        fg.filter((i) => {
                                            return i.dp === "DP_KM";
                                        })[0].value *
                                        passenger *
                                        0.00116;

                                    let updateind = monthly_fg.findIndex((i) => {
                                        return i.year === moment(date).format("YYYY") && i.month === moment(date).format("MMM-YYYY") && i.dcf === report.dcf && i.site === report.site && i.travel_mode === travel_mode;
                                    });

                                    if (updateind === -1) {
                                        monthly_fg.push({ scope: 3, year: moment(date).format("YYYY"), month: moment(date).format("MMM-YYYY"), ghg: total, dcf: report.dcf, site: report.site, travel_mode: travel_mode });
                                    } else {
                                        monthly_fg[updateind].ghg = monthly_fg[updateind].ghg + total;
                                    }

                                    fg_total = total + fg_total;
                                }
                            }
                        });
                    }

                    if (report.dcf === 11 && (search.indicator.id === 0 || search.indicator.id === 93)) {
                        let addedMonth = checkReportingPeriod(report.rp, betweenMonths);

                        let total = 0,
                            co2 = 0,
                            ch4 = 0,
                            n2o = 0,
                            sc_data = [];
                        report.response.forEach((fg, ind) => {
                            let fuel_cat = fg.filter((i) => {
                                return i.dp === "DPA0130";
                            })[0].value.name;
                            let fuel_type = fg.filter((i) => {
                                return i.dp === "DPA0131";
                            })[0].value.name;
                            let unit = fg.filter((i) => {
                                return i.dp === "DPA0132";
                            })[0].value.name;
                            let consumed = fg.filter((i) => {
                                return i.dp === "DPA0336";
                            })[0].value;
                            let fuel_cat_ind = sc_data.findIndex((k) => {
                                return k.fuel_type === fuel_type && k.unit === unit;
                            });

                            if (fuel_cat === "Solid Fuels" || fuel_cat === "Biomass") {
                                let carbon = fg.filter((i) => {
                                    return i.dp === "DPA0134";
                                })[0].value;
                                let cv = fg.filter((i) => {
                                    return i.dp === "DPA0133";
                                })[0].value;
                                if (carbon > 0 && cv > 0) {
                                    let gj = (carbon * 3.664 * 1000) / cv;
                                    total = (gj * 0.000004184 * consumed) / 1000;
                                    fg["value"] = ["Emission Factor-", 3.664, " ,EnergyProduced-", 0.000004184];
                                    co2 = 0;
                                    ch4 = 0;
                                    n2o = 0;
                                    if (fuel_cat_ind === -1) {
                                        sc_data.push({ fuel_type: fuel_type, ghg: total, co2, n2o, ch4, unit, co2_: 0, n2o_: 0, ch4_: 0 });
                                    } else {
                                        sc_data[fuel_cat_ind].ghg = sc_data[fuel_cat_ind].ghg + total;
                                        sc_data[fuel_cat_ind].co2 = sc_data[fuel_cat_ind].co2 + co2;
                                        sc_data[fuel_cat_ind].ch4 = sc_data[fuel_cat_ind].ch4 + ch4;
                                        sc_data[fuel_cat_ind].n2o = sc_data[fuel_cat_ind].n2o + n2o;
                                    }
                                } else {
                                    total =
                                        (emissionFactor[0].stdYears[0].stdNames[0].stdScopes[0].stdTopics[0].data1[0].importedData.filter((k) => {
                                            return k.fuel_type === fuel_cat && k.fuel === fuel_type && k.unit === unit;
                                        })[0]["co2e_in_kg"] /
                                            1000) *
                                        consumed;
                                    co2 =
                                        (emissionFactor[0].stdYears[0].stdNames[0].stdScopes[0].stdTopics[0].data1[0].importedData.filter((k) => {
                                            return k.fuel_type === fuel_cat && k.fuel === fuel_type && k.unit === unit;
                                        })[0]["co2_in_kg"] /
                                            1000) *
                                        consumed;
                                    ch4 =
                                        (emissionFactor[0].stdYears[0].stdNames[0].stdScopes[0].stdTopics[0].data1[0].importedData.filter((k) => {
                                            return k.fuel_type === fuel_cat && k.fuel === fuel_type && k.unit === unit;
                                        })[0]["ch4_in_kg"] /
                                            1000) *
                                        consumed;
                                    n2o =
                                        (emissionFactor[0].stdYears[0].stdNames[0].stdScopes[0].stdTopics[0].data1[0].importedData.filter((k) => {
                                            return k.fuel_type === fuel_cat && k.fuel === fuel_type && k.unit === unit;
                                        })[0]["n2o_in_kg"] /
                                            1000) *
                                        consumed;
                                    let co2_ = emissionFactor[0].stdYears[0].stdNames[0].stdScopes[0].stdTopics[0].data1[0].importedData.filter((k) => {
                                        return k.fuel_type === fuel_cat && k.fuel === fuel_type && k.unit === unit;
                                    })[0]["co2_in_kg"];
                                    let ch4_ = emissionFactor[0].stdYears[0].stdNames[0].stdScopes[0].stdTopics[0].data1[0].importedData.filter((k) => {
                                        return k.fuel_type === fuel_cat && k.fuel === fuel_type && k.unit === unit;
                                    })[0]["ch4_in_kg"];
                                    let n2o_ = emissionFactor[0].stdYears[0].stdNames[0].stdScopes[0].stdTopics[0].data1[0].importedData.filter((k) => {
                                        return k.fuel_type === fuel_cat && k.fuel === fuel_type && k.unit === unit;
                                    })[0]["n2o_in_kg"];
                                    fg["value"] = ["co2-", co2 / consumed, " ,ch4-", ch4 / consumed, " ,n2o-", n2o / consumed];
                                    if (fuel_cat_ind === -1) {
                                        sc_data.push({ fuel_type: fuel_type, ghg: total, unit, co2, n2o, ch4, co2_, n2o_, ch4_ });
                                    } else {
                                        sc_data[fuel_cat_ind].ghg = sc_data[fuel_cat_ind].ghg + total;
                                        sc_data[fuel_cat_ind].co2 = sc_data[fuel_cat_ind].co2 + co2;
                                        sc_data[fuel_cat_ind].ch4 = sc_data[fuel_cat_ind].ch4 + ch4;
                                        sc_data[fuel_cat_ind].n2o = sc_data[fuel_cat_ind].n2o + n2o;
                                    }
                                }
                            } else {
                                total =
                                    (emissionFactor[0].stdYears[0].stdNames[0].stdScopes[0].stdTopics[0].data1[0].importedData.filter((k) => {
                                        return k.fuel_type === fuel_cat && k.fuel === fuel_type && k.unit === unit;
                                    })[0]["co2e_in_kg"] /
                                        1000) *
                                    consumed;
                                co2 =
                                    (emissionFactor[0].stdYears[0].stdNames[0].stdScopes[0].stdTopics[0].data1[0].importedData.filter((k) => {
                                        return k.fuel_type === fuel_cat && k.fuel === fuel_type && k.unit === unit;
                                    })[0]["co2_in_kg"] /
                                        1000) *
                                    consumed;
                                ch4 =
                                    (emissionFactor[0].stdYears[0].stdNames[0].stdScopes[0].stdTopics[0].data1[0].importedData.filter((k) => {
                                        return k.fuel_type === fuel_cat && k.fuel === fuel_type && k.unit === unit;
                                    })[0]["ch4_in_kg"] /
                                        1000) *
                                    consumed;
                                n2o =
                                    (emissionFactor[0].stdYears[0].stdNames[0].stdScopes[0].stdTopics[0].data1[0].importedData.filter((k) => {
                                        return k.fuel_type === fuel_cat && k.fuel === fuel_type && k.unit === unit;
                                    })[0]["n2o_in_kg"] /
                                        1000) *
                                    consumed;
                                let co2_ = emissionFactor[0].stdYears[0].stdNames[0].stdScopes[0].stdTopics[0].data1[0].importedData.filter((k) => {
                                    return k.fuel_type === fuel_cat && k.fuel === fuel_type && k.unit === unit;
                                })[0]["co2_in_kg"];
                                let ch4_ = emissionFactor[0].stdYears[0].stdNames[0].stdScopes[0].stdTopics[0].data1[0].importedData.filter((k) => {
                                    return k.fuel_type === fuel_cat && k.fuel === fuel_type && k.unit === unit;
                                })[0]["ch4_in_kg"];
                                let n2o_ = emissionFactor[0].stdYears[0].stdNames[0].stdScopes[0].stdTopics[0].data1[0].importedData.filter((k) => {
                                    return k.fuel_type === fuel_cat && k.fuel === fuel_type && k.unit === unit;
                                })[0]["n2o_in_kg"];

                                fg["value"] = ["co2-", co2 / consumed, " ,ch4-", ch4 / consumed, " ,n2o-", n2o / consumed];
                                if (fuel_cat_ind === -1) {
                                    sc_data.push({ fuel_type: fuel_type, ghg: total, co2, n2o, ch4, unit, co2_, n2o_, ch4_ });
                                } else {
                                    sc_data[fuel_cat_ind].ghg = sc_data[fuel_cat_ind].ghg + total;
                                    sc_data[fuel_cat_ind].co2 = sc_data[fuel_cat_ind].co2 + co2;
                                    sc_data[fuel_cat_ind].ch4 = sc_data[fuel_cat_ind].ch4 + ch4;
                                    sc_data[fuel_cat_ind].n2o = sc_data[fuel_cat_ind].n2o + n2o;
                                }
                            }
                        });
                        addedMonth.rps.forEach((fm) => {
                            let date = moment(fm, "MM-YYYY").toDate();
                            let yearind = monthly_fg.findIndex((i) => {
                                return i.year === moment(date).format("YYYY");
                            });
                            let monthind = monthly_fg.findIndex((i) => {
                                return i.year === moment(date).format("YYYY") && i.month === moment(date).format("MMM-YYYY");
                            });
                            let dcfind = monthly_fg.findIndex((i) => {
                                return i.year === moment(date).format("YYYY") && i.month === moment(date).format("MMM-YYYY") && i.dcf === report.dcf;
                            });
                            let siteind = monthly_fg.findIndex((i) => {
                                return i.year === moment(date).format("YYYY") && i.month === moment(date).format("MMM-YYYY") && i.dcf === report.dcf && i.site === report.site;
                            });

                            sc_data.forEach((item) => {
                                let updateind = monthly_fg.findIndex((i) => {
                                    return i.year === moment(date).format("YYYY") && i.month === moment(date).format("MMM-YYYY") && i.dcf === report.dcf && i.site === report.site && i.fuel_type === item.fuel_type && i.unit === item.unit;
                                });

                                if (updateind === -1) {
                                    monthly_fg.push({
                                        scope: 1,
                                        year: moment(date).format("YYYY"),
                                        month: moment(date).format("MMM-YYYY"),
                                        ghg: item.ghg / addedMonth.count,
                                        dcf: report.dcf,
                                        site: report.site,
                                        fuel_type: item.fuel_type,
                                        unit: item.unit,
                                        co2: (item.co2 * 1000) / addedMonth.count,
                                        n2o: (item.n20 * 1000) / addedMonth.count,
                                        ch4: (item.ch4 * 1000) / addedMonth.count,
                                        co2_: item.co2_,
                                        ch4_: item.ch4_,
                                        n2o_: item.n2o_,
                                    });
                                } else {
                                    monthly_fg[updateind].co2 = ((item.co2 * 1000) / addedMonth.count) * 1000 + monthly_fg[updateind].co2;
                                    monthly_fg[updateind].n2o = ((item.n2o * 1000) / addedMonth.count) * 1000 + monthly_fg[updateind].n2o;
                                    monthly_fg[updateind].ch4 = ((item.ch4 * 1000) / addedMonth.count) * 1000 + monthly_fg[updateind].ch4;
                                    monthly_fg[updateind].ghg = monthly_fg[updateind].ghg + item.ghg / addedMonth.count;
                                }
                            });
                        });
                    }
                    if (report.dcf === 10 && (search.indicator.id === 0 || search.indicator.id === 116)) {
                        let add = [];

                        report.response.forEach((fg, ind) => {
                            let date = fg.filter((i) => {
                                return i.dp === "DPA0137";
                            })[0].value;
                            let yearind = monthly_fg.findIndex((i) => {
                                return i.year === moment(date).format("YYYY");
                            });
                            let monthind = monthly_fg.findIndex((i) => {
                                return i.year === moment(date).format("YYYY") && i.month === moment(date).format("MMM-YYYY");
                            });
                            let dcfind = monthly_fg.findIndex((i) => {
                                return i.year === moment(date).format("YYYY") && i.month === moment(date).format("MMM-YYYY") && i.dcf === report.dcf;
                            });
                            let siteind = monthly_fg.findIndex((i) => {
                                return i.year === moment(date).format("YYYY") && i.month === moment(date).format("MMM-YYYY") && i.dcf === report.dcf && i.site === report.site;
                            });

                            if (checkReportingPeriod([moment(date).format("MM-YYYY")], betweenMonths).result) {
                                let gastype = fg.filter((i) => {
                                    return i.dp === "DPA0136";
                                })[0].value.name;
                                let gasrefilled = fg.filter((i) => {
                                    return i.dp === "DPA0138";
                                })[0].value;
                                let total =
                                    (emissionFactor[0].stdYears[0].stdNames[0].stdScopes[0].stdTopics[2].data1[0].importedData.filter((k) => {
                                        return k.gas_type === gastype;
                                    })[0]["co2e_in_kg"] /
                                        1000) *
                                    gasrefilled;
                                let co2e_ = emissionFactor[0].stdYears[0].stdNames[0].stdScopes[0].stdTopics[2].data1[0].importedData.filter((k) => {
                                    return k.gas_type === gastype;
                                })[0]["co2e_in_kg"];
                                fg_total = total + fg_total;

                                fg["gas"] = gastype;
                                fg["gasfilled"] = gasrefilled;
                                fg["value"] = ["EmissionFactor-", total / gasrefilled];
                                let updateind = monthly_fg.findIndex((i) => {
                                    return i.year === moment(date).format("YYYY") && i.month === moment(date).format("MMM-YYYY") && i.dcf === report.dcf && i.site === report.site && i.gastype === gastype;
                                });

                                if (updateind === -1) {
                                    monthly_fg.push({ scope: 1, year: moment(date).format("YYYY"), month: moment(date).format("MMM-YYYY"), ghg: 0, dcf: report.dcf, site: report.site, gastype: gastype, ghg: total, co2e_ });
                                } else {
                                    monthly_fg[updateind].ghg = monthly_fg[updateind].ghg + total;
                                }
                            }
                        });
                    }
                    if (report.dcf === 72 && (search.indicator.id === 0 || search.indicator.id === 121)) {
                        report.rp.forEach((i) => {
                            if (checkReportingPeriod([i], betweenMonths).result) {
                                let yearind = monthly_fg.findIndex((i) => {
                                    return i.year === moment(i, "MM-YYYY").format("YYYY");
                                });
                                let monthind = monthly_fg.findIndex((i) => {
                                    return i.year === moment(i, "MM-YYYY").format("YYYY") && i.month === moment(i, "MM-YYYY").format("MMM-YYYY");
                                });
                                let dcfind = monthly_fg.findIndex((i) => {
                                    return i.year === moment(i, "MM-YYYY").format("YYYY") && i.month === moment(i, "MM-YYYY").format("MMM-YYYY") && i.dcf === report.dcf;
                                });
                                let siteind = monthly_fg.findIndex((i) => {
                                    return i.year === moment(i, "MM-YYYY").format("YYYY") && i.month === moment(i, "MM-YYYY").format("MMM-YYYY") && i.dcf === report.dcf && i.site === report.site;
                                });

                                let ef = emissionFactor[0].stdYears[0].stdNames[0].stdScopes[1].stdTopics[0].data1[0].importedData[0]["kwh_in_tco2e"];

                                let renewable =
                                    report.response[0].filter((i) => {
                                        return i.dp === "DPA0156";
                                    })[0].value / report.rp.length;
                                let nonrenewable =
                                    report.response[0].filter((i) => {
                                        return i.dp === "DPA0157";
                                    })[0].value / report.rp.length;

                                if (yearind === -1) {
                                    monthly_fg.push({ scope: 2, year: moment(i, "MM-YYYY").format("YYYY"), month: moment(i, "MM-YYYY").format("MMM-YYYY"), ghg: nonrenewable * ef, co2e_: ef, dcf: report.dcf, site: report.site, renewable, nonrenewable });
                                } else if (monthind === -1) {
                                    monthly_fg.push({ scope: 2, year: moment(i, "MM-YYYY").format("YYYY"), month: moment(i, "MM-YYYY").format("MMM-YYYY"), ghg: nonrenewable * ef, co2e_: ef, dcf: report.dcf, site: report.site, renewable, nonrenewable });
                                } else if (dcfind === -1) {
                                    monthly_fg.push({ scope: 2, year: moment(i, "MM-YYYY").format("YYYY"), month: moment(i, "MM-YYYY").format("MMM-YYYY"), ghg: nonrenewable * ef, co2e_: ef, dcf: report.dcf, site: report.site, renewable, nonrenewable });
                                } else if (siteind === -1) {
                                    monthly_fg.push({ scope: 2, year: moment(i, "MM-YYYY").format("YYYY"), month: moment(i, "MM-YYYY").format("MMM-YYYY"), ghg: nonrenewable * ef, dcf: report.dcf, co2e_: ef, site: report.site, renewable, nonrenewable });
                                }
                            }
                        });
                    }
                    if (report.dcf === 15 && (search.indicator.id === 0 || search.indicator.id === 118)) {
                        let addedMonth = checkReportingPeriod(report.rp, betweenMonths);

                        let ef_by_fuel = emissionFactor[0].stdYears[0].stdNames[0].stdScopes[0].stdTopics[4].data1[0].importedData;
                        let ef_by_distance = emissionFactor[0].stdYears[0].stdNames[0].stdScopes[0].stdTopics[1].data1[0].importedData;

                        let total = 0,
                            co2 = 0,
                            ch4 = 0,
                            n2o = 0,
                            mc_data = [];

                        report.response.forEach((fg, ind) => {
                            let mode = fg.filter((i) => {
                                return i.dp === "DP_MODE";
                            })[0].value
                                ? "by distance"
                                : "by fuel";
                            let fuel_cat = fg.filter((i) => {
                                return i.dp === "DPA0140";
                            })[0].value;
                            let fuel_type = fg.filter((i) => {
                                return i.dp === "DPA0139";
                            })[0].value;
                            let fuel = fg.filter((i) => {
                                return i.dp === "DPA0141";
                            })[0].value.name;
                            let unit = fg.filter((i) => {
                                return i.dp === "DPA0339";
                            })[0].value;
                            let km = fg.filter((i) => {
                                return i.dp === "DPA0144";
                            })[0].value;
                            let fuel_filled = fg.filter((i) => {
                                return i.dp === "DPA0143";
                            })[0].value;
                            let fuel_cat_ind = mc_data.findIndex((k) => {
                                return k.fuel === fuel;
                            });

                            if (mode === "by distance") {
                                total = ef_by_distance.filter((k) => {
                                    return k.vehicle_category === fuel_cat.name && k.vehicle_type === fuel_type.name && k.fuel_type === fuel && k.unit === unit.name;
                                })[0]["co2e_in_kg"];

                                if (fuel_cat_ind === -1) {
                                    mc_data.push({ mode: fuel, ghg: total * km, co2e_: total, fuel_cat: fuel_cat.name });
                                } else {
                                    mc_data[fuel_cat_ind].ghg = mc_data[fuel_cat_ind].ghg + total;
                                }
                            } else {
                                total = ef_by_fuel.filter((k) => {
                                    return k.fuel.trim() === fuel.trim();
                                })[0]["co2e_in_kg"];

                                if (fuel_cat_ind === -1) {
                                    mc_data.push({ mode: fuel, ghg: total * fuel_filled, co2e_: total, fuel_cat: "" });
                                } else {
                                    mc_data[fuel_cat_ind].ghg = mc_data[fuel_cat_ind].ghg + total;
                                }
                            }
                        });
                        addedMonth.rps.forEach((fm) => {
                            let date = moment(fm, "MM-YYYY").toDate();

                            mc_data.forEach((item) => {
                                let updateind = monthly_fg.findIndex((i) => {
                                    return i.year === moment(date).format("YYYY") && i.month === moment(date).format("MMM-YYYY") && i.dcf === report.dcf && i.site === report.site && i.mode === item.mode;
                                });

                                if (updateind === -1) {
                                    let updateind2 = monthly_fg.findIndex((i) => {
                                        return i.year === moment(date).format("YYYY") && i.month === moment(date).format("MMM-YYYY") && i.dcf === report.dcf && i.site === report.site && i.mode === item.mode;
                                    });
                                    if (updateind2 !== -1) {
                                        monthly_fg[updateind2].ghg = item.ghg / addedMonth.count + monthly_fg[updateind2].ghg;
                                        // monthly_fg[updateind2]['fuel_cat'] = item.fuel_cat
                                    } else {
                                        console.log("EF", item);
                                        monthly_fg.push({ scope: 1, year: moment(date).format("YYYY"), month: moment(date).format("MMM-YYYY"), ghg: 0, dcf: report.dcf, site: report.site, ghg: item.ghg / addedMonth.count, mode: item.mode, fuel_cat: item.fuel_cat, co2e_: item.co2e_ });
                                    }
                                } else {
                                    // monthly_fg[updateind]['fuel_cat'] = item.fuel_cat
                                    monthly_fg[updateind].ghg = monthly_fg[updateind].ghg + item.ghg / addedMonth.count;
                                }
                            });
                        });
                    }
                }
            });

            let scope12_array = groupArrayObject(
                monthly_fg.filter((i) => {
                    return i.scope === 1 || i.scope === 2;
                }),
                "month"
            );
            let scope3_array = groupArrayObject(
                monthly_fg.filter((i) => {
                    return i.scope === 3;
                }),
                "month"
            );
            let scope1_array = groupArrayObject(
                monthly_fg.filter((i) => {
                    return i.scope === 1;
                }),
                "month"
            );
            let scope2_array = groupArrayObject(
                monthly_fg.filter((i) => {
                    return i.scope === 2;
                }),
                "month"
            );
            let pie = [];
            monthly_fg
                .filter((i) => {
                    return i.scope === 1;
                })
                .forEach((item) => {
                    let index = pie.findIndex((j) => {
                        return j.name === "Scope " + item.scope;
                    });
                    if (index === -1) {
                        pie.push({ name: "Scope " + item.scope, y: item.ghg });
                    } else {
                        pie[index].y = pie[index].y + item.ghg;
                    }
                });
            monthly_fg
                .filter((i) => {
                    return i.scope === 2;
                })
                .forEach((item) => {
                    let index = pie.findIndex((j) => {
                        return j.name === "Scope " + item.scope;
                    });
                    if (index === -1) {
                        pie.push({ name: "Scope " + item.scope, y: item.ghg });
                    } else {
                        pie[index].y = pie[index].y + item.ghg;
                    }
                });
            monthly_fg
                .filter((i) => {
                    return i.scope === 3;
                })
                .forEach((item) => {
                    let index = pie.findIndex((j) => {
                        return j.name === "Scope " + item.scope;
                    });
                    if (index === -1) {
                        pie.push({ name: "Scope " + item.scope, y: item.ghg });
                    } else {
                        pie[index].y = pie[index].y + item.ghg;
                    }
                });

            let index1 = pie.findIndex((j) => {
                return j.name === "Scope 1";
            });
            let index2 = pie.findIndex((j) => {
                return j.name === "Scope 2";
            });
            let index3 = pie.findIndex((j) => {
                return j.name === "Scope 3";
            });

            if (index1 === -1) {
                pie.push({ name: "Scope 1", y: 0 });
            }
            if (index2 === -1) {
                pie.push({ name: "Scope 2", y: 0 });
            }
            if (index3 === -1) {
                pie.push({ name: "Scope 3", y: 0 });
            }

            if (scope1_array.length !== 0) {
                let scope1_tier2 = [],
                    final = [];
                Object.keys(scope1_array).forEach((key) => {
                    scope1_array[key].forEach((item) => {
                        item.dcfId = item.dcf;
                        item.dcf =
                            dcflist.filter((l) => {
                                return l.id === item.dcf;
                            }).length === 0
                                ? item.dcf
                                : dcflist.filter((l) => {
                                    return l.id === item.dcf;
                                })[0].title;

                        if (
                            scope1_tier2.findIndex((i) => {
                                return i.name === item.dcf;
                            }) === -1
                        ) {
                            scope1_tier2.push({
                                name: item.dcf,
                                drilldown: true,
                                color: "red",
                                y: item.ghg,

                                subset: [[key, item.ghg]],
                            });
                        } else {
                            let ind = scope1_tier2.findIndex((i) => {
                                return i.name === item.dcf;
                            });
                            let subind = scope1_tier2[ind].subset.findIndex((j) => {
                                return j[0] === key;
                            });
                            scope1_tier2[ind].y = scope1_tier2[ind].y + item.ghg;
                            if (subind === -1) {
                                scope1_tier2[ind].subset.push([key, item.ghg]);
                            } else {
                                scope1_tier2[ind].subset[subind][1] = scope1_tier2[ind].subset[subind][1] + item.ghg;
                            }
                        }
                    });
                });

                betweenYears.forEach((j) => {
                    let dataset = JSON.parse(JSON.stringify(scope1_tier2)).map((i) => {
                        i.subset.sort((a, b) => {
                            return moment(a[0], "MMM-YYYY").toDate() - moment(b[0], "MMM-YYYY").toDate();
                        });
                        i.subset = i.subset.filter((k) => {
                            return k[0].split("-")[1] === j;
                        });
                        i.y = i.subset.length === 0 ? 0 : getSum(i.subset);
                        i.incomplete = false;
                        return i;
                    });

                    final.push(dataset);
                });
            }
            if (scope2_array.length !== 0) {
                let scope2_tier2 = [],
                    final = [];
                Object.keys(scope2_array).forEach((key) => {
                    scope2_array[key].forEach((item) => {
                        item.dcfId = item.dcf;
                        item.dcf =
                            dcflist.filter((l) => {
                                return l.id === item.dcf;
                            }).length === 0
                                ? item.dcf
                                : dcflist.filter((l) => {
                                    return l.id === item.dcf;
                                })[0].title;
                        if (
                            scope2_tier2.findIndex((i) => {
                                return i.name === item.dcf;
                            }) === -1
                        ) {
                            scope2_tier2.push({
                                name: item.dcf,
                                drilldown: true,
                                color: "red",
                                y: item.ghg,
                                subset: [[key, item.ghg]],
                            });
                        } else {
                            let ind = scope2_tier2.findIndex((i) => {
                                return i.name === item.dcf;
                            });
                            let subind = scope2_tier2[ind].subset.findIndex((j) => {
                                return j[0] === key;
                            });
                            scope2_tier2[ind].y = scope2_tier2[ind].y + item.ghg;
                            if (subind === -1) {
                                scope2_tier2[ind].subset.push([key, item.ghg]);
                            } else {
                                scope2_tier2[ind].subset[subind][1] = scope2_tier2[ind].subset[subind][1] + item.ghg;
                            }
                        }
                    });
                });
                betweenYears.forEach((j) => {
                    let dataset = JSON.parse(JSON.stringify(scope2_tier2)).map((i) => {
                        i.subset.sort((a, b) => {
                            return moment(a[0], "MMM-YYYY").toDate() - moment(b[0], "MMM-YYYY").toDate();
                        });
                        i.subset = i.subset.filter((k) => {
                            return k[0].split("-")[1] === j;
                        });
                        i.y = i.subset.length === 0 ? 0 : getSum(i.subset);
                        i.incomplete = false;
                        return i;
                    });

                    final.push(dataset);
                });
            }
            if (scope3_array.length !== 0) {
                let scope3_tier2 = [],
                    final = [];
                Object.keys(scope3_array).forEach((key) => {
                    scope3_array[key].forEach((item) => {
                        item.dcfId = item.dcf;
                        item.dcf =
                            dcflist.filter((l) => {
                                return l.id === item.dcf;
                            }).length === 0
                                ? item.dcf
                                : dcflist.filter((l) => {
                                    return l.id === item.dcf;
                                })[0].title;
                        if (
                            scope3_tier2.findIndex((i) => {
                                return i.name === item.dcf;
                            }) === -1
                        ) {
                            scope3_tier2.push({
                                name: item.dcf,
                                drilldown: true,
                                color: "red",
                                y: item.ghg,
                                subset: [[key, item.ghg]],
                            });
                        } else {
                            let ind = scope3_tier2.findIndex((i) => {
                                return i.name === item.dcf;
                            });
                            let subind = scope3_tier2[ind].subset.findIndex((j) => {
                                return j[0] === key;
                            });
                            scope3_tier2[ind].y = scope3_tier2[ind].y + item.ghg;
                            if (subind === -1) {
                                scope3_tier2[ind].subset.push([key, item.ghg]);
                            } else {
                                scope3_tier2[ind].subset[subind][1] = scope3_tier2[ind].subset[subind][1] + item.ghg;
                            }
                        }
                    });
                });
                betweenYears.forEach((j) => {
                    let dataset = JSON.parse(JSON.stringify(scope3_tier2)).map((i) => {
                        i.subset.sort((a, b) => {
                            return moment(a[0], "MMM-YYYY").toDate() - moment(b[0], "MMM-YYYY").toDate();
                        });
                        i.subset = i.subset.filter((k) => {
                            return k[0].split("-")[1] === j;
                        });
                        i.y = i.subset.length === 0 ? 0 : getSum(i.subset);
                        i.incomplete = false;
                        return i;
                    });

                    final.push(dataset);
                });
            }
        }

        return monthly_fg;
    };
    const showSite = () => {
        let site_ids = [],
            site_names = [];
        dcfass.forEach((i) => {
            !site_ids.includes(i.site) && site_ids.push(i.site);
        });

        site_ids.forEach((i, j) => {
            site_names.push(
                locationList.find((k) => {
                    return k.id === i;
                }).title
            );
        });

        return site_names;
    };

    const getTableContent = (a, b, c) => {
        return (
            <div class="gridlines-container">
                <table class="gridlines">
                    <tbody>
                        <tr>
                            <td colspan="1" rowspan="1" className="text-center">
                                {a}
                            </td>
                            <td colspan="1" rowspan="1" className="text-center" >
                                {b}
                            </td>
                            <td colspan="1" rowspan="1" className="text-center">
                                {c}
                            </td>
                        </tr>

                    </tbody>
                </table>
            </div>
        )
    }
    return (
        <div>
            <div className="col-12" style={{ display: "flex", flexDirection: "row", justifyContent: "space-between" }}>
                {/* <div className="col-3 p-card" style={{ margin: 5, height: "calc(100vh - 9rem)", overflow: "scroll" }}>
                    <nav>
                        {headings.map((heading, ind) => {
                            let indexes = [];
                            return (
                                <>
                                    {heading.level === 0 ? (
                                        <label key={heading.id} style={{ display: "flex", margin: 5, fontWeight: activeId === heading.id ? "bold" : "normal", textDecoration: heading.text.includes("SECTION") && "underline" }} className={getClassName(heading.level)}>
                                            <a target="_blank"
                                                href={`#${heading.id}`}
                                                onClick={(e) => {
                                                    e.preventDefault();
                                                    document.querySelector(`#${heading.id}`).scrollIntoView({
                                                        behavior: "smooth",
                                                        block: "start",
                                                        inline: "nearest",
                                                    });
                                                }}
                                                style={{
                                                    fontWeight: activeId === heading.id ? "bold" : "normal",
                                                }}
                                            >
                                                {heading.text}
                                            </a>
                                        </label>
                                    ) : (
                                        <ul>
                                            {heading.item.map((item, ind2) => {
                                                return (
                                                    <li key={item.id} className={getClassName(item.level)}>
                                                        <a target="_blank"
                                                            href={`#${item.id}`}
                                                            onClick={(e) => {
                                                                e.preventDefault();
                                                                document.querySelector(`#${item.id}`).scrollIntoView({
                                                                    behavior: "smooth",
                                                                });
                                                            }}
                                                            style={{
                                                                fontWeight: activeId === item.id ? "bold" : "normal",
                                                            }}
                                                        >
                                                            {item.text}
                                                        </a>
                                                    </li>
                                                );
                                            })}
                                        </ul>
                                    )}
                                </>
                            );
                        })}
                    </nav>
                </div> */}
                <div className="col-12 p-card" style={{ margin: 5, height: "calc(100vh - 9rem)", overflow: "scroll", color: "white" }}>
                    {/* <div className="col-12" style={{ display: "flex", justifyContent: "flex-end" }}>
                        <Button icon='pi pi-eye' style={{ marginRight: 10 }} rounded text raised aria-label="Filter" onClick={() => { exportTable2Excel(1) }}> </Button>

                        <Button icon='pi pi-cloud-download' rounded text raised aria-label="Filter" onClick={() => { exportTable2Excel(0) }}> </Button>
                    </div> */}
                    <div style={{ display: "flex", flexDirection: "column" }}>
                        {/* <img id='ass' src='https://mpower-s3.s3-ap-southeast-1.amazonaws.com/uploads/hazards/1690426362525Pushing%20and%20Pulling.png' width={'100%'} /> */}
                        <div>
                            <div className="col-12 grid" style={{ margin: 5, color: "white" }}>
                                <div>
                                    <label
                                        style={{
                                            color: "black",
                                            padding: 15,
                                            justifyContent: "flex-start",
                                            display: "flex",
                                        }}
                                    >
                                        Select Year :
                                    </label>
                                </div>
                                <div className="col-4">
                                    <Dropdown options={[{ name: 2022 }, { name: 2023 }]} value={year} optionLabel="name" optionValue="name" onChange={(e) => { setYear(e.value) }} />
                                </div>

                            </div>

                        </div>
                        <div id="main" className="EcoVadis" style={{ flexDirection: "column", display: "flex" }}>



                            {getTableContent("Mandatory", "GEN3037", "Low Impact on the Theme Score ")}
                            <div className="para m-3">
                                <label className="fb5" > Does your company formally and publicly adhere to any external CSR initiatives or set of principles?
                                </label>
                                <ul className="pad30">

                                    <li > United Nations Global Compact (UNGC)
                                    </li>

                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachment:
                                </label>
                                <ul className="pad30">

                                    <li >	Science Based Targets Initiative (SBTi)
                                    </li>

                                </ul>
                            </div>


                            <div className="para m-3">
                                <label className="fb5" > Attachment:
                                </label>
                                <ul className="pad30">

                                    <li > 	Other external CSR initiative or set of principles
                                    </li>

                                </ul>
                            </div>


                            <div className="para m-3">
                                <label className="fb5" > Attachment:
                                </label>
                                <ul className="pad30">

                                    <li > No endorsement/Do not know
                                    </li>

                                </ul>
                            </div>


                            {getTableContent("Mandatory", "GEN600", "Medium Impact on the Theme Score ")}

                            <div className="para m-3">
                                <label className="fb5" > 	Which of the following applies to your company’s reporting on quantitative indicators on CSR topics?
                                </label>
                                <ul className="pad30">

                                    <li > 	The indicators are externally verified by an independent third-party
                                    </li>

                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachment:
                                </label>
                                <ul className="pad30">

                                    <li >	The Key Performance Indicators are formally aligned with a sustainability reporting standard (e.g. GRI, SASB, other - please specify
                                    </li>

                                </ul>
                            </div>


                            <div className="para m-3">
                                <label className="fb5" > Attachment:
                                </label>
                                <ul className="pad30">

                                    <li > A materiality analysis was conducted to identify relevant indicators
                                    </li>

                                </ul>
                            </div>


                            <div className="para m-3">
                                <label className="fb5" > Attachment:
                                </label>
                                <ul className="pad30">

                                    <li > My company reports on quantitative indicators on CSR but none of the above applies
                                    </li>

                                </ul>
                            </div>


                            <div className="para m-3">
                                <label className="fb5" > Attachment:
                                </label>
                                <ul className="pad30">

                                    <li > My company does not report on any quantitative CSR indicators/Do not know
                                    </li>

                                </ul>
                            </div>





                            {getTableContent("Mandatory", "GEN600", "Medium Impact on the Theme Score ")}


                            <div className="para m-3">
                                <label className="fb5" > Independent assurance statement of sustainability parameters
                                </label>
                                <ul className="pad30">

                                    <li > 	KPIs of sustainability (Env, Safety, Ethics, SCM, Social)
                                    </li>

                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachment:
                                </label>
                                <ul className="pad30">

                                    <li > 	Materiality assessment - Materiality assessment document & SR
                                    </li>

                                </ul>
                            </div>


                            {getTableContent("Mandatory", "GEN4001", "Medium Impact on the Theme Score ")}

                            <div className="para m-3">
                                <label className="fb5" > Does your company communicate progress towards the Sustainable Development Goals (SDGs)?
                                </label>
                                <ul className="pad30">

                                    <li > 	Yes
                                    </li>

                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachment:
                                </label>
                                <ul className="pad30">

                                    <li > No
                                    </li>

                                </ul>
                            </div>

                            {getTableContent("Mandatory", "GEN5004", "Medium Impact on the Theme Score ")}

                            <div className="para m-3">
                                <label className="fb5" > Has your company been audited on-site with regards to CSR issues (e.g Environment, Labor Practices & Human Rights, Business Ethics), on at least 1 of your company's locations? If yes, please upload on-site audit report and it must be within 2 years of today, contain a list of non-compliances identified (if any), contain Corrective Action Plan, and issued by a 3rd party auditor.
                                </label>
                                <ul className="pad30">

                                    <li > 	Yes, and the report has all of the stated characteristics
                                    </li>

                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachment:
                                </label>
                                <ul className="pad30">

                                    <li > Yes, but the report does not have all of the stated characteristics
                                    </li>

                                    <li>
                                        No on-site audit regarding CSR issues performed
                                    </li>

                                </ul>
                            </div>

                            <div className="para m-3" style={{ textAlign: 'center', fontWeight: 'bold' }}>
                                Labour and Human Rights
                            </div>


                            {getTableContent("Mandatory", "LAB100", "High Impact on the Theme Score")}

                            <div className="para m-3">
                                <label className="fb5" >	Does your company have a policy regarding labor practices or human rights issues?
                                </label>
                                <ul className="pad30">

                                    <li >Employees health and safety (e.g. management of employees health and safety issues)
                                    </li>



                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li > 	Working Conditions (e.g. wages, benefits, working hours, two-way communication on working conditions)
                                    </li>



                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li > 	Labor Relations (e.g. structured relations with employee representatives/trade unions)
                                    </li>



                                </ul>
                            </div>


                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li > Career Management (e.g. management of recruitment, training & career development)
                                    </li>



                                </ul>
                            </div>


                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >	Child and Forced Labor (e.g. engagements or measures taken to prevent or eradicate child or forced labor)
                                    </li>



                                </ul>
                            </div>


                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li > Diversity, Equity & Inclusion
                                    </li>



                                </ul>
                            </div>


                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li > 	External stakeholder human rights
                                    </li>



                                </ul>
                            </div>


                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li > No policies
                                    </li>



                                </ul>
                            </div>


                            {getTableContent("Mandatory", "LAB1001", "High Impact on the Theme Score")}
                            <div className="para m-3">

                                <div className="para m-3">
                                    <label className="fb5" > Does your company's health & safety policy also cover activities carried out by your subcontractors? (e.g. at construction sites, on chartered vessels)
                                    </label>
                                    <ul className="pad30">

                                        <li > 	Yes
                                        </li>

                                    </ul>
                                </div>

                                <div className="para m-3">
                                    <label className="fb5" > Attachments
                                    </label>
                                    <ul className="pad30">

                                        <li >	No
                                        </li>
                                        <li>
                                            No subcontractors working on the company premises or on construction sites
                                        </li>
                                        <li>
                                            o	Do not know
                                        </li>


                                    </ul>
                                </div>



                            </div>



                            {getTableContent("Mandatory", "LAB310", "High Impact on the Theme Score")}

                            <div className="para m-3">
                                <label className="fb5" > 	What actions are in place regarding employee health and safety?


                                </label>
                                <ul className="pad30">

                                    <li >Health and safety detailed risk assessment
                                    </li>

                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >	Procedure in place to anticipate health & safety risks related to change of operations (e.g. start-up of new operations, change of operations)
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >Provision of protective equipment to all impacted employees
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >Specific procedures for handling of chemicals or hazardous substances
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >Health and Safety procedures translated in major languages spoken by employees
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >Joint labor management health and safety committee in operation
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >Active preventive measures for stress and noise
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >Training of all relevant employees on health and safety risks and good working practices
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >Mandatory health check up for all employees
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >Training on health and safety issues for subcontractors working on premises (if applicable)
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >Regular inspection or audit to ensure safety of equipment
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >Others
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >No specific mechanisms in place
                                    </li>
                                </ul>
                            </div>

                            {getTableContent("Mandatory", "LAB3210", "High Impact on the Theme Score")}

                            <div className="para m-3">
                                <label className="fb5" >What actions are in place regarding working conditions?
                                </label>
                                <ul className="pad30">

                                    <li >Interactive communication session with employees regarding working conditions
                                    </li>
                                </ul>
                            </div>


                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >Compensation for extra or atypical working hours
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >Additional leave beyond standard vacation days
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >Flexible organization of work available to employees (e.g. remote work, flexi-time)
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >Childcare services or allowance
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >	Health care coverage of employees in place
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >Remuneration process (e.g. salary grid, procedure for salary advancement) communicated to employees
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >	Employee satisfaction survey
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >Employee stock ownership plan (not restricted to executive level)
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >	Bonus scheme related to company performance
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >Others
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >No specific mechanisms in place
                                    </li>
                                </ul>
                            </div>

                            {getTableContent("Mandatory", "LAB330", "High Impact on the Theme Score")}

                            <div className="para m-3">
                                <label className="fb5" > What actions are in place regarding social dialogue?
                                </label>
                                <ul className="pad30">

                                    <li >Employee representatives or employee representative body (e.g. works council)
                                    </li>
                                </ul>
                            </div>
                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >European Works Council in place
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >Collective agreement on employees’ health & safety
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >	Collective agreement on working conditions
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >Collective agreement on training & career management
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >Collective agreement on diversity, discrimination and/or harassment
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >Other actions to promote structured social dialogue not included elsewhere in this question
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >No actions yet/Do not know
                                    </li>
                                </ul>
                            </div>

                            {getTableContent("Mandatory", "LAB340", "High Impact on the Theme Score")}


                            <div className="para m-3">
                                <label className="fb5" >What actions are in place regarding training and career management?
                                </label>
                                <ul className="pad30">

                                    <li >Transparent recruitment process (communicated clearly and formally to all candidates)
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >Regular assessment (at least once a year) of individual performance
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >Setting of Individual development and career plan for all employees
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >Official measures promoting career mobility
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >Provision of skills development training
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >Official measures to anticipate or reduce layoffs and associated negative impacts (e.g. financial compensation, outplacement
                                        service)

                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >Other actions on career management & training not included elsewhere in this question
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >No specific mechanisms in place
                                    </li>
                                </ul>
                            </div>


                            {getTableContent("Mandatory", "LAB3501", "High Impact on the Theme Score")}

                            <div className="para m-3">
                                <label className="fb5" > 	What actions are in place to address child labor, slavery and/or human trafficking?
                                </label>
                                <ul className="pad30">

                                    <li >"Impact assessments identifying potential child labor, forced labor and/or human trafficking
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >"Stakeholder consultation with potentially affected groups or NGOs to address child labor, forced labor and/or human trafficking issues"
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >Awareness training on child labor, forced labor and/or human trafficking
                                    </li>
                                </ul>
                            </div>


                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >Age verification of candidates before hiring
                                    </li>
                                </ul>
                            </div>


                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >Actions in place to protect young workers (if any employed)
                                    </li>
                                </ul>
                            </div>


                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >	Process in place to prevent the retention of employee identification documents
                                    </li>
                                </ul>
                            </div>


                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >Actions in place to prevent excessive use of force and limitation of freedom of movement of employees by security forces
                                    </li>
                                </ul>
                            </div>


                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >Monitoring of internal controls and effectiveness of actions taken to prevent child labor, forced labor and/or human trafficking
                                    </li>
                                </ul>
                            </div>


                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >Grievance mechanism on child labor, forced labor and/or human trafficking
                                    </li>
                                </ul>
                            </div>


                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >	Remediation procedure in place for identified cases of child and/or forced labor
                                    </li>
                                </ul>
                            </div>


                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >Other proactive measures to prevent child and/or forced labor
                                    </li>
                                </ul>
                            </div>


                            {getTableContent("Mandatory", "LAB3601", "High Impact on the Theme Score ")}


                            <div className="para m-3">
                                <label className="fb5" >qWhat actions are in place to promote diversity, equity & inclusion?
                                </label>
                                <ul className="pad30">

                                    <li > 	Proactive measures to prevent discrimination during recruitment phase
                                    </li>
                                </ul>
                            </div>


                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li > 	Awareness training to prevent discrimination and/or harassment
                                    </li>
                                </ul>
                            </div>


                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li > 	Measures for the integration of employees with disabilities
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li > Whistleblower procedure on discrimination and harassment issues
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li > Measures to promote wage equality in the workplace (e.g. equal pay monitoring)
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li > Measures to prevent discrimination </li>
                                </ul>
                            </div>



                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li > 	Measures to promote a gender/minority inclusive environment
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li > 	Measures for the integration of senior employees
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li > oRemediation procedure in place for identified cases of discrimination and/or harassment
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li > o	Other actions to promote the inclusion of minority/vulnerable groups in the workplace
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li > No actions/ Do not know
                                    </li>
                                </ul>
                            </div>



                            {getTableContent("Mandatory", "LAB383")}

                            <div className="para m-3">
                                <label className="fb5" >Do workers have the right to join labor unions, workers’ councils, or other collective bargaining organizations?
                                </label>
                                <ul className="pad30">

                                    <li > Yes, the right to join labor unions, workers' councils, or other collective bargaining organizations is granted
                                    </li>
                                </ul>
                            </div>
                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >Partially yes, the right to join labor unions, workers' councils, or other collective bargaining organizations is granted, but restricted in
                                        compliance with applicable law

                                    </li>
                                </ul>
                            </div>
                            {getTableContent("Mandatory", "LAB4501", "High  Impact on the Theme Score ")}


                            <div className="para m-3">
                                <label className="fb5" > Please identify the coverage (in %) of your company's actions throughout all company operations/workforce on one or more of the following items:
                                </label>
                                <ul className="pad30">

                                    <li >Please identify the coverage (in %) of your company's actions throughout all company operations/workforce on one or more of the following items:
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >
                                        % of the total workforce across all locations represented in formal joint management-worker health & safety committees
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >
                                        % of the total workforce across all locations who are covered by formal collective agreements concerning working conditions (please
                                        specify)

                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >
                                        % of the total workforce across all locations who are covered by formal collective agreements concerning working conditions (please
                                        specify)

                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >
                                        % of the total workforce across all locations who received regular performance and career development reviews
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >
                                        % of the total workforce across all locations who received career- or skills-related training
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >
                                        % of the total workforce across all locations who received training on diversity, discrimination and/or harassment
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >
                                        % of all operational sites that have been subject to human rights reviews or human rights impact assessments
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Other indicator on the % of the deployment of labor or human rights actions throughout all company operations/workforce (please
                                        specify)

                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Do not know
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Other indicator on the % of the deployment of labor or human rights actions throughout all company operations/workforce (please
                                        specify)

                                    </li>
                                </ul>
                            </div>

                            {getTableContent("Mandatory", "LAB601", "Medium  Impact on the Theme Score")}

                            <div className="para m-3">
                                <label className="fb5" >	On which of the following topics does your company report Key Performance Indicators (KPIs)?
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Employee Health & Safety

                                    </li>
                                </ul>
                            </div>


                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Working Conditions

                                    </li>
                                </ul>
                            </div>


                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Social Dialogue

                                    </li>
                                </ul>
                            </div>


                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >

                                        Career Management & Training
                                    </li>
                                </ul>
                            </div>


                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Child Labor, Forced Labor and Human Trafficking (if applicable)

                                    </li>
                                </ul>
                            </div>


                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Diversity, Equity & Inclusion

                                    </li>
                                </ul>
                            </div>


                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >
                                        External Stakeholder Human Rights (if applicable

                                    </li>
                                </ul>
                            </div>


                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >
                                        None of the above/Do not know

                                    </li>
                                </ul>
                            </div>

                            {getTableContent("Mandatory", "LAB610", "Medium Impact on the Theme Score")}
                            <div className="para m-3">
                                <label className="fb5" > 	Please report on the following KPIs related to employee health & safety (please specify the year)
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Lost time injury (LT I) frequency rate for direct workforce - (total number of lost time injury events) x 1,000,000 / total hours worked
                                        company wide


                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Lost time injury (LT I) severity rate for direct workforce - (number of days lost due to injuries) x 1,000 / total hours worked

                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Not monitored/Do not know

                                    </li>
                                </ul>
                            </div>


                            {getTableContent("Mandatory", "LAB620", "Medium Impact on the Theme Score ")}

                            <div className="para m-3">
                                <label className="fb5" >	Please report on the following KPI related to career management & training (please specify the year)
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Average hours of training provided per employee

                                    </li>
                                </ul>
                            </div>

                            {getTableContent("Mandatory", "LAB561", "Medium Impact on the Theme Score ")}

                            <div className="para m-3">
                                <label className="fb5" > 	Please report on the following KPIs related to workplace diversity
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Percentage of women employed in the whole organization

                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Percentage of women in top executive positions (excluding boards of directors)
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Percentage of women within the organization’s board

                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Average unadjusted gender pay gap

                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Percentage of employees from minority and/or vulnerable groups in the whole organization (if applicable)

                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Percentage of employees from minority and/or vulnerable groups in top executive positions (excluding boards of directors) (if
                                        applicable)


                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Not monitored/Do not know

                                    </li>
                                </ul>
                            </div>

                            {getTableContent("Mandatory", "LAB7101", "Medium Impact on the Theme Score ")}


                            <div className="para m-3">
                                <label className="fb5" > Has your company obtained any labor and human rights management system certification?
                                </label>
                                <ul className="pad30">

                                    <li >
                                        ISO 45001
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >MASE
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >

                                        RCMS (Responsible Care Management System)    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >
                                        RC14001 (Responsible Care 14001)

                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Fair Wage Network (Fair Wage Certification or Living Wage Certification)

                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Others

                                    </li>
                                </ul>
                            </div>

                            {getTableContent("Mandatory", "LAB720", "Medium Impact on the Theme Score ")}



                            <div className="para m-3">
                                <label className="fb5" > 	What is the percentage of operational facilities that are certified ISO 45001 or against other labor or human rights management standard?
                                </label>
                                <ul className="pad30">

                                    <li> 	0-20%</li>
                                    <li> 	21-40%</li>
                                    <li>41-60% </li>
                                    <li>61-80% </li>
                                    <li>81-100%</li>
                                    <li>	Not applicable (if only one operational site) </li>
                                    <li> 	Not Monitored</li>
                                    <li>	Do not know </li>

                                </ul>
                            </div>



                            {getTableContent("Optional", "LAB800")}

                            <div className="para m-3">
                                <label className="fb5" > 	Additional comments regarding Labor and Human Rights topics of your company's management system. Please also indicate any CSR-related external recognition obtained by your company within the last five years, such as CSR-related awards, or listing in CSR rankings or indexes.
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Please specify

                                    </li>
                                </ul>
                            </div>

                            {getTableContent("Mandatory", "ENV100", "High Impact on the Theme Score")}
                            <div className="para m-3">
                                <label className="fb5" > Does your company have a formalized environmental policy? (for suppliers environmental issues, see section SUSTAINABLE PROCUREMENT)

                                </label>

                                <ul className="pad30">
                                    <li className={getClassNameByRFResponse(0, '')}>Energy Consumption and Greenhouse gases</li>

                                    {getClassNameByRFResponse(0, '') === 'check' &&
                                        <>
                                            <label className="fb5" >Attachment</label>
                                            <ol>
                                                {getAttachmentByRFResponse(0, '').map((i, j) => {

                                                    return (

                                                        <li>
                                                            <a target="_blank" href={API.Docs + i.originalname}>{i.originalname.substring(13)}</a>
                                                        </li>
                                                    )
                                                })}
                                            </ol>
                                        </>}
                                    <li className={getClassNameByRFResponse(0, '')}>Water</li>

                                    {getClassNameByRFResponse(0, '') === 'check' &&
                                        <>
                                            <label className="fb5" >Attachment</label>
                                            <ol>
                                                {getAttachmentByRFResponse(0, '').map((i, j) => {

                                                    return (

                                                        <li>
                                                            <a target="_blank" href={API.Docs + i.originalname}>{i.originalname.substring(13)}</a>
                                                        </li>
                                                    )
                                                })}
                                            </ol>
                                        </>}
                                    <li className={getClassNameByRFResponse(0, '')}>Biodiversity</li>

                                    {getClassNameByRFResponse(0, '') === 'check' &&
                                        <>
                                            <label className="fb5" >Attachment</label>
                                            <ol>
                                                {getAttachmentByRFResponse(0, '').map((i, j) => {

                                                    return (

                                                        <li>
                                                            <a target="_blank" href={API.Docs + i.originalname}>{i.originalname.substring(13)}</a>
                                                        </li>
                                                    )
                                                })}
                                            </ol>
                                        </>}
                                    <li className={getClassNameByRFResponse(0, '')}>Air Pollution</li>

                                    {getClassNameByRFResponse(0, '') === 'check' &&
                                        <>
                                            <label className="fb5" >Attachment</label>
                                            <ol>
                                                {getAttachmentByRFResponse(0, '').map((i, j) => {

                                                    return (

                                                        <li>
                                                            <a target="_blank" href={API.Docs + i.originalname}>{i.originalname.substring(13)}</a>
                                                        </li>
                                                    )
                                                })}
                                            </ol>
                                        </>}
                                    <li className={getClassNameByRFResponse(0, '')}>Materials, Chemicals and Waste</li>

                                    {getClassNameByRFResponse(0, '') === 'check' &&
                                        <>
                                            <label className="fb5" >Attachment</label>
                                            <ol>
                                                {getAttachmentByRFResponse(0, '').map((i, j) => {

                                                    return (

                                                        <li>
                                                            <a target="_blank" href={API.Docs + i.originalname}>{i.originalname.substring(13)}</a>
                                                        </li>
                                                    )
                                                })}
                                            </ol>
                                        </>}
                                    <li className={getClassNameByRFResponse(0, '')}>Environmental impacts from use of products</li>

                                    {getClassNameByRFResponse(0, '') === 'check' &&
                                        <>
                                            <label className="fb5" >Attachment</label>
                                            <ol>
                                                {getAttachmentByRFResponse(0, '').map((i, j) => {

                                                    return (

                                                        <li>
                                                            <a target="_blank" href={API.Docs + i.originalname}>{i.originalname.substring(13)}</a>
                                                        </li>
                                                    )
                                                })}
                                            </ol>
                                        </>}
                                    <li className={getClassNameByRFResponse(0, '')}>Environmental impacts from Products End-of-Life (e.g. recycling of products)</li>

                                    {getClassNameByRFResponse(0, '') === 'check' &&
                                        <>
                                            <label className="fb5" >Attachment</label>
                                            <ol>
                                                {getAttachmentByRFResponse(0, '').map((i, j) => {

                                                    return (

                                                        <li>
                                                            <a target="_blank" href={API.Docs + i.originalname}>{i.originalname.substring(13)}</a>
                                                        </li>
                                                    )
                                                })}
                                            </ol>
                                        </>}
                                    <li className={getClassNameByRFResponse(0, '')}>Customer Health and Safety</li>

                                    {getClassNameByRFResponse(0, '') === 'check' &&
                                        <>
                                            <label className="fb5" >Attachment</label>
                                            <ol>
                                                {getAttachmentByRFResponse(0, '').map((i, j) => {

                                                    return (

                                                        <li>
                                                            <a target="_blank" href={API.Docs + i.originalname}>{i.originalname.substring(13)}</a>
                                                        </li>
                                                    )
                                                })}
                                            </ol>
                                        </>}
                                    <li className={getClassNameByRFResponse(0, '')}>Promotion of Sustainable Consumption</li>

                                    {getClassNameByRFResponse(0, '') === 'check' &&
                                        <>
                                            <label className="fb5" >Attachment</label>
                                            <ol>
                                                {getAttachmentByRFResponse(0, '').map((i, j) => {

                                                    return (

                                                        <li>
                                                            <a target="_blank" href={API.Docs + i.originalname}>{i.originalname.substring(13)}</a>
                                                        </li>
                                                    )
                                                })}
                                            </ol>
                                        </>}
                                    <li className={getClassNameByRFResponse(0, '')}>No Policies</li>

                                    {getClassNameByRFResponse(0, '') === 'check' &&
                                        <>
                                            <label className="fb5" >Attachment</label>
                                            <ol>
                                                {getAttachmentByRFResponse(0, '').map((i, j) => {

                                                    return (

                                                        <li>
                                                            <a target="_blank" href={API.Docs + i.originalname}>{i.originalname.substring(13)}</a>
                                                        </li>
                                                    )
                                                })}
                                            </ol>
                                        </>}
                                </ul>
                            </div>

                            <div className="para m-3" style={{ textAlign: 'center', fontWeight: 'bold' }}>
                                Environment
                            </div>

                            {getTableContent("Mandatory", "ENV100 ", "High Impact on the Theme Score")}

                            <div className="para m-3">
                                <label className="fb5" > 	Does your company have a formalized environmental policy? (for suppliers environmental issues, see section SUSTAINABLE PROCUREMENT)
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Energy Consumption and Greenhouse gases

                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Water

                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Biodiversity

                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Air Pollution

                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Materials, Chemicals and Waste

                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Environmental impacts from use of products

                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >

                                        Environmental impacts from Products End-of-Life (e.g. recycling of products)
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Customer Health and Safety
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Promotion of Sustainable Consumption

                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >
                                        No Policies
                                    </li>
                                </ul>
                            </div>


                            {getTableContent("Mandatory", "ENV2301", "High Impact on the Theme Score")}

                            <div className="para m-3">
                                <label className="fb5" > Has your company formalized a process to assess and document environmental risks considering start-up of new operations, change of operations and/or periodic review of risks related to current activities?
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Yes, my company has a formalized process to assess and document environmental risks
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >
                                        No
                                    </li>
                                    <li >
                                        Do Not Know
                                    </li>
                                </ul>
                            </div>

                            {getTableContent("Mandatory", "ENV312", "High Impact on the Theme Score", "High Impact on carbon scorecard")}
                            <div className="para m-3">
                                <label className="fb5" >What actions are in place regarding the reduction of energy consumption and the emissions of GHG?
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Training of employees on energy conservation/climate actions
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Purchase(s) and/or generation of renewable energy
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Purchase(s) and/or generation of renewable energy
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Energy and/or carbon audit
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Improvement of energy efficiency through technology or equipment upgrades
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Use of waste heat recovery system(s) or combined heat and power unit(s)
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Fuel switch to achieve higher energy efficiency and/or lower carbon emission intensity
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Use of carbon capture and storage technology (CCS)
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Other actions to reduce energy consumption/GHG emissions not included elsewhere in this question
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >
                                        No actions/Do not know
                                    </li>
                                </ul>
                            </div>




                            {getTableContent("Mandatory", "ENV324", "High Impact on the Theme Score")}

                            <div className="para m-3">
                                <label className="fb5" > 	What actions are in place regarding water management?
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Closed water cooling system
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Control of wastewater generated in finishing operations such as quenching and deburring (e.g. oils and suspended solids)
                                    </li>
                                </ul>
                            </div>


                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Technologies or practices to recycle or reuse water
                                    </li>
                                </ul>
                            </div>


                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Water accounting or auditing performed
                                    </li>
                                </ul>
                            </div>


                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Control measures to monitor and/or prevent contamination of groundwater  </li>
                                </ul>
                            </div>


                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Adoption of cooling systems with reduced or recycled water consumption
                                    </li>
                                </ul>
                            </div>


                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Reduction of water consumption through innovative equipments or technologies
                                    </li>
                                </ul>
                            </div>


                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Water-stress assessment or mapping performed to identify exposure to water-related risks
                                    </li>
                                </ul>
                            </div>


                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >
                                        On-site or off-site wastewater treatment facilities
                                    </li>
                                </ul>
                            </div>


                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Infrastructures implemented to enable significant recycling of water
                                    </li>
                                </ul>
                            </div>


                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Wastewater quality assessment
                                    </li>
                                </ul>
                            </div>


                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Implementation of a rainwater harvesting system
                                    </li>
                                </ul>
                            </div>


                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Control measures to prevent contamination of groundwater
                                    </li>
                                </ul>
                            </div>


                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Measures implemented to treat or reduce pollutants rejected into water

                                    </li>
                                </ul>
                            </div>



                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Other actions to manage water efficiency or wastewater discharge not included elsewhere in this question
                                    </li>
                                </ul>
                            </div>


                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >
                                        No actions/ Do not know
                                    </li>
                                </ul>
                            </div>



                            {getTableContent("Mandatory", "ENV3499", "High Impact on the Theme Score")}

                            <div className="para m-3">
                                <label className="fb5" > What actions are in place regarding air pollution?
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Actions to prevent emissions of atmospheric pollutants and other environmental nuisances (e.g. noise, odor, vibration, road and light)
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Work processes or technologies implemented to mitigate emissions of dust and/or particulate matter
                                    </li>
                                </ul>
                            </div>


                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Periodical analysis on the volumes of major air pollutants or ambient air quality monitoring (testing levels of PM, NOx, SO2, VOC or
                                        heavy metals)

                                    </li>
                                </ul>
                            </div>


                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Work processes or technologies implemented to mitigate emissions of VOC, SO2, NOx or heavy metals
                                    </li>
                                </ul>
                            </div>


                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Regular noise measuring campaign (site boundary noise measurements undertaken)
                                    </li>
                                </ul>
                            </div>


                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Work processes or technologies implemented to mitigate noise
                                    </li>
                                </ul>
                            </div>


                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Work processes or technologies implemented to mitigate odor
                                    </li>
                                </ul>
                            </div>


                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Use of biofilters to minimize odor generated in plant operations
                                    </li>
                                </ul>
                            </div>


                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Other actions to mitigate air pollution not included elsewhere in this question
                                    </li>
                                </ul>
                            </div>


                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >
                                        No actions/ Do not know
                                    </li>
                                </ul>
                            </div>


                            {getTableContent("Mandatory", "ENV344", "High Impact on the Theme Score")}

                            <div className="para m-3">
                                <label className="fb5" > What actions are in place regarding local pollution (e.g. spills, accidental pollution)?
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Set up of dust collectors
                                    </li>
                                </ul>
                            </div>


                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Building of noise control walls in areas surrounding the plant
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Response procedure in place for emergencies (e.g. oil spill)
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Regular and formalized soil testing to check soil contamination (please specify which hazardous substances are monitored)
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Regular (at least annual) noise measuring campaigns
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Active measures to reduce noise level (e.g. fleet, manufacturing sites, ...)
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Company specific measures to avoid road congestions
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Company specific measures to avoid emissions of dust/particles
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >
                                        No actions/Do not know
                                    </li>
                                </ul>
                            </div>



                            {getTableContent("Mandatory", "ENV354", "High Impact on the Theme Score")}

                            <div className="para m-3">
                                <label className="fb5" > What actions are in place regarding hazardous materials?
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Company wide work processes for labeling, storing, handling and transporting hazardous goods
                                    </li>
                                </ul>
                            </div>


                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Reduction of material consumption through process optimization
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Reduction of internal wastes through material reuse, recovery or repurpose
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Use of recovered input materials
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Use of eco-friendly or bio-based input materials
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Environmental emergency measures in place.
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Specialized treatment and safe disposal of hazardous substances
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Use of alternative, less hazardous substances in operations
                                    </li>
                                </ul>
                            </div>


                            {getTableContent("Mandatory", "ENV3872", "High Impact on the Theme Score")}


                            <div className="para m-3">
                                <label className="fb5" > What actions are in place regarding protection of customer/consumer health and safety?
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Company awareness program and detailed information to customers on health and safety issues associated with your products/services
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Provision of information on product composition
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Product recall process
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Provision of information on potential health impacts of products/services (e.g. external or internal analyses of health and safety issues)
                                    </li>
                                </ul>
                            </div>


                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Provision of Safety Data Sheet (SDS)
                                    </li>
                                </ul>
                            </div>


                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Formalized process in place to assess and document risks related to customer health and safety (e.g. considering start-up of new
                                    </li>
                                </ul>
                            </div>


                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Measures in place to assess the potential adverse impacts of nanotechnologies or other emerging technologies
                                    </li>
                                </ul>
                            </div>


                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Other actions to ensure customer health & safety not included elsewhere in this question
                                    </li>
                                </ul>
                            </div>


                            {getTableContent("Mandatory", "ENV362")}
                            <div className="para m-3">
                                <label className="fb5" > What actions are in place to reduce the environmental impacts from the use of products?
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Integration of ecodesign features in product design (e.g. improving energy efficiency)
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Carbon footprint study performed on key products
                                    </li>
                                </ul>
                            </div>


                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Life Cycle Analysis implemented on key products
                                    </li>
                                </ul>
                            </div>


                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Ecolabel or Eco-certification obtained (e.g. ISO14040, ISO14025, Environmental Product Declaration)
                                    </li>
                                </ul>
                            </div>


                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Other product design/performance standards
                                    </li>
                                </ul>
                            </div>


                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Do not know
                                    </li>
                                </ul>
                            </div>

                            {getTableContent("Mandatory", "ENV372")}

                            <div className="para m-3">
                                <label className="fb5" > 	What actions are in place to reduce the environmental impacts from your product end of life?
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Products and packaging designed for easy dismantling and recyclability
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Company specific take back programs, and/or company specific collection facilities (e.g. free collection infrastructure for ink and cartridges)
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Company specific recycling infrastructure or formal partnership established
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Provision of specific information regarding product(s) end-of-life (e.g. end-of-life manual)
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Do not know
                                    </li>
                                </ul>
                            </div>

                            {getTableContent("Mandatory", "ENV7109", "High Impact on the Theme Score")}


                            <div className="para m-3">
                                <label className="fb5" > 	Has your company obtained any environmental management system certification?
                                </label>
                                <ul className="pad30">

                                    <li >
                                        ISO 14001
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >
                                        EMAS
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >
                                        RCMS (Responsible Care Management System)
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >
                                        RC14001 (Responsible Care 14001)
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Others
                                    </li>
                                </ul>
                            </div>





                            {getTableContent("Mandatory", "ENV5001", "High Impact on the Theme Score ")}

                            <div className="para m-3">
                                <label className="fb5" > 	Please identify the coverage (in %) of your company's actions throughout all company operations on one or more of the following items:
                                </label>
                                <ul className="pad30">

                                    <li >
                                        % of the total workforce across all locations who received training (internally or externally) on environmental issues
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >
                                        % of all operational sites for which an environmental risk assessment has been conducted
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Other indicator on the % of the deployment of environmental actions throughout all company operations
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >
                                        No environmental actions in place
                                    </li>
                                </ul>
                            </div>

                            {getTableContent("Mandatory", "ENV720", "High Impact on the Theme Score")}
                            <div className="para m-3">
                                <label className="fb5" > 	What is the percentage of operational facilities that are certified ISO 45001 or against other labor or human rights management standard?
                                </label>
                                <ul className="pad30">

                                    <li> 	0-20%</li>
                                    <li> 	21-40%</li>
                                    <li>41-60% </li>
                                    <li>61-80% </li>
                                    <li>81-100%</li>
                                    <li>	Not applicable (if only one operational site) </li>
                                    <li> 	Not Monitored</li>
                                    <li>	Do not know </li>

                                </ul>
                            </div>


                            {getTableContent("Mandatory", "ENV6000", "High Impact on the Theme Score")}

                            <div className="para m-3">
                                <label className="fb5" > 	On which of the following topics does your company report Key Performance Indicators (KPIs)?
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Energy Consumption & GHGs
                                    </li>
                                </ul>
                            </div>


                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Water
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Biodiversity
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Air Pollution
                                    </li>
                                </ul>
                            </div>


                            {getTableContent("Mandatory", "ENV6001", "medium Impact on the Theme Score")}

                            <div className="para m-3">
                                <label className="fb5" > 	Is your company a respondent to CDP's Climate Change Questionnaire? If so, please upload the latest response you have
                                    provided to CDP

                                </label>
                                <ul className="pad30">

                                    <li >
                                        Yes
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >
                                        No/Do not know
                                    </li>
                                </ul>
                            </div>



                            {getTableContent("Mandatory", "ENV630", "Medium Impact on the Theme Score ")}


                            <div className="para m-3">
                                <label className="fb5" > Please report on the following KPIs regarding GHGs emissions:
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Total Scope 1 GHG emissions in metric tons CO2e
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Total Scope 2 GHG emissions in metric tons CO2e
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Total gross Scope 3 Downstream GHG emissions
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Total gross Scope 3 Upstream GHG emissions

                                    </li>
                                </ul>
                            </div>



                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Not monitored/Do not know
                                    </li>
                                </ul>
                            </div>



                            {/* =======above done */}
                            {getTableContent("Mandatory", "ENV6332", "Medium Impact on the Theme Score")}

                            <div className="para m-3">
                                <label className="fb5" > Please report on the following environmental KPIs
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Total energy consumption in MWh
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Total renewable energy consumption
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Total water consumption
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Total weight of pollutants emitted to water
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Total weight of hazardous waste in tons
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Total weight of non-hazardous waste in tons
                                    </li>
                                </ul>
                            </div>

                            {getTableContent("Mandatory", "ENV6760", "Medium Impact on the Theme Score ")}

                            <div className="para m-3">
                                <label className="fb5" > Please report on the following KPI regarding the collection of unused or expired medicine by your company (please specify the year
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Reporting year
                                    </li>

                                    <li >
                                        <li >
                                            Amount of unused or expired medicines collected for recycling or waste treatment as a percentage of the total volume of products
                                            sold  </li>
                                    </li>

                                    <li >
                                        Not Monitored/Do not know
                                    </li>
                                </ul>
                            </div>


                            {getTableContent("Mandatory", "ENV6331", "Medium Impact on the Theme Score ")}

                            <div className="para m-3">
                                <label className="fb5" > Please report on the following environmental KPI
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Total energy consumption in MWh
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Total renewable energy consumption
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Total water consumption
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Total weight of pollutants emitted to water
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Total weight of hazardous waste in tons
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Total weight of non-hazardous waste in tons
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachments
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Total weight of waste recovered
                                    </li>
                                </ul>
                            </div>



                            {getTableContent("Mandatory", "CAR100", "Low Impact on the Theme Score")}

                            <div className="para m-3">
                                <label className="fb5" > 	Describe your entity's GHG reduction targets (select all that apply)
                                </label>
                                <ul className="pad30">

                                    <li >
                                        We publicly announce our targets
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachment:
                                </label>
                                <ul className="pad30">

                                    <li >
                                        We have an absolute scope 1 reduction target
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachment:
                                </label>
                                <ul className="pad30">

                                    <li >
                                        We have an absolute scope 2 reduction target
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachment:
                                </label>
                                <ul className="pad30">

                                    <li >
                                        We have an absolute scope 3 reduction target
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachment:
                                </label>
                                <ul className="pad30">

                                    <li >
                                        We have an intensity reduction target
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachment:
                                </label>
                                <ul className="pad30">

                                    <li >
                                        We have committed to or our targets approved by SBTi
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachment:
                                </label>
                                <ul className="pad30">

                                    <li >
                                        No formal targets yet/Do not know
                                    </li>
                                </ul>
                            </div>

                            {getTableContent("Mandatory", "CAR101", "Low  Impact on the Theme Score")}
                            <div className="para m-3">
                                <label className="fb5" > 	Describe your entity's plan to achieve GHG reductions (select all that apply)
                                </label>
                                <ul className="pad30">

                                    <li >
                                        We have a time-bound action plan to reduce GHG emissions
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachment:
                                </label>
                                <ul className="pad30">

                                    <li >
                                        We use LCA as an analytical tool for planning
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachment:
                                </label>
                                <ul className="pad30">

                                    <li >
                                        We have a time-bound action plan to transform into a low carbon business model
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachment:
                                </label>
                                <ul className="pad30">

                                    <li >
                                        No action plan yet/Do not know
                                    </li>
                                </ul>
                            </div>


                            {getTableContent("Mandatory", "CAR102", "Medium  Impact on the Theme Score")}


                            <div className="para m-3">
                                <label className="fb5" > How does your entity's management structure facilitate GHG emissions reduction?
                                </label>
                                <ul className="pad30">

                                    <li >
                                        We have a dedicated budget for GHG management
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachment:
                                </label>
                                <ul className="pad30">

                                    <li >
                                        We have a management team dedicated to GHG emissions reduction
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachment:
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Our management team's compensation is linked to progress towards GHG reduction targets
                                    </li>
                                </ul>
                            </div>


                            <div className="para m-3">
                                <label className="fb5" > Attachment:
                                </label>
                                <ul className="pad30">

                                    <li >
                                        GHG emissions reduction is not embedded in the management structure/Do not know
                                    </li>
                                </ul>
                            </div>


                            {getTableContent("Mandatory", "CAR104", "Medium  Impact on the Theme Score")}

                            <div className="para m-3">
                                <label className="fb5" > Describe your entity's GHG emissions inventory (select all that apply)
                                </label>
                                <ul className="pad30">

                                    <li >
                                        We have a GHG inventory at the corporate level in accordance with GHG protocol or other GHG accounting standards
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachment:
                                </label>
                                <ul className="pad30">

                                    <li >
                                        We have GHG data at the product level in accordance with the GHG Protocol or other GHG accounting standards
                                    </li>
                                </ul>
                            </div>


                            <div className="para m-3">
                                <label className="fb5" > Attachment:
                                </label>
                                <ul className="pad30">

                                    <li >
                                        We update our inventory at least once per year
                                    </li>
                                </ul>
                            </div>



                            <div className="para m-3">
                                <label className="fb5" > Attachment:
                                </label>
                                <ul className="pad30">

                                    <li >
                                        No GHG inventory yet/Do not know
                                    </li>
                                </ul>
                            </div>





                            {getTableContent("Mandatory", "CAR105", "Medium Impact on the Theme Score")}


                            <div className="para m-3">
                                <label className="fb5" >What is the scope of the GHG emissions monitoring in your entity?
                                </label>
                                <ul className="pad30">

                                    <li >
                                        We monitor relevant GHG emissions for our entire scope
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachment:
                                </label>
                                <ul className="pad30">

                                    <li >
                                        We monitor GHG emissions with significant exclusions
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachment:
                                </label>
                                <ul className="pad30">

                                    <li >
                                        No GHG inventory yet/Do not know
                                    </li>
                                </ul>
                            </div>

                            {getTableContent("Mandatory", "CAR107", "High Impact on the Theme Score")}

                            <div className="para m-3">
                                <label className="fb5" > Describe your entity's progress towards GHG reduction targets
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Our latest review showed that we are falling behind
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachment:
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Our latest review showed that we are on track to meet our targets
                                    </li>
                                </ul>
                            </div>


                            <div className="para m-3">
                                <label className="fb5" > Attachment:
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Our latest review showed that we are ahead
                                    </li>
                                </ul>
                            </div>



                            <div className="para m-3">
                                <label className="fb5" > Attachment:
                                </label>
                                <ul className="pad30">

                                    <li >
                                        No review/Do not know
                                    </li>
                                </ul>
                            </div>



                            {getTableContent("Mandatory", "CAR108", "Medium  Impact on the Theme Score")}

                            <div className="para m-3">
                                <label className="fb5" > 	Describe your entity's GHG reporting practices
                                </label>
                                <ul className="pad30">

                                    <li >
                                        We share GHG emissions with internal stakeholders
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachment:
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Our GHG emissions report is publicly available
                                    </li>
                                </ul>
                            </div>


                            <div className="para m-3">
                                <label className="fb5" > Attachment:
                                </label>
                                <ul className="pad30">

                                    <li >
                                        We respond to CDP
                                    </li>
                                </ul>
                            </div>


                            <div className="para m-3">
                                <label className="fb5" > Attachment:
                                </label>
                                <ul className="pad30">

                                    <li >
                                        We verify our GHG emissions through a third party
                                    </li>
                                </ul>
                            </div>


                            <div className="para m-3">
                                <label className="fb5" > Attachment:
                                </label>
                                <ul className="pad30">

                                    <li >
                                        We do not disclose our emissions/Do not know
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3" style={{ textAlign: 'center', fontWeight: 'bold' }}>
                                Procurement Practices
                            </div>

                            {getTableContent("Mandatory", "SUP104", "High Impact on the Theme Score")}

                            <div className="para m-3">
                                <label className="fb5" >	Does your company have a formal policy covering any of the topics below
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Sustainable procurement policy on environmental issues
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachment:
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Sustainable procurement policy on labor practices and human rights
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachment:
                                </label>
                                <ul className="pad30">

                                    <li >
                                        No policies
                                    </li>
                                </ul>
                            </div>



                            {getTableContent("Mandatory", "SUP2031", "High Impact on the Theme Score")}

                            <div className="para m-3">
                                <label className="fb5" >Are palm and/or palm-based derivatives (e.g. palm oil, palm kernel oil, palm oil-based ingredients and/or their derivatives) necessary for the manufacturing/production process of the products sold/distributed by your company?
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Yes
                                    </li>
                                </ul>
                            </div>


                            <div className="para m-3">
                                <label className="fb5" > Attachment:
                                </label>
                                <ul className="pad30">

                                    <li >
                                        No
                                    </li>
                                </ul>
                            </div>

                            {getTableContent("Mandatory", "SUP300", "High  Impact on the Theme Score")}

                            <div className="para m-3">
                                <label className="fb5" > 	What actions are in place regarding the integration of social or environmental factors within procurement?
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Supplier CSR code of conduct in place
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachment:
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Integration of social or environmental clauses into supplier contracts
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachment:
                                </label>
                                <ul className="pad30">

                                    <li >
                                        CSR risk analysis (i.e. prior to supplier assessments or audits)
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachment:
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Supplier assessment (e.g. questionnaire) on environmental or social practice
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachment:
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Training of buyers on social and environmental issues within the supply chain


                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachment:
                                </label>
                                <ul className="pad30">

                                    <li >
                                        On-site audits of suppliers on environmental or social issues
                                    </li>
                                </ul>
                            </div>

                            {getTableContent("Mandatory", "SUP320", "High Impact on the Theme Score")}

                            <div className="para m-3">
                                <label className="fb5" > Do you have a supplier diversity program in place or other actions designed to advance diversity in the supply chain?
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Actions to work with women-owned businesses in the supply chain
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachment:
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Actions to work with businesses owned by minorities/vulnerable groups in the supply chain
                                    </li>
                                </ul>
                            </div>


                            <div className="para m-3">
                                <label className="fb5" > Attachment:
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Actions to advance diversity, equity and inclusion in the workforce of suppliers
                                    </li>
                                </ul>
                            </div>


                            <div className="para m-3">
                                <label className="fb5" > Attachment:
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Other actions designed to advance diversity in the supply chain not included elsewhere in this question
                                    </li>
                                </ul>
                            </div>


                            <div className="para m-3">
                                <label className="fb5" > Attachment:
                                </label>
                                <ul className="pad30">

                                    <li >
                                        No actions/Do not know
                                    </li>
                                </ul>
                            </div>



                            {getTableContent("Optional", "SUP321")}

                            <div className="para m-3">
                                <label className="fb5" > Does your supplier diversity program apply to all your suppliers globally?
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Yes, my company has a supplier diversity program in place that applies to all suppliers globally
                                    </li>


                                    <li >
                                        No action/ Do not know
                                    </li>

                                </ul>
                            </div>


                            {getTableContent("Mandatory", "SUP613", "High  Impact on the Theme Score")}


                            <div className="para m-3">
                                <label className="fb5" > 	Has your company performed a formal assessment of its suppliers progress with regards to the REACH regulation?
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Yes, my company assesses its suppliers' progress with regards to the REACH requirements
                                    </li>
                                </ul>
                            </div>


                            {getTableContent("Mandatory", "SUP2034", "Medium  Impact on the Theme Score")}

                            <div className="para m-3">
                                <label className="fb5" >Has your company obtained any certification regarding sustainable procurement?
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Yes
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachment:
                                </label>
                                <ul className="pad30">

                                    <li >
                                        No
                                    </li>
                                </ul>
                            </div>

                            {getTableContent("Mandatory", "SUP621", "Medium  Impact on the Theme Score")}

                            <div className="para m-3">
                                <label className="fb5" >Has your company published a due diligence report on conflict minerals?
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Yes, my company has published a due diligence report on conflict minerals
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachment:
                                </label>
                                <ul className="pad30">

                                    <li >
                                        No
                                    </li>
                                </ul>
                            </div>

                            {getTableContent("Mandatory", "SUP711", "Medium  Impact on the Theme Score")}

                            <div className="para m-3">
                                <label className="fb5" > 	Does your company formally and publicly adhere to any external CSR initiatives or sets of principles?
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Responsible Minerals Initiative (RMI, formerly CFSI)
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachment:
                                </label>
                                <ul className="pad30">

                                    <li >
                                        World Gold Council (Conflict-Free Gold Standard)
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachment:
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Public-Private Alliance for Responsible Mineral Trade (PPA)
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachment:
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Association Connecting Electronics Industries (IPC)
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachment:
                                </label>
                                <ul className="pad30">

                                    <li >
                                        JEITA's Responsible Minerals Trade Working Group
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachment:
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Responsible Jewellery Council
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachment:
                                </label>
                                <ul className="pad30">

                                    <li >
                                        IT RI’s T in Supply Chain Initiative (iT SCi)
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachment:
                                </label>
                                <ul className="pad30">

                                    <li >
                                        T he London Bullion Market Association
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachment:
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Responsible Artisanal Gold Solutions Forum (RAGS)


                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachment:
                                </label>
                                <ul className="pad30">

                                    <li >
                                        European Partnership for Responsible Minerals
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachment:
                                </label>
                                <ul className="pad30">

                                    <li >
                                        No specific endorsement/ Do not know
                                    </li>
                                </ul>
                            </div>



                            {getTableContent("Mandatory", "SUP712", "Medium  Impact on the Theme Score")}

                            <div className="para m-3">
                                <label className="fb5" > 	What actions are implemented to identify and/or assess conflict mineral risk in the supply chain and mitigate such risk?
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Detailed conflict minerals risk analysis (per product or purchasing category) for downstream companies(e.g. manufacturers)
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachment:
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Grievance mechanism allowing any interested parties to voice and record concerns
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachment:
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Formal project or partnership with NGOs regarding conflict minerals (e.g. Enough Project, Pact, Global Witness)
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachment:
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Written communication sent to suppliers informing them of Conflict Minerals issues
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachment:
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Capacity building of suppliers on conflict minerals issues
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachment:
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Conflict Minerals taken into account in the supplier selection process
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachment:
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Inclusion of conflict minerals clauses in contractual agreements
                                    </li>
                                </ul>
                            </div>



                            {getTableContent("Mandatory", "SUP714", "Medium  Impact on the Theme Score")}

                            <div className="para m-3">
                                <label className="fb5" > 	What is the percentage of suppliers for which information regarding conflict minerals is available?
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Information regarding conflict minerals is available for less than 50% of suppliers
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachment:
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Information regarding conflict minerals is available for at least 50% of suppliers
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachment:
                                </label>
                                <ul className="pad30">

                                    <li >
                                        No information/Do not know
                                    </li>
                                </ul>
                            </div>



                            {getTableContent("Mandatory", "SUP6100", "High Impact on the Theme Score")}

                            <div className="para m-3">
                                <label className="fb5" > 	Please report on the following KPIs related to sustainable procurement (please specify the year)
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Percentage of targeted suppliers that have signed the sustainable procurement charter/supplier code of conduct
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachment:
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Percentage of targeted suppliers with contracts that include clauses on environmental, labor, and human rights requirements
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachment:
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Percentage of targeted suppliers that have gone through a CSR assessment (e.g. questionnaire)
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachment:
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Percentage of targeted suppliers that have gone through a CSR on-site audit
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachment:
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Percentage of buyers across all locations who have received training on sustainable procurement
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachment:
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Percentage or number of audited/assessed suppliers engaged in corrective actions or capacity building
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachment:
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Other CSR KPIs on suppliers
                                    </li>
                                </ul>
                            </div>


                            {getTableContent("Mandatory", "SUP710", "Medium Impact on the Theme Score")}

                            <div className="para m-3">
                                <label className="fb5" > Is there any tin, tantalum, tungsten, or gold remaining in the product that your company manufactures, subcontracts, or
                                    sells?

                                </label>
                                <ul className="pad30">

                                    <li >
                                        Yes
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachment:
                                </label>
                                <ul className="pad30">

                                    <li >
                                        T in, tantalum, tungsten or gold only originate from scrap or recycled sources
                                    </li>
                                    <li>
                                        No tin, tantalum, tungsten, or gold remaining in any product
                                    </li>
                                    <li>
                                        Do not know
                                    </li>
                                </ul>
                            </div>

                            {getTableContent("Mandatory", "SUP450", "Medium Impact on the Theme Score")}

                            <div className="para m-3">
                                <label className="fb5" > 	Has your company obtained any certification regarding sustainable procurement?
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Yes
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachment:
                                </label>
                                <ul className="pad30">

                                    <li >
                                        No/Do not know
                                    </li>
                                </ul>
                            </div>

                            {getTableContent("Mandatory", "SUP622", "High Impact on the Theme Score")}

                            <div className="para m-3">
                                <label className="fb5" > Please report on the following KPI regarding Scope 3 GHG emissions of your supply chain (please specify the year)
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Total Scope 3 GHG emissions in metric tons of CO2e
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachment:
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Reporting available at parent company level only
                                    </li>
                                </ul>
                            </div>

                            {getTableContent("Mandatory", "CAR106", "Medium Impact on the Theme Score")}

                            <div className="para m-3">
                                <label className="fb5" > How does your entity monitor scope 3 emissions?
                                </label>
                                <ul className="pad30">

                                    <li >
                                        We screen scope 3 categories to identify the most relevant for our activity
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachment:
                                </label>
                                <ul className="pad30">

                                    <li >
                                        We use industry-average-data to estimate our scope 3 emissions
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachment:
                                </label>
                                <ul className="pad30">

                                    <li >
                                        We collect primary scope 3 data from internal stakeholders
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachment:
                                </label>
                                <ul className="pad30">

                                    <li >
                                        We collect primary scope 3 data from suppliers
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachment:
                                </label>
                                <ul className="pad30">

                                    <li >
                                        We do not monitor scope 3 emissions yet/Do not know
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3" style={{ textAlign: 'center', fontWeight: 'bold' }}>
                                Ethics
                            </div>

                            {getTableContent("Mandatory", "FB100", "High Impact on the Theme Score")}

                            <div className="para m-3">
                                <label className="fb5" > Has your company implemented a formal policy covering any of the following topics?
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Corruption
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachment:
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Conflict of interest
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachment:
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Fraud
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachment:
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Money laundering
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachment:
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Anti-competitive practices
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachment:
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Information security
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachment:
                                </label>
                                <ul className="pad30">

                                    <li >
                                        No policy
                                    </li>
                                </ul>
                            </div>

                            {getTableContent("Mandatory", "FB3102", "High Impact on the Theme Score")}

                            <div className="para m-3">
                                <label className="fb5" >What actions are in place to prevent corruption and bribery?
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Awareness training performed to prevent corruption and bribery
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachment:
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Anti-corruption due diligence program on third parties in place
                                    </li>
                                </ul>
                            </div>


                            <div className="para m-3">
                                <label className="fb5" > Attachment:
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Whistleblower procedure for stakeholders to report corruption and bribery
                                    </li>
                                </ul>
                            </div>


                            <div className="para m-3">
                                <label className="fb5" > Attachment:
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Corruption risk assessments performed
                                    </li>
                                </ul>
                            </div>


                            <div className="para m-3">
                                <label className="fb5" > Attachment:
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Audits of control procedures (e.g. accounting, purchasing etc.) to prevent corruption and bribery
                                    </li>
                                </ul>
                            </div>


                            <div className="para m-3">
                                <label className="fb5" > Attachment:
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Specific approval procedure for sensitive transactions (e.g. gifts, travel)
                                    </li>
                                </ul>
                            </div>



                            <div className="para m-3">
                                <label className="fb5" > Attachment:
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Other actions to prevent any form of corruption not included elsewhere in this question
                                    </li>
                                </ul>
                            </div>


                            <div className="para m-3">
                                <label className="fb5" > Attachment:
                                </label>
                                <ul className="pad30">

                                    <li >
                                        No actions in place/Do not know
                                    </li>
                                </ul>
                            </div>

                            {getTableContent("Mandatory", "FB3201", "High Impact on the Theme Score")}

                            <div className="para m-3">
                                <label className="fb5" >	What actions are in place to prevent anticompetitive practices?
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Awareness training performed to prevent anti-competitive practices
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachment:
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Whistleblower procedure for stakeholders to report anti-competitive practices
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachment:
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Anti-competitive practices risk assessments performed
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachment:
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Audits of control procedures to prevent anti-competitive practices
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachment:
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Provision of competitor interaction guidelines to key employees
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachment:
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Other actions to prevent anticompetitive practices not included elsewhere in this question
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachment:
                                </label>
                                <ul className="pad30">

                                    <li >
                                        No actions in place/Do not know
                                    </li>
                                </ul>
                            </div>

                            {getTableContent("Mandatory", "FB330", "High Impact on the Theme Score")}

                            <div className="para m-3">
                                <label className="fb5" >	What actions are in place regarding information security?
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Awareness training to prevent information security breaches
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachment:
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Information security due diligence program on third parties in place
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachment:
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Whistleblower procedure for stakeholders to report information security concerns
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachment:
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Information security risk assessments performed
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachment:
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Audits of control procedures to prevent information security breaches
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachment:
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Incident response procedure (IRP) to manage breaches of confidential information
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachment:
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Implementation of a records retention schedule
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachment:
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Measures to protect third party data from unauthorized access or disclosure


                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachment:
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Measures for gaining stakeholder consent regarding the processing, sharing and retention of confidential information
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachment:
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Other actions to ensure information security not included elsewhere in this question


                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachment:
                                </label>
                                <ul className="pad30">

                                    <li >
                                        No actions in place/Do not know
                                    </li>
                                </ul>
                            </div>

                            {getTableContent("Mandatory", "FB4501", "Medium  Impact on the Theme Score")}

                            <div className="para m-3">
                                <label className="fb5" >		Has your company obtained external certifications related to business ethics issues (e.g. anti-corruption, information security)?
                                </label>
                                <ul className="pad30">

                                    <li >
                                        ISO 27000
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachment:
                                </label>
                                <ul className="pad30">

                                    <li >
                                        RCMS (Responsible Care Management System)
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachment:
                                </label>
                                <ul className="pad30">

                                    <li >
                                        RC14001 (Responsible Care 14001)
                                    </li>
                                </ul>
                            </div>




                            <div className="para m-3">
                                <label className="fb5" > Attachment:
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Other anti-corruption certification schemes (e.g. Ethic Intelligence, Trace, etc)
                                    </li>
                                </ul>
                            </div>




                            <div className="para m-3">
                                <label className="fb5" > Attachment:
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Other information security certification schemes (e.g. Cyber Essentials etc.)
                                    </li>
                                </ul>
                            </div>




                            {getTableContent("Mandatory", "FB610", "High Impact on the Theme Score")}

                            <div className="para m-3">
                                <label className="fb5" >	Please report on the following Key Performance Indicators (KPIs) related to ethics
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Percentage of total workforce trained (e.g. through e-learning) on business ethics issues
                                    </li>
                                </ul>
                            </div>


                            <div className="para m-3">
                                <label className="fb5" > Attachment:
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Number of reports related to whistleblower procedure
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachment:
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Number of confirmed corruption incidents
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachment:
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Number of confirmed information security incidents
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachment:
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Percentage of all operational sites for which an internal audit/risk assessment concerning business ethics issues has been conducted
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachment:
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Percentage of all operational sites with certified anti-corruption management system
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachment:
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Percentage of all operational sites with an information security management system (ISMS) certified to ISO 27000 (or other equivalent/similar standard)
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachment:
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Other KPIs on ethics
                                    </li>
                                </ul>
                            </div>

                            <div className="para m-3">
                                <label className="fb5" > Attachment:
                                </label>
                                <ul className="pad30">

                                    <li >
                                        Not monitored/Do not know
                                    </li>
                                </ul>
                            </div>

                            {getTableContent("Optional", "FB800")}

                            <div className="para m-3">
                                <label className="fb5" >	•	Additional comments regarding Ethics of your company's management system. Please also indicate any CSR-related external recognition obtained by your company within the last five years, such as CSR-related awards, or listing in CSR rankings or indexes.
                                </label>
                               
                               <div className="display-box"></div>
                            </div>




                        </div>
                    </div>
                </div>
            </div>
        </div>
    );

};

const comparisonFn = function (prevProps, nextProps) {
    return prevProps.location.pathname === nextProps.location.pathname;
};

export default React.memo(Ecovadis, comparisonFn);
