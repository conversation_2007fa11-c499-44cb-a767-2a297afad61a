// PrintSupplierReport.js
import React, { useRef } from 'react';
import ReactToPrint from 'react-to-print';
import SupplierReport from './SupplierReport';


function PrintSupplierReport() {
  const reportRef = useRef();

  return (
    <div>
      <ReactToPrint
        trigger={() => <button>Print or Save as PDF</button>}
        content={() => reportRef.current}
        documentTitle="SupplierReport"
      />
      {/* Render your main report here */}
      <SupplierReport ref={reportRef} />
    </div>
  );
}

export default PrintSupplierReport;
