import React, { useEffect, useRef } from "react";
import * as d3 from "d3";

const orgData = {
  name: "Board of Directors",
  children: [
    {
      name: "CEO",
      children: [{ name: "<PERSON><PERSON>" }, { name: "<PERSON><PERSON>" }, { name: "<PERSON><PERSON>" }],
    },
    {
      name: "Audit Committee",
      children: [
        { name: "Chair - Audit Committee" },
        { name: "Member - Audit Committee" },
      ],
    },
    {
      name: "Compensation Committee",
      children: [
        { name: "Chair - Compensation Committee" },
        { name: "Member - Compensation Committee" },
      ],
    },
    {
      name: "Risk Committee",
      children: [
        { name: "Chair - Risk Committee" },
        { name: "Member - Risk Committee" },
      ],
    },
  ],
};

const OrgChart = () => {
  const chartRef = useRef(null);

  useEffect(() => {
    renderOrgChart();
  }, []);

  const renderOrgChart = () => {
    const width = 800;
    const height = 600;
    const margin = { top: 20, right: 30, bottom: 40, left: 40 };

    // Clear any existing SVG before rendering the new chart
    d3.select(chartRef.current).select("svg").remove();

    const svg = d3
      .select(chartRef.current)
      .append("svg")
      .attr("width", width)
      .attr("height", height)
      .append("g")
      .attr("transform", `translate(${margin.left},${margin.top})`);

    // Create a tree layout
    const treeLayout = d3
      .tree()
      .size([
        height - margin.top - margin.bottom,
        width - margin.left - margin.right,
      ]);

    // Hierarchy structure from the data
    const root = d3.hierarchy(orgData);

    // Generate the tree layout
    treeLayout(root);

    // Create links (lines connecting nodes)
    svg
      .selectAll(".link")
      .data(root.links())
      .enter()
      .append("path")
      .attr("class", "link")
      .attr(
        "d",
        (d) => `
        M${d.source.y},${d.source.x}
        C${d.source.y + 50},${d.source.x}
         ${d.target.y - 50},${d.target.x}
         ${d.target.y},${d.target.x}`
      )
      .style("fill", "none")
      .style("stroke", "#ccc")
      .style("stroke-width", 2);

    // Create nodes (circle elements for each role)
    const nodes = svg
      .selectAll(".node")
      .data(root.descendants())
      .enter()
      .append("g")
      .attr("class", "node")
      .attr("transform", (d) => `translate(${d.y},${d.x})`);

    nodes.append("circle").attr("r", 10).style("fill", "#4caf50"); // Green color for nodes

    nodes
      .append("text")
      .attr("dy", -15)
      .attr("text-anchor", "middle")
      .style("font-size", "12px")
      .style("fill", "#333")
      .text((d) => d.data.name);

    // Add labels for each role (i.e., position title)
    nodes
      .append("text")
      .attr("dy", 15)
      .attr("text-anchor", "middle")
      .style("font-size", "10px")
      .style("fill", "#555")
      .text((d) => d.data.name.split(" ")[0]); // Display role name only (e.g., "CEO", "CFO")
  };

  return (
    <div>
      <div
        style={{
          fontFamily: "Lato",
          fontSize: "16px",
          fontWeight: 700,
          lineHeight: "19.2px",
          textAlign: "left",
          margin: "18px 10px 18px 10px",
        }}
      >
        {" "}
        Organizational Chart
      </div>
      <div ref={chartRef}></div>
    </div>
  );
};

export default OrgChart;
