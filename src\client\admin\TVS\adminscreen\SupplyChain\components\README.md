# Chart Sorting Components

This directory contains reusable components for adding sorting functionality to charts in the application.

## ChartSortDropdown Component

The `ChartSortDropdown` component provides a standardized way to add sorting functionality to any chart component. It includes:

1. A dropdown UI component for selecting sort options
2. Helper functions for creating sort options
3. A sorting function that can be used to sort data

## How to Use

### 1. Import the necessary components

```jsx
import { ChartSortDropdown, createSortOptions, sortData } from "./components/ChartSortDropdown";
```

### 2. Set up state for sorting

```jsx
// Sorting state
const [sortField, setSortField] = useState('none');
const [sortOrder, setSortOrder] = useState(1); // 1 for ascending, -1 for descending
```

### 3. Define sort options using the helper function

```jsx
// Sorting options
const sortOptions = createSortOptions([
  { name: 'category', label: 'Category', type: 'string' },
  { name: 'totalSuppliers', label: 'Total Suppliers', type: 'number' },
  { name: 'auditedUnderMSI', label: 'Audited Suppliers', type: 'number' },
  { name: 'auditedUnderMSIPercent', label: 'Audit Percentage', type: 'number' },
]);
```

### 4. Create a handler for sort changes

```jsx
// Handle sort change
const handleSortChange = (e) => {
  const selectedSort = e.value;
  const [, direction] = selectedSort.split('_');
  
  setSortField(selectedSort);
  setSortOrder(direction === 'asc' ? 1 : -1);
  
  // Apply sorting to the data
  const sortedData = sortData(data, selectedSort);
  setData(sortedData);
};
```

### 5. Add the dropdown to your UI

```jsx
<ChartSortDropdown
  value={sortField}
  options={sortOptions}
  onChange={handleSortChange}
/>
```

### 6. Apply sorting when sort field or order changes (optional)

```jsx
// Apply sorting when sort field or order changes
useEffect(() => {
  if (sortField !== 'none') {
    const sortedData = sortData(initialData, sortField);
    setData(sortedData);
  }
}, [sortField, sortOrder]);
```

## Example Implementation

See `FirstBarDemo.js` for a complete example of how to implement sorting in a chart component.

## API Reference

### ChartSortDropdown Component

| Prop | Type | Description |
|------|------|-------------|
| value | string | Current sort value |
| options | array | Array of sort options |
| onChange | function | Function to call when sort option changes |
| style | object | Additional styles for the dropdown |

### createSortOptions Function

```jsx
createSortOptions(fields)
```

| Parameter | Type | Description |
|-----------|------|-------------|
| fields | array | Array of field objects with name, label, and type properties |

Returns an array of sort options.

### sortData Function

```jsx
sortData(data, sortField)
```

| Parameter | Type | Description |
|-----------|------|-------------|
| data | array | Array of data objects to sort |
| sortField | string | Field to sort by in format 'fieldName_direction' |

Returns a sorted array of data objects.
