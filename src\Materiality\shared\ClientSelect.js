import React, { useEffect, useState } from "react";
import CreatableSelect from 'react-select/creatable';
import { STANDARDS_URL } from "../constants";
import { API } from "../../constants/api_url";

const ClientSelect = (props) => {

    const [optionsValue, setOptionsValue] = useState([])
console.log(props);
    useEffect(() => {
        getOptions()
    }, [props.values])

    const  getOptions = async () => {
        const response = await fetch(API.UserProfile);
        if(response.ok) {
          const data = await response.json();
            setOptionsValue(data.filter(i=> i.role === "clientadmin" ).map((i) => {
              return {label: i.information.enterpriseid, value: i.id}

          }).filter(i => i.label && i.label.length !== 0 ) )
          //   setTopics(data)
        }
      }

    const handleChange = (newValue, actionMeta) => {

      console.log();
        props.onChangeValues(newValue);
        
        
    }

    const handleAddOptions = async (optionValue) => {
        
        const response = await fetch(STANDARDS_URL, {
          method: 'POST',
          body: JSON.stringify({
            name: optionValue,
            editable: 1,
            sasb: 1
           
          }),
          headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json'
          },
        });
  
        const data = await response.json()
        const newData = { label: data.name, value: data.id};
        setOptionsValue((prevState) => [...prevState, newData])

       
    }

    return (
        <>
            <CreatableSelect
                    isMulti
                    onChange={handleChange}
                    // onCreateOption={handleAddOptions}
                    options={optionsValue}
                    value={optionsValue.filter(i => props.values &&  props.values.includes( i.value))}
                  />
        </>
    )
}

export default ClientSelect;