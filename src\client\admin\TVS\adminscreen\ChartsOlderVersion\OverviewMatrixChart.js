import React, { useEffect, useRef, useState } from "react";
import * as d3 from "d3";
import { Checkbox } from "@material-ui/core";
import { DataTable } from "primereact/datatable";
import { Column } from "primereact/column";

const matrixData = [
  {
    gender: "Male",
    ageGroup: "18-30",
    executive: 5,
    independent: 2,
    nonExecutive: 3,
  },
  {
    gender: "Female",
    ageGroup: "18-30",
    executive: 3,
    independent: 1,
    nonExecutive: 2,
  },
  {
    gender: "Male",
    ageGroup: "31-40",
    executive: 7,
    independent: 4,
    nonExecutive: 3,
  },
  {
    gender: "Female",
    ageGroup: "31-40",
    executive: 4,
    independent: 3,
    nonExecutive: 1,
  },
  {
    gender: "Male",
    ageGroup: "41-50",
    executive: 6,
    independent: 2,
    nonExecutive: 4,
  },
  {
    gender: "Female",
    ageGroup: "41-50",
    executive: 5,
    independent: 3,
    nonExecutive: 2,
  },
];

const OverviewMatrixChart = () => {
  const chartRef = useRef(null);
  const [activeMode, setActiveMode] = useState(true);
  const [selectedRole, setSelectedRole] = useState("executive");

  useEffect(() => {
    if (activeMode) {
      renderMatrixChart();
    }
  }, [activeMode, selectedRole]);

  const renderMatrixChart = () => {
    const width = 600;
    const height = 400;
    const margin = { top: 50, right: 30, bottom: 40, left: 40 };

    // Clear any existing SVG
    d3.select(chartRef.current).select("svg").remove();

    const svg = d3
      .select(chartRef.current)
      .append("svg")
      .attr("width", width)
      .attr("height", height);

    const roleData = matrixData.map((d) => ({
      gender: d.gender,
      ageGroup: d.ageGroup,
      value: d[selectedRole], // Use selected role data (executive, independent, or non-executive)
    }));

    const x = d3
      .scaleBand()
      .domain(roleData.map((d) => d.ageGroup))
      .range([margin.left, width - margin.right])
      .padding(0.1);

    const y = d3
      .scaleBand()
      .domain(roleData.map((d) => d.gender))
      .range([margin.top, height - margin.bottom])
      .padding(0.1);

    const colorScale = d3
      .scaleSequential(d3.interpolateBlues)
      .domain([0, d3.max(roleData, (d) => d.value)]);

    svg
      .append("g")
      .selectAll("rect")
      .data(roleData)
      .enter()
      .append("rect")
      .attr("x", (d) => x(d.ageGroup))
      .attr("y", (d) => y(d.gender))
      .attr("width", x.bandwidth())
      .attr("height", y.bandwidth())
      .attr("fill", (d) => colorScale(d.value))
      .append("title")
      .text((d) => `${d.gender}, ${d.ageGroup}: ${d.value} ${selectedRole}`);

    svg
      .append("g")
      .selectAll(".x-axis")
      .data([roleData])
      .enter()
      .append("g")
      .attr("transform", `translate(0,${height - margin.bottom})`)
      .call(d3.axisBottom(x));

    svg
      .append("g")
      .selectAll(".y-axis")
      .data([roleData])
      .enter()
      .append("g")
      .attr("transform", `translate(${margin.left}, 0)`)
      .call(d3.axisLeft(y));
  };

  const handleRoleChange = (role) => {
    setSelectedRole(role);
  };

  return (
    <>
      <div style={{ display: "flex", justifyContent: "space-between" }}>
        <div style={{ fontFamily: "Lato", fontSize: "16px", fontWeight: 700 }}>
          Diversity Matrix Chart
        </div>
        {/* <div style={{ display: "flex" }}>
          <Checkbox
            checked={selectedRole === "executive"}
            onChange={() => handleRoleChange("executive")}
            label="Executive"
            style={{ marginRight: "10px" }}
          />
          <Checkbox
            checked={selectedRole === "independent"}
            onChange={() => handleRoleChange("independent")}
            label="Independent"
            style={{ marginRight: "10px" }}
          />
          <Checkbox
            checked={selectedRole === "nonExecutive"}
            onChange={() => handleRoleChange("nonExecutive")}
            label="Non-Executive"
          />
        </div> */}
      </div>

      {/* Matrix Chart */}
      {activeMode && (
        <div
          style={{
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            height: "400px",
          }}
          ref={chartRef}
        />
      )}

      {/* Data Table */}
      {!activeMode && (
        <DataTable value={matrixData}>
          <Column header="Gender" field="gender" />
          <Column header="Age Group" field="ageGroup" />
          <Column header="Executive" field="executive" />
          <Column header="Independent" field="independent" />
          <Column header="Non-Executive" field="nonExecutive" />
        </DataTable>
      )}
    </>
  );
};

export default OverviewMatrixChart;
