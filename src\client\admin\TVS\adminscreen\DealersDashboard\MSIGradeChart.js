import React from "react";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  Tooltip,
  Cell,
  ResponsiveContainer,
} from "recharts";


const getMSIGrade = (score) => {
  if (score >= 85) return "Platinum";
  if (score >= 71) return "Gold";
  if (score >= 56) return "Silver";
  return "Not Met";
};

const MSIGradeChart = ({ data }) => {
  const aggregateData = (data) => {
    const branchGrades = {};
    const gradeColors = {
      Platinum: "#C0C0C0",
      Gold: "#FFD700",
      Silver: "#E0E0E0",
      "Not Met": "#FF2E00",
    };

    data.forEach((entry) => {
      const score = entry.score || 0;
      const grade = getMSIGrade(score);
      const branchCode = entry.branch_code;

      if (!branchGrades[branchCode]) {
        branchGrades[branchCode] = grade;
      }
    });

    const gradeCounts = {};
    Object.values(branchGrades).forEach((grade) => {
      if (!gradeCounts[grade]) {
        gradeCounts[grade] = 0;
      }
      gradeCounts[grade] += 1;
    });

    return Object.keys(gradeCounts).map((grade) => ({
      name: grade,
      value: gradeCounts[grade],
      color: gradeColors[grade] || "#888888",
    }));
  };

  const chartData = aggregateData(data);

  const renderCustomizedLabel = ({ value, x, y }) => (
    <text
      x={x}
      y={y}
      fill="black"
      textAnchor="middle"
      dominantBaseline="central"
      fontSize={12}
      fontWeight={600}
    >
      {value}
    </text>
  );

  return (
    <div style={{ display: "flex", flexDirection: "column", alignItems: "center",marginTop:"20px" }}>
      <h3 style={{ textAlign: "center", color: "#555" }}>MSI Grade</h3>

      <ResponsiveContainer width={600} height={380}>
        <PieChart>
          <Pie
            data={chartData}
            dataKey="value"
            nameKey="name"
            label={renderCustomizedLabel}
          >
            {chartData.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={entry.color} />
            ))}
          </Pie>
          <Tooltip />
        </PieChart>
      </ResponsiveContainer>

      <div className="legend" style={{ display: "flex", justifyContent: "center" }}>
        {["Platinum", "Gold", "Silver", "Not Met"].map((grade) => (
          <div key={grade} className="me-3">
            <span
              style={{
                backgroundColor: {
                  Platinum: "#C0C0C0",
                  Gold: "#FFD700",
                  Silver: "#E0E0E0",
                  "Not Met": "#FF2E00",
                }[grade],
                display: "inline-block",
                width: "10px",
                height: "10px",
                borderRadius: "50%",
                marginRight: "5px",
              }}
            ></span>
            {grade}
          </div>
        ))}
      </div>
    </div>
  );
};

export default MSIGradeChart;
