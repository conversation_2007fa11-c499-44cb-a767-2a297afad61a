import React, { useEffect, useRef, useState } from "react";
import * as d3 from "d3";
import { Checkbox } from "@mui/material";

const committeeData = [
  { committee: "Audit", independent: 4, nonIndependent: 2 },
  { committee: "Compensation", independent: 3, nonIndependent: 3 },
  { committee: "Nominations", independent: 5, nonIndependent: 1 },
  { committee: "Risk", independent: 2, nonIndependent: 4 },
];

const DirectorCompositionByCommittee = () => {
  const chartRef = useRef(null);
  const [visibleCategories, setVisibleCategories] = useState({
    independent: true,
    nonIndependent: true,
  });

  useEffect(() => {
    renderBarChart();
  }, [visibleCategories]); // Re-render when visibleCategories state changes

  const renderBarChart = () => {
    const width = 600;
    const height = 400;
    const margin = { top: 20, right: 30, bottom: 50, left: 40 };
    const chartWidth = width - margin.left - margin.right;
    const chartHeight = height - margin.top - margin.bottom;

    // Clear any existing SVG before rendering the new chart
    d3.select(chartRef.current).select("svg").remove();

    const svg = d3
      .select(chartRef.current)
      .append("svg")
      .attr("width", width)
      .attr("height", height)
      .append("g")
      .attr("transform", `translate(${margin.left},${margin.top})`);

    // Subtle colors for Independent and Non-Independent categories
    const color = d3
      .scaleOrdinal()
      .domain(["Independent", "Non-Independent"])
      .range(["#8bc34a", "#f28d62"]); // Soft green for Independent, Soft red for Non-Independent

    // Prepare data based on the visible categories
    const data = committeeData.map((d) => ({
      committee: d.committee,
      independent: visibleCategories.independent ? d.independent : 0,
      nonIndependent: visibleCategories.nonIndependent ? d.nonIndependent : 0,
    }));

    // Set up scales
    const x = d3
      .scaleBand()
      .domain(data.map((d) => d.committee))
      .range([0, chartWidth])
      .padding(0.3);
    const y = d3
      .scaleLinear()
      .domain([0, d3.max(data, (d) => d.independent + d.nonIndependent)])
      .nice()
      .range([chartHeight, 0]);

    // Add the bars for each category (Independent and Non-Independent)
    svg
      .selectAll(".bar")
      .data(data)
      .enter()
      .append("g")
      .attr("class", "bar")
      .attr("transform", (d) => `translate(${x(d.committee)},0)`)
      .each(function (d) {
        d3.select(this)
          .append("rect")
          .attr("x", 0)
          .attr("y", y(d.independent + d.nonIndependent))
          .attr("width", x.bandwidth())
          .attr("height", (d) => chartHeight - y(d.independent))
          .attr("fill", color("Independent"));

        d3.select(this)
          .append("rect")
          .attr("x", 0)
          .attr("y", y(d.nonIndependent))
          .attr("width", x.bandwidth())
          .attr("height", (d) => chartHeight - y(d.nonIndependent))
          .attr("fill", color("Non-Independent"));
      });

    // Add axis labels
    svg
      .append("g")
      .selectAll(".x-axis")
      .data(data)
      .enter()
      .append("text")
      .attr("x", (d) => x(d.committee) + x.bandwidth() / 2)
      .attr("y", chartHeight + 20)
      .style("text-anchor", "middle")
      .text((d) => d.committee)
      .style("font-size", "14px")
      .style("fill", "#333");

    svg
      .append("g")
      .selectAll(".y-axis")
      .data([0, d3.max(data, (d) => d.independent + d.nonIndependent)])
      .enter()
      .append("text")
      .attr("x", -10)
      .attr("y", (d, i) => y(d) - i * 20)
      .style("text-anchor", "middle")
      .text((d) => d)
      .style("font-size", "12px")
      .style("fill", "#333");

    // Add labels inside bars
    svg
      .selectAll(".label")
      .data(data)
      .enter()
      .append("text")
      .attr("x", (d) => x(d.committee) + x.bandwidth() / 2)
      .attr(
        "y",
        (d) =>
          y(d.independent + d.nonIndependent) +
          (chartHeight - y(d.independent + d.nonIndependent)) / 2
      )
      .attr("dy", ".35em")
      .style("text-anchor", "middle")
      .style("font-size", "14px")
      .style("fill", "#fff")
      .text((d) => `${d.independent} / ${d.nonIndependent}`);

    // Tooltip effect
    svg
      .selectAll("rect")
      .on("mouseover", (event) => {
        d3.select(event.currentTarget)
          .style("opacity", 0.7)
          .style("cursor", "pointer");
      })
      .on("mouseout", (event) => {
        d3.select(event.currentTarget).style("opacity", 1);
      });
  };

  const handleCheckboxChange = (category) => {
    setVisibleCategories((prevState) => ({
      ...prevState,
      [category]: !prevState[category],
    }));
  };

  return (
    <div>
      <div
        style={{
          fontFamily: "Lato",
          fontSize: "16px",
          fontWeight: 700,
          marginBottom: "10px",
        }}
      >
        Director Composition by Committee
        <div style={{ fontWeight: 200, fontSize: "14px" }}>
          Compare the ratio of independent and non-independent directors across
          different committees.
        </div>
      </div>
      <div ref={chartRef} style={{ textAlign: "center" }} />

      {/* Legends */}
      <div style={{ textAlign: "center", marginTop: "20px" }}>
        <div style={{ display: "inline-block", marginRight: "20px" }}>
          <Checkbox
            checked={visibleCategories["independent"]}
            onChange={() => handleCheckboxChange("independent")}
            style={{ color: "#8bc34a", marginRight: 4, fontSize: "20px" }}
          />
          <span style={{ color: "#555", fontSize: "14px" }}>
            Independent Directors
          </span>
        </div>
        <div style={{ display: "inline-block" }}>
          <Checkbox
            checked={visibleCategories["nonIndependent"]}
            onChange={() => handleCheckboxChange("nonIndependent")}
            style={{ color: "#f28d62", marginRight: 4, fontSize: "20px" }}
          />
          <span style={{ color: "#555", fontSize: "14px" }}>
            Non-Independent Directors
          </span>
        </div>
      </div>
    </div>
  );
};

export default DirectorCompositionByCommittee;
