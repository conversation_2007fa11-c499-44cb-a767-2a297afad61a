import Axios from "axios";
import React, { useEffect, useState, useRef } from "react";
import { useSelector } from "react-redux";
import useForceUpdate from "use-force-update";
import { Dropdown } from "primereact/dropdown";
import { EditText } from "react-edit-text";
import { Button } from "primereact/button";
import { InputText } from "primereact/inputtext";
import { CascadeSelect } from "primereact/cascadeselect";
import Swal from "sweetalert2";
import { Checkbox } from "primereact/checkbox";
import $ from "jquery";
import { API } from "../../../../constants/api_url";

import { ContextMenu } from 'primereact/contextmenu';
import { Tooltip } from "primereact/tooltip";
import moment from "moment";
import { Calendar } from "primereact/calendar";
import { RadioButton } from "primereact/radiobutton";
import { InputTextarea } from 'primereact/inputtextarea'
import { useHistory, useLocation, useParams } from "react-router-dom";
import { Tag } from "primereact/tag";

import { Editor } from "primereact/editor";
import { InputSwitch } from "primereact/inputswitch";
import { Slider } from "primereact/slider";
import { BGSQ14, BGSQ15, BGSQ21, BGSQ24, BP1EQ2, BP4EQ2, BP7EQ1B, BP7EQ2, BP7LQ1, BP8EQ1, BP8LQ1, BP8LQ2, BP8LQ4, BP8LQ5, BP8LQ6, BP9LQ1, BP2LQ1, BP2LQ2, BP6EQ10, BP6EQ11, BP6EQ12, BP6LQ6, SGXGSQ7, SGXGSQ6, SGXGSQ4 } from "../../../hardcoded/hardcodedRF";
import { hardcoded } from "../../../constants/hardcodedid";
import APIServices from "../../../../service/APIService";
import { getReportingFiscalYearByReportingperiod, getRPTextFormat } from "../../../../components/BGHF/helper";
import { DateTime } from "luxon";
import { InputNumber } from "primereact/inputnumber";
import { ReadMoreComponent } from "../../../../components/Forms/ReadMoreComponent";
window.jQuery = $;
window.$ = $;

const TVSDFInputEntryApproval = () => {
    const login_data = useSelector((state) => state.user.userdetail);
    const admin_data = useSelector((state) => state.user.admindetail)
    const [data, setData] = useState([])

    const [response, setResponse] = useState({})
    const { fymonth } = useSelector((state) => state.user.fyStartMonth);

    const [sitelist, setSiteList] = useState([])
    const navigate = useHistory()
    const forceUpdate = useForceUpdate();
    const userList = useSelector(state => state.userlist.userList)

    const { submitId: id, params } = JSON.parse(sessionStorage.getItem('dfapproval'))

    // const hardcodedrf = ['85', '89', '102', '104', '110', '111', '112', '113', '114', '115', '116', '117', '118', '121', '133', '134', '139', '140','148','149','150','151','181','182','183']
    const [show, setShow] = useState(true)
    useEffect(async () => {
        console.log(params.state)
        APIServices.get(API.RF_Edit(params.state.data.data1[0].rf)).then((res) => {
            if (id) {

                APIServices.get(API.QL_Approval_Edit(id)).then((res2) => {
                    console.log(res2)
                    setResponse(res2.data)
                    if (res2.data.form_type === 1) {
                        console.log(res2.data)
                        res2.data.response = migrateResponse(res.data.data1, res2.data.response)

                    }
                    setShow(true)
                    setData(res2.data)

                })
            } else {
                setShow(false)
                res.data.response = res.data.data1
                setData(res.data)
            }
        })


    }, [login_data]);

    const getRP = (months) => {
        if (months.includes('to')) {
            let startDate = moment(months.split('to')[0].trim())
            let endDate = moment(months.split('to')[1].trim())
            let rp = []
            while (startDate <= endDate) {

                rp.push(startDate.format('MM-YYYY'));
                startDate.add(1, 'month');


            }
            return rp
        } else {
            return [moment(months).format('MM-YYYY')]
        }
    }
    const checkHardcoded = () => {
        console.log(id)
        if (hardcoded.rf.includes(id)) {
            return true
        } else {
            return false
        }

    }
    const migrateResponse = (formData, oldData) => {
        console.log(oldData)
        formData.forEach((i) => {
            let index = oldData.findIndex((j) => { return getType(j.type) === i.type && i.name === j.name })
            console.log(index)
            if (index !== -1) {
                if (i.type !== 'checkbox-group' && i.type !== 'radio-group') {

                    i.value = oldData[index].value
                } else {

                    i.values.forEach((k, l) => {
                        k.selected = oldData[index].value.includes(k.value)
                    })
                }
            }
        })
        console.log(formData)
        return formData
    }
    const onCheckBoxSelected = (item, cbind) => {
        console.log(item)
        item.values.map((items, ind) => {
            if (ind === cbind) {

                items.selected = !items.selected
            }
        })
        forceUpdate()
    }
    const onRadioButtonSelected = (item, cbind) => {
        console.log(item)
        item.values.map((items, ind) => {
            if (ind === cbind) {

                items.selected = true
            } else {
                items.selected = false
            }
        })
        forceUpdate()
    }
    const onDateSelected = (item, val) => {

        item.value = val;
        forceUpdate()
    }
    const onNumberChange = (item, val, nan) => {
        console.log(item, val)

        item.value = val;


        forceUpdate()
    }

    const onChangeDropwdown = (item, val) => {
        item.value = val;
        console.log(val)
        item.values.forEach((i) => {
            if (i.label === val) {
                i.selected = true
            } else {
                i.selected = false
            }
        })
        forceUpdate()
    }
    const renderItems = (item, index) => {


        if (item.type === 'checkbox-group') {

            return (
                <div className="flex flex-wrap  gap-3  grid" style={{ padding: 10, border: item.error === 1 && '1px solid red' }}>
                    <Tooltip target={".tooltip" + index} position='top' />
                    <label className='col-5 text-justify fs-16 fw-5'>{item.label.replace(/(<([^>]+)>)/gi, "")
                        .replace(/\n/g, " ")
                        .replace(/&nbsp;/g, " ").replace('&amp;', '&')} {item.required && <span className="mandatory mr-2">*</span>}
                        {item.description !== undefined && item.description.trim().length !== 0 && <i style={{

                            marginLeft: '5px'
                        }} className={`material-icons fs-14 tooltip` + index} data-pr-tooltip={`${item.description}`}


                        > help</i>} </label>
                    <div className="col-5">
                        {item.values.map((cb, cbind) => {
                            return (
                                <div className="flex text-justify fs-14 fw-5" style={{ marginBottom: 10 }}>
                                    <Checkbox disabled={data.type === 3} inputId={"cb" + index + cbind} name={cb.label} value={cb.value} onChange={(e) => { onCheckBoxSelected(item, cbind) }} checked={cb.selected} />
                                    <label htmlFor={"cb" + index + cbind} className="ml-2">{cb.label}</label>
                                </div>
                            )
                        })

                        }
                    </div>

                </div>
            )
        } else if (item.type === 'date') {

            return (
                <div className="flex flex-wrap  gap-3  grid" style={{ padding: 10, border: item.error === 1 && '1px solid red' }}>
                    <Tooltip target={".tooltip" + index} position='top' />
                    <label className='col-5 fs-16 fw-5 text-justify'> {item.label.replace(/(<([^>]+)>)/gi, "")
                        .replace(/\n/g, " ")
                        .replace(/&nbsp;/g, " ").replace('&amp;', '&')} {item.required && <span className="mandatory mr-2">*</span>}
                        {item.description !== undefined && item.description.trim().length !== 0 && <span><i style={{

                            marginLeft: '5px'
                        }} className={`material-icons fs-14 tooltip` + index} data-pr-tooltip={`${item.description}`}


                        > help</i></span>} </label>
                    <Calendar placeholder={item.placeholder} disabled={data.type === 3} className="col-5 fs-14 fw-4" value={(item.value !== null && item.value !== undefined) ? moment(item.value).toDate() : null} onChange={(e) => { onDateSelected(item, e.value) }} />
                </div>
            )
        } else if (item.type === 'number') {
            return (
                <div className="flex flex-wrap  gap-3 grid" style={{ padding: 10, border: item.error === 1 && '1px solid red' }}>
                    <Tooltip target={".tooltip" + index} position='top' />
                    <label className='col-5 fs-16 fw-5 text-justify'>{item.label.replace(/(<([^>]+)>)/gi, "")
                        .replace(/\n/g, " ")
                        .replace(/&nbsp;/g, " ").replace('&amp;', '&')} {item.required && <span className="mandatory mr-2">*</span>}  {item.description !== undefined && item.description.trim().length !== 0 && <span><i style={{

                            marginLeft: '5px'
                        }} className={`material-icons fs-14 tooltip` + index} data-pr-tooltip={`${item.description}`}


                        > help</i></span>} </label>
                    <div className="col-5 fs-14 fw-4" >
                        <InputNumber min={0} disabled={data.type === 3} placeholder={item.placeholder} maxFractionDigits={(item.fraction === undefined || item.fraction) ? 0 : item.fraction < 0 ? 0 : item.fraction} onWheel={(e) => e.target.blur()} keyfilter="num" style={{ width: '100%' }} value={item.value} onChange={(e) => { onNumberChange(item, e.value) }} />

                    </div>
                </div>
            )
        } else if (item.type === 'paragraph') {
            return (
                <div className="flex flex-wrap  gap-3 fs-16 fw-5 text-justify justify-content-center" style={{ padding: 10 }}>

                    <label >{item.label.replace(/(<([^>]+)>)/gi, "")
                        .replace(/\n/g, " ")
                        .replace(/&nbsp;/g, " ").replace('&amp;', '&')} {item.required && <span className="mandatory mr-2">*</span>} </label>

                </div>
            )
        } else if (item.type === 'radio-group') {
            return (
                <div className="flex flex-wrap  gap-3 grid" style={{ padding: 10, border: item.error === 1 && '1px solid red' }}>
                    <Tooltip target={".tooltip" + index} position='top' />
                    <label className='col-5 fs-16 fw-5 text-justify'>{item.label.replace(/(<([^>]+)>)/gi, "")
                        .replace(/\n/g, " ")
                        .replace(/&nbsp;/g, " ").replace('&amp;', '&')} {item.required && <span className="mandatory mr-2">*</span>}  {item.description !== undefined && item.description.trim().length !== 0 && <span><i style={{

                            marginLeft: '5px'
                        }} className={`material-icons fs-14 tooltip` + index} data-pr-tooltip={`${item.description}`}


                        > help</i></span>} </label>
                    <div className="col-5 grid" style={{ padding: 10 }} >
                        {item.values.map((cb, cbind) => {
                            return (
                                <div className="p-2 flex text-justify fs-14 fw-5 align-items-center" >
                                    <RadioButton disabled={data.type === 3} inputId={"rg" + index + cbind} name={cb.label} value={cb.value} onChange={(e) => onRadioButtonSelected(item, cbind)} checked={cb.selected === true} />

                                    <label htmlFor={"rg" + index + cbind} className="ml-2">{cb.label}</label>
                                </div>
                            )
                        })

                        }
                    </div>

                </div>
            )
        } else if (item.type === 'select') {
            return (
                <div className="flex flex-wrap  gap-3 grid" style={{ padding: 10, border: item.error === 1 && '1px solid red' }}>
                    <Tooltip target={".tooltip" + index} position='top' />
                    <label className='col-5 fw-5 fs-16 text-justify'>{item.label.replace(/(<([^>]+)>)/gi, "")
                        .replace(/\n/g, " ")
                        .replace(/&nbsp;/g, " ").replace('&amp;', '&')} {item.required && <span className="mandatory mr-2">*</span>}  {item.description !== undefined && item.description.trim().length !== 0 && <span><i style={{
                            fontSize: '18px',
                            marginLeft: '5px'
                        }} className={`material-icons fs-14 tooltip` + index} data-pr-tooltip={`${item.description}`}


                        > help</i></span>} </label>


                    <div className="col-5 fw-4 fs-14">
                        <Dropdown disabled={data.type === 3} placeholder={item.placeholder} options={item.values} style={{ width: '100%' }} optionLabel='label' optionValue="value" value={item.value} onChange={(e) => { onChangeDropwdown(item, e.value) }} />
                    </div>

                </div>
            )
        } else if (item.type === 'text') {
            return (
                <div className="flex flex-wrap  gap-3 grid" style={{ padding: 10, border: item.error === 1 && '1px solid red' }}>
                    <Tooltip target={".tooltip" + index} position='top' />
                    <label className='col-5 fs-16 fw-5 text-justify'>{item.label.replace(/(<([^>]+)>)/gi, "")
                        .replace(/\n/g, " ")
                        .replace(/&nbsp;/g, " ").replace('&amp;', '&')} {item.required && <span className="mandatory mr-2">*</span>}  {item.description !== undefined && item.description.trim().length !== 0 && <span><i style={{
                            fontSize: '18px',
                            marginLeft: '5px'
                        }} className={`material-icons fs-14 tooltip` + index} data-pr-tooltip={`${item.description}`}


                        > help</i></span>} </label>
                    <div className="col-5 fs-14 fw-4" >
                        <InputText disabled={data.type === 3} style={{ width: '100%' }} value={item.value} placeholder={item.placeholder} onChange={(e) => { onNumberChange(item, e.target.value) }} />

                    </div>
                </div>
            )
        } else if (item.type === 'textarea') {
            return (
                <div className="flex flex-wrap  gap-3 grid" style={{ padding: 10, border: item.error === 1 && '1px solid red' }}>
                    <Tooltip target={".tooltip" + index} position='top' />
                    <label className='col-5 fs-16 fw-5 text-justify'>{item.label.replace(/(<([^>]+)>)/gi, "")
                        .replace(/\n/g, " ")
                        .replace(/&nbsp;/g, " ").replace('&amp;', '&')} {item.required && <span className="mandatory mr-2">*</span>}  {item.description !== undefined && item.description.trim().length !== 0 && <span><i style={{
                            fontSize: '18px',
                            marginLeft: '5px'
                        }} className={`material-icons fs-14 tooltip` + index} data-pr-tooltip={`${item.description}`}

                        > help</i></span>} </label>
                    <div className="col-5 " >
                        <Editor className="text-area" value={item.value} style={{ width: '100%', padding: 10, maxHeight: 350, height: 158, overflow: 'scroll' }} onTextChange={(e) => onNumberChange(item, e.htmlValue)} />


                    </div>

                </div>
            )
        } else if (item.type === 'file') {
            return (
                <div className="flex flex-wrap  gap-3 grid" style={{ marginBottom: 15, padding: 10, border: item.error === 1 && '1px solid red' }}>
                    <Tooltip target={".tooltip" + index} />
                    <label style={{ display: 'flex' }} className='col-5 fw-5 fs-16 text-justify'>{item.label.replace(/(<([^>]+)>)/gi, "")
                        .replace(/\n/g, " ")
                        .replace(/&nbsp;/g, " ").replace('&amp;', '&')} {item.required && <span className="mandatory mr-2">*</span>}  {item.description !== undefined && item.description.trim().length !== 0 && <span><i style={{
                            fontSize: '18px',
                            marginLeft: '5px'
                        }} className={`material-icons fs-14 tooltip` + index} data-pr-tooltip={`${item.description}`}
                            data-pr-position="right"
                            data-pr-at="right+5 top"
                            data-pr-my="left center-2"> help</i></span>} </label>
                    <div className="col-5" >
                        <div style={{
                            background: '#f8f9fa',
                            border: '1px solid #ced4da',
                            borderRadius: '6px 6px 0px 0px',
                            padding: '8px'
                        }}>
                            <label htmlFor={'fp' + index} className="fs-14 clr-navy" style={{
                                marginRight: 10,
                                padding: '5px',

                                background: 'white',
                                border: '1px solid cornflowerblue',
                                borderRadius: '10px',

                            }} >
                                <i style={{ fontSize: 15, margin: 5 }} className="pi pi-folder-open clr-navy" />
                                Add Attachment
                            </label>
                            <label
                                onClick={() => { resetFiles(item, index) }}
                                style={{
                                    padding: '5px',
                                    fontSize: '15px',
                                    border: '1px solid indianred',
                                    background: 'white',
                                    borderRadius: '10px',
                                    color: 'indianred'
                                }} >
                                <i style={{ fontSize: 15, margin: 5 }} className="pi pi-undo" />
                                Reset
                            </label>
                            <input type='file' accept=".jpg,.JPG,.jpeg,.JPEG,.png,.PNG,.pdf,.PDF,.xls,.xlsx,.ppt,.doc,.docx,.pptx" id={'fp' + index} hidden onChange={(e) => { handleFileUpload(e, item) }} ></input>
                        </div>
                        {(item.value != null && item.value.length !== 0) ?
                            <div className="col-12" style={{
                                maxHeight: 300,
                                overflow: 'auto',
                                border: '1px solid #ced4da'
                            }} >
                                <div style={{
                                    border: '1px solid #6366F170',
                                    borderRadius: '10px'
                                }}>
                                    {item.value.map((file, findex) => {

                                        return (
                                            <>
                                                <div style={{
                                                    display: 'flex',
                                                    alignItems: 'center', margin: 5
                                                }} >
                                                    <div className="flex align-items-center " style={{ width: '60%' }}>
                                                        {(file.extension === '.pdf' || file.extension === '.PDF') ?
                                                            <div>
                                                                <iframe src={API.Docs + file.originalname} /> </div> :
                                                            <img alt={file.originalname} role="presentation" src={API.Docs + file.originalname} width={100} style={{ borderRadius: 10 }} />}
                                                        <span className="flex flex-column text-left ml-3">
                                                            {file.originalname}
                                                            <small>{new Date().toLocaleDateString()}</small>
                                                        </span>
                                                    </div>
                                                    <Tag value={'View'} onClick={() => { window.open(API.Docs + file.originalname) }} style={{ width: '20%' }} severity="warning" className="px-3 py-2" />

                                                </div>

                                            </>
                                        )
                                    })

                                    }
                                </div>
                            </div>
                            :
                            <div >No Files</div>
                        }
                    </div>
                </div>
            )
        }

    }
    const handleFileUpload = (e, item) => {
        let ext = e.target.files[0].name.substr(e.target.files[0].name.lastIndexOf('.'))
        let allowedext = ['.jpg', '.JPG', '.jpeg', '.JPEG', '.png', '.PNG', '.pdf', '.PDF']
        if (allowedext.includes(ext)) {
            let formData = new FormData()
            formData.append('file', e.target.files[0])
            APIServices.post(API.FilesUpload, formData, {
                headers: {
                    'content-type': 'multipart/form-data'

                }
            }).then((res) => {
                res.data.files[0].extension = ext
                if (!(item.value != null)) {
                    item['value'] = [res.data.files[0]]
                } else {
                    if (item.multiple) {
                        item['value'].push(res.data.files[0])
                    } else {
                        item['value'] = [res.data.files[0]]
                    }

                }
                forceUpdate()

            })
        } else {
            Swal.fire({
                position: "center",
                icon: "warning",
                title: "invalid file format, supported format JPEG,PNG & PDF only",
                showConfirmButton: false,
                timer: 2000,
            });
        }
    }
    const removeImage = (index, findex) => {

        data.response[index].value.splice(findex, 1)
        forceUpdate()

    }
    const resetFiles = (item, index) => {

        item.value = []
        forceUpdate()

    }
    const checkResponse = () => {
        let result = 0
        let total = data.response.filter((i) => { return i.required === true }).length

        data.response.forEach((item) => {


            if (item.type === 'checkbox-group' && item.required === true) {


                if (item.values.filter((i) => { return i.selected }).length !== 0) {
                    result = result + 1
                    item.error = 0
                } else {
                    item.error = 1
                }
            } else if (item.type === 'date' && item.required) {

                if (item.value !== undefined && item.value !== null) {
                    result = result + 1
                    item.error = 0
                } else {
                    item.error = 1
                }
            }
            else if (item.type === 'number' && item.required) {

                if (item.value !== undefined && item.value !== null && parseFloat(item.value.toString()) >= 0) {
                    result = result + 1
                    item.error = 0
                } else {
                    item.error = 1
                }
            } else if (item.type === 'number' && item.required === false) {
                if (item.value !== undefined && item.value !== null) {
                    if (item.value === null || isNaN(item.value)) {
                        result = result + 1
                        item.error = 1
                    } else if (parseFloat(item.value.toString()) < 0) {
                        result = result + 1
                        item.error = 1
                    } else if (parseFloat(item.value.toString()) >= 0) {
                        item.error = 0
                    }
                }
            } else if (item.type === 'radio-group' && item.required) {

                if (item.values.filter((i) => { return i.selected }).length === 1) {
                    result = result + 1
                    item.error = 0
                } else {
                    item.error = 1
                }
            }
            else if (item.type === 'select' && item.required) {

                if (item.values.filter((i) => { return i.selected }).length === 1) {
                    result = result + 1
                    item.error = 0
                } else {
                    item.error = 1
                }
            }
            else if (item.type === 'text' && item.required) {

                if (item.value !== undefined && item.value !== null && item.value.trim().length !== 0) {
                    result = result + 1
                    item.error = 0
                } else {
                    item.error = 1
                }
            }
            else if (item.type === 'textarea' && item.required) {

                if (item.value !== undefined && item.value !== null && item.value.trim().length !== 0) {
                    result = result + 1
                    item.error = 0
                } else {
                    item.error = 1
                }
            }
            else if (item.type === 'file' && item.required) {
                if (item.value !== undefined && item.value !== null && item.value.length !== 0) {
                    result = result + 1
                    item.error = 0
                } else {
                    item.error = 1
                }
            }


        })

        return result === total
    }
    const makeEmpty = () => {
        let result = 0
        let data_ = JSON.parse(JSON.stringify(data.response))

        data_.forEach((item) => {


            if (item.type === 'checkbox-group') {
                item.values.forEach((i) => {
                    i.selected = false
                })


            } else if (item.type === 'date') {

                item.value = null
            }
            else if (item.type === 'number') {

                item.result = 0
            }

            else if (item.type === 'radio-group' && item.required) {
                item.values.forEach((i) => {
                    i.selected = false
                })

            }
            else if (item.type === 'select') {

                item.values.forEach((i) => {
                    i.selected = false
                })
            }
            else if (item.type === 'text') {

                item.value = 0
            }
            else if (item.type === 'textarea') {

                item.value = 0
            } else if (item.type === 'file') {
                item['value'] = []
            }


        })


        return data_
    }


    const checkResponse_ = () => {
        console.log(data)
        if (data.response.length === 0) {
            Swal.fire({
                position: "center",
                icon: "warning",
                title: "Data set is Empty, requires minimum 1 record to submit/draft",
                showConfirmButton: false,
                timer: 1500,
            });
            return false
        } else {

            return true
        }
    }

    const getType = (item) => {
        if (item === 1) {
            return 'text'
        } else if (item === 2) {
            return 'textarea'
        } else if (item === 3) {
            return 'select'
        } else if (item === 4) {
            return 'checkbox-group'
        } else if (item === 5) {
            return 'number'
        } else if (item === 6) {
            return 'radio-group'
        } else if (item === 7) {
            return 'file'
        } else if (item === 'paragraph') {
            return 'paragraph'
        } else if (item === 9) {
            return 'date'
        }
    }
    const reduceResponse = (item) => {
        if (item.type === 'text') {
            return { type: 1, value: item.value === undefined ? '' : item.value, name: item.name }
        } else if (item.type === 'textarea') {

            return { type: 2, value: item.value === undefined ? '<p></p>' : item.value, name: item.name }
        } else if (item.type === 'select') {

            return { type: 3, value: item.value === undefined ? null : item.value, name: item.name }
        } else if (item.type === 'checkbox-group') {
            return { type: 4, selectedValue: item?.values?.filter(i => i.selected).map(i => i.label), value: item.values.map((k, i) => { if (k.selected === true) { return k.value } }).filter((i) => { return i !== undefined }), name: item.name }
        } else if (item.type === 'number') {
            return { type: 5, value: item.value, name: item.name }
        } else if (item.type === 'radio-group') {
            return { type: 6, selectedValue: item?.values?.filter(i => i.selected).map(i => i.label), value: item.values.map((k, i) => { if (k.selected === true) { return k.value } }).filter((i) => { return i !== undefined }), name: item.name }
        } else if (item.type === 'file') {
            return { type: 7, value: item.value, name: item.name }
        } else if (item.type === 'paragraph') {
            return item
        } if (item.type === 'date') {
            return { type: 9, value: item.value, name: item.name }
        }
    }
    const saveRF = () => {
        let newObj = {}, newObj2 = {}
        let dt = DateTime.utc()
        if (id) {
            if (checkResponse()) {
                newObj['type'] = 3
                newObj['reject'] = 0
                newObj['approver_modified_on'] = dt
                newObj['approver_modified_by'] = login_data.id
                newObj['last_modified_on'] = dt
                newObj['last_modified_by'] = login_data.id
                newObj2['user_type'] = login_data.role === 'clientuser' ? 1 : login_data.role === 'clientadmin' ? 0 : 2

                APIServices.patch(API.QL_Approval_Edit(id), newObj).then((res) => {

                    Swal.fire({
                        title: "Approved Successfully",

                        confirmButtonText: 'Exit',
                        allowOutsideClick: false,
                    }).then((result) => {
                        /* Read more about isConfirmed, isDenied below */
                        if (result.isConfirmed) {
                            window.close()
                        }
                    })
                }).catch((e) => {
                    Swal.fire({
                        title: "Something went wrong, try after some time. Contact admin if issue still persist",

                        confirmButtonText: 'Exit',
                        allowOutsideClick: false,
                    }).then((result) => {
                        /* Read more about isConfirmed, isDenied below */
                        if (result.isConfirmed) {
                            navigate.goBack()
                        }
                    })

                })



            }
        } else {
            newObj['categoryId'] = params.state.data.cat_id
            newObj['topicId'] = params.state.data.top_id
            newObj['indicatorId'] = params.state.data.id
            newObj['reporting_year'] = parseFloat(params.state.data.year.split('-')[0]) + 1
            newObj['response'] = hardcoded.rf.includes(params.state.data.data1[0].rf.toString()) ? data.data1 : data.data1.map((i) => reduceResponse(i))
            newObj['dfId'] = params.state.data.data1[0].rf
            newObj['type'] = 3
            newObj['reject'] = 0
            newObj['last_modified_on'] = DateTime.utc()
            newObj['last_modified_by'] = login_data.id
            newObj['user_type'] = 1
            newObj['form_type'] = hardcoded.rf.includes(params.state.data.data1[0].rf.toString()) ? 2 : 1

            APIServices.post(API.QL_Approval_UP(admin_data.id), newObj).then((res) => {

                Swal.fire({
                    title: "Approved Successfully",

                    confirmButtonText: 'Exit',
                    allowOutsideClick: false,
                }).then((result) => {
                    /* Read more about isConfirmed, isDenied below */
                    if (result.isConfirmed) {
                        window.close()
                    }
                })
            }).catch((e) => {
                Swal.fire({
                    title: "Something went wrong, try after some time. Contact admin if issue still persist",

                    confirmButtonText: 'Exit',
                    allowOutsideClick: false,
                }).then((result) => {
                    /* Read more about isConfirmed, isDenied below */
                    if (result.isConfirmed) {
                        navigate.goBack()
                    }
                })

            })
        }
        forceUpdate()

    }

    const saveRF__ = () => {
        let dt = DateTime.utc()
        let newObj = {}
        if (id) {
            if (data.response.length !== 0) {
                newObj['type'] = 1
                newObj['reject'] = 1
                newObj['approver_modified_on'] = dt
                newObj['approver_modified_by'] = login_data.id
                newObj['last_modified_on'] = dt
                newObj['last_modified_by'] = login_data.id
                newObj['user_type'] = login_data.role === 'clientuser' ? 1 : login_data.role === 'clientadmin' ? 0 : 2
                APIServices.patch(API.QL_Approval_Edit(id), newObj).then((res) => {
                    Swal.fire({
                        title: "Approved Successfully",

                        confirmButtonText: 'Exit',
                        allowOutsideClick: false,
                    }).then((result) => {
                        /* Read more about isConfirmed, isDenied below */
                        if (result.isConfirmed) {
                            window.close()
                        }
                    })
                }).catch((e) => {
                    Swal.fire({
                        title: "Something went wrong, try after some time. Contact admin if issue still persist",

                        confirmButtonText: 'Exit',
                        allowOutsideClick: false,
                    }).then((result) => {
                        /* Read more about isConfirmed, isDenied below */
                        if (result.isConfirmed) {
                            navigate.goBack()
                        }
                    })

                })



            } else {
                Swal.fire({
                    title: "Cannot submit as empty",

                    confirmButtonText: 'Exit',
                    allowOutsideClick: false,
                }).then((result) => {
                    Swal.close()
                })


            }
        } else {
            newObj['categoryId'] = params.state.data.cat_id
            newObj['topicId'] = params.state.data.top_id
            newObj['indicatorId'] = params.state.data.id
            newObj['reporting_year'] = parseFloat(params.state.data.year.split('-')[0]) + 1
            newObj['response'] = hardcoded.rf.includes(params.state.data.data1[0].rf.toString()) ? data.data1 : data.data1.map((i) => reduceResponse(i))
            newObj['dfId'] = params.state.data.data1[0].rf
            newObj['type'] = 3
            newObj['reject'] = 0
            newObj['last_modified_on'] = DateTime.utc()
            newObj['last_modified_by'] = login_data.id
            newObj['user_type'] = 1
            newObj['form_type'] = hardcoded.rf.includes(params.state.data.data1[0].rf.toString()) ? 2 : 1

            APIServices.post(API.QL_Approval_UP(admin_data.id), newObj).then((res) => {

                Swal.fire({
                    title: "Approved Successfully",

                    confirmButtonText: 'Exit',
                    allowOutsideClick: false,
                }).then((result) => {
                    /* Read more about isConfirmed, isDenied below */
                    if (result.isConfirmed) {
                        window.close()
                    }
                })
            }).catch((e) => {
                Swal.fire({
                    title: "Something went wrong, try after some time. Contact admin if issue still persist",

                    confirmButtonText: 'Exit',
                    allowOutsideClick: false,
                }).then((result) => {
                    /* Read more about isConfirmed, isDenied below */
                    if (result.isConfirmed) {
                        navigate.goBack()
                    }
                })

            })
        }
        forceUpdate()

    }

    const returnToReporter = async () => {
        let { value: return_remarks } = await Swal.fire({
            title: `<div style="overflow:visible;font-size:20px;font-weight:600;margin-top:0px">Alert</div>`,
            html: `<div style="overflow:auto;max-height:200px" >Please enter reason for returning reporter on this parameter(s)</div>`,
            input: 'textarea',
            inputValue: '',
            allowOutsideClick: false,
            showCancelButton: true,
            inputValidator: (value) => {
                if (!value.trim()) {
                    return 'Enter Remarks'
                }
            }
        })

        if (return_remarks.trim()) {
            let newObj = {}, newObj2 = {}

            let dt = DateTime.utc()
            newObj['type'] = 0
            newObj['reject'] = 1
            newObj['approver_modified_on'] = dt
            newObj['approver_modified_by'] = login_data.id
            newObj['last_modified_on'] = dt
            newObj['last_modified_by'] = login_data.id
            newObj['user_type'] = login_data.role === 'clientuser' ? 1 : login_data.role === 'clientadmin' ? 0 : 2

            if (data.return_remarks === null) {
                newObj['return_remarks'] = [{ remarks: return_remarks, user_type: 3, type: 1, user_id: login_data.id, created_on: dt }]

            } else {
                let lt = data.return_remarks
                lt.push({ remarks: return_remarks, user_type: 3, type: 1, user_id: login_data.id, created_on: dt })
                newObj['return_remarks'] = lt
            }
            APIServices.patch(API.QL_Approval_Edit(data.id), newObj).then((res) => {


                Swal.fire({
                    title: "Data Submission Sent Back To Reporter For Correction",

                    confirmButtonText: 'Exit',
                    allowOutsideClick: false,
                }).then((result) => {
                    /* Read more about isConfirmed, isDenied below */
                    if (result.isConfirmed) {
                        navigate.goBack()
                    }
                })
            }).catch((e) => {
                Swal.fire({
                    title: "Something went wrong, try after some time. Contact admin if issue still persist",

                    confirmButtonText: 'Exit',
                    allowOutsideClick: false,
                }).then((result) => {
                    /* Read more about isConfirmed, isDenied below */
                    if (result.isConfirmed) {
                        navigate.goBack()
                    }
                })

            })

        }
    }
    const getUser = (id) => {
        let user_name = 'Not Found'
        if (id === admin_data.id) {
            return 'Enterprise Admin'
        }
        let index = userList.findIndex(i => i.id === id)
        if (index !== -1) {
            user_name = userList[index].information.empname
        }
        return user_name
    }

    const getBGColor = (clr) => {

        if (data.implevel === 1 && clr === data.implevel) {
            return 'red'
        } else if (data.implevel === 2 && clr === data.implevel) {
            return 'indianred'
        } else if (data.implevel === 3 && clr === data.implevel) {
            return 'darkorange'
        } else if (data.implevel === 4 && clr === data.implevel) {
            return 'mediumseagreen'
        } else if (data.implevel === 5 && clr === data.implevel) {
            return 'green'
        } else {
            return 'white'
        }
    }
    const getColor = (clr) => {

        if (data.implevel === 1 && clr === data.implevel) {
            return 'white'
        } else if (data.implevel === 2 && clr === data.implevel) {
            return 'white'
        } else if (data.implevel === 3 && clr === data.implevel) {
            return 'white'
        } else if (data.implevel === 4 && clr === data.implevel) {
            return 'white'
        } else if (data.implevel === 5 && clr === data.implevel) {
            return 'white'
        } else { return 'black' }
    }


    return (
        <>
            {(admin_data.id === 289 || admin_data.id === 17 || admin_data.id === 28) ? <div className="grid" style={{ margim: 10 }} >
                <div className="col-12">
                    {(login_data.id !== undefined && data.length !== 0) ?
                        <div>

                            <div className="fs-20 fw-7 clr-gray-900">

                                <h4><span className="mr-2">{'DF ' + params.state.data.data1[0].rf}</span> {params.state.data.rf_title}</h4>
                            </div>


                            <div className="bg-white" style={{ padding: 24, borderBottom: '1px solid #E0E0E0' }}  >
                                <div className="grid col-12 ">
                                    <div className="flex fs-16 col-4" style={{ flexDirection: 'row' }}>          <span className="clr-gray-3 fw-4">Aspects:&nbsp;</span> <div className="clr-gray-900 fs-16 fw-7" style={{ display: 'flex' }}>{params.state.data.title}
                                        <Tooltip className="tag-tooltip" target={".tags"} position={'top'} autoHide={true}> {params.state.data.overallTags.map((i, j) => {
                                            if (i.length !== 0) {
                                                return (
                                                    <>
                                                        <label style={{ color: 'black', display: 'flex' }}> {
                                                            j === 0 ? 'Must Have' : j === 1 ? 'Progressive' : 'Advanced'

                                                        }
                                                        </label>
                                                        {
                                                            i.map((tag, k) => {

                                                                return (
                                                                    <label style={{ color: 'green' }}>{tag}{k !== i.length - 1 && ','}</label>
                                                                )

                                                            })
                                                        }
                                                        <div style={{ marginBottom: 10 }} />
                                                    </>
                                                )
                                            }
                                        })} </Tooltip>
                                        <div style={{ alignItems: 'center' }} ><i className={"material-icons ml-2 tags"} style={{ fontSize: 14, cursor: 'pointer' }}>info</i>  </div>

                                    </div>  </div>
                                    <div className="flex fs-16 col-4" style={{ flexDirection: 'row' }}>          <span className="clr-gray-3 fw-4">Category:&nbsp;</span> <span className="clr-gray-900 fw-7">{params.state.data.cat_title}  </span>  </div>

                                    {show && <div className="flex fs-16 col-4" style={{ flexDirection: 'row' }}>          <span className="clr-gray-3 fw-4">Last updated:&nbsp;</span> <span className="clr-gray-900 fw-7"> {params.state.oldData.length !== 0 ? moment(params.state.oldData.created_on).local().format('DD MMM YYYY, hh:mm A') : 'NA'}</span>  </div>}
                                </div>
                                <div className="grid col-12">
                                    <div className="flex fs-16 col-4" style={{ flexDirection: 'row' }}>          <span className="clr-gray-3 fw-4">Requirement:&nbsp;</span>  <span className="clr-gray-900 fw-7">{params.state.data.data1[0].title}</span> </div>
                                    <div className="flex fs-16 col-4" style={{ flexDirection: 'row' }}>          <span className="clr-gray-3 fw-4">Topic:&nbsp;</span> <span className="clr-gray-900 fw-7">{params.state.data.top_title}   </span>  </div>

                                    <div className="flex fs-16 col-4" style={{ flexDirection: 'row' }}>          <span className="clr-gray-3 fw-4">Reporting Period:&nbsp;</span> <span className="clr-gray-900 fw-7">  {getRPTextFormat(params.state.data.reporting_period)}</span>  </div>
                                </div>
                                {show && <div className="grid col-12">
                                    <div className="flex fs-16 col-6" style={{ flexDirection: 'row' }}>          <span className="clr-gray-3 fw-4"> {(data.type === 0 && !data.reject) ? 'Drafted On' : (data.type === 0 && data.reject) ? 'Rejected On' : data.type === 1 ? 'Submitted On' : (data.type === 3) ? 'Approved On' : ''}&nbsp;</span>  <span className="clr-gray-900 fw-7">{DateTime.fromISO(data.last_modified_on, { zone: 'utc' }).toLocal().toFormat('dd-MM-yyyy hh:mm a')}</span> </div>
                                    <div className="flex fs-16 col-6" style={{ flexDirection: 'row' }}>          <span className="clr-gray-3 fw-4"> {(data.type === 0 && !data.reject) ? 'Drafted By' : (data.type === 0 && data.reject) ? 'Rejected By' : data.type === 1 ? 'Submitted By' : (data.type === 3) ? 'Approved By' : ''}&nbsp;</span> <span className="clr-gray-900 fw-7">{getUser(data.last_modified_by)}   </span>  </div>


                                </div>}
                                {data?.comments &&
                                <ReadMoreComponent content={data.comments} />

                            }
                            </div>

                          
                            <div >


                                {(data.response.length !== 0 && !checkHardcoded(id)) ?
                                    <div>
                                        <div className="bg-white" style={{ padding: 24 }} >
                                            {
                                                data.response.map((item, index) => {

                                                    return renderItems(item, index)
                                                })

                                            }
                                        </div>
                                        <div style={{ borderTop: '1px solid #E0E0E0' }} />
                                        <div className="bg-white grid m-0 p-0" style={{ paddingTop: 24 }} >
                                            <label className="col-5 fw-7 fs-16">Comments</label>
                                            <div className="col-12" style={{ maxHeight: 300, overflowY: 'scroll', overflow: 'auto' }}>
                                                {data !== undefined && data.return_remarks != null &&
                                                    data.return_remarks.map((cmnt) => {
                                                        return (
                                                            <div className="col-12 grid" style={{ marginBottom: 10, borderBottom: '1px solid gray' }}>
                                                                <div className="col-5">
                                                                    <div>   {cmnt.user_id === login_data.id ? 'You' : getUser(cmnt.user_id)}</div>
                                                                    <div className="mt-2" >     {DateTime.fromISO(cmnt.created_on, { zone: 'utc' }).toLocal().toFormat('dd-LLL-yyyy')} </div>
                                                                </div>
                                                                <div className="col-5">
                                                                    {cmnt.remarks}
                                                                </div>
                                                            </div>
                                                        )
                                                    })
                                                }
                                            </div>
                                        </div>

                                        <div style={{ position: 'sticky', bottom: 0, zIndex: 100 }}>
                                            <div className="flex justify-content-end" style={{ padding: 10, background: 'white' }}>
                                                <Button label='Close' className="ml-2" onClick={() => { window.close() }}></Button>
                                                {(((data.type === 0 && !data.reject) || (data.type === 1)) && id) && <Button label='Return to Reporter' className="ml-2" onClick={() => { returnToReporter() }}></Button>}


                                                {(data.type !== 3 || !id) && <Button label='Save & Approve' className="ml-2" onClick={() => { saveRF() }} ></Button>}

                                            </div>
                                        </div>
                                    </div>
                                    : checkHardcoded(id) &&
                                    <div>
                                        <div className="bg-white" style={{ padding: 24 }}>
                                            {id === '85' ?
                                                <div>
                                                    <BGSQ14 data={data.response} edit={1} setData={() => { forceUpdate() }} getData={(e) => { data.response = e; }} />

                                                </div>
                                                : id === '89' ?
                                                    <div>
                                                        <BGSQ15 data={data.response} edit={1} setData={() => { forceUpdate() }} getData={(e) => { data.response = e; }} />

                                                    </div> : id === '102' ?
                                                        <div>
                                                            <BGSQ21 data={data.response} edit={1} setData={() => { forceUpdate() }} getData={(e) => { data.response = e; }} />

                                                        </div> : id === '104' ?
                                                            <div>
                                                                <BP1EQ2 data={data.response} edit={1} setData={() => { forceUpdate() }} getData={(e) => { data.response = e; }} />

                                                            </div> : id === '110' ? <div>
                                                                <BGSQ24 data={data.response} edit={1} setData={() => { forceUpdate() }} getData={(e) => { data.response = e; }} />

                                                            </div> : id === '111' ? <div>
                                                                <BP4EQ2 data={data.response} edit={1} setData={() => { forceUpdate() }} getData={(e) => { data.response = e; }} />

                                                            </div> : id === '112' ? <div>
                                                                <BP7EQ2 data={data.response} edit={1} setData={() => { forceUpdate() }} getData={(e) => { data.response = e; }} />

                                                            </div> : id === '113' ? <div>
                                                                <BP7LQ1 data={data.response} edit={1} setData={() => { forceUpdate() }} getData={(e) => { data.response = e; }} />

                                                            </div> : id === '114' ? <div>
                                                                <BP8LQ1 data={data.response} edit={1} setData={() => { forceUpdate() }} getData={(e) => { data.response = e; }} />

                                                            </div> : id === '115' ? <div>
                                                                <BP8LQ2 data={data.response} edit={1} setData={() => { forceUpdate() }} getData={(e) => { data.response = e; }} />

                                                            </div> : id === '116' ? <div>
                                                                <BP8LQ4 data={data.response} edit={1} setData={() => { forceUpdate() }} getData={(e) => { data.response = e; }} />

                                                            </div> : id === '117' ? <div>
                                                                <BP8LQ5 data={data.response} edit={1} setData={() => { forceUpdate() }} getData={(e) => { data.response = e; }} />

                                                            </div> : id === '118' ? <div>
                                                                <BP8LQ6 data={data.response} edit={1} setData={() => { forceUpdate() }} getData={(e) => { data.response = e; }} />

                                                            </div> : id === '121' ? <div>
                                                                <BP7EQ1B data={data.response} edit={1} setData={() => { forceUpdate() }} getData={(e) => { data.response = e; }} />

                                                            </div> : id === '133' ? <div>
                                                                <BP8EQ1 data={data.response} edit={1} setData={() => { forceUpdate() }} getData={(e) => { data.response = e; }} />

                                                            </div> : id === '134' ? <div>
                                                                <BP9LQ1 data={data.response} edit={1} setData={() => { forceUpdate() }} getData={(e) => { data.response = e; }} />

                                                            </div> : id === '139' ? <div>
                                                                <BP2LQ1 data={data.response} edit={1} setData={() => { forceUpdate() }} getData={(e) => { data.response = e; }} />

                                                            </div> : id === '140' ? <div>
                                                                <BP2LQ2 data={data.response} edit={1} setData={() => { forceUpdate() }} getData={(e) => { data.response = e; }} />

                                                            </div> : id === '148' ? <div>
                                                                <BP6EQ10 data={data.response} edit={1} setData={() => { forceUpdate() }} getData={(e) => { data.response = e; }} />

                                                            </div> : id === '149' ? <div>
                                                                <BP6EQ11 data={data.response} edit={1} setData={() => { forceUpdate() }} getData={(e) => { data.response = e; }} />

                                                            </div> : id === '150' ? <div>
                                                                <BP6EQ12 data={data.response} edit={1} setData={() => { forceUpdate() }} getData={(e) => { data.response = e; }} />

                                                            </div> : id === '151' ? <div>
                                                                <BP6LQ6 data={data.response} edit={1} setData={() => { forceUpdate() }} getData={(e) => { data.response = e; }} />

                                                            </div> : id === '181' ? <div>
                                                                <SGXGSQ6 data={data.response} edit={1} setData={() => { forceUpdate() }} getData={(e) => { data.response = e; }} />

                                                            </div> : id === '182' ? <div>
                                                                <SGXGSQ7 data={data.response} edit={1} setData={() => { forceUpdate() }} getData={(e) => { data.response = e; }} />

                                                            </div> : id === '183' &&
                                                            <div>
                                                                <SGXGSQ4 data={data.response} edit={1} setData={() => { forceUpdate() }} getData={(e) => { data.response = e; }} />

                                                            </div>
                                            }</div>
                                        <div style={{ borderTop: '1px solid #E0E0E0' }} />
                                        <div className="bg-white grid m-0 p-0" style={{ paddingTop: 24 }} >
                                            <label className="col-5 fw-7 fs-16">Comments</label>
                                            <div className="col-12" style={{ maxHeight: 300, overflowY: 'scroll', overflow: 'auto' }}>
                                                {data !== undefined && data.return_remarks != null &&
                                                    data.return_remarks.map((cmnt) => {
                                                        return (
                                                            <div className="col-12 grid" style={{ marginBottom: 10, borderBottom: '1px solid gray' }}>
                                                                <div className="col-5">
                                                                    <div>   {cmnt.user_id === login_data.id ? 'You' : getUser(cmnt.user_id)}</div>
                                                                    <div className="mt-2" >     {DateTime.fromISO(cmnt.created_on, { zone: 'utc' }).toLocal().toFormat('dd-LLL-yyyy')} </div>
                                                                </div>
                                                                <div className="col-5">
                                                                    {cmnt.remarks}
                                                                </div>
                                                            </div>
                                                        )
                                                    })
                                                }
                                            </div>
                                        </div>
                                        <div style={{ position: 'sticky', bottom: 0, zIndex: 100 }}>
                                            <div className="flex justify-content-end" style={{ padding: 10, background: 'white' }}>


                                                <Button label='Close' className="ml-2" onClick={() => { window.close() }}></Button>
                                                {(((data.type === 0 && !data.reject) || (data.type === 1)) && id) && <Button className="ml-2" label='Return to Reporter' onClick={() => { returnToReporter() }}></Button>}


                                                {(data.type !== 3 || !id) && <Button className="ml-2" label='Save & Approve' onClick={() => { saveRF__() }} ></Button>}



                                            </div>
                                        </div>
                                    </div>
                                }

                            </div>

                        </div>
                        :
                        <></>
                    }
                </div>
            </div >
                :
                < div className='flex justify-content-center'> You Have No Rights To Access This Page </div>}
        </>
    );
};

const comparisonFn = function (prevProps, nextProps) {
    return prevProps.location.pathname === nextProps.location.pathname;
};

export default React.memo(TVSDFInputEntryApproval, comparisonFn);
