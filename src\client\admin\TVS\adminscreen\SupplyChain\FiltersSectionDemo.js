import React, { useState, useEffect, useCallback } from "react";
import { MultiSelect } from "primereact/multiselect";
import { Calendar } from "primereact/calendar";



const FiltersSectionDemo = ({ supplyData, setFilteredSupplyData, setSelectedBu }) => {
  const [category, setCategory] = useState(["All"]);
  const [businessUnit, setBusinessUnit] = useState(["All"]);
  const [location, setLocation] = useState(["All Sites"]);
  const [fromDate, setFromDate] = useState(null);
  const [toDate, setToDate] = useState(null);

  const [categories, setCategories] = useState([]);
  const [businessUnits, setBusinessUnits] = useState([]);
  const [locations, setLocations] = useState([]);
  const [rankedData, setRankedData] = useState([]);

  // useEffect(() => {console.log(supplyData)}, [supplyData])
  useEffect(() => {
    if (supplyData.length > 0) {
      // Rank the data initially (without filtering)
      let rankedData = [...supplyData].sort(
        (a, b) => b.msi_score - a.msi_score
      );

      rankedData = rankedData.map((item, index) => ({
        ...item,
        rank: index + 1,
      }));

      setRankedData(rankedData); // Store the ranked data in the state
      setFilteredSupplyData(rankedData); // Set the initial filtered data to the ranked data

      // Set categories, business units, and locations for dropdowns
      setCategories([
        "All",
        ...new Set(supplyData.map((item) => item.category)),
      ]);
      setBusinessUnits([
        "All",
        ...new Set(
          supplyData.map(
            (item) =>
              `${item.company_name} - ${item.vendor_code}` || "N/A"
          )
        ),
      ]);
      setLocations([
        "All Sites",
        ...new Set(supplyData.map((item) => item.location)),
      ]);

      // Reset filters to default values (arrays with "All" selected)
      setCategory(["All"]);
      setBusinessUnit(["All"]);
      setLocation(["All Sites"]);
    }
  }, [supplyData]); // Removed setFilteredSupplyData from dependencies

  const applyFilters = useCallback(() => {
    let filteredData = [...rankedData]; // Use ranked data, not raw supplyData

    // Filter by category (multiple selection)
    if (!category.includes("All") && category.length > 0) {
      filteredData = filteredData.filter((item) => category.includes(item.category));
    }

    // Filter by business unit (multiple selection)
    if (!businessUnit.includes("All") && businessUnit.length > 0) {
      const selectedVendorCodes = businessUnit.map(bu => bu.split(" - ").pop());
      filteredData = filteredData.filter(
        (item) => selectedVendorCodes.includes(item.vendor_code.toString())
      );
    }

    // Filter by location (multiple selection)
    if (!location.includes("All Sites") && location.length > 0) {
      filteredData = filteredData.filter((item) => location.includes(item.location));
    }

    // Parse date function with fallback to null
    const parseDate = (dateString) => {
      if (!dateString) return null;
      const [day, month, year] = dateString.split(/[./-]/).map(Number);
      return new Date(year, month - 1, day);
    };

    if (fromDate) {
      filteredData = filteredData.filter((item) => {
        const startDate = parseDate(item.audit_start_date);
        return startDate && startDate >= fromDate;
      });
    }

    if (toDate) {
      filteredData = filteredData.filter((item) => {
        const endDate = parseDate(item.audit_end_date);
        return endDate && endDate <= toDate;
      });
    }

    setFilteredSupplyData(filteredData);
  }, [category, businessUnit, location, fromDate, toDate, rankedData]); // Removed setFilteredSupplyData from dependencies

  useEffect(() => {
    // For backward compatibility, pass the first business unit to parent component
    // or "All" if no business unit is selected
    setSelectedBu(businessUnit.length > 0 ? businessUnit[0] : "All");
    applyFilters();
  }, [category, businessUnit, location, fromDate, toDate, applyFilters]); // Removed setSelectedBu from dependencies

  const handleCategoryChange = (e) => {
    const newCategories = e.value;
    setCategory(newCategories);
    setBusinessUnit(["All"]);
    setLocation(["All Sites"]);

    // If "All" is selected or no categories are selected, show all business units
    if (newCategories.includes("All") || newCategories.length === 0) {
      setBusinessUnits([
        "All",
        ...new Set(
          supplyData.map(
            (item) =>
              `${item.company_name} - ${item.vendor_code}` || "N/A"
          )
        ),
      ]);
      setLocations([
        "All Sites",
        ...new Set(supplyData.map((item) => item.location)),
      ]);
    } else {
      // Filter business units based on selected categories
      setBusinessUnits([
        "All",
        ...new Set(
          supplyData
            .filter((item) => newCategories.includes(item.category))
            .map((item) => `${item.company_name} - ${item.vendor_code}`)
        ),
      ]);
      setLocations(["All Sites"]);
    }
  };

  const handleBusinessUnitChange = (e) => {
    const newBusinessUnits = e.value;
    setBusinessUnit(newBusinessUnits);
    setLocation(["All Sites"]);

    // If "All" is selected or no business units are selected, show all locations
    if (newBusinessUnits.includes("All") || newBusinessUnits.length === 0) {
      setLocations([
        "All Sites",
        ...new Set(supplyData.map((item) => item.location)),
      ]);
    } else {
      // Get all vendor codes from selected business units
      const selectedVendorCodes = newBusinessUnits.map(bu => bu.split(" - ").pop());

      // Filter locations based on selected vendor codes
      setLocations([
        "All Sites",
        ...new Set(
          supplyData
            .filter(
              (item) => selectedVendorCodes.includes(item.vendor_code.toString())
            )
            .map((item) => item.location)
        ),
      ]);
    }
  };

  return (
    <div className="container gap-3 mt-4 d-flex">
      {/* Supplier Category */}
      <div>
        <label>Supplier Category</label>
        <MultiSelect
          value={category}
          options={categories}
          onChange={handleCategoryChange}
          placeholder="Select Category"
          className="w-100"
          filter
          display="chip"
          panelClassName="hidefilter"
          maxSelectedLabels={3}
        />
      </div>

      {/* Business Unit */}
      <div>
        <label>Name & Vendor Code</label>
        <MultiSelect
          value={businessUnit}
          options={businessUnits}
          onChange={handleBusinessUnitChange}
          placeholder="Select Vendor"
          className="w-100"
          filter
          display="chip"
          panelClassName="hidefilter"
          maxSelectedLabels={3}
        />
      </div>

      {/* Location */}
      <div>
        <label>Location</label>
        <MultiSelect
          value={location}
          options={locations}
          onChange={(e) => setLocation(e.value)}
          placeholder="Select Location"
          className="w-100"
          filter
          display="chip"
          panelClassName="hidefilter"
          maxSelectedLabels={3}
        />
      </div>

      {/* Date Filters */}
      <div style={{ display: "flex", flexDirection: "column" }}>
        <label>From</label>
        <Calendar
          value={fromDate}
          onChange={(e) => {
            setFromDate(e.value);
            setToDate(null);
          }}
          placeholder="Select Date"
          showIcon
          showButtonBar
        />
      </div>

      <div style={{ display: "flex", flexDirection: "column" }}>
        <label>To</label>
        <Calendar
          value={toDate}
          onChange={(e) => setToDate(e.value)}
          placeholder="Select Date"
          showIcon
          minDate={fromDate}
          showButtonBar
          disabled={!fromDate}
        />
      </div>
    </div>
  );
};

export default FiltersSectionDemo;
