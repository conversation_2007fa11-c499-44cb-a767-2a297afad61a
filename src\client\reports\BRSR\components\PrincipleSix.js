import React from "react";

const PrincipleSix = () => {
  return (
    <div style={{ minHeight: "80vh" }}>
      <div
        style={{
          maxWidth: "90%",
          margin: "auto",
          marginBottom: "2rem",
        }}
      >
        <p
          style={{
            fontWeight: "bold",
            color: "black",
            textAlign: "center",
            marginBottom: "1rem",
          }}
        >
          PRINCIPLE 6- BUSINESS SHOULD RESPECT AND MAKE EFFORTS TO PROTECT AND
          RESTORE THE ENVIRONMENT
        </p>
        <p
          style={{
            fontWeight: "bold",
            color: "blue",
            textAlign: "center",
            marginBottom: "1rem",
          }}
        >
          Essential Indicators
        </p>
        <p style={{ fontWeight: "bold", color: "black", marginBottom: "1rem" }}>
          1. Details of total energy consumption (in joules or multiples) and
          energy intensity, in the following format:
        </p>
        <table
          border={1}
          cellPadding={8}
          style={{
            borderCollapse: "collapse",
            width: "100%",
            textAlign: "left",
          }}
        >
          <thead>
            <tr>
              <th style={{ width: "30%" }}>Parameter</th>
              <th style={{ width: "10%" }}>Unit</th>
              <th>FY 2024-25</th>
              <th>FY 2023-24</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td
                colSpan={4}
                style={{ fontWeight: "bold", textAlign: "center" }}
              >
                From renewable sources
              </td>
            </tr>
            <tr>
              <td>Total electricity consumption (A)</td>
              <td>TJ</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
            <tr>
              <td>Total fuel consumption (B)</td>
              <td>TJ</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
            <tr>
              <td>Energy consumption through other sources (C)</td>
              <td>TJ</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
            <tr>
              <td>
                <strong>
                  Total energy consumed from renewable sources (A+B+C)
                </strong>
              </td>
              <td>TJ</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>

            <tr>
              <td
                colSpan={4}
                style={{ fontWeight: "bold", textAlign: "center" }}
              >
                From non-renewable sources
              </td>
            </tr>
            <tr>
              <td>Total electricity consumption (D)</td>
              <td>TJ</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
            <tr>
              <td>Total fuel consumption (E)</td>
              <td>TJ</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
            <tr>
              <td>Energy consumption through other sources (F)</td>
              <td>TJ</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
            <tr>
              <td>
                <strong>
                  Total energy consumed from non-renewable sources (D+E+F)
                </strong>
              </td>
              <td>TJ</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
            <tr>
              <td>
                <strong>Total energy consumed (A+B+C+D+E+F)</strong>
              </td>
              <td>TJ</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>

            <tr>
              <td>
                <strong>Energy intensity per rupee of turnover</strong>
              </td>
              <td>TJ/Million Rupees</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
            <tr>
              <td>
                <strong>
                  Energy intensity per rupee of turnover adjusted for Purchasing
                  Power Parity (PPP)
                </strong>
              </td>
              <td>TJ/Million Rupees</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
            <tr>
              <td>
                <strong>Energy intensity in terms of physical output</strong>
              </td>
              <td>TJ/Tons of Production</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
          </tbody>
        </table>

        <p
          style={{ fontSize: "0.9em", fontStyle: "italic", marginTop: "0.5em" }}
        >
          <strong>Note:</strong> Indicate if any independent assessment/
          evaluation/assurance has been carried out by an external agency. (Y/N)
          If yes, the name of the external agency.
        </p>
      </div>

      <div
        style={{
          maxWidth: "90%",
          margin: "auto",
          marginBottom: "2rem",
        }}
      >
        <p style={{ fontWeight: "bold", color: "black", marginBottom: "1rem" }}>
          2. Does the entity have any sites/facilities identified as designated
          consumers (DCs) under the 29 Performance, Achieve and Trade (PAT)
          Scheme of the Government of India? (Y/N) If yes, disclose whether
          targets set under the PAT scheme have been achieved. In case targets
          have not been achieved, provide the remedial action taken, if any.
        </p>
      </div>

      <div
        style={{
          maxWidth: "90%",
          margin: "auto",
          marginBottom: "2rem",
        }}
      >
        <p style={{ fontWeight: "bold", color: "black", marginBottom: "1rem" }}>
          3. Provide details of the following disclosures related to water:
        </p>
        <table
          border={1}
          cellPadding={8}
          style={{
            borderCollapse: "collapse",
            width: "100%",
            textAlign: "left",
          }}
        >
          <thead>
            <tr>
              <th style={{ width: "30%" }}>Parameter</th>
              <th style={{ width: "20%" }}>Unit</th>
              <th>FY 2024-25</th>
              <th>FY 2023-24</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td colSpan={4} style={{ fontWeight: "bold" }}>
                Water withdrawal by source
              </td>
            </tr>
            <tr>
              <td>(i) Surface water</td>
              <td>KL</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
            <tr>
              <td>(ii) Ground Water</td>
              <td>KL</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
            <tr>
              <td>(iii) Third Party Water</td>
              <td>KL</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
            <tr>
              <td>(iv) Seawater/ Desalinated Water</td>
              <td>KL</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
            <tr>
              <td>(v) Others : (Rainwater Harvesting)</td>
              <td>KL</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
            <tr>
              <td>
                <strong>
                  Total volume of water withdrawal (in kilolitres)
                </strong>
              </td>
              <td>KL</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
            <tr>
              <td>
                <strong>
                  Total volume of water consumption (in kilolitres)
                </strong>
              </td>
              <td>KL</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
            <tr>
              <td>
                <strong>
                  Water Consumption intensity per rupee of turnover
                </strong>
              </td>
              <td>KL/Million Rupee</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
            <tr>
              <td>
                <strong>
                  Water intensity per rupee of turnover adjusted for Purchasing
                  Power Parity (PPP) <br />
                  (Total water consumption / Revenue from operations adjusted
                  for PPP)
                </strong>
              </td>
              <td>KL/Million Rupee</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
            <tr>
              <td>
                <strong>Water intensity in terms of physical output</strong>
              </td>
              <td>KL/Tons of Production</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
          </tbody>
        </table>

        <p
          style={{ fontSize: "0.9em", fontStyle: "italic", marginTop: "0.5em" }}
        >
          <strong>Note:</strong> Indicate if any independent assessment/
          evaluation/assurance has been carried out by an external agency. (Y/N)
          If yes, name of the external agency.
        </p>
      </div>

      <div
        style={{
          maxWidth: "90%",
          margin: "auto",
          marginBottom: "2rem",
        }}
      >
        <p style={{ fontWeight: "bold", color: "black", marginBottom: "1rem" }}>
          4. Provide the following details related to water discharged:
        </p>
        <table
          border={1}
          cellPadding={8}
          style={{
            borderCollapse: "collapse",
            width: "100%",
            textAlign: "left",
          }}
        >
          <thead>
            <tr>
              <th style={{ width: "30%" }}>Parameter</th>
              <th>FY 2024-25</th>
              <th>FY 2023-24</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td
                colSpan={3}
                style={{ fontWeight: "bold", textAlign: "center" }}
              >
                Water discharge by destination and level of treatment (in
                kilolitres)
              </td>
            </tr>

            <tr>
              <td>
                <strong>i. To Surface water</strong>
              </td>
              <td></td>
              <td></td>
            </tr>
            <tr>
              <td>&nbsp;&nbsp;&nbsp;- No treatment</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
            <tr>
              <td>
                &nbsp;&nbsp;&nbsp;- With treatment – please specify level of
                treatment
              </td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>

            <tr>
              <td>
                <strong>ii. To Groundwater</strong>
              </td>
              <td></td>
              <td></td>
            </tr>
            <tr>
              <td>&nbsp;&nbsp;&nbsp;- No treatment</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
            <tr>
              <td>
                &nbsp;&nbsp;&nbsp;- With treatment – please specify level of
                treatment
              </td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>

            <tr>
              <td>
                <strong>iii. To Seawater</strong>
              </td>
              <td></td>
              <td></td>
            </tr>
            <tr>
              <td>&nbsp;&nbsp;&nbsp;- No treatment</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
            <tr>
              <td>
                &nbsp;&nbsp;&nbsp;- With treatment – please specify level of
                treatment
              </td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>

            <tr>
              <td>
                <strong>iv. Sent to third-parties</strong>
              </td>
              <td></td>
              <td></td>
            </tr>
            <tr>
              <td>&nbsp;&nbsp;&nbsp;- No treatment</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
            <tr>
              <td>
                &nbsp;&nbsp;&nbsp;- With treatment – please specify level of
                treatment
              </td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>

            <tr>
              <td>
                <strong>v. Others</strong>
              </td>
              <td></td>
              <td></td>
            </tr>
            <tr>
              <td>&nbsp;&nbsp;&nbsp;- No treatment</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
            <tr>
              <td>
                &nbsp;&nbsp;&nbsp;- With treatment – please specify level of
                treatment
              </td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>

            <tr>
              <td>
                <strong>Total water discharged (in kilolitres)</strong>
              </td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
          </tbody>
        </table>
      </div>

      <div
        style={{
          maxWidth: "90%",
          margin: "auto",
          marginBottom: "2rem",
        }}
      >
        <p style={{ fontWeight: "bold", color: "black", marginBottom: "1rem" }}>
          5. Has the entity implemented a mechanism for Zero Liquid Discharge?
          If yes, provide details of its coverage and implementation.
        </p>
      </div>

      <div
        style={{
          maxWidth: "90%",
          margin: "auto",
          marginBottom: "2rem",
        }}
      >
        <p style={{ fontWeight: "bold", color: "black", marginBottom: "1rem" }}>
          6. Please provide details of air emissions (other than GHG emissions)
          by the entity:
        </p>
        <table
          border={1}
          cellPadding={8}
          style={{
            borderCollapse: "collapse",
            width: "100%",
            textAlign: "left",
          }}
        >
          <thead>
            <tr>
              <th style={{ width: "30%" }}>Parameter</th>
              <th>Unit</th>
              <th>FY 2024-25</th>
              <th>FY 2023-24</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>NOx</td>
              <td>MT</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
            <tr>
              <td>Sox</td>
              <td>MT</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
            <tr>
              <td>Particulate matter (PM)</td>
              <td>MT</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
            <tr>
              <td>Persistent organic pollutants (POP)</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
            <tr>
              <td>Volatile organic compounds (VOC)</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
            <tr>
              <td>Hazardous air pollutants (HAP)</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
          </tbody>
        </table>

        <div style={{ marginTop: "1em", fontSize: "0.9em" }}>
          <p>
            <strong>Note:</strong> Indicate if any independent assessment/
            evaluation/assurance has been carried out by an external agency?
            (Y/N) If yes, name of the external agency.
          </p>
          <p>
            <strong>No</strong>
          </p>
          <ul style={{ listStyleType: "none", paddingLeft: "0" }}>
            <li>
              Note 1 - Currently calculated for Stacks of Diesel Generators
              (DGs) and boilers
            </li>
            <li>
              Note 2 - Currently not being monitored would consider monitoring
              going forward
            </li>
            <li>
              Note 3 - Data is being monitored through online system but
              retrieval of data is not feasible as it's in the servers of
              Pollution Control Board (PCB)
            </li>
          </ul>
        </div>
      </div>

      <div
        style={{
          maxWidth: "90%",
          margin: "auto",
          marginBottom: "2rem",
        }}
      >
        <p style={{ fontWeight: "bold", color: "black", marginBottom: "1rem" }}>
          7. Provide details of greenhouse gas emissions (Scope 1 and Scope 2
          emissions) &amp; its intensity:
        </p>
        <table
          border={1}
          cellPadding={8}
          style={{
            borderCollapse: "collapse",
            width: "100%",
            textAlign: "left",
          }}
        >
          <thead>
            <tr>
              <th>Parameter</th>
              <th>Unit</th>
              <th>FY 2024-25</th>
              <th>FY 2023-24</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>
                Total Scope 1 emissions (Break-up of the GHG into CO2, CH4, N2O,
                HFCs, PFCs, SF6, NF3, if available)
              </td>
              <td>tCO2e</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
            <tr>
              <td>
                Total Scope 2 emissions (Break-up of the GHG into CO2, CH4, N2O,
                HFCs, PFCs, SF6, NF3, if available)
              </td>
              <td>tCO2e</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
            <tr>
              <td>
                <strong>Total Scope 1 + 2 Emissions</strong>
              </td>
              <td>tCO2e</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
            <tr>
              <td>
                <strong>
                  Total Scope 1 and Scope 2 emissions per rupee of turnover
                </strong>
              </td>
              <td>tCO2e/ Million Rupees</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
            <tr>
              <td>
                <strong>
                  Total Scope 1 and Scope 2 emission intensity per rupee of
                  turnover adjusted for Purchasing Power Parity (PPP)
                </strong>
              </td>
              <td>tCO2e/ Million Rupees</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
            <tr>
              <td>
                <strong>
                  Total Scope 1 and Scope 2 emission intensity in terms of
                  physical output
                </strong>
              </td>
              <td>tCO2e/ Ton of Production</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
          </tbody>
        </table>

        <p style={{ fontSize: "0.9em", fontStyle: "italic", marginTop: "1em" }}>
          <strong>Note:</strong> Indicate if any independent assessment/
          evaluation/assurance has been carried out by an external agency? (Y/N)
          If yes, name of the external agency.
        </p>
      </div>

      <div
        style={{
          maxWidth: "90%",
          margin: "auto",
          marginBottom: "2rem",
        }}
      >
        <p style={{ fontWeight: "bold", color: "black", marginBottom: "1rem" }}>
          8. Does the entity have any project related to reducing Green House
          Gas emissions? If yes, then provide details.
        </p>
      </div>

      <div
        style={{
          maxWidth: "90%",
          margin: "auto",
          marginBottom: "2rem",
        }}
      >
        <p style={{ fontWeight: "bold", color: "black", marginBottom: "1rem" }}>
          9. Provide details related to waste management by the entity:
        </p>
        <table
          border={1}
          cellPadding={8}
          style={{
            borderCollapse: "collapse",
            width: "100%",
            textAlign: "left",
          }}
        >
          <thead>
            <tr>
              <th>Parameter</th>
              <th>FY 2023-24</th>
              <th>FY 2022-23</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>Total waste generated (in metric tonnes)</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
            <tr>
              <td>Plastic waste (A)</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
            <tr>
              <td>E-Waste (B)</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
            <tr>
              <td>Bio-Medical Waste (C)</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
            <tr>
              <td>Construction and demolition waste (D)</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
            <tr>
              <td>Battery Waste (E)*</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
            <tr>
              <td>Radioactive waste (F)</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
            <tr>
              <td>Other Hazardous waste. Please specify, if any. (G)</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
            <tr>
              <td>
                Other Non-hazardous waste generated (H). Please specify, if any.{" "}
                <br />
                (Break-up by composition i.e., by materials relevant to the
                sector) Metal Scrap (MS, Aluminum etc.) Paper and Paper Board
                Glass Waste Wood Waste
              </td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
            <tr>
              <td>
                <strong>Total (A+B+C+D+E+F+G+H)</strong>
              </td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
            <tr>
              <td>
                <strong>Waste intensity per rupee of turnover</strong>
              </td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
            <tr>
              <td>
                <strong>
                  Waste intensity per rupee of turnover adjusted Purchasing for
                  Power Parity (PPP)
                </strong>
              </td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
            <tr>
              <td>
                <strong>
                  Waste intensity in terms of physical output (MT/ Ton of
                  Production)
                </strong>
              </td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>

            <tr>
              <td colSpan={3}>
                <em>
                  * Old batteries are disposed to the vendor on "buy back
                  system"
                </em>
              </td>
            </tr>

            <tr>
              <td colSpan={3} style={{ fontWeight: "bold" }}>
                For each category of waste generated, total waste recovered
                through recycling, re-using or other recovery operations (in
                metric tonnes)
              </td>
            </tr>
            <tr>
              <td>(i) Recycled</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
            <tr>
              <td>(ii) Re-used</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
            <tr>
              <td>(iii) Other recovery operations (safely disposed)</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
            <tr>
              <td>
                <strong>Total</strong>
              </td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>

            {/* Disposal Methods */}
            <tr>
              <td colSpan={3} style={{ fontWeight: "bold" }}>
                For each category of waste generated, total waste disposed by
                nature of disposal method (in metric tonnes)
              </td>
            </tr>
            <tr>
              <td>(i) Incineration</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
            <tr>
              <td>(ii) Landfilling</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
            <tr>
              <td>(iii) Other disposal operations (Co-processing)</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
            <tr>
              <td>
                <strong>Total</strong>
              </td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
          </tbody>
        </table>

        <p style={{ fontSize: "0.9em", fontStyle: "italic", marginTop: "1em" }}>
          <strong>Note:</strong> Indicate if any independent assessment/
          evaluation/assurance has been carried out by an external agency? (Y/N)
          If yes, name of the external agency.
        </p>
      </div>

      <div
        style={{
          maxWidth: "90%",
          margin: "auto",
          marginBottom: "2rem",
        }}
      >
        <p style={{ fontWeight: "bold", color: "black", marginBottom: "1rem" }}>
          10. Briefly describe the waste management practices adopted in your
          establishments. Describe the strategy adopted by your company to
          reduce usage of hazardous and toxic chemicals in your products and
          processes and the practices adopted to manage such wastes.
        </p>
      </div>

      <div
        style={{
          maxWidth: "90%",
          margin: "auto",
          marginBottom: "2rem",
        }}
      >
        <p style={{ fontWeight: "bold", color: "black", marginBottom: "1rem" }}>
          11. If the entity has operations/offices in/around ecologically
          sensitive areas (such as national parks, wildlife sanctuaries,
          biosphere reserves, wetlands, biodiversity hotspots, forests, coastal
          regulation zones etc.) where environmental approvals / clearances are
          required, please specify details in the following format:
        </p>
        <table
          border={1}
          cellPadding={8}
          style={{
            borderCollapse: "collapse",
            width: "100%",
            textAlign: "left",
          }}
        >
          <thead>
            <tr>
              <th>S.No</th>
              <th>Location of operations/ offices</th>
              <th>Type of operations</th>
              <th>
                Whether the conditions of environmental approval/ clearance are
                being complied with? (Y/N) If no, the reasons thereof and
                corrective action taken, if any.
              </th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
          </tbody>
        </table>
      </div>

      <div
        style={{
          maxWidth: "90%",
          margin: "auto",
          marginBottom: "2rem",
        }}
      >
        <p style={{ fontWeight: "bold", color: "black", marginBottom: "1rem" }}>
          12. Details of environmental impact assessments of projects undertaken
          by the entity based on applicable laws, in the current financial year:
        </p>
        <table
          border={1}
          cellPadding={8}
          style={{
            borderCollapse: "collapse",
            width: "100%",
            textAlign: "left",
          }}
        >
          <thead>
            <tr>
              <th>Name and brief details of project</th>
              <th>EIA Notification Number</th>
              <th>Date</th>
              <th>
                Whether conducted by independent external agency (Yes / No)
              </th>
              <th>Results communicated in public domain (Yes/No)</th>
              <th>Relevant Web Links</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
          </tbody>
        </table>
      </div>

      <div
        style={{
          maxWidth: "90%",
          margin: "auto",
          marginBottom: "2rem",
        }}
      >
        <p style={{ fontWeight: "bold", color: "black", marginBottom: "1rem" }}>
          13. Is the entity compliant with the applicable environmental law/
          regulations/ guidelines in India, such as the Water (Prevention and
          Control of Pollution) Act, Air (Prevention and Control of Pollution)
          Act, Environment protection act and rules thereunder (Y/N). If not,
          provide details of all such non-compliances, in the following format:
        </p>
        <table
          border={1}
          cellPadding={8}
          style={{
            borderCollapse: "collapse",
            width: "100%",
            textAlign: "left",
          }}
        >
          <thead>
            <tr>
              <th>S.No.</th>
              <th>
                Specify the law / regulation / guidelines which was not complied
                with
              </th>
              <th>Provide details of the non- compliance</th>
              <th>
                Any fines / penalties / action taken by regulatory agencies such
                as pollution control boards or by courts
              </th>
              <th>Corrective action taken if any</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
          </tbody>
        </table>
      </div>

      <div
        style={{
          maxWidth: "90%",
          margin: "auto",
          marginBottom: "2rem",
        }}
      >
        <p
          style={{
            fontWeight: "bold",
            color: "blue",
            textAlign: "center",
            marginBottom: "1rem",
          }}
        >
          Leadership Indicators
        </p>
        <p style={{ fontWeight: "bold", color: "black", marginBottom: "1rem" }}>
          1. Water withdrawal, consumption and discharge in areas of water
          stress (in kilolitres):
        </p>
        <p
          style={{ fontWeight: "normal", color: "black", marginBottom: "1rem" }}
        >
          For each facility / plant located in areas of water stress, provide
          the following information:
        </p>
        <ul style={{ listStyleType: "none", paddingLeft: "0" }}>
          <li>(i) Name of the area </li>
          <li>(ii) Nature of operations</li>
          <li>
            (iii) Water withdrawal, consumption and discharge in the following
            format:
          </li>
        </ul>
        <table
          border={1}
          cellPadding={8}
          style={{
            borderCollapse: "collapse",
            width: "100%",
            textAlign: "left",
          }}
        >
          <thead>
            <tr>
              <th style={{ width: "30%" }}>Parameter</th>
              <th>Unit</th>
              <th>FY 2023-24</th>
              <th>FY 2022-23</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td colSpan={4}>
                <strong>Water withdrawal by source</strong>
              </td>
            </tr>
            <tr>
              <td>(i) Surface water</td>
              <td>KL</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
            <tr>
              <td>(ii) Ground Water</td>
              <td>KL</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
            <tr>
              <td>(iii) Third Party Water</td>
              <td>KL</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
            <tr>
              <td>(iv) Seawater/ Desalinated Water</td>
              <td>KL</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
            <tr>
              <td>(v) Others: (Rainwater Harvesting)</td>
              <td>KL</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>

            <tr>
              <td>
                <strong>
                  Total volume of water withdrawal (in kilolitres)
                </strong>
              </td>
              <td>KL</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
            <tr>
              <td>
                <strong>
                  Total volume of water consumption (in kilolitres)
                </strong>
              </td>
              <td>KL</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>

            <tr>
              <td>
                <strong>
                  Water Consumption intensity per rupee of turnover
                </strong>
              </td>
              <td>KL/Million Rupee</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
            <tr>
              <td>
                <strong>
                  Water intensity per rupee of turnover adjusted for Purchasing
                  Power Parity (PPP)
                  <br />
                  (Total water consumption / Revenue from operations adjusted
                  for PPP)
                </strong>
              </td>
              <td>KL/Million Rupee</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
            <tr>
              <td>
                <strong>Water intensity in terms of physical output</strong>
              </td>
              <td>KL/Tons of Production</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>

            <tr>
              <td colSpan={4} style={{ textAlign: "center" }}>
                <strong>
                  Water discharge by destination and level of treatment (in
                  kilolitres)
                </strong>
              </td>
            </tr>

            <tr>
              <td>
                <strong>vi. To Surface water</strong>
              </td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
            <tr>
              <td>- No treatment</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
            <tr>
              <td>- With treatment – please specify level of treatment</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>

            <tr>
              <td>
                <strong>vii. To Groundwater</strong>
              </td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
            <tr>
              <td>- No treatment</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
            <tr>
              <td>- With treatment – please specify level of treatment</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>

            <tr>
              <td>
                <strong>viii. To Seawater</strong>
              </td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
            <tr>
              <td>- No treatment</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
            <tr>
              <td>- With treatment – please specify level of treatment</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>

            <tr>
              <td>
                <strong>ix. Sent to third-parties</strong>
              </td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
            <tr>
              <td>- No treatment</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
            <tr>
              <td>- With treatment – please specify level of treatment</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>

            <tr>
              <td>
                <strong>x. Others</strong>
              </td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
            <tr>
              <td>- No treatment</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
            <tr>
              <td>- With treatment – please specify level of treatment</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>

            <tr>
              <td>
                <strong>Total water discharged (in kilolitres)</strong>
              </td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
          </tbody>
        </table>
      </div>

      <div
        style={{
          maxWidth: "90%",
          margin: "auto",
          marginBottom: "2rem",
        }}
      >
        <p style={{ fontWeight: "bold", color: "black", marginBottom: "1rem" }}>
          2. Please provide details of total Scope 3 emissions &amp; its
          intensity:
        </p>
        <table
          border={1}
          cellPadding={8}
          style={{
            borderCollapse: "collapse",
            width: "100%",
            textAlign: "left",
          }}
        >
          <thead>
            <tr>
              <th style={{ width: "30%" }}>Parameter</th>
              <th>Unit</th>
              <th>FY 2023-24</th>
              <th>FY 2022-23</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>
                Total Scope 3 emissions (Break-up of the GHG into CO2, CH4, N2O,
                HFCs, PFCs, SF6, NF3, if available)
              </td>
              <td>tCO2e</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
            <tr>
              <td>Total Scope 3 emissions per rupee of turnover</td>
              <td>tCO2e/INR</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
            <tr>
              <td>Scope 3 emission Intensity in terms of physical output</td>
              <td>tCO2e/ Tons of Production</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
          </tbody>
        </table>
      </div>

      <div
        style={{
          maxWidth: "90%",
          margin: "auto",
          marginBottom: "2rem",
        }}
      >
        <p style={{ fontWeight: "bold", color: "black", marginBottom: "1rem" }}>
          3. With respect to the ecologically sensitive areas reported at
          Question 10 of Essential Indicators above, provide details of
          significant direct &amp; indirect impact of the entity on biodiversity
          in such areas along-with prevention and remediation activities.
        </p>
        <p style={{ fontWeight: "bold", color: "black", marginBottom: "1rem" }}>
          3. With respect to the ecologically sensitive areas reported at
          Question 10 of Essential Indicators above, provide details of
          significant direct &amp; indirect impact of the entity on biodiversity
          in such areas along-with prevention and remediation activities.
        </p>
      </div>

      <div
        style={{
          maxWidth: "90%",
          margin: "auto",
          marginBottom: "2rem",
        }}
      >
        <p style={{ fontWeight: "bold", color: "black", marginBottom: "1rem" }}>
          4. If the entity has undertaken any specific initiatives or used
          innovative technology or solutions to improve resource efficiency, or
          reduce impact due to emissions / effluent discharge / waste generated,
          please provide details of the same as well as outcome of such
          initiatives:
        </p>
        <table
          border={1}
          cellPadding={8}
          style={{
            borderCollapse: "collapse",
            width: "100%",
            textAlign: "left",
          }}
        >
          <thead>
            <tr>
              <th style={{ width: "10%" }}>S.No</th>
              <th>Initiatives Undertaken</th>
              <th>
                Details of the initiative (Web-link, if any, may be provided
                along-with summary)
              </th>
              <th>Outcome of the initiative</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>1</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
            <tr>
              <td>2</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
            <tr>
              <td>3</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
            <tr>
              <td>4</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
          </tbody>
        </table>
      </div>

      <div
        style={{
          maxWidth: "90%",
          margin: "auto",
          marginBottom: "8rem",
        }}
      >
        <p style={{ fontWeight: "bold", color: "black", marginBottom: "1rem" }}>
          5. Does the entity have a business continuity and disaster management
          plan? Give details in 100 words/ web link.
        </p>
        <p style={{ fontWeight: "bold", color: "black", marginBottom: "1rem" }}>
          6. Percentage of value chain partners (by value of business done with
          such partners) that were assessed for environmental impacts
        </p>
      </div>
    </div>
  );
};

export default PrincipleSix;
