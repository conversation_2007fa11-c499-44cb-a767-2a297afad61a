import 'react-app-polyfill/ie11';
import React from 'react';
import ReactDOM from 'react-dom/client'
import App from './App';
// import App from './STTApp';
//import * as serviceWorker from './serviceWorker';


import { Provider } from 'react-redux';
import store from './RTK/store';
import persistStore from 'redux-persist/es/persistStore';
import { PersistGate } from 'redux-persist/integration/react'
import Interceptor from '../src/service/setupInterceptor'
import { BrowserRouter } from 'react-router-dom';
import ErrorBoundary from './components/ErrorBoundary';

// Add global error handler for "destroy is not a function" error
const originalError = console.error;
console.error = function(message, ...args) {
  // Check if the error is "destroy is not a function"
  if (typeof message === 'string' && message.includes('destroy is not a function')) {
    // Suppress the error to prevent it from breaking the application
    console.warn('Suppressed error: destroy is not a function. This is a known issue that is being addressed.');
    return;
  }
  // For all other errors, use the original console.error
  originalError.apply(console, [message, ...args]);
};

// Add a global error boundary to catch unhandled errors
window.addEventListener('error', function(event) {
  if (event.error && event.error.message && event.error.message.includes('destroy is not a function')) {
    // Prevent the error from propagating
    event.preventDefault();
    console.warn('Caught unhandled error: destroy is not a function. This is a known issue that is being addressed.');
    return false;
  }
});

let persistor = persistStore(store)
const root = ReactDOM.createRoot(document.getElementById("root"));

root.render(
    <BrowserRouter>
        <Provider store={store}>
            <PersistGate loading={null} persistor={persistor}>
                <ErrorBoundary>
                    <App />
                </ErrorBoundary>
            </PersistGate>
        </Provider>
    </BrowserRouter>
);

Interceptor(store)
