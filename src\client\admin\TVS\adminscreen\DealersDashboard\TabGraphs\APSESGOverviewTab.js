import React from "react";
import {
  <PERSON><PERSON><PERSON>,
  Bar,
  XAxis,
  <PERSON>A<PERSON><PERSON>,
  Tooltip,
  Legend,
  ResponsiveContainer,
  CartesianGrid,
  ReferenceLine,
  Label,
  Cell,
} from "recharts";
import { useEffect, useState } from "react";

const APSESGOverviewTab = ({ data }) => {
  const [selectedData, setSelectedData] = useState([]);
  const [msiScore, setMsiScore] = useState(0);

  useEffect(() => {
    const transformedData = transformData(data);
    console.log(transformedData, 'transformedData');
    setSelectedData(transformedData);
    
    // Calculate average MSI score from total_score
    if (data && data.length > 0) {
      const avgScore = data.reduce((sum, item) => sum + (item.total_score || 0), 0) / data.length;
      setMsiScore(avgScore);
    }
  }, [data]);

  const transformData = (data) => {
    if (!data || data.length === 0) return [];

    // Define the categories based on APS data structure
    const categories = [
      { key: "water_management", label: "Water Management", maxValue: 10 },
      { key: "waste_management", label: "Waste Management", maxValue: 10 },
      { key: "energy_management", label: "Energy Management", maxValue: 10 },
      { key: "road_safety", label: "Road Safety", maxValue: 10 },
      { key: "electrical_safety", label: "Electrical Safety", maxValue: 12 },
      { key: "fire_safety", label: "Fire Safety", maxValue: 10 },
      { key: "5s", label: "5S & House Keeping", maxValue: 12 },
      { key: "personal_safety", label: "Personnel Safety", maxValue: 6 },
      { key: "governance_framework", label: "Governance Framework", maxValue: 10 },
    ];

    return categories.map(({ key, label, maxValue }) => {
      // Calculate average value for this category across all data points
      const achieved = data.reduce((sum, item) => {
        const value = item[key] !== undefined ? item[key] : 0;
        return sum + value;
      }, 0) / data.length;
      
      const achievedClamped = Math.max(0, parseFloat(achieved.toFixed(2))); // Ensuring non-negative values
      
      return {
        category: label,
        achieved: achievedClamped,
        remaining: parseFloat((maxValue - achievedClamped).toFixed(2)),
        maxValue: maxValue
      };
    });
  };

  const getMSIGrade = (score) => {
    if (score >= 85) return "Platinum";
    if (score >= 71) return "Gold";
    if (score >= 56) return "Silver";
    if (score >= 41) return "Bronze";
    return "Needs Improvement";
  };

  const msiGrade = getMSIGrade(msiScore);

  const CustomLegend = (props) => {
    const { payload } = props;
    return (
      <ul
        style={{
          display: "flex",
          listStyleType: "none",
          justifyContent: "center",
          padding: 0,
          marginTop: "10px",
        }}
      >
        {payload.map((entry, index) => (
          <li
            key={`item-${index}`}
            style={{
              color: entry.color,
              marginRight: "5px",
            }}
          >
            <span
              style={{
                color: entry.color,
                backgroundColor: entry.color,
                marginRight: 4,
                fontSize: "20px",
                width: "10px",
                height: "10px",
                borderRadius: "50%",
                display: "inline-block",
              }}
            ></span>
            <span style={{ color: "#555", fontSize: "14px" }}>
              {entry.value}
            </span>
          </li>
        ))}
      </ul>
    );
  };

  // Bar colors based on MSI grade
  const getBarColor = (score) => {
    if (score === "Platinum") return "#C0C0C0"; // Silver color for Platinum
    if (score === "Gold") return "#FFD700"; // Gold color
    if (score === "Silver") return "#E0E0E0"; // Light gray for Silver
    if (score === "Bronze") return "#CD7F32"; // Bronze color
    return "#FF2E00"; // Red for Needs Improvement
  };

  return (
    <div style={{ padding: "20px" }}>
      {/* MSI Score and Grade Display */}
      <div
        style={{
          display: "flex",
          justifyContent: "space-around",
          marginBottom: "20px",
        }}
      >
        <h2 style={{ color: "#0D5EAF" }}>MSI Score: {msiScore.toFixed(1)}</h2>
        <h2 style={{ color: "#0D5EAF" }}>MSI Grade: {msiGrade}</h2>
      </div>

      {/* MSI Chart */}
      <div style={{ width: "100%", height: 400, marginBottom: "40px" }}>
        <ResponsiveContainer width="100%" height="100%">
          <BarChart
            data={selectedData}
            barSize={40}
            margin={{ top: 20, right: 30, left: 20, bottom: 15 }}
          >
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="category" tick={{ fontSize: 10, angle: -45, textAnchor: 'end' }} height={80} />
            <YAxis domain={[0, 'dataMax']} />
            <Tooltip
              formatter={(value, name, props) => {
                if (name === "Achieved Score") {
                  return [`${value.toFixed(2)} (Max: ${props.payload.maxValue})`, name];
                }
                return [null, null];
              }}
            />
            <Legend content={CustomLegend} />
            <Bar dataKey="achieved" stackId="score" name="Achieved Score">
              {selectedData.map((entry, index) => {
                const scorePercentage = (entry.achieved / entry.maxValue) * 100;
                return (
                  <Cell
                    key={`cell-achieved-${index}`}
                    fill={getBarColor(getMSIGrade(scorePercentage))}
                  />
                );
              })}
            </Bar>
            <Bar
              dataKey="remaining"
              stackId="score"
              fill="#CCCED5"
              name="Remaining"
            />
            {/* Reference Line for MSI Score */}
            <ReferenceLine y={msiScore / 10} stroke="#FF5733" strokeDasharray="3 3">
              <Label
                value={`Average MSI: ${msiScore.toFixed(1)}`}
                position="center"
                fill="#FF5733"
              />
            </ReferenceLine>
          </BarChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
};

export default APSESGOverviewTab;
