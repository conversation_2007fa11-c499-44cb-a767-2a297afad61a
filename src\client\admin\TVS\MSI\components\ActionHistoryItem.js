import React from 'react';
import { Card } from 'primereact/card';
import { Divider } from 'primereact/divider';
import { Tag } from 'primereact/tag';
import moment from 'moment';

const ActionHistoryItem = ({ history, index, getUserName }) => {
    return (
        <Card className="shadow-1 h-full">
            <div className="flex justify-content-between align-items-center mb-2">
                <h5 className="m-0 flex align-items-center">
                    <i className="pi pi-check-circle mr-2" style={{ color: history.reject === 1 ? '#f44336' : '#4caf50' }}></i>
                    Action {index + 1}
                </h5>
                <Tag
                    value={history.reject === 1 ? 'Rejected' : history.type === 2 ? 'Approved' : history.type === 3 ? 'Closed' : 'Submitted'}
                    severity={history.reject === 1 ? 'danger' : 'success'}
                />
            </div>
            <Divider className="my-2" />

            <div className="grid">
                <div className="col-12 md:col-6">
                    <div className="field mb-2">
                        <label className="block text-sm font-medium text-gray-700">Root Cause</label>
                        <div className="p-2 border-1 border-gray-300 border-round bg-gray-50" dangerouslySetInnerHTML={{ __html: history.rootCause }} />
                    </div>
                </div>

                <div className="col-12 md:col-6">
                    <div className="field mb-2">
                        <label className="block text-sm font-medium text-gray-700">Corrective Action</label>
                        <div className="p-2 border-1 border-gray-300 border-round bg-gray-50" dangerouslySetInnerHTML={{ __html: history.correctiveAction }} />
                    </div>
                </div>

                <div className="col-12">
                    <div className="field mb-2">
                        <label className="block text-sm font-medium text-gray-700">Action Taken</label>
                        <div className="p-2 border-1 border-gray-300 border-round bg-gray-50" dangerouslySetInnerHTML={{ __html: history.actionTaken }} />
                    </div>
                </div>

                <div className="col-12">
                    <div className="grid">
                        <div className="col-6">
                            <div className="field mb-2">
                                <label className="block text-sm font-medium text-gray-700">Action Taken Date</label>
                                <div>{moment(history.supplier_submitted_on).format('DD-MM-YYYY hh:mm A')}</div>
                            </div>
                        </div>

                        {history.reject === 1 && (
                            <>
                                <div className="col-6">
                                    <div className="field mb-2">
                                        <label className="block text-sm font-medium text-gray-700">Rejected By</label>
                                        <div>{getUserName(history.returned_by)}</div>
                                    </div>
                                </div>
                                <div className="col-6">
                                    <div className="field mb-2">
                                        <label className="block text-sm font-medium text-gray-700">Rejected Date</label>
                                        <div>{moment(history.returned_on).format('DD-MM-YYYY hh:mm A')}</div>
                                    </div>
                                </div>
                            </>
                        )}

                        {(history.reject === 0 && (history.type === 2 || history.type === 3)) && (
                            <>
                                <div className="col-6">
                                    <div className="field mb-2">
                                        <label className="block text-sm font-medium text-gray-700">Approved By</label>
                                        <div>{getUserName(history.approved_by)}</div>
                                    </div>
                                </div>
                                <div className="col-6">
                                    <div className="field mb-2">
                                        <label className="block text-sm font-medium text-gray-700">Approved On</label>
                                        <div>{moment(history.approved_on).format('DD-MM-YYYY hh:mm A')}</div>
                                    </div>
                                </div>
                            </>
                        )}
                    </div>
                </div>

                {(history.reject === 1 || (history.reject === 0 && (history.type === 2 || history.type === 3))) && (
                    <div className="col-12">
                        <div className="field mb-2">
                            <label className="block text-sm font-medium text-gray-700">
                                {history.reject === 1 ? 'Approver Comments (Rejection)' : 'Approver Comments'}
                            </label>
                            <div className="p-2 border-1 border-gray-300 border-round bg-gray-50">{history.approverComments}</div>
                        </div>
                    </div>
                )}
            </div>
        </Card>
    );
};

export default ActionHistoryItem;
