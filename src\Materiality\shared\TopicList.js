import React, { useState } from "react";
import Editable from "react-bootstrap-editable";
const TopicList = (props) => {


    return (
        <ul className="profile-list">
            {
                props.topics.map((topic) => {
                    return <li key={topic.id} className={"profile-list-item " + (props.topicId === topic.id ? "active" : "")}> <a style={{'cursor' : 'pointer'}} onClick={(e) => props.onClick(e, topic.id)}> <div className="user"><p className="u-name">
                        
                        <Editable
                            onSubmit={(value) => props.onTopicChange(value, topic.id)}
                            initialValue={topic.name}
                            isValueClickable
                            mode="inline"
                        />
                
                </p>
                
            </div>
            <div className="details d-flex align-items-center">
                <i className="mdi mdi-chevron-right"></i>
            </div>            
        </a>
        
    </li>
                })
            }
            
                   </ul>
    );
}

export default TopicList;