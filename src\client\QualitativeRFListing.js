import Axios from "axios";
import React, { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import useForceUpdate from "use-force-update";
import { Dropdown } from "primereact/dropdown";
import { EditText } from "react-edit-text";
import { Button } from "primereact/button";
import { Calendar } from "primereact/calendar";
import { InputTextarea } from "primereact/inputtextarea";
import { Dialog } from "primereact/dialog";
import { InputText } from "primereact/inputtext";
import { CascadeSelect } from "primereact/cascadeselect";
import Swal from "sweetalert2";
import { Checkbox } from 'primereact/checkbox';
import $, { data } from "jquery";
import { API } from "../constants/api_url";
import { RadioButton } from "primereact/radiobutton";
import LazyView from "../components/LazyView";
import { MultiSelect } from 'primereact/multiselect';
import moment from "moment";
import { DataTable } from "primereact/datatable";
import { Column } from "primereact/column";
import { useHistory } from "react-router-dom/cjs/react-router-dom.min";
import { Tooltip } from 'primereact/tooltip';
import APIServices from "../service/APIService";
import { TabMenu } from "primereact/tabmenu";
import { DateTime } from "luxon";
import { Badge } from "primereact/badge";
import { ListBox } from "primereact/listbox";
import { FilterMatchMode, FilterService } from "primereact/api";

window.jQuery = $;
window.$ = $;
let activeRowData = {}
FilterService.register('custom_coverage_', (value, filters) => {
    if (value === undefined && filters === null) return true;
    if (value !== undefined && filters === null) return true;
    if (value !== undefined && filters.length === 0) return true;
    console.log(value, filters)
    return filters.map(i => i.name).includes(value)
});
const QualitativeRFListing = () => {
    const [rfuserass, setRFUserAss] = useState([])
    const [rfentityass, setRFEntityAss] = useState([])
    const [assfilter, setAssFilter] = useState({ coverage_: { value: null, matchMode: 'custom' } })
    const [activeindex, setActiveIndex] = useState(0)
    const [requiredList, setRequiredList] = useState([])
    const [requiredListBK, setRequiredListBK] = useState([])
    const [requiredList2, setRequiredList2] = useState([])
    const [requiredList2BK, setRequiredList2BK] = useState([])
    const [selectedFramework, setSelectedFramework] = useState('All')
    const [assFramework, setAssFramework] = useState([])
    const [selectedFramework2, setSelectedFramework2] = useState('All')
    const [assFramework2, setAssFramework2] = useState([])
    const login_data = useSelector((state) => state.user.userdetail);
    const admin_data = useSelector((state) => state.user.admindetail);

    const userList = useSelector(state => state.userlist.userList)
    const [user, setUser] = useState(null);
    const [list, setList] = useState({ category: null, topic: null, metric: null, framework: null })
    const [selected, setSelected] = useState({ category: [], topic: [], framework: [], metric: [] })
    const [selectedloc, setSelectedLoc] = useState({ level: null, coverage: null, reporter_ids: [], type: 1, rfid: null })
    const [selectedentity, setSelectedEntity] = useState({ country_ids: [], other_ids: [], city_ids: [], site_ids: [], rfid: null, type: activeindex === 0 ? 1 : 2 })
    const navigate = useHistory()
    const [selDataPoint, setSelDataPoint] = useState([])
    const configtype = [{ name: 'Location' }, { name: 'Data Point' }]
    const forceUpdate = useForceUpdate();
    const [editmode, setEditMode] = useState(false)
    const [old, setOld] = useState([]);
    const [datapoint, setDataPoint] = useState([]);
    const [metriclist, setMetricList] = useState([]);
    const [metricbk, setMetricBk] = useState([])
    const [raw, setRaw] = useState([])
    const [usermodal, setUserModal] = useState(false)
    const [userassmodal, setUserAssModal] = useState(false)
    const [enititymodal, setEntityModal] = useState(false)

    const [selectedrf, setSelectedRF] = useState({})
    const [response, setResponse] = useState([])
    const [rfresponse, setRFResponse] = useState([])
    const [historydata, setHistoryData] = useState([])
    const [historydialog, setHistoryDialog] = useState(false)
    const [assigndialog, setAssignDialog] = useState(false)
    const [usermetric, setUserMetric] = useState([])
    const [rawrf, setRawRF] = useState([])
    const [dupdpiddialog, setDupDPIDDialog] = useState(false)
    const [dupdpid, setDupId] = useState([])
    const [selecteddcf, setSelectedDCF] = useState([]);
    const [selecteddcfbk, setSelectedDCFBK] = useState([]);
    const [selectedlist, setSelectedList] = useState({ title: '', data: [] })
    const [selectedLoclist, setSelectedLocList] = useState({ country: [], city: [], site: [] })
    const [rawsitelist, setRawSitelist] = useState([])
    const [prevdialog, setPrevDialog] = useState(false);
    const [search, setSearch] = useState({ metric: '', dcf: '' })
    const [location, setLocation] = useState([]);
    const [overallmetric, setOverallMetric] = useState([]);
    const [userConfig, setUserConfig] = useState({
        name: "", type: "",
        location: ''
    });
    const items = [
        { label: 'Required', id: 1 },
        { label: 'Additional', id: 2 },
        { label: 'Assignment', id: 3 }
    ];
    const [module, setModule] = useState({
        tier1: "",
        tier2: "",
        tier3: "",
    });
    const [cascade, setCascade] = useState("");
    const [showSave, setShowSave] = useState(0);

    const [tier2, setTier2] = useState([]);
    const [tier3, setTier3] = useState([]);
    const [moduleList, setModuleList] = useState({
        mod: [],
        title: [],
        topic: [],
    });

    useEffect(async () => {
       
        APIServices.get(API.RF_User_UP(admin_data.id)).then((k) => {
            setRFUserAss(k.data)
        })
        APIServices.get(API.RF_Entity_UP(admin_data.id)).then((k) => {
            setRFEntityAss(k.data)
        })

        let uriString = {
            "include": [{ "relation": "locationTwos", "scope": { "include": [{ "relation": "locationThrees" }] } }]

        }
        let uriString2 = {
            "include": [{ "relation": "newTopics", "scope": { "include": [{ "relation": "newMetrics", "scope": { "include": [{ "relation": "newDataPoints" }] } }] } }]


        }

        let promise1 = APIServices.get(API.RF)


        let promise2 = APIServices.get(API.RF_Submit_UP(admin_data.id))
        let promise3 = APIServices.get(API.Report_Name_Twos)
        let promise5 = APIServices.get(API.QL_Listing_Filter_UP(admin_data.id))
        let promise6 = APIServices.get(API.AssignDCFClient_UP(admin_data.id))
        let Overall = API.LocationOne_UP(admin_data.id) + `?filter=${encodeURIComponent(JSON.stringify(uriString))}`;

        let url = API.Categories + `?filter=${encodeURIComponent(JSON.stringify(uriString2))}`;
        let promise4 = APIServices.get(Overall)
        let promise7 = APIServices.get(url)
        Promise.all([promise1, promise2, promise3, promise4, promise5, promise6, promise7]).then(function (values) {

            const shapedSite = values[3].data.map(item => {
                if (item.locationTwos) {
                    item.locationTwos = item.locationTwos.filter(locationTwo =>
                        locationTwo.locationThrees && locationTwo.locationThrees.length > 0
                    );
                }
                return item;
            }).filter(item => item.locationTwos && item.locationTwos.length > 0)
            let country = [{ name: 'All', id: 0 }], city = [{ name: 'All', id: 0 }], site = [{ name: 'All', id: 0 }]
            console.log(shapedSite)
            shapedSite.forEach((i) => {
                country.push({ name: i.name, id: i.id })
                i.locationTwos.forEach((j) => {
                    city.push({ name: j.name, id: j.id })
                    j.locationThrees.forEach((k) => {
                        site.push({ name: k.name, id: k.id })
                    })
                })
            })

            setSelectedLocList({ country, city, site })
            setRawSitelist(shapedSite)
            let topic_ids = values[5].data[0].topic_ids
            let required_rf = [], optional_rf = []
            setRawRF(values[0].data)
            setRFResponse(values[1].data)
            values[6].data.forEach((cat) => {
                if (cat.newTopics) {
                    cat.newTopics.forEach((topic) => {
                        if (topic_ids.includes(topic.id) && topic.newMetrics) {
                            topic.newMetrics.forEach((metric) => {
                                if (Array.isArray(metric.data1) && metric.data1[0] !== undefined && metric.data1[0].type === 1 && values[0].data.map(i => i.id).includes(metric.data1[0].rf) && (metric.tag === null || parseFloat(metric.tag) === admin_data.id)) {
                                    console.log(values[0].data.find(i => i.id === metric.data1[0].rf))
                                    required_rf.push({ cat_id: cat.id, top_id: topic.id, overallTags: [metric.data1[0].tags1, metric.data1[0].tags2, metric.data1[0].tags3], cat_title: cat.title, top_title: topic.title, title: metric.title, ...metric })

                                }
                            })
                        } else if (!topic_ids.includes(topic.id) && topic.newMetrics) {
                            topic.newMetrics.forEach((metric) => {
                                if (Array.isArray(metric.data1) && metric.data1[0] !== undefined && metric.data1[0].type === 1 && values[0].data.map(i => i.id).includes(metric.data1[0].rf) && (metric.tag === null || parseFloat(metric.tag) === admin_data.id)) {
                                    console.log(values[0].data.find(i => i.id === metric.data1[0].rf))
                                    optional_rf.push({ cat_id: cat.id, top_id: topic.id, overallTags: [metric.data1[0].tags1, metric.data1[0].tags2, metric.data1[0].tags3], cat_title: cat.title, top_title: topic.title, title: metric.title, ...metric })

                                }
                            })
                        }
                    }
                    )
                }
            })
            
            setAssFramework(values[2].data.filter((i) => { return admin_data.information.report.includes(i.id) }))
            setAssFramework2(values[2].data)
            setRequiredList2(optional_rf.filter((i) => { return !required_rf.map(i => i.id).includes(i.id) }))
            setRequiredList2BK(optional_rf.filter((i) => { return !required_rf.map(i => i.id).includes(i.id) }))
            setRequiredList(required_rf)
            setRequiredListBK(required_rf)
        })

    }, [login_data]);

    const renderResponseTable = (id, category, topic, frameworks, resdata, listdata, frameList) => {
        let loc = selected, topicList = [], framework = [], categoryList = []
        let loclist = list
        loc['id'] = id;
        loc.category = category
        loc.topic = topic
        loc.framework = frameworks
        resdata.forEach((cat) => {
            if (cat.newTopics !== undefined) {
                categoryList.push(cat)
                if (loc.category.includes(cat.id)) {
                    cat.newTopics.forEach((topic) => {
                        topic.newMetrics !== undefined && topicList.push({ name: topic.title, title: topic.title + "(" + cat.title + ")", id: topic.id })
                        if (topic.newMetrics !== undefined && loc.topic.includes(topic.id)) {

                            topic.newMetrics.forEach((metric) => {
                                if (Array.isArray(metric.data1) && metric.data1.length !== 0) {
                                    if (Object.keys(metric.data1[0]).length > 2) {
                                        if (metric.data1[0].type === 1 && metric.data1[0].rf !== null) {

                                            // let index = framework.findIndex((k) => { return k.cat_id === cat.id && k.top_id === topic.id })
                                            // if (index !== -1) {
                                            //     framework[index].metric.push(metric)
                                            // } else {
                                            let tag = []
                                            console.log(loc.framework, listdata.framework)
                                            let tags = JSON.parse(JSON.stringify(loc.framework)).map((fw) => { return listdata.framework.filter((k) => { return k.id === fw })[0].title.trim().toLowerCase() })
                                            let frame = JSON.parse(JSON.stringify(loc.framework)).map((fw) => { return listdata.framework.filter((k) => { return k.id === fw })[0] })


                                            let one = metric.data1[0].tags1.some((str) => tags.some((fruit) => str.toLowerCase().includes(fruit.toLowerCase())))
                                            let two = metric.data1[0].tags2.some((str) => tags.some((fruit) => str.toLowerCase().includes(fruit.toLowerCase())))
                                            let three = metric.data1[0].tags3.some((str) => tags.some((fruit) => str.toLowerCase().includes(fruit.toLowerCase())))
                                            const matchedTag1 = metric.data1[0].tags1.filter((str) => tags.some((fruit) => str.toLowerCase().includes(fruit.toLowerCase())));
                                            const matchedTag2 = metric.data1[0].tags2.filter((str) => tags.some((fruit) => str.toLowerCase().includes(fruit.toLowerCase())));
                                            const matchedTag3 = metric.data1[0].tags3.filter((str) => tags.some((fruit) => str.toLowerCase().includes(fruit.toLowerCase())));

                                            frame.forEach((t) => {
                                                matchedTag1.forEach((i) => {

                                                    if (i.trim().toLowerCase().includes(t.title.trim().toLowerCase())) {
                                                        !tag.includes(t.id) && tag.push(t.id)
                                                    }


                                                })
                                                matchedTag2.forEach((i) => {

                                                    if (i.trim().toLowerCase().includes(t.title.trim().toLowerCase())) {
                                                        !tag.includes(t.id) && tag.push(t.id)
                                                    }


                                                })
                                                matchedTag3.forEach((i) => {

                                                    if (i.trim().toLowerCase().includes(t.title.trim().toLowerCase())) {
                                                        !tag.includes(t.id) && tag.push(t.id)
                                                    }


                                                })
                                            })
                                            console.log(matchedTag1, matchedTag2, matchedTag3, tag)
                                            if (one || two || three) {
                                                framework.push({ tags: tag, overallTags: [metric.data1[0].tags1, metric.data1[0].tags2, metric.data1[0].tags3], cat_id: cat.id, top_id: topic.id, cat_title: cat.title, top_title: topic.title, ...metric })
                                            }
                                            // }

                                        }
                                    }
                                }

                            })
                        }
                    })
                }
            }


        })
        loclist.metric = framework
        loclist.category = categoryList
        loclist.topic = topicList
        loc.category = categoryList.filter((i) => { return loc.category.includes(i.id) }).map((k) => { return k.id })
        loc['topic'] = topicList.filter((i) => { return loc.topic.includes(i.id) }).map((k) => { return k.id })
        loc.framework = frameList.filter((i) => { return loc.framework.includes(i.id) }).map((k) => { return k.id })
        console.log(categoryList)
        setSelected(loc)

        setList(loclist)
        forceUpdate()
    }

    const updateSelected = (obj, val) => {
        let loc = selected;
        loc[obj] = val;
        console.log(val)
        let ser = search
        ser.dcf = ''
        ser.metric = ''
        let categoryList = [], metricList = [], topicList = [], userSelectedMetric = []
        let loclist = list


        setSearch(ser)
        if (obj === 'user') {
            APIServices.get(API.AssignDCFClient_UP(val.id)).then((res) => {
                setEditMode(res.data.length === 0 ? false : true)

                if (res.data.length !== 0) {



                    response.forEach((cat) => {
                        if (cat.newTopics !== undefined) {
                            categoryList.push({ id: cat.id, title: cat.title })
                            cat.newTopics.forEach((topic) => {
                                topic.newMetrics !== undefined && topicList.push({ name: topic.title, title: topic.title + "(" + cat.title + ")", id: topic.id })
                                if (topic.newMetrics !== undefined) {

                                    topic.newMetrics.forEach((metric) => {
                                        metricList.push(metric)
                                    })
                                }
                            })
                        }


                    })
                    let loc = JSON.parse(JSON.stringify(metricList)).map(k => { return { title: k.title, id: k.id, selected: false } })

                    res.data.forEach((item) => {
                        userSelectedMetric = item.metric_ids
                    })
                    setUserMetric(userSelectedMetric)
                    loclist.category = categoryList
                    loclist.metric = metricList
                    loclist.topic = topicList

                    setList(loclist)

                    forceUpdate()
                }
            })
        } else if (obj === 'category') {
            let framework = []
            response.forEach((cat) => {
                if (cat.newTopics !== undefined && val.includes(cat.id)) {

                    cat.newTopics.forEach((topic) => {
                        topic.newMetrics !== undefined && topicList.push({ name: topic.title, title: topic.title + "(" + cat.title + ")", id: topic.id })
                        if (topic.newMetrics !== undefined && loc.topic.includes(topic.id)) {

                            topic.newMetrics.forEach((metric) => {
                                if (Array.isArray(metric.data1) && metric.data1.length !== 0) {
                                    if (Object.keys(metric.data1[0]).length > 2) {
                                        if (metric.data1[0].type === 1 && metric.data1[0].rf !== null) {

                                            // let index = framework.findIndex((k) => { return k.cat_id === cat.id && k.top_id === topic.id })
                                            // if (index !== -1) {
                                            //     framework[index].metric.push(metric)
                                            // } else {
                                            let tag = []
                                            let tags = JSON.parse(JSON.stringify(loc.framework)).map((fw) => { return list.framework.filter((k) => { return k.id === fw })[0].title.trim().toLowerCase() })
                                            let frame = JSON.parse(JSON.stringify(loc.framework)).map((fw) => { return list.framework.filter((k) => { return k.id === fw })[0] })


                                            let one = metric.data1[0].tags1.some((str) => tags.some((fruit) => str.toLowerCase().includes(fruit.toLowerCase())))
                                            let two = metric.data1[0].tags2.some((str) => tags.some((fruit) => str.toLowerCase().includes(fruit.toLowerCase())))
                                            let three = metric.data1[0].tags3.some((str) => tags.some((fruit) => str.toLowerCase().includes(fruit.toLowerCase())))
                                            const matchedTag1 = metric.data1[0].tags1.filter((str) => tags.some((fruit) => str.toLowerCase().includes(fruit.toLowerCase())));
                                            const matchedTag2 = metric.data1[0].tags2.filter((str) => tags.some((fruit) => str.toLowerCase().includes(fruit.toLowerCase())));
                                            const matchedTag3 = metric.data1[0].tags3.filter((str) => tags.some((fruit) => str.toLowerCase().includes(fruit.toLowerCase())));

                                            frame.forEach((t) => {
                                                matchedTag1.forEach((i) => {

                                                    if (i.trim().toLowerCase().includes(t.title.trim().toLowerCase())) {
                                                        !tag.includes(t.id) && tag.push(t.id)
                                                    }


                                                })
                                                matchedTag2.forEach((i) => {

                                                    if (i.trim().toLowerCase().includes(t.title.trim().toLowerCase())) {
                                                        !tag.includes(t.id) && tag.push(t.id)
                                                    }


                                                })
                                                matchedTag3.forEach((i) => {

                                                    if (i.trim().toLowerCase().includes(t.title.trim().toLowerCase())) {
                                                        !tag.includes(t.id) && tag.push(t.id)
                                                    }


                                                })
                                            })
                                            console.log(matchedTag1, matchedTag2, matchedTag3, tag)
                                            if (one || two || three) {
                                                framework.push({ tags: tag, overallTags: [metric.data1[0].tags1, metric.data1[0].tags2, metric.data1[0].tags3], cat_id: cat.id, top_id: topic.id, cat_title: cat.title, top_title: topic.title, ...metric })
                                            }
                                            // }

                                        }
                                    }
                                }
                            })
                        }
                    })
                }


            })

            loclist.metric = framework
            loclist.topic = topicList
            loc['topic'] = topicList.filter((i) => { return loc.topic.includes(i.id) }).map((k) => { return k.id })



            setList(loclist)
        } else if (obj === 'topic') {
            let framework = []
            response.forEach((cat) => {
                if (cat.newTopics !== undefined) {

                    cat.newTopics.forEach((topic) => {
                        topic.newMetrics !== undefined && topicList.push({ name: topic.title, title: topic.title + "(" + cat.title + ")", id: topic.id })
                        if (topic.newMetrics !== undefined && val.includes(topic.id)) {

                            topic.newMetrics.forEach((metric) => {
                                if (Array.isArray(metric.data1) && metric.data1.length !== 0) {
                                    if (Object.keys(metric.data1[0]).length > 2) {
                                        if (metric.data1[0].type === 1 && metric.data1[0].rf !== null) {

                                            // let index = framework.findIndex((k) => { return k.cat_id === cat.id && k.top_id === topic.id })
                                            // if (index !== -1) {
                                            //     framework[index].metric.push(metric)
                                            // } else {
                                            let tag = []
                                            let tags = JSON.parse(JSON.stringify(loc.framework)).map((fw) => { return list.framework.filter((k) => { return k.id === fw })[0].title.trim().toLowerCase() })
                                            let frame = JSON.parse(JSON.stringify(loc.framework)).map((fw) => { return list.framework.filter((k) => { return k.id === fw })[0] })


                                            let one = metric.data1[0].tags1.some((str) => tags.some((fruit) => str.toLowerCase().includes(fruit.toLowerCase())))
                                            let two = metric.data1[0].tags2.some((str) => tags.some((fruit) => str.toLowerCase().includes(fruit.toLowerCase())))
                                            let three = metric.data1[0].tags3.some((str) => tags.some((fruit) => str.toLowerCase().includes(fruit.toLowerCase())))
                                            const matchedTag1 = metric.data1[0].tags1.filter((str) => tags.some((fruit) => str.toLowerCase().includes(fruit.toLowerCase())));
                                            const matchedTag2 = metric.data1[0].tags2.filter((str) => tags.some((fruit) => str.toLowerCase().includes(fruit.toLowerCase())));
                                            const matchedTag3 = metric.data1[0].tags3.filter((str) => tags.some((fruit) => str.toLowerCase().includes(fruit.toLowerCase())));

                                            frame.forEach((t) => {
                                                matchedTag1.forEach((i) => {

                                                    if (i.trim().toLowerCase().includes(t.title.trim().toLowerCase())) {
                                                        !tag.includes(t.id) && tag.push(t.id)
                                                    }


                                                })
                                                matchedTag2.forEach((i) => {

                                                    if (i.trim().toLowerCase().includes(t.title.trim().toLowerCase())) {
                                                        !tag.includes(t.id) && tag.push(t.id)
                                                    }


                                                })
                                                matchedTag3.forEach((i) => {

                                                    if (i.trim().toLowerCase().includes(t.title.trim().toLowerCase())) {
                                                        !tag.includes(t.id) && tag.push(t.id)
                                                    }


                                                })
                                            })
                                            console.log(matchedTag1, matchedTag2, matchedTag3, tag)
                                            if (one || two || three) {
                                                framework.push({ tags: tag, overallTags: [metric.data1[0].tags1, metric.data1[0].tags2, metric.data1[0].tags3], cat_id: cat.id, top_id: topic.id, cat_title: cat.title, top_title: topic.title, ...metric })
                                            }
                                            // }

                                        }
                                    }
                                }
                            })
                        }
                    })
                }


            })
            console.log(loc)
            loclist.metric = framework


            setList(loclist)
        } else if (obj === 'framework') {
            let framework = []
            response.forEach((cat) => {
                if (cat.newTopics !== undefined) {

                    cat.newTopics.forEach((topic) => {
                        topic.newMetrics !== undefined && topicList.push({ name: topic.title, title: topic.title + "(" + cat.title + ")", id: topic.id })
                        if (topic.newMetrics !== undefined && selected.topic.includes(topic.id)) {

                            topic.newMetrics.forEach((metric) => {
                                if (Array.isArray(metric.data1) && metric.data1.length !== 0) {
                                    if (Object.keys(metric.data1[0]).length > 2) {
                                        if (metric.data1[0].type === 1 && metric.data1[0].rf !== null) {

                                            // let index = framework.findIndex((k) => { return k.cat_id === cat.id && k.top_id === topic.id })
                                            // if (index !== -1) {
                                            //     framework[index].metric.push(metric)
                                            // } else {
                                            let tag = []
                                            let tags = JSON.parse(JSON.stringify(val)).map((fw) => { return list.framework.filter((k) => { return k.id === fw })[0].title.trim().toLowerCase() })
                                            let frame = JSON.parse(JSON.stringify(val)).map((fw) => { return list.framework.filter((k) => { return k.id === fw })[0] })


                                            let one = metric.data1[0].tags1.some((str) => tags.some((fruit) => str.toLowerCase().includes(fruit.toLowerCase())))
                                            let two = metric.data1[0].tags2.some((str) => tags.some((fruit) => str.toLowerCase().includes(fruit.toLowerCase())))
                                            let three = metric.data1[0].tags3.some((str) => tags.some((fruit) => str.toLowerCase().includes(fruit.toLowerCase())))
                                            const matchedTag1 = metric.data1[0].tags1.filter((str) => tags.some((fruit) => str.toLowerCase().includes(fruit.toLowerCase())));
                                            const matchedTag2 = metric.data1[0].tags2.filter((str) => tags.some((fruit) => str.toLowerCase().includes(fruit.toLowerCase())));
                                            const matchedTag3 = metric.data1[0].tags3.filter((str) => tags.some((fruit) => str.toLowerCase().includes(fruit.toLowerCase())));

                                            frame.forEach((t) => {
                                                matchedTag1.forEach((i) => {

                                                    if (i.trim().toLowerCase().includes(t.title.trim().toLowerCase())) {
                                                        !tag.includes(t.id) && tag.push(t.id)
                                                    }


                                                })
                                                matchedTag2.forEach((i) => {

                                                    if (i.trim().toLowerCase().includes(t.title.trim().toLowerCase())) {
                                                        !tag.includes(t.id) && tag.push(t.id)
                                                    }


                                                })
                                                matchedTag3.forEach((i) => {

                                                    if (i.trim().toLowerCase().includes(t.title.trim().toLowerCase())) {
                                                        !tag.includes(t.id) && tag.push(t.id)
                                                    }


                                                })
                                            })
                                            console.log(matchedTag1, matchedTag2, matchedTag3, tag)
                                            if (one || two || three) {
                                                framework.push({ tags: tag, overallTags: [metric.data1[0].tags1, metric.data1[0].tags2, metric.data1[0].tags3], cat_id: cat.id, top_id: topic.id, cat_title: cat.title, top_title: topic.title, ...metric })
                                            }
                                            // }

                                        }
                                    }
                                }

                            })
                        }
                    })
                }


            })

            loclist.metric = framework
            console.log(framework)

            setList(loclist)
        }
        setSelected(loc)


        forceUpdate();
    };
    const prevDialogFooter = (
        <>
            <Button label="Cancel" icon="pi pi-times" className="p-button-text" onClick={() => { setPrevDialog(false) }} />
        </>
    );
    const dupdpidDialogFooter = (
        <>
            <Button label="Cancel" icon="pi pi-times" className="p-button-text" onClick={() => { setDupDPIDDialog(false) }} />
        </>
    );


    const removeHTMLTag = (html) => {
        return html.replace(/(<([^>]+)>)/gi, "")
            .replace(/\n/g, " ")
            .replace(/&nbsp;/g, " ")
    }
    const renderPreview = () => {


        return (
            <div className="col-12 grid" >
                {selectedlist.data.map((i, j) => {
                    return (
                        <label className="col-12">{j + 1}. &nbsp; {i.title}</label>
                    )
                })

                }
            </div>
        )
    }
    const checkDCFForDPDuplication = () => {
        let metrics = JSON.parse(JSON.stringify(metricbk));
        let dcf = JSON.parse(JSON.stringify(selecteddcfbk));
        let selectedMetric = metrics.filter((i) => { return i.selected }).map((j) => { return j.id })
        let selectedDataPoints = []
        raw.forEach((k) => {
            if (selectedMetric.findIndex((ind) => { return ind === k.id }) !== -1) {
                if (k.newDataPoints !== undefined) {
                    k.newDataPoints.forEach((k) => {
                        selectedDataPoints.push(k.suffix)
                    })

                }
            }
        })
        let dps = [], duplicatedids = []
        selectedDataPoints.forEach((i) => {
            if (!dps.includes(i)) {
                dps.push(i)
            } else {
                if (!duplicatedids.includes(i)) {
                    duplicatedids.push(i)
                }
            }
        })

        if (dps.length === selectedDataPoints.length) {
            Swal.fire({
                position: "center",
                icon: "success",
                title: `No Duplicates Found`,
                showConfirmButton: false,
                timer: 1500,
            });
        } else {
            console.log(duplicatedids, 'ids')
            setDupId(duplicatedids)
            setDupDPIDDialog(true)

        }

    }
    const checkDCFForDPDuplication_ = () => {
        let metrics = JSON.parse(JSON.stringify(metricbk));
        let dcf = JSON.parse(JSON.stringify(selecteddcfbk));
        let selectedMetric = metrics.filter((i) => { return i.selected }).map((j) => { return j.id })
        let selectedDataPoints = []
        raw.forEach((k) => {
            if (selectedMetric.findIndex((ind) => { return ind === k.id }) !== -1) {
                if (k.newDataPoints !== undefined) {
                    k.newDataPoints.forEach((k) => {
                        selectedDataPoints.push(k.suffix)
                    })

                }
            }
        })
        let dps = [], duplicatedids = []
        selectedDataPoints.forEach((i) => {
            if (!dps.includes(i)) {
                dps.push(i)
            } else {
                if (!duplicatedids.includes(i)) {
                    duplicatedids.push(i)
                }
            }
        })

        if (dps.length === selectedDataPoints.length) {
            return true
        } else {
            return false
        }

    }
    const saveAssignedDCF = () => {
        let metrics = JSON.parse(JSON.stringify(metricbk));
        let dcf = JSON.parse(JSON.stringify(selecteddcfbk));
        let selectedMetric = metrics.filter((i) => { return i.selected }).map((j) => { return j.id })
        let selectedDataPoints = []
        raw.forEach((k) => {
            if (selectedMetric.findIndex((ind) => { return ind === k.id }) !== -1) {
                if (k.newDataPoints !== undefined) {
                    k.newDataPoints.forEach((k) => {
                        selectedDataPoints.push(k.id)
                    })

                }
            }
        })
        let selectedDCF = dcf.map((k) => { return k.id })
        if (selectedMetric.length !== 0 && selectedDCF.length !== 0 && checkDCFForDPDuplication_()) {
            if (editmode) {
                APIServices.patch(API.AssignDCFClient_UP(userConfig.name.id), { dcf_ids: selectedDCF, dp_ids: selectedDataPoints, metric_ids: selectedMetric, user_id: login_data.id }).then((a) => {
                    console.log(a)
                    Swal.fire({
                        position: "center",
                        icon: "success",
                        title: `Data updated successfully`,
                        showConfirmButton: false,
                        timer: 1500,
                    });
                })
            } else {
                APIServices.post(API.AssignDCFClient_UP(userConfig.name.id), { dcf_ids: selectedDCF, dp_ids: selectedDataPoints, metric_ids: selectedMetric, user_id: login_data.id, created: moment.utc() }).then((a) => {
                    Swal.fire({
                        position: "center",
                        icon: "success",
                        title: `Data saved successfully`,
                        showConfirmButton: false,
                        timer: 1500,
                    });
                })
            }
        } else {
            if (!checkDCFForDPDuplication_()) {
                checkDCFForDPDuplication()
            } else {
                Swal.fire({
                    position: "center",
                    icon: "error",
                    title: `Unable to save, DCF not assigned / not empty `,
                    showConfirmButton: false,
                    timer: 1500,
                })
            }
        }


    }
    const listingMulitSelectTemplate = (option) => {
        if (option) {
            return (
                <div >
                    <span class="p-multiselect-token-label">{option.name}</span>
                    {/* <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg" class="p-icon p-multiselect-token-icon" aria-hidden="true"><g clip-path="url(#pr_icon_clip_2)"><path fill-rule="evenodd" clip-rule="evenodd" d="M7 14C5.61553 14 4.26215 13.5895 3.11101 12.8203C1.95987 12.0511 1.06266 10.9579 0.532846 9.67879C0.00303296 8.3997 -0.13559 6.99224 0.134506 5.63437C0.404603 4.2765 1.07129 3.02922 2.05026 2.05026C3.02922 1.07129 4.2765 0.404603 5.63437 0.134506C6.99224 -0.13559 8.3997 0.00303296 9.67879 0.532846C10.9579 1.06266 12.0511 1.95987 12.8203 3.11101C13.5895 4.26215 14 5.61553 14 7C14 8.85652 13.2625 10.637 11.9497 11.9497C10.637 13.2625 8.85652 14 7 14ZM7 1.16667C5.84628 1.16667 4.71846 1.50879 3.75918 2.14976C2.79989 2.79074 2.05222 3.70178 1.61071 4.76768C1.16919 5.83358 1.05367 7.00647 1.27876 8.13803C1.50384 9.26958 2.05941 10.309 2.87521 11.1248C3.69102 11.9406 4.73042 12.4962 5.86198 12.7212C6.99353 12.9463 8.16642 12.8308 9.23232 12.3893C10.2982 11.9478 11.2093 11.2001 11.8502 10.2408C12.4912 9.28154 12.8333 8.15373 12.8333 7C12.8333 5.45291 12.2188 3.96918 11.1248 2.87521C10.0308 1.78125 8.5471 1.16667 7 1.16667ZM4.66662 9.91668C4.58998 9.91704 4.51404 9.90209 4.44325 9.87271C4.37246 9.84333 4.30826 9.8001 4.2544 9.74557C4.14516 9.6362 4.0838 9.48793 4.0838 9.33335C4.0838 9.17876 4.14516 9.0305 4.2544 8.92113L6.17553 7L4.25443 5.07891C4.15139 4.96832 4.09529 4.82207 4.09796 4.67094C4.10063 4.51982 4.16185 4.37563 4.26872 4.26876C4.3756 4.16188 4.51979 4.10066 4.67091 4.09799C4.82204 4.09532 4.96829 4.15142 5.07887 4.25446L6.99997 6.17556L8.92106 4.25446C9.03164 4.15142 9.1779 4.09532 9.32903 4.09799C9.48015 4.10066 9.62434 4.16188 9.73121 4.26876C9.83809 4.37563 9.89931 4.51982 9.90198 4.67094C9.90464 4.82207 9.84855 4.96832 9.74551 5.07891L7.82441 7L9.74554 8.92113C9.85478 9.0305 9.91614 9.17876 9.91614 9.33335C9.91614 9.48793 9.85478 9.6362 9.74554 9.74557C9.69168 9.8001 9.62748 9.84333 9.55669 9.87271C9.4859 9.90209 9.40996 9.91704 9.33332 9.91668C9.25668 9.91704 9.18073 9.90209 9.10995 9.87271C9.03916 9.84333 8.97495 9.8001 8.9211 9.74557L6.99997 7.82444L5.07884 9.74557C5.02499 9.8001 4.96078 9.84333 4.88999 9.87271C4.81921 9.90209 4.74326 9.91704 4.66662 9.91668Z" fill="currentColor"></path></g><defs><clipPath id="pr_icon_clip_2"><rect width="14" height="14" fill="white"></rect></clipPath></defs></svg> */}
                </div>
            );
        }

        return 'Select SDGs';
    };
    const checkStandlone = (metric) => {
        if (Array.isArray(metric.data1) && metric.data1.length !== 0) {
            if (metric.data1[0].source === undefined) {
                return false
            } else if (metric.data1[0].source === 1) {
                return true
            }
        }
    }
    const rrTemplate = (rowData) => {


        return (
            < >
                <a  >       {rowData.data1[0].title} </a>

            </>
        )
    }
    const rrTemplate_ = (rowData) => {
        let data = rowData.data1[0], oldData = [], id = 0, show = true

        let index = rfresponse.findLastIndex((i) => { return i.rfid === data.rf && rowData.top_id === i.topicId && rowData.id === i.indicatorId && rowData.cat_id === i.categoryId && rowData.coverage === i.coverage && rowData.level === i.level})
        if (index !== -1) {
            oldData = rfresponse[index]

        }

        return (
            < >
                <a style={{ cursor:rowData.level === 0 ? 'pointer' : 'default', textDecoration:rowData.level === 0 ? 'underline' : 'none' }} onClick={() => { if(rowData.level === 0 ){ navigate.push({ pathname: '/rf_input_entry/' + rowData.data1[0].rf, state: { data: rowData, oldData } })} }}>       {rowData.data1[0].title} </a>

            </>
        )
    }
    const getRF = (rfid) => {
        let index = rawrf.findIndex(rf => rf.id === rfid);
        if (index !== -1) {
            return { name: rawrf[index].title, id: rfid }
        } else {
            return null
        }

    }
 
    function removeDuplicatesByProperties(arr, keys) {
        const seen = new Set();
        return arr.filter(item => {
            const key = JSON.stringify(keys.map(key => item[key]));
            if (!seen.has(key)) {
                seen.add(key);
                return true;
            }
            return false;
        });
    }
    const entityToUserAssignment = (arr) => {
        const result = [];
        let locrfuser = JSON.parse(JSON.stringify(rfuserass))
        arr.forEach(item => {
            const { rfid, city_ids, site_ids, country_ids, other_ids, type } = item;
            if (other_ids.length > 0) {
                other_ids.forEach(other => {
                    let rfuserindex = locrfuser.findIndex(i => i.rfid === rfid && i.coverage === other && i.level === 0)
                    if (rfuserindex === -1) {
                        result.push({ rfid, level: 0, coverage: other, type });
                    } else {
                        result.push(locrfuser[rfuserindex]);
                    }

                });
            }
            if (country_ids.length > 0) {
                country_ids.forEach(country => {
                    const level = 1;
                    let rfuserindex = locrfuser.findIndex(i => i.rfid === rfid && i.coverage === country && i.level === level)
                    console.log(level, country, rfuserindex, locrfuser, rfid)
                    if (rfuserindex === -1) {
                        result.push({ rfid, level, coverage: country, type });
                    } else {
                        result.push(locrfuser[rfuserindex]);
                    }

                });
            }
            if (city_ids.length > 0) {
                city_ids.forEach(city => {
                    let rfuserindex = locrfuser.findIndex(i => i.rfid === rfid && i.coverage === city && i.level === 2)
                    if (rfuserindex === -1) {
                        result.push({ rfid, level: 2, coverage: city, type });
                    } else {
                        result.push(locrfuser[rfuserindex]);
                    }

                });
            }
            if (site_ids.length > 0) {
                site_ids.forEach(site => {
                    let rfuserindex = locrfuser.findIndex(i => i.rfid === rfid && i.coverage === site && i.level === 3)
                    if (rfuserindex === -1) {
                        result.push({ rfid, level: 3, coverage: site, type });
                    } else {
                        result.push(locrfuser[rfuserindex]);
                    }

                });
            }
        });
        console.log(result)
        return result;
    }

    const deleteRFAssignment = async (rassid, rid) => {
        const { value: accept } = await Swal.fire({
            title: `<div style="overflow:visible;font-size:20px;font-weight:600;margin-top:0px">Warning</div>`,
            html: `<div style="overflow:auto;max-height:200px" >Are you sure want to remove RF Assignment
          </div>`,

            confirmButtonColor: 'red',
            showCancelButton: true,
            confirmButtonText:
                'Remove',

        })
        if (accept) {
            console.log(rassid)
            APIServices.delete(API.RF_User_Edit(rassid)).then((l) => {
                let data_ = JSON.parse(JSON.stringify(rfuserass))
                data_.splice(rid, 1)
                setRFUserAss(data_)

            })
        }
    }
    const responsibilityTemplate = (rowData) => {
        // setUserModal(true); setSelectedRF(rowData);
        let index = 0
        console.log(rowData)
        let rf_index = rfentityass.findIndex((i) => { return i.rfid === rowData.data1[0].rf })
        if (rf_index !== -1) {
            let findIndex = rfentityass.filter((i) => { return i.rfid === rowData.data1[0].rf }).length
            console.log(findIndex)
            if (findIndex) {
                let count = rfentityass[rf_index].city_ids.length + rfentityass[rf_index].site_ids.length + rfentityass[rf_index].country_ids.length + rfentityass[rf_index].other_ids.length
                if (count) {
                    index = count
                }
            }

        }
        return (
            <div className="flex justify-content-center">
                {index !== 0 ?
                    <div style={{ display: 'flex', alignItems: 'center', flexDirection: 'column' }}>
                        {/* {userList[index].information.empname} */}

                        <Badge value={index}></Badge>
                        <label onClick={() => { setUserModal(true); setSelectedEntity((prev) => { return { ...prev, rfid: rowData.data1[0].rf } }); updateUserList2(rowData.data1[0].rf) }} className="text-underline col-12" >Add  </label>
                        {/* <i className="material-icons" onClick={() => { deleteRFAssignment(rfuserass[rf_index].id, rf_index) }} style={{ color: 'red', cursor: 'pointer', fontSize: 14, marginLeft: 5 }}>close</i> */}
                    </div> :
                    <div style={{ display: 'flex', alignItems: 'center', flexDirection: 'column' }}>
                        <Badge value={'0'} style={{ background: 'green', color: 'white', fontSize: 14 }}></Badge>
                        <label onClick={() => { setUserModal(true); setSelectedEntity((prev) => { return { ...prev, rfid: rowData.data1[0].rf } }); updateUserList2(rowData.data1[0].rf) }} className="text-underline col-12" >Add  </label>
                    </div>

                }
            </div>
        )
    }
    const entityTemplate = (rowData) => {
        let index = 0
        let rf_index = rfentityass.findIndex((i) => { return i.rfid === rowData.data1[0].rf })
        if (rf_index !== -1) {
            let findIndex = rfentityass.filter((i) => { return i.rfid === rowData.data1[0].rf }).length
            if (findIndex) {
                let count = rfentityass[rf_index].city_ids.length + rfentityass[rf_index].site_ids.length + rfentityass[rf_index].country_ids.length + rfentityass[rf_index].other_ids.length
                if (count) {
                    index = count
                }
            }

        }
        return (
            <div className="flex justify-content-center">
                {index !== 0 ?
                    <div style={{ display: 'flex', alignItems: 'center', flexDirection: 'column' }}>

                        <label onClick={() => { setEntityModal(true); setSelectedRF(getRF(rowData.data1[0].rf)); setSelectedEntity(rfentityass[rf_index]) }} className="text-underline cur-pointer col-12" >view</label>

                    </div> :
                    'NA'

                }
            </div>
        )
    }
    const responsibilityTemplate_ = (rowData) => {
        // setUserModal(true); setSelectedRF(rowData);
        let index = 0
        console.log(rowData)
        let rf_index = rfuserass.findIndex((i) => { return i.rfid === rowData.data1[0].rf && i.reporter_ids !== null && i.reporter_ids.length !== 0 && i.coverage === rowData.coverage && i.level === rowData.level })
        if (rf_index !== -1) {
            index = rfuserass[rf_index].reporter_ids.length
        }
        return (
            <div className="flex justify-content-center">
                {index !== 0 ?
                    <div style={{ display: 'flex', alignItems: 'center', flexDirection: 'column' }}>
                        {/* {userList[index].information.empname} */}
                        <Badge value={index}></Badge>
                        <label onClick={() => { setUserAssModal(true); setSelectedRF(rowData); updateUserList3(rowData) }} className="text-underline col-12" >Add  </label>

                        {/* <i className="material-icons" onClick={() => { deleteRFAssignment(rfuserass[rf_index].id, rf_index) }} style={{ color: 'red', cursor: 'pointer', fontSize: 14, marginLeft: 5 }}>close</i> */}
                    </div> :
                    <div style={{ display: 'flex', alignItems: 'center', flexDirection: 'column' }}>

                        <Badge value={'0'} style={{ background: 'green', color: 'white', fontSize: 14 }}></Badge>
                        <label onClick={() => { setUserAssModal(true); setSelectedRF(rowData); updateUserList3(rowData); }} className="text-underline col-12" >Add  </label>
                    </div>

                }
            </div>
        )
    }
    const updateEntityList = (obj, val) => {


        setSelectedEntity((prev) => { return { ...prev, [obj]: val } })

    }
    const updateUserList = (obj, val) => {


        setSelectedLoc((prev) => { return { ...prev, [obj]: val } })

    }
    const updateUserList2 = (id) => {
        let locass = JSON.parse(JSON.stringify(rfentityass))
        let country = [], city = [], site = []
        rawsitelist.forEach((i) => {
            country.push({ name: i.name, id: i.id })
            i.locationTwos.forEach((j) => {
                city.push({ name: j.name, id: j.id })
                j.locationThrees.forEach((k) => {
                    site.push({ name: k.name, id: k.id })
                })
            })

        })

        let assindex = locass.findIndex((i) => { return i.rfid === id })
        if (assindex !== -1) {
            setSelectedEntity({ country_ids: locass[assindex].country_ids, other_ids: locass[assindex].other_ids, city_ids: locass[assindex].city_ids, site_ids: locass[assindex].site_ids, rfid: id })
        } else {
            setSelectedEntity({ country_ids: [], other_ids: [], city_ids: [], site_ids: [], rfid: id, type: activeindex === 0 ? 1 : 2 })
        }

        setSelectedLocList({ country, city, site })

    }

    const updateUserList3 = (assdata) => {
        let locass = JSON.parse(JSON.stringify(rfuserass))
        let rf_index = locass.findIndex((i) => { return i.rfid === assdata.data1[0].rf && i.coverage === assdata.coverage && i.level === assdata.level })
        if (rf_index !== -1) {
            let newObj = { rfid: assdata.data1[0].rf, coverage: assdata.coverage, type: assdata.type, level: assdata.level, reporter_ids: locass[rf_index].reporter_ids }
            console.log(newObj)
            setSelectedLoc(newObj)
        } else {
            let newObj = { rfid: assdata.data1[0].rf, coverage: assdata.coverage, type: assdata.type, level: assdata.level, reporter_ids: [] }
            setSelectedLoc(newObj)
        }


    }
    const titleTemplate = (rowData) => {

        return (
            < >
                <Tooltip className="tag-tooltip" target={".tags" + rowData.id} position={'top'} autoHide={true}> {rowData.overallTags.map((i, j) => {
                    if (i.length !== 0) {
                        return (
                            <>
                                <label style={{ color: 'black', display: 'flex' }}> {
                                    j === 0 ? 'Must Have' : j === 1 ? 'Progressive' : 'Advanced'

                                }
                                </label>
                                {
                                    i.map((tag, k) => {

                                        return (
                                            <label style={{ color: 'green' }}>{tag}{k !== i.length - 1 && ','}</label>
                                        )

                                    })
                                }
                                <div style={{ marginBottom: 10 }} />
                            </>
                        )
                    }
                })} </Tooltip>
                <div style={{ alignItems: 'center' }} >{rowData.title} <i className={"material-icons " + "tags" + rowData.id} style={{ fontSize: 14, cursor: 'pointer' }}>info</i>  </div>

            </>
        )
    }
    const lastResponse = (rowData) => {

        let text = 'Not Responded'
        let data = rowData.data1[0]

        let index = rfresponse.findLastIndex((i) => { return i.rfid === data.rf && rowData.top_id === i.topicId && rowData.id === i.indicatorId && rowData.cat_id === i.categoryId && rowData.coverage === i.coverage && rowData.level === i.level  })
        if (index !== -1) {
            text = moment.utc(rfresponse[index].created_on).format('DD MMMM YYYY')

        }
        return (
            <>
                {text}
            </>
        )
    }
    const historyTemplate = (rowData) => {
        let text = true
        let data = rowData.data1[0]
        let mergeData = []
        console.log(rowData)
        let index = rfresponse.findLastIndex((i) => { return i.rfid === data.rf && rowData.top_id === i.topicId && rowData.id === i.indicatorId && rowData.cat_id === i.categoryId && rowData.level === i.level && rowData.coverage === i.coverage })
        console.log(index, rfresponse)
        if (index !== -1) {
            text = false
            mergeData = rfresponse.filter((i) => { return i.rfid === data.rf && rowData.top_id === i.topicId && rowData.id === i.indicatorId && rowData.cat_id === i.categoryId && rowData.level === i.level && rowData.coverage === i.coverage })
            mergeData.forEach((i) => {
                i.top_title = rowData.top_title
                i.cat_title = rowData.cat_title
            })
        }
        return (
            <>
                {text ?
                    <span>NA</span> :
                    <a className="cur-pointer" onClick={() => { setHistoryData(mergeData); activeRowData = rowData; setHistoryDialog(true) }}>View History</a>
                }

            </>
        )
    }
    const getMetric = (rf) => {
        console.log(rf)
        let loc = [...requiredList, ...requiredList2].findIndex((i) => { return i.data1 !== null && i.data1[0].rf !== undefined && i.data1[0].rf === rf.rfid })
        if (loc !== -1) {
            return { ...[...requiredList, ...requiredList2][loc], level: rf.level, coverage: rf.coverage, type: rf.type }
        } else {
            return null
        }
    }
    const addNewAssign = () => {
        setAssignDialog(true)
        setSelectedLoc({ rfid: null, country: 0, city: 0, site: 0, reporter_ids: [], type: 1 })

    }
    const saveDFAssignment = () => {
        if (selectedloc.rfid !== undefined && selectedloc.rfid !== null && selectedloc.type !== null) {
            let loc = JSON.parse(JSON.stringify(rfuserass))
            let rf_index = rfuserass.findIndex(i => i.country === selectedloc.country && i.city === selectedloc.city && i.site === selectedloc.site && i.rfid === selectedloc.rfid);
            if (rf_index === -1) {


                let data = { ...selectedloc, submitted_by: login_data.id, created_on: DateTime.utc() }
                APIServices.post(API.RF_User_UP(admin_data.id), data).then((res) => {
                    let rfass_ = JSON.parse(JSON.stringify(rfuserass))
                    rfass_.push(res.data)
                    setSelectedLoc({ country: 0, city: 0, site: 0, reporter_ids: [], type: 1 })
                    setRFUserAss(rfass_)
                    setAssignDialog(false)

                    setList(loc)
                })
            } else {
                let data = { reporter_ids: selectedloc.reporter_ids, submitted_by: login_data.id, created_on: moment.utc() }
                APIServices.patch(API.RF_User_Edit(loc[rf_index].id), data).then((res) => {

                    loc[rf_index].reporter_ids = selectedloc.reporter_ids
                    setRFUserAss(loc)
                    setSelectedLoc({ country: 0, city: 0, site: 0, reporter_ids: [], type: 1 })
                    setAssignDialog(false)

                    setList(loc)
                })
            }
        }

    }
    const updateDFAssignment = (obj, val) => {
        let locass = JSON.parse(JSON.stringify(rfuserass))
        let country = [{ name: 'All', id: 0 }], city = [{ name: 'All', id: 0 }], site = [{ name: 'All', id: 0 }]
        let loc = selectedloc
        loc[obj] = val
        if (obj === 'type') {
            loc['reporter_ids'] = []
            loc['rfid'] = null

        } else if (obj === 'rfid') {
            rawsitelist.forEach((i) => {
                country.push({ name: i.name, id: i.id })
                if (i.id === selectedloc.country || selectedloc.country === 0) {
                    i.locationTwos.forEach((j) => {
                        city.push({ name: j.name, id: j.id })
                        if (j.id === selectedloc.city || selectedloc.city === 0) {
                            j.locationThrees.forEach((k) => {
                                site.push({ name: k.name, id: k.id })

                            })
                        }
                    })
                }
            })
            let ass = { country: selectedloc.country, city: selectedloc.city, site: selectedloc.site }

            let assindex = locass.findIndex((i) => { return i.country === ass.country && i.city === ass.city && i.site === ass.site && i.rfid === val })
            console.log(assindex, ass, val)
            if (assindex !== -1) {
                setSelectedLoc((prev) => { return { ...prev, ...{ country: selectedloc.country, city: selectedloc.city, site: selectedloc.site, reporter_ids: locass[assindex].reporter_ids } } })
            } else {
                setSelectedLoc((prev) => { return { ...prev, ...{ country: selectedloc.country, city: selectedloc.city, site: selectedloc.site, reporter_ids: [] } } })
            }



            setSelectedLocList({ country, city, site })
        }
        if (obj !== 'rfid') {
            setSelectedLoc(loc)
        }

        forceUpdate()

    }
    const coverageFilterTemplate = (options) => {
        return <MultiSelect value={options.value} options={removeDuplicatesByProperties(entityToUserAssignment(rfentityass).map((i) => { return getMetric(i) }).filter(i => i !== null).map(i => getCoverageText(i)), ['name'])} itemTemplate={coverageItemTemplate} onChange={(e) => options.filterCallback(e.value)} optionLabel="name" valueLabel="name" placeholder="Any" className="p-column-filter" />;
    };
    const coverageItemTemplate = (option) => {
        return (
            <div className="flex align-items-center gap-2">

                <span>{option.name}</span>
            </div>
        );
    };
    const getCoverageText = (rowData) => {
        let text = 'Not Found'
        console.log(rowData)
        if (rowData.level === 0) {
            text = 'Corporate'
        } else if (rowData.level === 1) {
            let country_index = rawsitelist.findIndex(i => i.id === rowData.coverage)
            if (country_index !== -1) {
                text = rawsitelist[country_index].name
            }
        } else if (rowData.level === 2) {
            let city_index = rawsitelist.flatMap(i => i.locationTwos.flatMap(j => j.locationThrees.map((k) => { return { site_id: k.id, site_name: k.name, city_id: j.id, city_name: j.name, country_id: i.id, country_name: i.name } }))).findIndex((i) => { return i.city_id === rowData.coverage })
            if (city_index !== -1) {
                text = rawsitelist.flatMap(i => i.locationTwos.flatMap(j => j.locationThrees.map((k) => { return { site_id: k.id, site_name: k.name, city_id: j.id, city_name: j.name, country_id: i.id, country_name: i.name } })))[city_index].city_name
            }

        } else if (rowData.level === 3) {
            let site_index = rawsitelist.flatMap(i => i.locationTwos.flatMap(j => j.locationThrees.map((k) => { return { site_id: k.id, site_name: k.name, city_id: j.id, city_name: j.name, country_id: i.id, country_name: i.name } }))).findIndex((i) => { return i.site_id === rowData.coverage })
            if (site_index !== -1) {
                text = rawsitelist.flatMap(i => i.locationTwos.flatMap(j => j.locationThrees.map((k) => { return { site_id: k.id, site_name: k.name, city_id: j.id, city_name: j.name, country_id: i.id, country_name: i.name } })))[site_index].site_name
            }
        }
        return { name: text }
    }
    const coverageTemplate = (rowData) => {
        let text = 'Not Found'
        console.log(rowData)
        if (rowData.level === 0) {
            text = 'Corporate'
        } else if (rowData.level === 1) {
            let country_index = rawsitelist.findIndex(i => i.id === rowData.coverage)
            if (country_index !== -1) {
                text = rawsitelist[country_index].name
            }
        } else if (rowData.level === 2) {
            let city_index = rawsitelist.flatMap(i => i.locationTwos.flatMap(j => j.locationThrees.map((k) => { return { site_id: k.id, site_name: k.name, city_id: j.id, city_name: j.name, country_id: i.id, country_name: i.name } }))).findIndex((i) => { return i.city_id === rowData.coverage })
            if (city_index !== -1) {
                text = rawsitelist.flatMap(i => i.locationTwos.flatMap(j => j.locationThrees.map((k) => { return { site_id: k.id, site_name: k.name, city_id: j.id, city_name: j.name, country_id: i.id, country_name: i.name } })))[city_index].city_name
            }

        } else if (rowData.level === 3) {
            let site_index = rawsitelist.flatMap(i => i.locationTwos.flatMap(j => j.locationThrees.map((k) => { return { site_id: k.id, site_name: k.name, city_id: j.id, city_name: j.name, country_id: i.id, country_name: i.name } }))).findIndex((i) => { return i.site_id === rowData.coverage })
            if (site_index !== -1) {
                text = rawsitelist.flatMap(i => i.locationTwos.flatMap(j => j.locationThrees.map((k) => { return { site_id: k.id, site_name: k.name, city_id: j.id, city_name: j.name, country_id: i.id, country_name: i.name } })))[site_index].site_name
            }
        }
        console.log(text)
        rowData.coverage_ = text
        return (
            <>{text}</>
        )
    }
    const checkChildrenSelected = (metric) => {
        console.log(metric)

        if (Array.isArray(metric.data1) && metric.data1.length !== 0) {
            let children = metric.data1[0].indicator.filter((i) => { return usermetric.includes(i) })
            if (children.length !== 0) {
                if (!usermetric.includes(metric.id)) {
                    usermetric.push(metric.id)
                }
                return true
            } else {
                if (usermetric.includes(metric.id)) {
                    let index = usermetric.findIndex((i) => { return i === metric.id })
                    usermetric.splice(index, 1)
                }
            }
        }

        return false

    }
    const checkChildrenSelected_ = (metric) => {


        if (Array.isArray(metric.data1) && metric.data1.length !== 0) {
            let children = metric.data1[0].indicator.filter((i) => { return usermetric.includes(i) })
            if (children.length !== 0) {

                return true
            }
        }

        return false

    }
    const showList = (id) => {
        let loclist = selectedlist
        if (id === 1) {
            loclist.title = 'Selected Categories'
            loclist.data = list.category.filter((k) => { return selected.category.includes(k.id) })
        } else if (id === 2) {
            loclist.title = 'Selected Topics'
            loclist.data = list.topic.filter((k) => { return selected.topic.includes(k.id) })
        } else if (id === 3) {
            loclist.title = 'Selected Standards/Frameworks'
            loclist.data = list.framework.filter((k) => { return selected.framework.includes(k.id) })
        }
        setSelectedList(loclist)
        setPrevDialog(true)

    }
    const renderTable = (val) => {
        setSelectedFramework(val)
        let filtered = requiredListBK.filter((i) => { return (i.overallTags.some(array => array.some(item => item.includes(val))) || val === 'All') })
        setRequiredList(filtered)
    }
    const renderTable2 = (val) => {
        setSelectedFramework2(val)
        let filtered = requiredList2BK.filter((i) => { return (i.overallTags.some(array => array.some(item => item.includes(val))) || val === 'All') })
        setRequiredList2(filtered)
    }
    const userTemplate = (rowData) => {
        return (
            <div className="flex align-items-center p-1 m-1">
                <Checkbox inputId={rowData.id + 'user'} name="pizza" value={rowData.information.empname} onChange={(e) => { e.stopPropagation(); addResponsibility(e, rowData.id) }} checked={selectedloc.reporter_ids.includes(rowData.id) === true} />
                <label htmlFor={rowData.id + 'user'} className="ml-2">{rowData.information.empname}</label>
            </div>
        )
    }
    const addResponsibility = (e, val) => {

        let loc = selectedloc
        if (loc.reporter_ids !== null && loc.reporter_ids.length !== 0) {
            if (loc.reporter_ids.includes(val)) {
                let index = loc.reporter_ids.findIndex(i => i === val)
                loc.reporter_ids.splice(index, 1)

            } else {
                loc.reporter_ids.push(val)
            }
        } else {
            loc.reporter_ids = [val]
        }
        console.log(loc)
        setSelectedLoc(loc)
        forceUpdate()
    }

    const AssignRFEntity = () => {
        let loc = JSON.parse(JSON.stringify(rfentityass))
        let rf_index = loc.findIndex((k) => { return k.rfid === selectedentity.rfid })
        console.log(loc[rf_index])
        if (rf_index === -1 && selectedentity.rfid !== null && selectedentity.type !== null) {


            let data = { ...selectedentity, submitted_by: login_data.id, created_on: DateTime.utc() }
            delete data.reporter_ids
            APIServices.post(API.RF_Entity_UP(admin_data.id), data).then((res) => {
                let rfass_ = JSON.parse(JSON.stringify(rfentityass))
                rfass_.push({ ...res.data })
                setSelectedEntity({ country_ids: [], city_ids: [], other_ids: [], site_ids: [], rfid: null, type: null })
                setRFEntityAss(rfass_)
                setUserModal(false)
                setUser(null)

            })
        } else if (rf_index !== -1 && loc[rf_index].id !== undefined) {
            let data = { ...selectedentity, submitted_by: login_data.id, created_on: moment.utc() }
            delete data.type
            delete data.rfid

            APIServices.patch(API.RF_Entity_Edit(loc[rf_index].id), data).then((res) => {
                delete data.created_on
                delete data.submitted_by
                data.rfid = selectedentity.rfid
                data.id = loc[rf_index].id
                console.log(data)
                loc[rf_index] = data
                setRFEntityAss(loc)
                setSelectedEntity({ country_ids: [], city_ids: [], other_ids: [], site_ids: [], rfid: null, type: null })
                setUserModal(false)
                setUser(null)

            })
        }

    }
    const AssignRFUser = () => {
        let loc = JSON.parse(JSON.stringify(rfuserass))
        let rf_index = loc.findIndex((k) => { return k.level === selectedloc.level && k.coverage === selectedloc.coverage && k.rfid === selectedloc.rfid })
        console.log(selectedloc)
        if (rf_index !== -1 && selectedloc.level !== null && selectedloc.coverage !== null && selectedloc.rfid !== null) {

            let data = { reporter_ids: selectedloc.reporter_ids, submitted_by: login_data.id, created_on: DateTime.utc() }
            APIServices.patch(API.RF_User_Edit(loc[rf_index].id), data).then((res) => {

                loc[rf_index].reporter_ids = selectedloc.reporter_ids
                console.log(loc)
                setRFUserAss(loc)
                setSelectedLoc({ coverage: null, level: null, reporter_ids: [], type: null })
                setUserAssModal(false)

                setList(loc)
            })
        } else if (rf_index === -1) {
            let data = { rfid: selectedloc.rfid, coverage: selectedloc.coverage, level: selectedloc.level, type: selectedloc.type, reporter_ids: selectedloc.reporter_ids, submitted_by: login_data.id, created_on: DateTime.utc() }
            APIServices.post(API.RF_User_UP(admin_data.id), data).then((res) => {

                loc.push({ ...res.data })
                setRFUserAss(loc)
                setSelectedLoc({ coverage: null, level: null, reporter_ids: [], type: null })
                setUserAssModal(false)

                setList(loc)
            })
        }


    }

    const updateFilter = () => {
        if (selected.id === undefined) {
            let newObj = selected
            newObj.created = moment.utc()

            APIServices.post(API.QL_Listing_Filter_UP(admin_data.id), newObj).then((a) => {
                delete newObj.created
                newObj.id = a.data.id
                setSelected(newObj)
                Swal.fire({
                    title: "Filter Saved Successfully",

                    confirmButtonText: 'Exit',
                    allowOutsideClick: false,
                })

            })
        } else {
            let newObj = selected


            newObj.created = moment.utc()
            APIServices.patch(API.QL_Listing_Filter_Edit(selected.id), newObj).then((a) => {
                Swal.fire({
                    title: "Filter Saved Successfully",

                    confirmButtonText: 'Exit',
                    allowOutsideClick: false,
                })

            })
        }
    }
    const selectedTab = (val) => {
        setActiveIndex(val)

    }
    function countObjects(arr) {
        let count = 0;

        function countRecursive(arr) {
            for (let obj of arr) {
                count++;
                if (obj.locationTwos) {
                    countRecursive(obj.locationTwos);
                }
                if (obj.locationThrees) {
                    countRecursive(obj.locationThrees);
                }
                // Add more conditions for nested arrays if needed
            }
        }

        countRecursive(arr);
        return count;
    }
    function entityCount(arr) {
        const result = arr.map(item => {
            const { city_ids, country_ids, site_ids, other_ids } = item;
            const cityLength = city_ids ? city_ids.length : 0;
            const countryLength = country_ids ? country_ids.length : 0;
            const siteLength = site_ids ? site_ids.length : 0;
            const otherLength = other_ids ? other_ids.length : 0;
            const sum = cityLength + countryLength + siteLength + otherLength;
            return sum;
        });
        return result.reduce((a, b) => { return a + b }, 0);
    }
    const groupEntityTemplate = (option) => {
        console.log(option)
        return (
            <div className="flex align-items-center gap-2">
                {
                    option.items.length ?
                        <div>{option.label + ' (' + option.items.length + ')'}</div>
                        :
                        <div>{option.label}</div>
                }

            </div>
        );
    };
    const itemTemplate = (option) => {
        console.log(option)
        return (
            <div className="flex align-items-center ">

                <div>{option.name}</div>


            </div>
        );
    };
    const getEntityOptions = (item) => {
        let newObj = []
        if (item.other_ids.length > 0) {
            newObj.push({ label: 'Corporate', items: [] })
        }
        if (item.country_ids.length > 0) {

            let obj = { label: 'Tier1', items: [] }
            item.country_ids.filter(i => i != 0).forEach((country) => {
                obj.items.push(getCoverageText({ level: 1, coverage: country }))
            })
            newObj.push(obj)

        }
        if (item.city_ids.length) {
            let obj = { label: 'Tier2', items: [] }
            item.city_ids.filter(i => i != 0).forEach((city) => {
                obj.items.push(getCoverageText({ level: 2, coverage: city }))
            })
            newObj.push(obj)
        }
        if (item.site_ids.length) {
            let obj = { label: 'Tier3', items: [] }
            item.site_ids.filter(i => i != 0).forEach((site) => {
                obj.items.push(getCoverageText({ level: 3, coverage: site }))
            })
            newObj.push(obj)
        }
        console.log(newObj, item)
        return newObj



    }
    return (
        <div className="grid">
            <div className="col-12">
                <div className="card" >
                    <div style={{
                        fontSize: '16px',
                        display: 'flex',
                        justifyContent: 'center',
                        fontWeight: '600', marginBottom: 30
                    }}>Qualitative Disclosures </div>
                    {(login_data.role === "clientadmin" || login_data.role === 'clientuser') ?
                        <>

                            <TabMenu model={items} activeIndex={activeindex} onTabChange={(e) => { setSelectedLoc((prev) => { return { ...prev, type: e.index + 1 } }); selectedTab(e.index) }} />
                            {activeindex === 0 &&
                                <div className="mt-4">
                                    <div>
                                        <div style={{ marginBottom: 10 }}>
                                            <label
                                                style={{
                                                    marginRight: 10,
                                                }}
                                            >
                                                Filter by selected Standards / Frameworks / Disclosures
                                            </label>
                                            <Dropdown style={{ width: 200 }} options={[{ title: 'All', id: 0 }, ...assFramework]} value={selectedFramework} optionValue="title" optionLabel="title" onChange={(e) => { renderTable(e.value) }} />
                                        </div>
                                    </div>
                                    {requiredList.length ? <div>
                                        <div style={{
                                            fontSize: '16px',
                                            display: 'flex',
                                            justifyContent: 'flex-start',
                                            fontWeight: '600', marginBottom: 30
                                        }}>Applicable Disclosures for Selected Topics ({requiredList.length})</div>
                                        <div className="col-12  p-1 mb-1 mt-1">
                                            <div className="col-12 grid justify-content-between">
                                                <div >
                                                    <lable className='fs-16 fw-5' >Qualitative Assigned : {requiredList.filter(i => rfentityass.filter(i => i.type === 1).map(j => j.rfid).includes(i.data1[0].rf)).length + '/' + requiredList.length} </lable>
                                                </div>
                                                <div >
                                                    <lable className='fs-16 fw-5' >Entity Assigned : {entityCount(rfentityass.filter(i => i.type === 1).filter(i => requiredList.map(j => j.data1[0].rf).includes(i.rfid))) + '/' + ((countObjects(rawsitelist) + 1) * requiredList.length)} </lable>
                                                </div>
                                            </div>
                                        </div>
                                        <DataTable value={requiredList} scrollable >
                                            <Column field='cat_title' header='Category' />
                                            <Column field='top_title' header='Topic' />
                                            <Column field='title' body={titleTemplate} header='Aspect' />
                                            <Column body={rrTemplate} header='Requirement' />
                                            <Column header='Responding Entities' alignHeader={'center'} body={responsibilityTemplate} />
                                            <Column header='Assigned Entities' body={entityTemplate} />


                                            {/* <Column body={historyTemplate} header='History' />
                                            <Column body={lastResponse} header='Last Response Date' /> */}

                                        </DataTable>
                                    </div>
                                        :
                                        <div>No Data Found</div>

                                    }
                                </div>

                            }
                            {activeindex === 1 &&
                                <div className="mt-4">
                                    <div>
                                        <div style={{ marginBottom: 10 }}>
                                            <label
                                                style={{
                                                    marginRight: 10,
                                                }}
                                            >
                                                Filter by  Standards / Frameworks / Disclosures
                                            </label>
                                            <Dropdown style={{ width: 200 }} options={[{ title: 'All', id: 0 }, ...assFramework2]} value={selectedFramework2} optionValue="title" optionLabel="title" onChange={(e) => { renderTable2(e.value) }} />
                                        </div>
                                    </div>
                                    {requiredList2.length ? <div>
                                        <div style={{
                                            fontSize: '16px',
                                            display: 'flex',
                                            justifyContent: 'flex-start',
                                            fontWeight: '600', marginBottom: 30
                                        }}>Other Disclosures ({requiredList2.length})</div>
                                        <div className="col-12  p-1 mb-1 mt-1">
                                            <div className="col-12 grid justify-content-between">
                                                <div >
                                                    <lable className='fs-16 fw-5' >Qualitative Assigned : {requiredList2.filter(i => rfentityass.filter(i => i.type === 2).map(j => j.rfid).includes(i.data1[0].rf)).length + '/' + requiredList2.length} </lable>
                                                </div>
                                                <div >
                                                    <lable className='fs-16 fw-5' >Entity Assigned : {entityCount(rfentityass.filter(i => i.type === 2).filter(i => requiredList2.map(j => j.data1[0].rf).includes(i.rfid))) + '/' + ((countObjects(rawsitelist) + 1) * requiredList2.length)} </lable>
                                                </div>
                                            </div>
                                        </div>
                                        <DataTable value={requiredList2} scrollable >
                                            <Column field='cat_title' header='Category' />
                                            <Column field='top_title' header='Topic' />
                                            <Column field='title' body={titleTemplate} header='Aspect' />
                                            <Column body={rrTemplate} header='Requirement' />
                                            <Column header='Responding Entities' alignHeader={'center'} body={responsibilityTemplate} />
                                            <Column header='Assigned Entities' body={entityTemplate} />
                                            {/* <Column body={historyTemplate} header='History' />
                                            <Column body={lastResponse} header='Last Response Date' /> */}

                                        </DataTable>
                                    </div>
                                        :
                                        <div>No Data Found</div>

                                    }
                                </div>

                            }
                            {activeindex === 2 &&
                                <div className="mt-4">

                                    {(requiredList2.length + requiredList.length) ? <div>
                                        <div style={{
                                            fontSize: '16px',
                                            display: 'flex',
                                            justifyContent: 'flex-start',
                                            fontWeight: '600', marginBottom: 30
                                        }}>Qualitative Assignment </div>

                                        <div className="col-12  p-1 mb-1 mt-1">
                                            <div className="col-12 grid justify-content-between">
                                                {/* <div >
                                                    <lable className='fs-16 fw-5' > Required Entity Assigned : {removeDuplicatesByProperties(rfentityass.filter(i => i.type === 1), ['rfid']).length + '/' + requiredList.length} </lable>
                                                </div>
                                                <div >
                                                    <lable className='fs-16 fw-5' > Additional Entity Assigned : {removeDuplicatesByProperties(rfentityass.filter(i => i.type === 2), ['rfid']).length + '/' + requiredList2.length} </lable>
                                                </div> */}
                                                <div >
                                                    <lable className='fs-16 fw-5' > User Assigned : {entityToUserAssignment(rfentityass).filter(i => i.reporter_ids !== undefined).map((i) => { return getMetric(i) }).filter(i => i !== null).length + '/' + entityToUserAssignment(rfentityass).map((i) => { return getMetric(i) }).filter(i => i !== null).length} </lable>
                                                </div>
                                            </div>
                                        </div>

                                        <div className="col-12" style={{ display: 'flex', justifyContent: 'space-between' }} >
                                            <div hidden className="col-6" >
                                                <span className="p-input-icon-left">
                                                    <i className="pi pi-search" />
                                                    {/* <InputText value={search_.ass} onChange={searchAssignment} style={{ width: 350 }} placeholder="Search DCF" /> */}
                                                </span>
                                            </div>
                                            {/* <div className="col-12" style={{ display: 'flex', justifyContent: 'flex-end' }} >


                                                <Button onClick={() => { addNewAssign() }}> + Assign Qualitative </Button>
                                            </div> */}
                                        </div>
                                        <DataTable filters={assfilter} value={entityToUserAssignment(rfentityass).map((i) => { return getMetric(i) }).filter(i => i !== null)} scrollable emptyMessage="No Entity found.">

                                            <Column body={rrTemplate_} header='Requirement' />
                                            <Column header='Entity' field="coverage_" body={coverageTemplate} showFilterMatchModes={false} filter filterElement={coverageFilterTemplate} />
                                            <Column body={responsibilityTemplate_} alignHeader={'center'} header='Assignees' />
                                            <Column body={historyTemplate} header='History' />
                                            <Column body={lastResponse} header='Last Response Date' />
                                        </DataTable>
                                    </div>
                                        :
                                        <div>No Data Found</div>

                                    }
                                </div>
                            }

                        </>


                        :
                        <div className=" col-12">You have no rights to access this page</div>

                    }
                </div>
            </div>
            <Dialog
                visible={prevdialog}
                style={{
                    width: "60%",
                }}
                header={selectedlist.title}
                modal
                className="p-fluid"

                onHide={() => { setPrevDialog(false) }}
            >
                {renderPreview()}
            </Dialog>
            <Dialog
                visible={historydialog}
                style={{
                    width: "30%",
                }}
                header={"Response History"}
                modal
                className="p-fluid"

                onHide={() => { setHistoryDialog(false) }}
            >
                <div>
                    {historydata.sort((a, b) => { return moment(b.created_on).toDate() - moment(a.created_on).toDate() }).map((i) => {
                        return (
                            <div style={{ flexDirection: 'column', alignItems: 'center', display: 'flex', padding: 5, borderRadius: 10, margin: 5, boxShadow: 'rgb(0 0 0 / 24%) 0px 3px 8px' }}>
                                <text style={{ color: 'black', borderRadius: 10, padding: 5, fontStyle: 'italic', cursor: 'pointer' }} onClick={() => { navigate.push({ pathname: '/rf_submission_preview/' + i.rfid, state: { data: activeRowData, oldData: i } }) }}>{moment(i.created_on).local().format('DD MMM YYYY, hh:mm A')} by <span style={{ color: 'green' }}>{i.submitted_by === login_data.id ? 'Admin' : !userList.findIndex((j) => { return j.id === i.submitted_by }) === -1 ? 'User Deleted' : userList.find((j) => { return j.id === i.submitted_by }).information.empname}</span> </text>

                            </div>
                        )
                    })

                    }
                </div>
            </Dialog>
            <Dialog
                visible={usermodal}
                style={{
                    width: "75%",
                }}
                header={"Assign Entities"}
                modal
                className="p-fluid"

                onHide={() => { setUserModal(false) }}
            >
                <div>
                    <div className="col-12 grid">
                        <div className="col-6 ">
                            <div className='col-12 grid'>
                                <div className="flex align-items-center">
                                    <Checkbox inputId="corporate" name="corporate" onChange={(e) => { updateEntityList('other_ids', e.checked ? [0] : []) }} checked={selectedentity.other_ids.includes(0)} />
                                    <label htmlFor="corporate" className="ml-2">Corporate</label>
                                </div>
                            </div>
                            <div className='col-12 grid align-items-center justify-content-between'>
                                <div className="col-3">
                                    <label className="fs-16 fw-5">Tier1</label>
                                </div>
                                <div className="col-8">
                                    <MultiSelect filter optionLabel="name" multiple optionValue="id" style={{ width: '100%' }} value={selectedentity.country_ids} options={selectedLoclist.country} onChange={(e) => { updateEntityList('country_ids', e.value) }} />
                                </div>
                            </div>
                            <div className='col-12 grid align-items-center justify-content-between'>
                                <div className="col-3">
                                    <label className="fs-16 fw-5">Tier2</label>
                                </div>
                                <div className="col-8">
                                    <MultiSelect filter optionLabel="name" multiple optionValue="id" style={{ width: '100%' }} value={selectedentity.city_ids} options={selectedLoclist.city} onChange={(e) => { updateEntityList('city_ids', e.value) }} />
                                </div>
                            </div>
                            <div className='col-12 grid align-items-center justify-content-between'>
                                <div className="col-3">
                                    <label className="fs-16 fw-5">Tier3</label>
                                </div>
                                <div className="col-8">
                                    <MultiSelect filter optionLabel="name" multiple optionValue="id" style={{ width: '100%' }} value={selectedentity.site_ids} options={selectedLoclist.site} onChange={(e) => { updateEntityList('site_ids', e.value) }} />
                                </div>
                            </div>
                        </div>
                        <div className="col-6">
                            <ListBox options={getEntityOptions(selectedentity)} optionLabel="name"
                                optionGroupLabel="name" optionGroupChildren="items" listClassName="dis-option-hover" itemTemplate={itemTemplate} optionGroupTemplate={groupEntityTemplate} className="w-full" listStyle={{ height: '250px' }} />

                        </div>
                    </div>
                    <div className="flex justify-content-end">
                        <Button style={{ marginTop: 20, width: 'auto' }} onClick={() => { AssignRFEntity() }}>Assign Entity</Button>
                    </div>

                </div>
            </Dialog>
            <Dialog
                visible={userassmodal}
                style={{
                    width: "75%",
                }}
                header={"Assign Respondent(s)"}
                modal
                className="p-fluid"

                onHide={() => { setUserAssModal(false) }}
            >
                <div>


                    <div className=" flex justify-content-center">
                        <MultiSelect filter value={selectedloc.reporter_ids} onChange={(e) => { updateUserList('reporter_ids', e.value, selectedrf.data1[0].rf) }} options={userList.filter(i => i.information.role.reporter)} optionLabel="information.empname" optionValue={'id'} display="chip"
                            placeholder="Select User" className="w-full " />
                    </div>
                    <div className="flex justify-content-end">
                        <Button style={{ marginTop: 20, width: 'auto' }} onClick={() => { AssignRFUser() }}>Assign User</Button>
                    </div>

                </div>
            </Dialog>
            <Dialog
                visible={assigndialog}
                style={{
                    width: "75%",
                }}
                header={"Assign "}
                modal
                className="p-fluid"

                onHide={() => { setAssignDialog(false) }}
            >
                <div>

                    <div style={{ marginBottom: 10 }}>
                        <label
                            style={{
                                marginRight: 10, marginBottom: 20
                            }}
                        >
                            Select Type  <span style={{ color: 'red' }}>*</span>
                        </label>

                        <Dropdown showClear style={{ width: '100%' }} optionValue={'id'} value={selectedloc.type} options={[{ name: 'Required', id: 1 }, { name: 'Optional', id: 2 }]} onChange={(e) => { updateDFAssignment("type", e.value) }} optionLabel="name" placeholder="Select Type" />

                    </div>
                    {selectedloc.type === 1 ?
                        <div>
                            <div style={{ marginBottom: 10 }}>
                                <label
                                    style={{
                                        marginRight: 10, marginBottom: 20
                                    }}
                                >
                                    Select Disclosure  <span style={{ color: 'red' }}>*</span>
                                </label>

                                <Dropdown filter style={{ width: '100%' }} optionValue={'id'} value={selectedloc.rfid} options={requiredList.map(i => i.data1[0].rf).filter(i => rawrf.map(j => j.id).includes(i)).map(i => getRF(i)).filter(i => i !== null)} onChange={(e) => { updateDFAssignment("rfid", e.value) }} optionLabel="name" placeholder="Select Disclosure" />

                            </div>
                        </div> :
                        <div>

                            <div style={{ marginBottom: 10 }}>
                                <label
                                    style={{
                                        marginRight: 10, marginBottom: 20
                                    }}
                                >
                                    Select Disclosure  <span style={{ color: 'red' }}>*</span>
                                </label>

                                <Dropdown filter style={{ width: '100%' }} optionValue={'id'} value={selectedloc.rfid} options={requiredList2.map(i => i.data1[0].rf).filter(i => rawrf.map(j => j.id).includes(i)).map(i => getRF(i)).filter(i => i !== null)} onChange={(e) => { updateDFAssignment("rfid", e.value) }} optionLabel="name" placeholder="Select Disclosure" />


                            </div>
                        </div>

                    }

                    {selectedloc.rfid !== null &&
                        <div>
                            <div className="col-12 grid">
                                <div className='col-4 grid align-items-center justify-content-between'>
                                    <div className="col-3">
                                        <label className="fs-16 fw-5">Country</label>
                                    </div>
                                    <div className="col-8">
                                        <Dropdown optionLabel="name" optionValue="id" style={{ width: '100%' }} value={selectedloc.country} options={selectedLoclist.country} onChange={(e) => { updateEntityList('country', e.value, selectedloc.rfid) }} />
                                    </div>
                                </div>
                                {selectedloc.country !== 0 && <div className='col-4 grid align-items-center justify-content-between'>
                                    <div className="col-3">
                                        <label className="fs-16 fw-5">Region</label>
                                    </div>
                                    <div className="col-8">
                                        <Dropdown optionLabel="name" optionValue="id" style={{ width: '100%' }} value={selectedloc.city} options={selectedLoclist.city} onChange={(e) => { updateEntityList('city', e.value, selectedloc.rfid) }} />
                                    </div>
                                </div>}
                                {selectedloc.country !== 0 && selectedloc.city !== 0 && <div className='col-4 grid align-items-center justify-content-between'>
                                    <div className="col-3">
                                        <label className="fs-16 fw-5">Site</label>
                                    </div>
                                    <div className="col-8">
                                        <Dropdown optionLabel="name" optionValue="id" style={{ width: '100%' }} value={selectedloc.site} options={selectedLoclist.site} onChange={(e) => { updateEntityList('site', e.value, selectedloc.rfid) }} />
                                    </div>
                                </div>}
                            </div>
                        </div>

                    }
                    {selectedloc.rfid !== null &&
                        <div style={{ maxHeight: 300, overflow: 'auto' }} className="grid">
                            {userList.filter(i => { return i.information.role.reporter }).map((item => {
                                return (

                                    userTemplate(item)

                                )
                            }))}
                        </div>
                    }
                    {selectedloc.rfid !== null && selectedloc.type !== null &&
                        <div className="flex justify-content-end">
                            <Button label="Save Assignment" style={{ width: 180 }} className="mt-2" onClick={saveDFAssignment} />
                        </div>

                    }
                </div>
            </Dialog>
            <Dialog
                visible={enititymodal}
                style={{
                    width: "75%",
                }}
                header={`Assigned Entity for " ` + selectedrf.name + ` "`}
                modal
                className="p-fluid"

                onHide={() => { setEntityModal(false) }}
            >
                <div>
                    <ListBox options={getEntityOptions(selectedentity)} optionLabel="name"
                        optionGroupLabel="name" optionGroupChildren="items" listClassName="dis-option-hover" itemTemplate={itemTemplate} optionGroupTemplate={groupEntityTemplate} className="w-full" listStyle={{ height: '250px' }} />


                </div>
            </Dialog>
        </div>
    );
};

const comparisonFn = function (prevProps, nextProps) {
    return prevProps.location.pathname === nextProps.location.pathname;
};

export default React.memo(QualitativeRFListing, comparisonFn);
