import React, { useEffect, useRef } from "react";
import * as d3 from "d3";

const data = [
  {
    year: "2020",
    men_below30: 50,
    men_30_50: 70,
    men_above50: 30,
    women_below30: 20,
    women_30_50: 30,
    women_above50: 10,
  },
  {
    year: "2021",
    men_below30: 55,
    men_30_50: 75,
    men_above50: 30,
    women_below30: 25,
    women_30_50: 35,
    women_above50: 15,
  },
  {
    year: "2022",
    men_below30: 60,
    men_30_50: 80,
    men_above50: 30,
    women_below30: 30,
    women_30_50: 40,
    women_above50: 10,
  },
  {
    year: "2023",
    men_below30: 65,
    men_30_50: 85,
    men_above50: 30,
    women_below30: 35,
    women_30_50: 45,
    women_above50: 15,
  },
];

const DiversityByAgeGroup = () => {
  const chartRef = useRef(null);

  useEffect(() => {
    renderChart();
  }, []);

  const renderChart = () => {
    const width = 600;
    const height = 400;
    const margin = { top: 50, right: 190, bottom: 150, left: 60 };

    d3.select(chartRef.current).select("svg").remove();

    const svg = d3
      .select(chartRef.current)
      .append("svg")
      .attr("width", width)
      .attr("height", height);

    const chartWidth = width - margin.left - margin.right;
    const chartHeight = height - margin.top - margin.bottom;

    const g = svg
      .append("g")
      .attr("transform", `translate(${margin.left},${margin.top})`);

    const keys = [
      "men_below30",
      "men_30_50",
      "men_above50",
      "women_below30",
      "women_30_50",
      "women_above50",
    ];

    const stackedData = d3.stack().keys(keys)(data);

    const x = d3
      .scaleBand()
      .domain(data.map((d) => d.year))
      .range([0, chartWidth])
      .padding(0.4);

    const y = d3
      .scaleLinear()
      .domain([0, d3.max(stackedData[stackedData.length - 1], (d) => d[1])])
      .nice()
      .range([chartHeight, 0]);

    // Subtle colors for bars
    const color = d3.scaleOrdinal().domain(keys).range([
      "#cfe2f3", // Light blue
      "#fce2b3", // Light yellow
      "#d9e3f5", // Soft lavender
      "#f8d7da", // Soft red
      "#c6d9f1", // Soft blue
      "#fbe4d5", // Soft peach
    ]);

    g.append("g")
      .attr("transform", `translate(0,${chartHeight})`)
      .call(d3.axisBottom(x));

    g.append("g").call(d3.axisLeft(y));

    g.append("text")
      .attr("x", chartWidth / 2)
      .attr("y", chartHeight + 40)
      .style("text-anchor", "middle")
      .style("font-size", "14px")
      .text("Year");

    g.append("text")
      .attr("transform", "rotate(-90)")
      .attr("x", -chartHeight / 2)
      .attr("y", -40)
      .style("text-anchor", "middle")
      .style("font-size", "14px")
      .text("Number of Employees");

    g.selectAll("g.stack")
      .data(stackedData)
      .enter()
      .append("g")
      .attr("fill", (d) => color(d.key))
      .selectAll("rect")
      .data((d) => d)
      .enter()
      .append("rect")
      .attr("x", (d) => x(d.data.year))
      .attr("y", (d) => y(d[1]))
      .attr("height", (d) => y(d[0]) - y(d[1]))
      .attr("width", 60) // Set bar width to 60
      .append("title")
      .text((d) => `${d.key}: ${d[1] - d[0]}`);

    // Adjusting legend to lower position
    const legend = svg
      .append("g")
      .attr(
        "transform",
        `translate(${width - margin.right + 20},${margin.top})`
      ); // Position legends to the right of the chart

    color.domain().forEach((key, index) => {
      legend
        .append("rect")
        .attr("x", -50)
        .attr("y", index * 25 + 70) // Adjust spacing between legend items
        .attr("width", 15)
        .attr("height", 15)
        .attr("fill", color(key));

      legend
        .append("text")
        .attr("x", 20 - 50)
        .attr("y", index * 25 + 12 + 70) // Align text with the corresponding rectangle
        .style("font-size", "12px") // Reduced font size for better fit
        .style("font-family", "Arial, sans-serif")
        .style("fill", "#333")
        .text(key.replace("_", " ").replace("jg", "Job Grade"));
    });
  };

  return (
    <>
      <h3 style={{ fontSize: "18px", margin: "25px" }}>
        Diversity of Employees by Age Group
      </h3>
      <div style={{ fontSize: "18px", margin: "25px" }}>
        Number and percentage of men and women employees in different age
        groups.
      </div>
      <div
        ref={chartRef}
        style={{ display: "flex", justifyContent: "center" }}
      />
    </>
  );
};

export default DiversityByAgeGroup;
