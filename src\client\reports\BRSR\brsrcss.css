.brsr-report-container {
  display: flex;
  flex-direction: row;
  height: 100vh;
  background-color: #f9fafb;
  overflow: hidden;
}

.report-sidebar {
  width: 280px;
  background-color: #ffffff;
  padding: 1.5rem;
  border-right: 1px solid #e2e8f0;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: column;
}

.report-sidebar-title {
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 1.2rem;
  color: #2d3748;
  text-align: center;
}

.report-sidebar-scroll {
  flex: 1;
  overflow-y: auto;
  padding-right: 0.5rem;
}

.principles-list {
  list-style-type: none;
  padding: 0;
  margin: 0;
}

.principle-item {
  padding: 10px 14px;
  font-size: 0.95rem;
  color: #4a5568;
  border-radius: 6px;
  margin-bottom: 6px;
  cursor: pointer;
  transition: background-color 0.2s ease, color 0.2s ease;
}

.principle-item:hover {
  background-color: #edf2f7;
}

.principle-item.selected {
  background-color: #3182ce;
  color: white;
  font-weight: 600;
}

.main-content {
  flex: 1;
  overflow-y: auto;
  padding: 2rem;
}

.content-card {
  margin-bottom: 2rem;
}

.export-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 2rem;
}
