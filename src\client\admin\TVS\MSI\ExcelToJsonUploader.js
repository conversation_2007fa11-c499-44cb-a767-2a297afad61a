import React, { useState, useRef } from 'react';
import * as XLSX from 'xlsx';
import { v4 as uuidv4 } from 'uuid';
import { FileUpload } from 'primereact/fileupload';
import { Card } from 'primereact/card';
import { Panel } from 'primereact/panel';
import { ScrollPanel } from 'primereact/scrollpanel';
import { Divider } from 'primereact/divider';
import { Toast } from 'primereact/toast';
import APIServices from '../../../../service/APIService';
import { API } from '../../../../constants/api_url';
import { DateTime } from 'luxon';
import { useSelector } from 'react-redux';
import { Accordion, AccordionTab } from 'primereact/accordion';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { Button } from 'primereact/button';

const ExcelToJsonUploader = ({ dealerList, assessorList }) => {
  const [checklistGroups, setChecklistGroups] = useState([]);
  const toast = useRef(null);
  const [excelData, setExcelData] = useState({ response: [], summary: {}, auditorId: null, vendorId: null, dealerId: null })
  const admin_data = useSelector((state) => state.user.admindetail);
  const login_data = useSelector((state) => state.user.userdetail);
  const processSheet = (sheet, sheetName, sectionNumber) => {
    const data = XLSX.utils.sheet_to_json(sheet, { header: 1 });
    let currentGroup = null;
    const structuredData = [];

    for (let i = 1; i < data.length; i++) {
      const row = data[i];
      const [criteria, subCriteria, maxScore, label, question, score] = row;

      if (criteria && subCriteria) {
        currentGroup = {
          type: 'checklist-group',
          required: false,
          label,
          name: `checklist-group-${uuidv4()}`,
          criteria,
          subCriteria,
          maxScore: parseInt(maxScore) || 0,
          section: sectionNumber,
          value: question,
          questions: []
        };
        structuredData.push(currentGroup);
      }

      if (currentGroup && question) {
        // Parse score as float to handle decimal values
        let scoreVal = parseFloat(score);
        // Handle NaN case
        scoreVal = isNaN(scoreVal) ? 0 : scoreVal;

        // Determine which option is checked based on the score value
        const isYesChecked = scoreVal !== 0;
        const isNoChecked = scoreVal === 0;

        currentGroup.questions.push({
          label: question,
          name: `question-${uuidv4()}`,
          options: [
            { label: 'Yes', checked: isYesChecked ? 1 : 0, score: scoreVal },
            { label: 'No', checked: isNoChecked ? 1 : 0, score: 0 },
            { label: 'Not Applicable', checked: 0, score: 0 }
          ],
          type: 'Fulfil'
        });
      }
    }

    // Add header object
    structuredData.unshift({
      type: 'header',
      subtype: 'h1',
      label: `<span style=\"color: rgb(0, 0, 0); font-family: Arial; font-size: 16px; font-weight: 700; text-align: center; white-space-collapse: preserve;\">${sheetName.toUpperCase()}</span>`
    });

    return structuredData;
  };
  function isValidExcelDate(value) {
    if (!value) return false
    if (typeof value !== 'number') return false;

    // Excel serial date must be >= 1 (Excel starts at 1900-01-01)
    if (value < 1) return false;

    const date = XLSX.SSF.parse_date_code(value);
    return !!(date && date.y && date.m && date.d); // check for valid date parts
  }
  const uploadAuditReport = async () => {
    const { response, summary: summaryData, dealerId, vendorId, auditorId } = excelData
    try {
      let newObj = {}
      const excelEpoch = DateTime.fromISO("1899-12-30");
      const auditDate = excelEpoch.plus({ days: Number(summaryData['Audit Date']) });
      let sales = response.filter(x => x.section === 1).reduce((a, b) => { return a + b.score }, 0)
      let services = response.filter(x => x.section === 2).reduce((a, b) => { return a + b.score }, 0)
      const sectionCount = [sales, services].filter(val => val > 0).length || 1;
      let score = JSON.stringify({
        overallScore: (sales + services) / sectionCount,
        salesScore: sales,
        serviceScore: services
      });
      let assObj = {
        assessors: [auditorId], auditStartDate: auditDate,
        dealerId, vendorId, vendorCode: summaryData['Dealer ID']?.toString()?.trim(),
        created_on: DateTime.utc(), created_by: login_data.id
      }
      const res = await APIServices.post(API.DealerAssessmentAss_Up(admin_data.id), assObj);
      newObj['response'] = JSON.stringify(response)
      newObj['dealerId'] = dealerId
      newObj['vendorId'] = vendorId
      newObj['status'] = 'Approved'
      newObj['type'] = 2
      newObj['score'] = score
      newObj['dealerAssessmentAssignmentId'] = res.data.id
      newObj['created_on'] = auditDate
      newObj['created_by'] = auditorId
      newObj['modified_on'] = auditDate
      newObj['modified_by'] = auditorId
      await APIServices.post(API.DealerAuditorSubmission, newObj);
    } catch {
      setExcelData({ response: [], summary: {}, auditorId: null, vendorId: null, dealerId: null })
      toast.current.show({ severity: 'warning', summary: 'Something went wrong', detail: 'Try reload, if issue presist raise ticket with detail' });

    } finally {
      setExcelData({ response: [], summary: {}, auditorId: null, vendorId: null, dealerId: null })
      toast.current.show({ severity: 'success', summary: 'Upload Complete', detail: 'Checklist JSON generated for both sheets.' });

    }

  }
  const handleFileUpload = (event) => {
    const file = event.files[0];
    const reader = new FileReader();

    reader.onload = async (evt) => {
      const bstr = evt.target.result;
      const wb = XLSX.read(bstr, { type: 'binary' });
const sheetName = wb.Sheets['Sales'] ? 'Sales' : wb.Sheets['APO'] ? 'APO' : wb.Sheets['AO'] ? 'AO' : ''
      const salesSheet = wb.Sheets['Sales'] || wb.Sheets['APO'] || wb.Sheets['AO']
      const serviceSheet = wb.Sheets['Service'] ?? null;
      const summarySheet = wb.Sheets['Summary'];
      const range = XLSX.utils.decode_range(summarySheet['!ref']);

      const summaryData = {};

      for (let row = range.s.r; row <= range.e.r; row++) {
        const keyCell = summarySheet[XLSX.utils.encode_cell({ r: row, c: 0 })]; // Column A
        const valueCell = summarySheet[XLSX.utils.encode_cell({ r: row, c: 1 })]; // Column B

        const key = keyCell?.v?.toString().trim();
        const value = valueCell?.v;

        if (key) summaryData[key] = value ?? "";
      }

      console.log(summaryData)

      const dealerId = dealerList.find(x => x?.vendorData?.code === summaryData['Dealer ID']?.toString()?.trim())?.id
      const vendorId = dealerList.find(x => x?.vendorData?.code === summaryData['Dealer ID']?.toString()?.trim())?.vendorId
      const auditorId = assessorList.find(x => x?.email === summaryData['Auditor Mail Id'])?.id
      if (isValidExcelDate(summaryData['Audit Date']) && dealerId && vendorId && auditorId) {
        const salesData = salesSheet ? processSheet(salesSheet, sheetName, 1) : [];
        const serviceData = serviceSheet ? processSheet(serviceSheet, 'Service', 2) : [];

        const combined = [...salesData, ...serviceData];


        combined.forEach((entry) => {
          if (entry.questions && Array.isArray(entry.questions)) {
            (entry?.questions || []).forEach((question) => {
              if (question.score === undefined || question.score === null) {
                const selectedOption = question.options?.find((opt) => opt.checked === 1);
                // Use the score from the selected option, which could be decimal or negative
                question.score = selectedOption ? selectedOption.score : 0;
              }
            });

            // Calculate the total score by summing all question scores
            // This will include decimal and negative values
            entry.score = entry.questions.reduce((sum, q) => {
              // Convert to number to ensure proper addition
              const questionScore = Number(q.score) || 0;
              return sum + questionScore;
            }, 0);
          }
        });
        console.log(combined)

        setExcelData({ response: combined, summary: summaryData, dealerId, vendorId, auditorId })
        toast.current.show({ severity: 'success', summary: 'Upload Complete', detail: 'Checklist JSON generated for both sheets.' });

        setChecklistGroups(combined);
      } else {
        setExcelData({ response: [], summary: {}, auditorId: null, vendorId: null, dealerId: null })
        toast.current.show({ severity: 'warning', summary: 'Missing/Incorrect Data in Summary', detail: 'Check Dealer ID, Audit Start Date and Audit Mail Id ' });

      }
      event.options.clear()

    };

    reader.readAsBinaryString(file);
  };

  return (
    <div className="p-6">
      <Toast ref={toast} />
      <Card >

        <FileUpload
          name="file"
          customUpload
          uploadHandler={handleFileUpload}
          accept=".xlsx,.xls"
          mode="basic"
          auto
          chooseLabel="Upload Excel"
        />
        <Divider />
        <Panel header="Output from excel" toggleable>
          <ScrollPanel style={{ width: '100%', height: '400px' }}>
            <pre style={{ whiteSpace: 'pre-wrap', wordBreak: 'break-word' }}>
              <Accordion >
                {excelData?.response?.filter(x => x.type === "checklist-group")?.map((item, index) => (
                  <AccordionTab key={index} header={item.label.replace(/(<([^>]+)>)/gi, "") + " - " + item.score}>
                    <DataTable value={item?.questions?.filter(x => x.label !== "checkpoints") || []} >
                      <Column field={'label'} header={'Question'} style={{ width: '80%' }} />
                      <Column header={'Response'} body={(rowData) => { return rowData?.options ? rowData?.options?.find(x => x.checked)?.label || '-' : 'No Option' }} />
                      <Column field={'score'} header={'Score'} />
                    </DataTable>

                  </AccordionTab>
                )
                )
                }
              </Accordion>
            </pre>
          </ScrollPanel>
          {excelData?.response?.length !== 0 &&
            <div className='col-12 flex justify-content-end'>
              <Button label={'Upload'} onClick={() => { uploadAuditReport() }} />
            </div>
          }
        </Panel>
      </Card>
    </div>
  );
};

export default ExcelToJsonUploader;
