import React, { useEffect, useRef } from "react";
import * as d3 from "d3";

const CompensationRadarChart = () => {
  const chartRef = useRef(null);

  // Example data representing pay across levels and genders
  const compensationData = [
    { level: "Junior", male: 50000, female: 48000, nonBinary: 49000 },
    { level: "Mid", male: 70000, female: 68000, nonBinary: 69000 },
    { level: "Senior", male: 100000, female: 95000, nonBinary: 97000 },
  ];

  const genders = ["male", "female", "nonBinary"];
  const levels = compensationData.map((d) => d.level);

  useEffect(() => {
    renderRadarChart();
  }, []);

  const renderRadarChart = () => {
    const width = 600;
    const height = 600;
    const margin = { top: 50, right: 50, bottom: 50, left: 50 };

    // Remove any previous SVG elements
    d3.select(chartRef.current).selectAll("*").remove();

    const svg = d3
      .select(chartRef.current)
      .append("svg")
      .attr("width", width)
      .attr("height", height);

    const radius =
      Math.min(width, height) / 2 - Math.max(margin.top, margin.bottom);

    // Scales
    const radiusScale = d3
      .scaleLinear()
      .domain([
        0,
        d3.max(compensationData, (d) =>
          Math.max(d.male, d.female, d.nonBinary)
        ),
      ])
      .range([0, radius]);

    // Calculate angle slice for each level and gender (we now need a slice per gender)
    const angleSlice = (Math.PI * 2) / (levels.length * genders.length);

    // Line generator for radar chart
    const lineGenerator = d3
      .lineRadial()
      .radius((d) => radiusScale(d.value))
      .angle((_, i) => i * angleSlice);

    const genderColors = { male: "blue", female: "pink", nonBinary: "purple" };

    // Draw paths for each gender
    genders.forEach((gender, genderIndex) => {
      const genderData = compensationData.map((d) => ({
        level: d.level,
        value: d[gender],
      }));

      svg
        .append("path")
        .datum(genderData)
        .attr("d", lineGenerator)
        .attr("fill", "none")
        .attr("stroke", genderColors[gender])
        .attr("stroke-width", 2)
        .attr("transform", `translate(${width / 2}, ${height / 2})`);
    });

    // Draw radial axes for all levels and genders
    svg
      .selectAll(".axis-line")
      .data(levels)
      .enter()
      .append("line")
      .attr("class", "axis-line")
      .attr("x1", width / 2)
      .attr("y1", height / 2)
      .attr(
        "x2",
        (d, i) => width / 2 + radius * Math.cos(i * angleSlice - Math.PI / 2)
      )
      .attr(
        "y2",
        (d, i) => height / 2 + radius * Math.sin(i * angleSlice - Math.PI / 2)
      )
      .attr("stroke", "black")
      .attr("stroke-width", 1);

    // Add level labels for each gender
    svg
      .selectAll(".level-label")
      .data(levels)
      .enter()
      .append("text")
      .attr(
        "x",
        (d, i) =>
          width / 2 + (radius + 20) * Math.cos(i * angleSlice - Math.PI / 2)
      )
      .attr(
        "y",
        (d, i) =>
          height / 2 + (radius + 20) * Math.sin(i * angleSlice - Math.PI / 2)
      )
      .text((d) => d)
      .style("text-anchor", "middle")
      .style("font-size", "12px");

    // Draw grid circles for the radar chart
    const gridLevels = [0.2, 0.4, 0.6, 0.8, 1.0];
    gridLevels.forEach((level) => {
      svg
        .append("circle")
        .attr("cx", width / 2)
        .attr("cy", height / 2)
        .attr("r", radius * level)
        .attr("fill", "none")
        .attr("stroke", "grey")
        .attr("stroke-dasharray", "2 2")
        .attr("opacity", 0.7);
    });

    // Add pay scale labels (values for each grid circle)
    gridLevels.forEach((level) => {
      svg
        .append("text")
        .attr("x", width / 2 - 10)
        .attr("y", height / 2 - radius * level)
        .text(
          `$${Math.round(
            level *
              d3.max(compensationData, (d) =>
                Math.max(d.male, d.female, d.nonBinary)
              )
          )}`
        )
        .style("text-anchor", "end")
        .style("font-size", "10px");
    });
  };

  return (
    <div>
      <div
        style={{
          fontSize: "14px",
          fontWeight: 700,
          lineHeight: "19.2px",
          textAlign: "left",
          marginBottom: "5px",
        }}
      >
        Compensation Radar Chart: Pay Equity Across Levels and Genders
      </div>
      <div
        ref={chartRef}
        style={{ display: "flex", justifyContent: "center" }}
      ></div>
    </div>
  );
};

export default CompensationRadarChart;
