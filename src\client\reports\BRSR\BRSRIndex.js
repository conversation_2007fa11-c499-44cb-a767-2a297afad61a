import React, { useEffect, useRef, useState } from "react";
import { But<PERSON> } from "primereact/button";
import { ScrollPanel } from "primereact/scrollpanel";
import axios from "axios";
import './brsrcss.css'
import '../toc_style.css'
// Import all your section components
import SectionA from "./components/SectionAWordExport";
import SectionB from "./components/SectionBWordExport";
import PrincipleOne from "./components/PrincipleOne";
import PrincipleTwo from "./components/PrincipleTwo";
import PrincipleThree from "./components/PrincipleThree";
import PrincipleFour from "./components/PrincipleFour";
import PrincipleFive from "./components/PrincipleFive";
import PrincipleSix from "./components/PrincipleSix";
import PrincipleSeven from "./components/PrincipleSeven";
import PrincipleEight from "./components/PrincipleEight";
import PrincipleNine from "./components/PrincipleNine";
import { useReactToPrint } from 'react-to-print';

// List of all sections
const principles = [
  "SECTION A: GENERAL DISCLOSURES",
  "SECTION B: MANAGEMENT AND PROCESS DISCLOSURES",
  "PRINCIPLE 1: BUSINESSES SHOULD CONDUCT AND GOVERN...",
  "PRINCIPLE 2: BUSINESSES SHOULD PROVIDE GOODS AND SERVICES...",
  "PRINCIPLE 3: BUSINESSES SHOULD RESPECT AND PROMOTE...",
  "PRINCIPLE 4: BUSINESSES SHOULD RESPECT THE INTERESTS...",
  "PRINCIPLE 5: BUSINESSES SHOULD RESPECT AND PROMOTE HUMAN RIGHTS",
  "PRINCIPLE 6: BUSINESSES SHOULD RESPECT AND PROTECT THE ENVIRONMENT",
  "PRINCIPLE 7: BUSINESSES SHOULD ENGAGE IN POLICY IN A TRANSPARENT WAY",
  "PRINCIPLE 8: BUSINESSES SHOULD PROMOTE INCLUSIVE GROWTH",
  "PRINCIPLE 9: BUSINESSES SHOULD PROVIDE VALUE TO CONSUMERS",
];

export default function BRSRIndex() {
  const [selected, setSelected] = useState(principles[0]);
  const [isLoading, setIsLoading] = useState(false);
  const sectionRefs = useRef([]);
  const contentRef = useRef(null)

  const reactToPrintFn = useReactToPrint({ contentRef });
  // Observe section scroll and update selected
  useEffect(() => {
    const options = {
      root: null,
      rootMargin: "0px",
      threshold: Array.from({ length: 11 }, (_, i) => i * 0.1),
    };

    const observer = new IntersectionObserver((entries) => {
      const visibleEntries = entries
        .filter((entry) => entry.isIntersecting)
        .sort((a, b) => b.intersectionRatio - a.intersectionRatio);

      if (visibleEntries.length > 0) {
        const mostVisible = visibleEntries[0];
        const sectionTitle = mostVisible.target.getAttribute("data-title");
        if (sectionTitle) {
          setSelected(sectionTitle);
        }
      }
    }, options);

    sectionRefs.current.forEach((ref) => {
      if (ref) observer.observe(ref);
    });

    return () => {
      sectionRefs.current.forEach((ref) => {
        if (ref) observer.unobserve(ref);
      });
    };
  }, []);

  // Component mapping
  const sectionComponents = {
    "SECTION A: GENERAL DISCLOSURES": <SectionA />,
    "SECTION B: MANAGEMENT AND PROCESS DISCLOSURES": <SectionB />,
    "PRINCIPLE 1: BUSINESSES SHOULD CONDUCT AND GOVERN...": <PrincipleOne />,
    "PRINCIPLE 2: BUSINESSES SHOULD PROVIDE GOODS AND SERVICES...": (
      <PrincipleTwo />
    ),
    "PRINCIPLE 3: BUSINESSES SHOULD RESPECT AND PROMOTE...": (
      <PrincipleThree />
    ),
    "PRINCIPLE 4: BUSINESSES SHOULD RESPECT THE INTERESTS...": (
      <PrincipleFour />
    ),
    "PRINCIPLE 5: BUSINESSES SHOULD RESPECT AND PROMOTE HUMAN RIGHTS": (
      <PrincipleFive />
    ),
    "PRINCIPLE 6: BUSINESSES SHOULD RESPECT AND PROTECT THE ENVIRONMENT": (
      <PrincipleSix />
    ),
    "PRINCIPLE 7: BUSINESSES SHOULD ENGAGE IN POLICY IN A TRANSPARENT WAY": (
      <PrincipleSeven />
    ),
    "PRINCIPLE 8: BUSINESSES SHOULD PROMOTE INCLUSIVE GROWTH": (
      <PrincipleEight />
    ),
    "PRINCIPLE 9: BUSINESSES SHOULD PROVIDE VALUE TO CONSUMERS": (
      <PrincipleNine />
    ),
  };

  // Dummy data structure for report
  const reportStructure = {
    cover: {
      title: "Business Responsibility and Sustainability Report",
      year: "2024",
      company: "Your Company Name",
    },
    index: principles,
    sections: principles.map((p) => ({
      title: p,
      content: `Placeholder content for ${p}.`,
      formulaData: {},
    })),
    selected,
  };

  // Export handler
  const exportReport = async (type) => {
    setIsLoading(true);
    try {
      const fileName = type === "pdf" ? "BRSR_Report.pdf" : "BRSR_Report.docx";
      const response = await axios.post(
        `http://127.0.0.1:3000/report/download/${type}`,
        reportStructure,
        { responseType: "blob" }
      );

      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement("a");
      link.href = url;
      link.setAttribute("download", fileName);
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);
    } catch (error) {
      alert("Failed to download report");
      console.error(error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="brsr-report-container">
      {/* Sidebar */}
      <div className="report-sidebar">
        <h2 className="report-sidebar-title">BRSR Report Index</h2>
        <ScrollPanel className="report-sidebar-scroll">
          <ul className="principles-list">
            {principles.map((p, index) => (
              <li
                key={p}
                onClick={() => {
                  setSelected(p);
                  sectionRefs.current[index]?.scrollIntoView({
                    behavior: "smooth",
                    block: "start",
                  });
                }}
                className={`principle-item ${selected === p ? "selected" : ""}`}
              >
                {p}
              </li>
            ))}
          </ul>
        </ScrollPanel>
      </div>

      {/* Main Content */}
      <div className="main-content scroll-sections" ref={contentRef}>
        {principles.map((p, index) => (
          <div
            key={p}
            ref={(el) => (sectionRefs.current[index] = el)}
            data-title={p}
            className="content-card gridlines"
            style={{ minHeight: "80vh", paddingBottom: "2rem" }}
          >
            {sectionComponents[p] || <p>Section not available</p>}
          </div>
        ))}

        {/* Export Buttons */}
        <div className="export-buttons" style={{ marginTop: "2rem" }}>
          <Button
            label={isLoading ? "Exporting PDF..." : "Export PDF"}
            icon="pi pi-file-pdf"
        onClick={() => { reactToPrintFn() }} 

            loading={isLoading}
            severity="danger"
            disabled={isLoading}
          />
          <Button
            label={isLoading ? "Exporting Word..." : "Export Word"}
            icon="pi pi-file-word"

            loading={isLoading}
            severity="info"
            disabled={isLoading}
          />
        </div>
      </div>
    </div>
  );
}
