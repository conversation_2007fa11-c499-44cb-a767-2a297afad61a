import React, { useEffect, useRef, useState } from "react";
import * as d3 from "d3";
import { Button } from "primereact/button";
import { Column } from "primereact/column";
import { DataTable } from "primereact/datatable";
import { Checkbox } from "@material-ui/core";
const dummyData = [
  { name: "Scope 1", value: 30, category: "Stationary" },
  { name: "Scope 1", value: 20, category: "Mobile" },
  { name: "Scope 1", value: 10, category: "Fugitive" },
  { name: "Scope 2", value: 40, category: null },
  { name: "Scope 3", value: 50, category: "Category 1" },
  { name: "Scope 3", value: 40, category: "Category 11" },
  { name: "Scope 3", value: 60, category: "Category 12" },
];

const OverviewBubbleChart = () => {
  const [activeMode, setActiveMode] = useState(true);
  const chartRef = useRef(null);
  //const menuRef = useRef(null);
  const tableRef = useRef(null);
  const [visibleSeries, setVisibleSeries] = useState({
    scope1: true,
    scope2: true,
    scope3: true,
  });
  const [data, setData] = useState(dummyData);

  const handleCheckboxChange = (key) => {
    setVisibleSeries((prevState) => ({
      ...prevState,
      [key]: !prevState[key],
    }));

    setData((prevData) =>
      prevData.map((item) => {
        if (item.name.toLowerCase().replace(/\s+/g, "") === key) {
          return {
            ...item,
            value: visibleSeries[key]
              ? 0
              : dummyData.find(
                  (d) => d.name === item.name && d.category === item.category
                )?.value,
          };
        }
        return item;
      })
    );
  };

  useEffect(() => {
    if (activeMode) {
      renderBubbleChart();
    }
  }, [activeMode]);

  const renderBubbleChart = () => {
    const width = 600;
    const height = 400;

    // Clear any existing SVG
    d3.select(chartRef.current).select("svg").remove();

    const svg = d3
      .select(chartRef.current)
      .append("svg")
      .attr("width", width)
      .attr("height", height);

    const bubbleData = d3.pack().size([width, height]).padding(5)(
      d3.hierarchy({ children: dummyData }).sum((d) => d.value)
    );

    const nodes = svg
      .selectAll("circle")
      .data(bubbleData.leaves())
      .enter()
      .append("g")
      .attr("transform", (d) => `translate(${d.x}, ${d.y})`);

    nodes
      .append("circle")
      .attr("r", (d) => d.r)
      .attr("fill", (d) => {
        switch (d.data.name) {
          case "Scope 1":
            return "#003f5c";
          case "Scope 2":
            return "#58508d";
          case "Scope 3":
            return "#bc5090";
          default:
            return "#ddd";
        }
      })
      .attr("stroke", "#333")
      .attr("stroke-width", 1);

    nodes
      .append("text")
      .attr("dy", "0.3em")
      .attr("text-anchor", "middle")
      .style("font-size", "10px")
      .text((d) => d.data.category || d.data.name);
  };

  return (
    <>
      <div
        style={{
          width: "100%",
          display: "flex",
          justifyContent: "space-between",
        }}
      >
        <div
          style={{
            fontFamily: "Lato",
            fontSize: "16px",
            fontWeight: 700,
            lineHeight: "19.2px",
            textAlign: "left",
            margin: "18px 10px 18px 10px",
          }}
        >
          Bubble Chart for Emissions
          <div style={{ fontWeight: 200, fontSize: "14px" }}>
            A visual representation of Scope 1, Scope 2, and Scope 3 emissions
            using bubbles to signify relative values.
          </div>
        </div>
        <div
          style={{
            margin: "18px 10px 18px 10px",
            display: "flex",
          }}
        >
          <div
            className="buttons"
            style={{
              background: "#F0F2F4",
              borderRadius: "3px",
              width: "4.5rem",
              height: "30px",
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
            }}
          >
            <Button
              style={{
                background: !activeMode ? "#FFFFFF" : "transparent",
                padding: "6px",
                color: "black",
                border: "0px",
                marginRight: "4px",
              }}
              onClick={() => setActiveMode(false)}
            >
              <i className="pi pi-table fs-19 " />
            </Button>
            <Button
              style={{
                background: activeMode ? "#FFFFFF" : "transparent",
                padding: "6px",
                color: "black",
                border: "0px",
              }}
              onClick={() => setActiveMode(true)}
            >
              <i className="pi pi-chart-bar fs-19" />
            </Button>
          </div>
        </div>
      </div>

      {/* Bubble Chart */}
      {activeMode && (
        <>
          <div
            style={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
              height: "400px",
            }}
            ref={chartRef}
          />
          {/* Legend */}
          <div style={{ textAlign: "center", marginTop: "20px" }}>
            <div style={{ display: "inline-block", marginRight: "20px" }}>
              <Checkbox
                checked={visibleSeries["scope1"]}
                onChange={() => handleCheckboxChange("scope1")}
                style={{
                  color: "#003f5c",
                  marginRight: 4,
                  fontSize: "20px",
                }}
              />

              <span style={{ color: "#555", fontSize: "14px" }}>Scope 1</span>
            </div>
            <div style={{ display: "inline-block", marginRight: "20px" }}>
              <Checkbox
                checked={visibleSeries["scope2"]}
                onChange={() => handleCheckboxChange("scope2")}
                style={{
                  color: "#58508d",
                  marginRight: 4,
                  fontSize: "20px",
                }}
              />

              <span style={{ color: "#555", fontSize: "14px" }}>Scope 2</span>
            </div>
            <div style={{ display: "inline-block" }}>
              <Checkbox
                checked={visibleSeries["scope3"]}
                onChange={() => handleCheckboxChange("scope3")}
                style={{
                  color: "#bc5090",
                  marginRight: 4,
                  fontSize: "20px",
                }}
              />
              <span style={{ color: "#555", fontSize: "14px" }}>Scope 3</span>
            </div>
          </div>
        </>
      )}

      {/* Data Table */}
      {!activeMode && (
        <DataTable value={dummyData} tableClassName="font-lato" ref={tableRef}>
          <Column
            header="Name"
            style={{ minWidth: "20%" }}
            field="name"
            emptyMessage="No data available"
          />
          <Column
            header="Category"
            style={{ minWidth: "20%" }}
            field="category"
            emptyMessage="No data available"
          />
          <Column
            header="Value"
            style={{ minWidth: "20%" }}
            field="value"
            emptyMessage="No data available"
          />
        </DataTable>
      )}
    </>
  );
};

export default OverviewBubbleChart;
