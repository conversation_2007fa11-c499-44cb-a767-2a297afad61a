import React from "react";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>Axis,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ip,
  Legend,
  ResponsiveContainer,
  CartesianGrid,
  ReferenceLine,
  Label,
  Cell,
} from "recharts";
import { useEffect, useState } from "react";
const ESGOverviewTab = ({ data }) => {
  const [selectedData, setSelectedData] = useState([]);

  useEffect(() => {
    console.log(data, ' T Data');
    setSelectedData(transformData(data));
  }, [data]);

  const transformData = (data) => {
    const categories = [
      { key: "water_management", label: "Water Management", maxValue: 10 },
      {
        key: "waste_water_management",
        label: "Waste Water Management",
        maxValue: 10,
      },
      { key: "waste_management", label: "Waste Management", maxValue: 10 },
      { key: "energy_management", label: "Energy Management", maxValue: 10 },
      { key: "road_safety", label: "Road Safety", maxValue: 10 },
      { key: "electrical_safety", label: "Electrical Safety", maxValue: 10 },
      { key: "office_safety", label: "Office Safety", maxValue: 10 },
      {
        key: "fair_business_practices",
        label: "Fair Business Practices",
        maxValue: 10,
      },
      { key: "data_privacy", label: "Data Privacy", maxValue: 10 },
    ];

    return categories.map(({ key, label, maxValue }) => {
      const achieved =
        data.reduce((sum, item) => sum + item[key], 0) / data.length;
      const achievedClamped = Math.max(0, parseFloat(achieved.toFixed(2))); // Ensuring non-negative values
      return {
        category: label,
        achieved: achievedClamped,
        remaining: parseFloat((maxValue - achievedClamped).toFixed(2)),
      };
    });
  };

  // Data for Tab 2
  // const tab2Data = [
  //   {
  //     category: "Sales",
  //     achieved: 70, // Example score for Sales in Tab 2
  //     remaining: 30,
  //   },
  //   {
  //     category: "Service",
  //     achieved: 80, // Example score for Service in Tab 2
  //     remaining: 20,
  //   },
  // ];

  // // Data for Tab 3
  // const tab3Data = [
  //   {
  //     category: "Sales",
  //     achieved: 75, // Example score for Sales in Tab 3
  //     remaining: 25,
  //   },
  //   {
  //     category: "Service",
  //     achieved: 82, // Example score for Service in Tab 3
  //     remaining: 18,
  //   },
  // ];

  // Calculate MSI Score for the selected tab
  // const selectedData = tabIndex === 1 ? tab2Data : tab3Data;
  const msiScore = (selectedData[0].achieved + selectedData[1].achieved) / 2;

  const getMSIGrade = (score) => {
    if (score > 86) return "Platinum";
    if (score >= 71) return "Gold";
    if (score >= 56) return "Silver";
    if (score >= 41) return "Bronze";
    return "Needs Improvement";
  };

  const msiGrade = getMSIGrade(msiScore);

  const CustomLegend = (props) => {
    const { payload } = props;
    return (
      <ul
        style={{
          display: "flex",
          listStyleType: "none",
          justifyContent: "center",
          padding: 0,
          marginTop: "10px",
        }}
      >
        {payload.map((entry, index) => (
          <li
            key={`item-${index}`}
            style={{
              color: entry.color,
              marginRight: "5px",
            }}
          >
            <span
              style={{
                color: entry.color,
                backgroundColor: entry.color,
                marginRight: 4,
                fontSize: "20px",
                width: "10px",
                height: "10px",
                borderRadius: "50%",
                display: "inline-block",
              }}
            ></span>
            <span style={{ color: "#555", fontSize: "14px" }}>
              {entry.value}
            </span>
          </li>
        ))}
      </ul>
    );
  };

  // Bar colors based on MSI grade
  const getBarColor = (score) => {
    if (score === "Platinum") return "#CCCED5"; // Dark Green for Platinum
    if (score === "Gold") return "#F5C37B"; // Green for Gold
    if (score === "Silver") return "#EAECF0"; // Yellow for Silver
    if (score === "Bronze") return "#D28B24"; // Orange for Bronze
    return "#EE5724"; // Red for Needs Improvement
  };

  return (
    <div style={{ padding: "20px" }}>
      {/* MSI Score and Grade Display */}
      <div
        style={{
          display: "flex",
          justifyContent: "space-around",
          marginBottom: "20px",
        }}
      >
        <h2 style={{ color: "#0D5EAF" }}>MSI Scores: {msiScore.toFixed(1)}</h2>
        <h2 style={{ color: "#0D5EAF" }}>MSI Grade: {msiGrade}</h2>
      </div>

      {/* MSI Chart */}
      <div style={{ width: "100%", height: 400, marginBottom: "40px" }}>
        <ResponsiveContainer width="100%" height="100%">
          <BarChart
            data={selectedData}
            barSize={60}
            margin={{ top: 20, right: 30, left: 20, bottom: 15 }}
          >
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="category" tick={{ fontSize: 12 }} />
            <YAxis domain={[0, 100]} />
            <Tooltip
              formatter={(value, name, props) => {
                if (name === "Achieved Score") {
                  return [`${value.toFixed(2)} (Max: 100)`, name];
                }
                return [null, null];
              }}
            />
            <Legend content={CustomLegend} />
            <Bar dataKey="achieved" stackId="score" name="Achieved Score">
              {selectedData.map((entry, index) => (
                <Cell
                  key={`cell-achieved-${index}`}
                  fill={getBarColor(getMSIGrade(entry.achieved))}
                />
              ))}
            </Bar>
            <Bar
              dataKey="remaining"
              stackId="score"
              fill="#CCCED5"
              name="Max Score"
            />
            {/* Reference Line for MSI Score */}
            <ReferenceLine y={msiScore} stroke="#FF5733" strokeDasharray="3 3">
              <Label
                value={`Average MSI: ${msiScore.toFixed(1)}`}
                position="center"
                fill="#FF5733"
              />
            </ReferenceLine>
          </BarChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
};

export default ESGOverviewTab;
