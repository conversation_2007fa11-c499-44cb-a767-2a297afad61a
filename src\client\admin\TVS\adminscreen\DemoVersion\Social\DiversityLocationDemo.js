import React, { useState } from "react";
import {
  <PERSON><PERSON><PERSON>,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  Label,
} from "recharts";

const data = {
  India: [
    {
      year: "FY-2024",
      male: 84.8,
      female: 15.2,
    },
    {
      year: "FY-2025",
      male: 0,
      female: 0,
    },
  ],
  Indonesia: [
    {
      year: "FY-2024",
      male: 54.2,
      female: 45.8,
    },
    {
      year: "FY-2025",
      male: 0,
      female: 0,
    },
  ],
  "United Kingdom": [
    {
      year: "FY-2024",
      male: 74.4,
      female: 25.6,
    },
    {
      year: "FY-2025",
      male: 0,
      female: 0,
    },
  ],
  Global: [
    {
      year: "FY-2023",
      male: 83.7,
      female: 16.3,
    },
    {
      year: "FY-2025",
      male: 0,
      female: 0,
    },
  ],
};

const DiversityLocationDemo = () => {
  const [location, setLocation] = useState("India");

  return (
    <div
      style={{
        fontFamily: "Lato",
        fontSize: "16px",
        fontWeight: 700,
        lineHeight: "19.2px",
        textAlign: "left",
        margin: "18px 10px",
      }}
    >
      <h3 style={{ fontSize: "18px", margin: "25px" }}>
        Gender Diversity by Location
      </h3>
      <div style={{ fontWeight: 200, fontSize: "14px", marginBottom: "10px" }}>
        Select a location to view the gender diversity data.
      </div>
      {/* Location Selection Buttons */}
      <div style={{ marginBottom: "20px" }}>
        {Object.keys(data).map((locationName) => (
          <button
            key={locationName}
            onClick={() => setLocation(locationName)}
            style={{
              padding: "8px 16px",
              backgroundColor: location === locationName ? "#8888" : "#F0F0F0",
              color: location === locationName ? "white" : "gray",
              border: "none",
              borderRadius: "8px",
              height: "2.5rem",
              marginRight: "2rem",
              cursor: "pointer",
            }}
          >
            {locationName}
          </button>
        ))}
      </div>
      {/* Responsive Container for Recharts */}
      <ResponsiveContainer width="100%" height={500}>
        <BarChart
          data={data[location]}
          margin={{ top: 20, right: 30, left: 20, bottom: 60 }}
        >
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis dataKey="year"></XAxis>
          <YAxis>
            <Label
              value="Percentage"
              angle={-90}
              position="insideLeft"
              style={{
                textAnchor: "middle",
                fill: "#333",
                fontSize: "14px",
              }}
            />
          </YAxis>
          <Tooltip />
          <Legend verticalAlign="bottom" />
          <Bar dataKey="male" name="Male" fill="#47CC" barSize={60} />
          <Bar dataKey="female" name="Female" fill="#88CC" barSize={60} />
        </BarChart>
      </ResponsiveContainer>
    </div>
  );
};

export default DiversityLocationDemo;
