import React from 'react';
import { Card } from 'primereact/card';
import { Divider } from 'primereact/divider';
import { DateTime } from 'luxon';

const SupplierInformationCard = ({ action }) => {
    const dateTemplate = (rowData, field) => {
        const dateStr = rowData?.[field];
        return dateStr ? DateTime.fromISO(dateStr).toFormat('dd-MM-yyyy') : 'NA';
    };

    return (
        <Card className="h-full shadow-2"
            title={
                <div className="flex align-items-center">
                    <i className="pi pi-building mr-2" style={{ color: '#315975' }}></i>
                    <span className="font-bold">Supplier Information</span>
                </div>
            }
        >
            <div className="grid">
                <div className="col-6">
                    <div className="field mb-2">
                        <label className="block text-sm font-medium text-gray-700">Supplier</label>
                        <div className="text-lg font-semibold">{action.vendorName}</div>
                    </div>
                </div>

                <div className="col-6">
                    <div className="field mb-2">
                        <label className="block text-sm font-medium text-gray-700">Location</label>
                        <div className="text-lg font-semibold">{action.vendorLocation}</div>
                    </div>
                </div>

                <div className="col-6">
                    <div className="field mb-2">
                        <label className="block text-sm font-medium text-gray-700">MSI ID</label>
                        <div>{action.msiId}</div>
                    </div>
                </div>

                <div className="col-6">
                    <div className="field mb-2">
                        <label className="block text-sm font-medium text-gray-700">Stat</label>
                        <div>{action.stat}</div>
                    </div>
                </div>

                <div className="col-12">
                    <Divider className="my-2" />
                    <h5 className="mt-0 mb-2 flex align-items-center">
                        <i className="pi pi-calendar mr-2" style={{ color: '#315975' }}></i>
                        Audit & Assessment Periods
                    </h5>
                </div>

                <div className="col-6">
                    <div className="field mb-2">
                        <label className="block text-sm font-medium text-gray-700">Audit Period</label>
                        <div>{dateTemplate(action, 'auditStartDate')} to {dateTemplate(action, 'auditEndDate')}</div>
                    </div>
                </div>

                <div className="col-6">
                    <div className="field mb-2">
                        <label className="block text-sm font-medium text-gray-700">Assessment Period</label>
                        <div>{dateTemplate(action, 'assessmentStartDate')} to {dateTemplate(action, 'assessmentEndDate')}</div>
                    </div>
                </div>

                <div className="col-12">
                    <Divider className="my-2" />
                    <h5 className="mt-0 mb-2 flex align-items-center">
                        <i className="pi pi-users mr-2" style={{ color: '#315975' }}></i>
                        Team Members
                    </h5>
                </div>

                <div className="col-6">
                    <div className="field mb-2">
                        <label className="block text-sm font-medium text-gray-700">Team 1</label>
                        <div>{action.team1Members?.join(', ') || 'Not Assigned'}</div>
                    </div>
                </div>

                <div className="col-6">
                    <div className="field mb-2">
                        <label className="block text-sm font-medium text-gray-700">Team 2</label>
                        <div>{action.team2Members?.join(', ') || 'Not Assigned'}</div>
                    </div>
                </div>

                <div className="col-6">
                    <div className="field mb-2">
                        <label className="block text-sm font-medium text-gray-700">Team 3</label>
                        <div>{action.team3Members?.join(', ') || 'Not Assigned'}</div>
                    </div>
                </div>

                <div className="col-6">
                    <div className="field mb-2">
                        <label className="block text-sm font-medium text-gray-700">Team 4</label>
                        <div>{action.team4Members?.join(', ') || 'Not Assigned'}</div>
                    </div>
                </div>
            </div>
        </Card>
    );
};

export default SupplierInformationCard;
