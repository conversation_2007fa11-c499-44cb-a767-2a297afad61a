import React, { useState, useMemo } from 'react';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { InputText } from 'primereact/inputtext';
import { MultiSelect } from 'primereact/multiselect';
import { DateTime } from 'luxon';
import { Button } from 'primereact/button';
import * as XLSX from 'xlsx';
import { saveAs } from 'file-saver';


const categoryOptions = [
    { label: 'Good Practices', value: 1 },
    { label: 'Opportunity of Improvement', value: 2 },
    { label: 'Non-compliance', value: 3 },
];

const nonComplianceOptions = [
    { label: 'Regulatory (Major)', value: 1 },
    { label: 'Regulatory (Minor)', value: 2 },
    { label: 'Minor', value: 3 },
];

const SupplierActionCompletedTable = ({ data, assessorList }) => {
    const [globalFilter, setGlobalFilter] = useState('');
    const supplierActions = useMemo(() => {
        // Filter actions to only include those with status 'completed' or 'submitted'
        return data?.flatMap(item =>
            item?.supplierActions
                ?.filter(action => {
                    const status = action?.status?.toLowerCase();
                    return status === 'completed' || status === 'submitted';
                })
                ?.map(action => ({
                    ...action,
                    vendorName: item?.vendor?.supplierName
                })) || []
        ) || [];
    }, [data]);


    const getUserName = (id) => {
        const user = assessorList?.find(u => u.id === id);
        return user?.information?.empname || `User ID: ${id}`;
    };

    const getCategoryLabel = (id) => {
        return categoryOptions.find(option => option.value === id)?.label || `Category ID: ${id}`;
    };

    const getNonComplianceLabel = (id) => {
        return id ? nonComplianceOptions.find(option => option.value === id)?.label || `Type ID: ${id}` : 'NA';
    };

    const getStatusLabel = (status) => {
        return status ? status : 'Assigned';
    };

    const dateTemplate = (rowData, field) => {
        const dateStr = rowData?.[field];
        return dateStr ? DateTime.fromISO(dateStr).toFormat('dd-MM-yyyy') : 'NA';
    };

    const getUniqueOptions = (field, labelMapFn) => {
        let values = [...new Set(supplierActions.map(d => d[field]))];

        // For status field, ensure "Assigned" is included in the options
        if (field === 'status') {
            // Add null to the values if it doesn't exist
            if (!values.includes(null) && !values.includes('')) {
                values.push(null);
            }

            // Map the values to their display labels
            return values.map(val => ({
                label: val ? val : 'Assigned',
                value: val
            }));
        }

        // For other fields, use the standard mapping
        return values.map(val => ({
            label: labelMapFn ? labelMapFn(val) : val,
            value: val
        }));
    };

    const createMultiSelectFilter = (field, options) => {
        return (optionsProps) => {
            return (
                <MultiSelect
                    value={optionsProps.value}
                    options={options}
                    optionLabel="label"
                    onChange={(e) => optionsProps.filterCallback(e.value)}
                    placeholder={`Select ${field}`}
                    filter
                    panelClassName='hidefilter'
                    className="p-column-filter"
                    maxSelectedLabels={1}
                    style={{ minWidth: "14rem" }}
                />
            );
        };
    };
    const exportExcel = () => {
        if (!supplierActions || supplierActions.length === 0) {
            alert('Warning', 'No actions to export.', 'warning');
            return;
        }

        const exportData = supplierActions.map((action, index) => ({
            'S.No': index + 1,
            'Supplier Name': action.vendorName || 'NA',
            'Finding': action.finding || 'NA',
            'Description': action.description || 'NA',
            'Category': getCategoryLabel(action.categoryOfFinding) || 'NA',
            'Non-Compliance Type': getNonComplianceLabel(action.nonComplianceType) || 'NA',
            'Status': getStatusLabel(action.status),
            'Due Date': action.actionDueDate ? DateTime.fromISO(action.actionDueDate).toFormat('dd-MM-yyyy') : 'NA',
            'Created On': action.created_on ? DateTime.fromISO(action.created_on).toFormat('dd-MM-yyyy') : 'NA',
            'Created By': getUserName(action.created_by)
        }));

        const worksheet = XLSX.utils.json_to_sheet(exportData);
        const workbook = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(workbook, worksheet, 'CompletedActions');

        const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });
        const dataBlob = new Blob([excelBuffer], { type: 'application/octet-stream' });
        saveAs(dataBlob, `Completed_Actions_${DateTime.now().toFormat('yyyyMMdd_HHmmss')}.xlsx`);
    };

    return (
        <div className="card">
            <div className="flex justify-content-between align-items-center mb-3">
                <div className="flex align-items-center">
                    <h3 className="m-0 mr-3 me-3">Completed Actions</h3>
                    <Button
                        label="Export Excel"
                        icon="pi pi-file-excel"
                        className="p-button-success p-button-outlined"
                        onClick={exportExcel}
                    />
                </div>
                <span className="p-input-icon-left">
                    <i className="pi pi-search" />
                    <InputText
                        value={globalFilter}
                        onChange={(e) => setGlobalFilter(e.target.value)}
                        placeholder="Global Search"
                    />
                </span>
            </div>


            <DataTable
                value={supplierActions}
                paginator
                rows={10}
             rowsPerPageOptions={[10, 25, 50, 100,150,200]}
                globalFilter={globalFilter}
                filterDisplay="menu"
                scrollable
                scrollHeight="500px"
                className="p-datatable-gridlines"
                emptyMessage="No supplier actions found."
                removableSort
                filters={{
                    vendorName: { matchMode: 'in', value: null },
                    finding: { matchMode: 'in', value: null },
                    description: { matchMode: 'in', value: null },
                    categoryOfFinding: { matchMode: 'in', value: null },
                    nonComplianceType: { matchMode: 'in', value: null },
                    status: { matchMode: 'in', value: null },
                    created_by: { matchMode: 'in', value: null }
                }}
            >
                <Column field="id" header="Action ID" />

                <Column
                    field="vendorName"
                    header="Supplier Name"
                    sortable
                    filter
                    filterField="vendorName"
                    filterElement={createMultiSelectFilter(
                        'Supplier',
                        getUniqueOptions('vendorName')
                    )}
                    filterFunction={(value, filter) => !filter || filter.includes(value)}
                    showFilterMatchModes={false}
                />

                <Column
                    field="finding"
                    header="Finding"
                    sortable
                    filter
                    filterField="finding"
                    filterElement={createMultiSelectFilter(
                        'Finding',
                        getUniqueOptions('finding')
                    )}
                    filterFunction={(value, filter) => !filter || filter.includes(value)}
                    showFilterMatchModes={false}
                />

                <Column
                    field="description"
                    header="Description"
                    sortable
                    filter
                    style={{ minWidth: '300px' }}
                    filterField="description"
                    filterElement={createMultiSelectFilter(
                        'Description',
                        getUniqueOptions('description')
                    )}
                    filterFunction={(value, filter) => !filter || filter.includes(value)}
                    showFilterMatchModes={false}
                />

                <Column
                    field="categoryOfFinding"
                    header="Category"
                    sortable
                    filter
                    filterField="categoryOfFinding"
                    body={(rowData) => getCategoryLabel(rowData.categoryOfFinding)}
                    filterElement={createMultiSelectFilter(
                        'Category',
                        categoryOptions
                    )}
                    filterFunction={(value, filter) => !filter || filter.includes(value)}
                    showFilterMatchModes={false}
                />

                <Column
                    field="nonComplianceType"
                    header="Non-Compliance Type"
                    sortable
                    filter
                    filterField="nonComplianceType"
                    body={(rowData) => getNonComplianceLabel(rowData.nonComplianceType)}
                    filterElement={createMultiSelectFilter(
                        'Non-Compliance Type',
                        nonComplianceOptions
                    )}
                    filterFunction={(value, filter) => !filter || filter.includes(value)}
                    showFilterMatchModes={false}
                />

                <Column
                    field="status"
                    header="Status"
                    sortable
                    filter
                    filterField="status"
                    body={(rowData) => getStatusLabel(rowData.status)}
                    filterElement={createMultiSelectFilter(
                        'Status',
                        getUniqueOptions('status')
                    )}
                    filterFunction={(value, filter) => !filter || filter.includes(value)}
                    showFilterMatchModes={false}
                />

                <Column
                    field="actionDueDate"
                    header="Due Date"
                    sortable
                    body={(rowData) => dateTemplate(rowData, 'actionDueDate')}
                />

                <Column
                    field="created_on"
                    header="Created On"
                    sortable
                    body={(rowData) => dateTemplate(rowData, 'created_on')}
                />

                <Column
                    field="created_by"
                    header="Created By"
                    sortable
                    filter
                    filterField="created_by"
                    body={(rowData) => getUserName(rowData.created_by)}
                    filterElement={createMultiSelectFilter(
                        'Created By',
                        getUniqueOptions('created_by', getUserName)
                    )}
                    filterFunction={(value, filter) => !filter || filter.includes(value)}
                    showFilterMatchModes={false}
                />
            </DataTable>
        </div>
    );
};

export default SupplierActionCompletedTable;
