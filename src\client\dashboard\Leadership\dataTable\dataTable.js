import React, { useState } from 'react';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { Dropdown } from 'primereact/dropdown';
import { MultiSelect } from 'primereact/multiselect';
import { Button } from 'primereact/button';
import { Dialog } from 'primereact/dialog';
import { Checkbox } from 'primereact/checkbox';
import 'primeflex/primeflex.css';

const countries = [
    { name: 'India', id: 1 },
    { name: 'USA', id: 2 },
    // Add more countries as needed
];

const cities = [
    {name: 'All', id: 1},
    { name: 'New York', id: 2 },
    { name: 'Los Angeles', id: 3 },
    // Add more cities as needed
];

const units = [
    {name: 'All', id: 1},
    { name: 'Unit 1', id: 2 },
    { name: 'Unit 2', id: 3 },
    // Add more units as needed
];

const names = [{
    name: 'Anisha'
}, {
    name: '<PERSON>'
}];


const DataTableComponent = () => {
    const [dialogState, setDialogState] = useState([]);
    const [currentObj, setCurrentObj] = useState({
        name: null,
        selectedCountry: null,
        selectedCity: null,
        selectedUnit: null,
    });
    const [visible, setVisible] = useState(false);
    const [userName, setUserName] = useState();
    const [ingredients, setIngredients] = useState([]);

    const showDialog = (currentName) => {
        setCurrentObj({
            selectedCountry: null,
            selectedCity: null,
            selectedUnit: null,
            name: currentName,
        });
        setIngredients([]);
        setUserName(currentName);
        setVisible(true);
    };

    const hideDialog = () => {
        setVisible(false);
    };

    const findDialogState = (current) => {
        let obj = null;
        for(let i=0;i<dialogState.length;i++) {
            console.log("Current diagonal state object: " + JSON.stringify(dialogState[i]));
            console.log("Current object:" + JSON.stringify(currentObj));
            if(dialogState[i].name === current.name && 
                dialogState[i].selectedCountry === current.selectedCountry && 
                dialogState[i].selectedCity === current.selectedCity && 
                dialogState[i].selectedUnit === current.selectedUnit) {
                obj = dialogState[i];
            }
        }
        return obj;
    }

    const handleDropdownChange = (field, value) => {
        console.log("handleDropdownChange:" , value);
        let current;
        if (field === 'selectedCountry') {
            current = {
                ...currentObj,
                selectedCountry: value,
                selectedCity: cities[0].id,
                selectedUnit: units[0].id,
            }
            setCurrentObj(current);
        } else if (field === 'selectedCity') {
            current = {
                ...currentObj,
                selectedCity: value,
                selectedUnit: units[0].id,
            }
            setCurrentObj(current);
        } else if (field === 'selectedUnit') {
            current = {
                ...currentObj,
                selectedUnit: value
            }
            setCurrentObj(current);
        }
        const obj = findDialogState(current);
        console.log("Obj:", obj);
        if(obj) {
            const checkBoxes = obj.checkboxValues;
            setIngredients(checkBoxes);
        } else {
            setIngredients([]);
        }
    };

    const onIngredientsChange = (e) => {
        let _ingredients = [...ingredients];

        if (e.checked)
            _ingredients.push(e.value);
        else
            _ingredients.splice(_ingredients.indexOf(e.value), 1);

        setIngredients(_ingredients);
    };

    const saveAssignment = () => {
        const dialogObject = {
            name: currentObj.name,
            selectedCountry: currentObj.selectedCountry,
            selectedCity: currentObj.selectedCity,
            selectedUnit: currentObj.selectedUnit,
            checkboxValues: ingredients,
        }
        const newDialogArr = [...dialogState, dialogObject];
        setDialogState(newDialogArr);
        hideDialog();
    };

    const resetAssignment = () => {
        setIngredients([]);
    };

    return (
        <div className="datatable">
            <DataTable value={names}  tableStyle={{ minWidth: '50rem' }}>
                <Column field="name" header="Name" />
                <Column
                    field="action"
                    header="Action"
                    body={(rowData) => (
                        <Button
                            icon="pi pi-pencil"
                            onClick={() => showDialog(rowData.name)}
                        />
                    )}
                />
            </DataTable>

            <Dialog header={`Assignment permission to ${userName}`} visible={visible} onHide={hideDialog}>
            
                <div class="grid p-4">
                    <div class="col-4">
                            <Dropdown
                                id="country"
                                optionLabel="name"
                                optionValue="id"
                                value={currentObj.selectedCountry}
                                options={countries}
                                onChange={(e) =>
                                    handleDropdownChange('selectedCountry', e.value)
                                }
                                placeholder='Choose country'
                                className="w-full"
                            />
                    </div>
                    <div class="col-4">
                            <Dropdown
                                id="city"
                                optionLabel="name"
                                optionValue="id"
                                value={currentObj.selectedCity}
                                options={cities}
                                onChange={(e) =>
                                    handleDropdownChange('selectedCity', e.value)
                                }
                                placeholder='Choose city'
                                className="w-full"
                            />
                    </div>
                    <div class="col-4">
                            <Dropdown
                                id="unit"
                                optionLabel="name"
                                optionValue="id"
                                value={currentObj.selectedUnit}
                                options={units}
                                onChange={(e) =>
                                    handleDropdownChange('selectedUnit', e.value)
                                }
                                placeholder='Choose business unit'
                                className="w-full"
                            />
                    </div>
                </div>

                
                <div className="grid p-4">
                    <div className="col-4">
                        <Checkbox inputId="ingredient1" name="Reporter" value="Reporter" onChange={onIngredientsChange} checked={ingredients.includes('Reporter')} />
                        <label htmlFor="ingredient1" className="ml-2">Reporter</label>
                    </div>
                    <div className="col-4">
                        <Checkbox inputId="ingredient2" name="Reviewer" value="Reviewer" onChange={onIngredientsChange} checked={ingredients.includes('Reviewer')} />
                        <label htmlFor="ingredient2" className="ml-2">Reviewer</label>
                    </div>
                    <div className="col-4">
                        <Checkbox inputId="ingredient3" name="Approver" value="Approver" onChange={onIngredientsChange} checked={ingredients.includes('Approver')} />
                        <label htmlFor="ingredient3" className="ml-2">Approver</label>
                    </div>
                    <div className="col-4">
                        <Checkbox inputId="ingredient4" name="Performance" value="Performance" onChange={onIngredientsChange} checked={ingredients.includes('Performance')} />
                        <label htmlFor="ingredient4" className="ml-2">Performance Viewer</label>
                    </div>
                    <div className="col-4">
                        <Checkbox inputId="ingredient5" name="Administrator" value="Administrator" onChange={onIngredientsChange} checked={ingredients.includes('Administrator')} />
                        <label htmlFor="ingredient5" className="ml-2">Administrator</label>
                    </div>
                    <div className="col-4">
                        <Checkbox inputId="ingredient6" name="Leadership" value="Leadership" onChange={onIngredientsChange} checked={ingredients.includes('Leadership')} />
                        <label htmlFor="ingredient6" className="ml-2">Leadership Viewer</label>
                    </div>
                </div>
                <div className="flex justify-content-evenly p-4">
                    <Button
                        label="Reset Assignment"
                        severity="danger"
                        onClick={resetAssignment}
                    />
                    <Button
                        label="Save Assignment"
                        severity="success"
                        onClick={saveAssignment}
                    />
                </div>
            </Dialog>
        </div>
    );
};

export default DataTableComponent;
