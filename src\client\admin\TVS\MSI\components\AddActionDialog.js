import React, { useState, useEffect } from 'react';
import { Dialog } from 'primereact/dialog';
import { But<PERSON> } from 'primereact/button';
import { InputText } from 'primereact/inputtext';
import { InputTextarea } from 'primereact/inputtextarea';
import { Dropdown } from 'primereact/dropdown';
import { Calendar } from 'primereact/calendar';
import { Card } from 'primereact/card';
import { DateTime } from 'luxon';
import moment from 'moment';
import APIServices from '../../../../../service/APIService';
import { API } from '../../../../../constants/api_url';
import Swal from 'sweetalert2';

const AddActionDialog = ({ visible, onHide, supplier, onActionAdded }) => {
    const [actions, setActions] = useState([{
        finding: '',
        categoryOfFinding: null,
        description: '',
        status: 'Open',
        completionDate: null,
        dueDate: null,
        nonComplianceType: null
    }]);

    const categoryOptions = [
        { label: 'Good Practices', value: 1 },
        { label: 'Opportunity of Improvement', value: 2 },
        { label: 'Non-compliance', value: 3 }
    ];

    const nonComplianceOptions = [
        { label: 'Regulatory (Major)', value: 1 },
        { label: 'Regulatory (Minor)', value: 2 },
        { label: 'Minor', value: 3 }
    ];



    const statusOptions = [
        { label: 'Open', value: 'Open' },
        { label: 'In Progress', value: 'In Progress' },
        { label: 'Closed', value: 'Closed' }
    ];

    const addNewAction = () => {
        setActions([...actions, {
            finding: '',
            categoryOfFinding: null,
            description: '',
            status: 'Open',
            completionDate: null,
            dueDate: null,
            nonComplianceType: null
        }]);
    };

    const removeAction = (index) => {
        const updatedActions = [...actions];
        updatedActions.splice(index, 1);
        setActions(updatedActions);
    };

    const updateAction = (index, field, value) => {
        const updatedActions = [...actions];
        updatedActions[index][field] = value;
        setActions(updatedActions);
    };

    const handleSubmit = async () => {
        // Validate actions
        const isValid = actions.every(action => {
            // Basic validation for all actions
            if (!action.finding || !action.description || action.categoryOfFinding === null || !action.status) {
                return false;
            }

            // Status-specific validation
            if (action.status === 'Closed' && !action.completionDate) {
                return false;
            }

            if ((action.status === 'Open' || action.status === 'In Progress') && !action.dueDate) {
                return false;
            }

            return true;
        });

        if (!isValid) {
            Swal.fire({
                icon: 'error',
                title: 'Validation Error',
                text: 'Please fill all required fields for all actions. For Closed status, completion date is required. For Open or In Progress status, due date is required.'
            });
            return;
        }

        try {
            // Process each action individually
            const successResponses = [];

            for (const action of actions) {
                // Map status string to numeric value
                let statusValue = 1; // Default: Open
                if (action.status === 'In Progress') statusValue = 2;
                if (action.status === 'Closed') statusValue = 3;

                // Map type according to status
                // Open=1 (after action plan approved), In progress/Submitted by supplier=2, Approved=3
                let typeValue = 1; // Default: Open (after action plan approved)
                if (action.status === 'In Progress') typeValue = 2; // Submitted by supplier
                if (action.status === 'Closed') typeValue = 3; // Approved

                // Prepare action with required fields from SupplierAction model
                const actionToSubmit = {
                    finding: action.finding,
                    description: action.description,
                    categoryOfFinding: action.categoryOfFinding,
                    nonComplianceType: action.nonComplianceType,
                    status: statusValue,
                    actionDueDate: action.dueDate ? new Date(action.dueDate).toISOString() : null,
                    actionTargetDate: action.dueDate ? new Date(action.dueDate).toISOString() : null,
                    approved_on: action.status === 'Closed' ? new Date(action.completionDate).toISOString() : null,
                    created_by: supplier.created_by || 1, // Default to 1 if not available
                    created_on: new Date().toISOString(),
                    supplierAssessmentAssignmentId: supplier.id,
                    vendorCodeId: parseInt(supplier.vendorCode),
                    type: typeValue, // Set type based on status
                    priority: 2 // Medium priority as default
                };

                // Add action using POST request
                const response = await APIServices.post(API.SupplierAction_SupplierAssignment(supplier.id), actionToSubmit);

                if (response.status === 200) {
                    successResponses.push(response.data);
                }
            }

            if (successResponses.length > 0) {
                Swal.fire({
                    icon: 'success',
                    title: 'Success',
                    text: 'Actions added successfully'
                });
                onActionAdded(successResponses);
                onHide();
            }
        } catch (error) {
            console.error('Error adding actions:', error);
            Swal.fire({
                icon: 'error',
                title: 'Error',
                text: 'Failed to add actions. Please try again.'
            });
        }
    };

    const renderActionCard = (action, index) => {
        return (
            <Card
                key={index}
                className="mb-3 shadow-1"
                title={
                    <div className="flex justify-content-between align-items-center">
                        <span>Action {index + 1}</span>
                        {actions.length > 1 && (
                            <Button
                                icon="pi pi-trash"
                                className="p-button-rounded p-button-danger p-button-text"
                                onClick={() => removeAction(index)}
                            />
                        )}
                    </div>
                }
            >
                <div className="grid">
                    {/* 1. Finding */}
                    <div className="col-12">
                        <div className="field">
                            <label htmlFor={`finding-${index}`} className="block font-medium mb-2">Finding*</label>
                            <InputText
                                id={`finding-${index}`}
                                value={action.finding}
                                onChange={(e) => updateAction(index, 'finding', e.target.value)}
                                className="w-full"
                            />
                        </div>
                    </div>

                    {/* 2. Category of Finding */}
                    <div className="col-12 md:col-6">
                        <div className="field">
                            <label htmlFor={`category-${index}`} className="block font-medium mb-2">Category of Finding*</label>
                            <Dropdown
                                id={`category-${index}`}
                                value={action.categoryOfFinding}
                                options={categoryOptions}
                                onChange={(e) => updateAction(index, 'categoryOfFinding', e.value)}
                                placeholder="Select Category"
                                className="w-full"
                            />
                        </div>
                    </div>

                    {/* Non-compliance Type (only shown when Category is Non-compliance) */}
                    {action.categoryOfFinding === 3 && (
                        <div className="col-12 md:col-6">
                            <div className="field">
                                <label htmlFor={`nonCompliance-${index}`} className="block font-medium mb-2">Non-compliance Type</label>
                                <Dropdown
                                    id={`nonCompliance-${index}`}
                                    value={action.nonComplianceType}
                                    options={nonComplianceOptions}
                                    onChange={(e) => updateAction(index, 'nonComplianceType', e.value)}
                                    placeholder="Select Type"
                                    className="w-full"
                                />
                            </div>
                        </div>
                    )}

                    {/* 3. Action Description */}
                    <div className="col-12">
                        <div className="field">
                            <label htmlFor={`description-${index}`} className="block font-medium mb-2">Action Description*</label>
                            <InputTextarea
                                id={`description-${index}`}
                                value={action.description}
                                onChange={(e) => updateAction(index, 'description', e.target.value)}
                                rows={3}
                                className="w-full"
                            />
                        </div>
                    </div>

                    {/* 4. Status of Action */}
                    <div className="col-12 md:col-6">
                        <div className="field">
                            <label htmlFor={`status-${index}`} className="block font-medium mb-2">Status of Action*</label>
                            <Dropdown
                                id={`status-${index}`}
                                value={action.status}
                                options={statusOptions}
                                onChange={(e) => updateAction(index, 'status', e.value)}
                                placeholder="Select Status"
                                className="w-full"
                            />
                        </div>
                    </div>

                    {/* 5. Completion Date (only shown when Status is Closed) */}
                    {action.status === 'Closed' && (
                        <div className="col-12 md:col-6">
                            <div className="field">
                                <label htmlFor={`completionDate-${index}`} className="block font-medium mb-2">Completion Date*</label>
                                <Calendar
                                    id={`completionDate-${index}`}
                                    value={action.completionDate}
                                    onChange={(e) => updateAction(index, 'completionDate', e.value)}
                                    showIcon
                                    dateFormat="dd-mm-yy"
                                    className="w-full"
                                />
                            </div>
                        </div>
                    )}

                    {/* 6. Due Date (only shown when Status is Open or In Progress) */}
                    {(action.status === 'Open' || action.status === 'In Progress') && (
                        <div className="col-12 md:col-6">
                            <div className="field">
                                <label htmlFor={`dueDate-${index}`} className="block font-medium mb-2">Due Date*</label>
                                <Calendar
                                    id={`dueDate-${index}`}
                                    value={action.dueDate}
                                    onChange={(e) => updateAction(index, 'dueDate', e.value)}
                                    showIcon
                                    dateFormat="dd-mm-yy"
                                    className="w-full"
                                />
                            </div>
                        </div>
                    )}
                </div>
            </Card>
        );
    };

    const renderSupplierInfo = () => {
        if (!supplier) return null;

        return (
            <Card className="mb-3 shadow-1">
                <div className="grid">
                    <div className="col-12 md:col-6">
                        <div className="field">
                            <label className="block text-sm font-medium text-gray-700">Supplier</label>
                            <div className="text-lg font-semibold">{supplier.vendor?.supplierName || 'NA'}</div>
                        </div>
                    </div>

                    <div className="col-12 md:col-6">
                        <div className="field">
                            <label className="block text-sm font-medium text-gray-700">Location</label>
                            <div className="text-lg font-semibold">{supplier.vendor?.supplierLocation || 'NA'}</div>
                        </div>
                    </div>

                    <div className="col-12 md:col-6">
                        <div className="field">
                            <label className="block text-sm font-medium text-gray-700">MSI ID</label>
                            <div>{`MSI-${supplier.vendorCode}-${DateTime.fromISO(supplier.created_on, { zone: 'utc' }).toLocal().toFormat('ddMMyyyy')}` || 'NA'}</div>
                        </div>
                    </div>

                    <div className="col-12 md:col-6">
                        <div className="field">
                            <label className="block text-sm font-medium text-gray-700">MSI Rating</label>
                            <div>{supplier.stat || 'NA'}</div>
                        </div>
                    </div>
                </div>
            </Card>
        );
    };

    const dialogFooter = (
        <div>
            <Button label="Add More Action" icon="pi pi-plus" className="p-button-secondary mr-2" onClick={addNewAction} />
            <Button label="Submit" icon="pi pi-check" className="p-button-success" onClick={handleSubmit} />
            <Button label="Cancel" icon="pi pi-times" className="p-button-danger" onClick={onHide} />
        </div>
    );

    return (
        <Dialog
            header="Add Actions"
            visible={visible}
            style={{ width: '80vw', maxWidth: '1200px' }}
            onHide={onHide}
            footer={dialogFooter}
            modal
            className="p-fluid"
        >
            {renderSupplierInfo()}

            <div className="action-cards">
                {actions.map((action, index) => renderActionCard(action, index))}
            </div>
        </Dialog>
    );
};

export default AddActionDialog;
