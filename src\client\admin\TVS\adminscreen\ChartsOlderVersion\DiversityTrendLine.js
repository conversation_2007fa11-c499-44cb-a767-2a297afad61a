import React, { useEffect, useRef } from "react";
import * as d3 from "d3";

const DiversityTrendLine = () => {
  const chartRef = useRef(null);

  const diversityTrendData = [
    { year: 2020, male: 40, female: 35, nonBinary: 5 },
    { year: 2021, male: 42, female: 36, nonBinary: 7 },
    { year: 2022, male: 44, female: 38, nonBinary: 8 },
    { year: 2023, male: 46, female: 39, nonBinary: 10 },
    // More years can be added here...
  ];

  useEffect(() => {
    renderTrendLine();
  }, []);

  const renderTrendLine = () => {
    const width = 800;
    const height = 400;
    const margin = { top: 50, right: 50, bottom: 50, left: 50 };

    // Clear any existing content in chartRef
    d3.select(chartRef.current).selectAll("*").remove();

    const svg = d3
      .select(chartRef.current)
      .append("svg")
      .attr("width", width)
      .attr("height", height);

    // Use scaleBand for the X-axis to treat years as categorical data
    const xScale = d3
      .scaleBand()
      .domain(diversityTrendData.map((d) => d.year)) // Set years as discrete categories
      .range([margin.left, width - margin.right])
      .padding(0.1);

    const yScale = d3
      .scaleLinear()
      .domain([
        0,
        d3.max(diversityTrendData, (d) =>
          Math.max(d.male, d.female, d.nonBinary)
        ),
      ])
      .nice()
      .range([height - margin.bottom, margin.top]);

    // Line generator function
    const lineGenerator = d3
      .line()
      .x((d) => xScale(d.year) + xScale.bandwidth() / 2) // Center line on the band
      .y((d) => yScale(d.value));

    // Create lines for each category (e.g., Male, Female, Non-Binary)
    const categories = ["male", "female", "nonBinary"];

    categories.forEach((category) => {
      const categoryData = diversityTrendData.map((d) => ({
        year: d.year,
        value: d[category],
      }));

      svg
        .append("path")
        .data([categoryData])
        .attr("d", lineGenerator)
        .attr("fill", "none")
        .attr("stroke", getLineColor(category)) // Color based on category
        .attr("stroke-width", 2);
    });

    // Add axes
    svg
      .append("g")
      .attr("transform", `translate(0,${height - margin.bottom})`)
      .call(d3.axisBottom(xScale)); // xScale is now categorical

    svg
      .append("g")
      .attr("transform", `translate(${margin.left}, 0)`)
      .call(d3.axisLeft(yScale));
  };

  // Function to get line color based on category
  const getLineColor = (category) => {
    switch (category) {
      case "male":
        return "blue";
      case "female":
        return "pink";
      case "nonBinary":
        return "purple";
      default:
        return "black";
    }
  };

  return (
    <>
      <div>
        <div
          style={{
            fontSize: "14px",
            fontWeight: 700,
            lineHeight: "19.2px",
            textAlign: "left",
            marginBottom: "5px",
          }}
        >
          Year-over-Year Diversity Trend
        </div>
        <div
          ref={chartRef}
          style={{ display: "flex", justifyContent: "center" }}
        ></div>
      </div>
    </>
  );
};

export default DiversityTrendLine;
