import React from "react";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  ResponsiveContainer,
  CartesianGrid,
} from "recharts";

const LegalComplianceChart = ({ data }) => {
  // Define max values for each category
  const maxValues = {
    sales: {
      legal_compliance: 10,
      water_management: 10,
      wastewater_management: 0, // Different for Sales
      waste_management: 10,
      energy_management: 10,
    },
    service: {
      legal_compliance: 10,
      water_management: 5,
      wastewater_management: 5, // Different for Service
      waste_management: 10,
      energy_management: 10,
    }
  };

  const extractScores = (criteriaData = {}, categoryType) => {
    const subCriteria = criteriaData?.environment?.subCriteria || [];
    const typeMaxValues = categoryType === "Service" ? maxValues.service : maxValues.sales;

    const findScore = (name) => {
      const item = subCriteria.find((c) => c.name === name);
      return item ? item.score : 0;
    };

    // Helper function to clamp values between 0 and maxValue for graph display
    const clampForGraph = (value) => Math.max(0, value);
    const clampRemaining = (value, maxValue) => Math.max(0, Math.min(maxValue, maxValue - value));

    return [
      {
        category: "legal_compliance",
        avgValue: findScore("legal_compliance"),
        avgValueForGraph: clampForGraph(findScore("legal_compliance")),
        maxValue: typeMaxValues.legal_compliance,
        remainingToMax: clampRemaining(
          findScore("legal_compliance"),
          typeMaxValues.legal_compliance
        ),
      },
      {
        category: "Water Management",
        avgValue: findScore("water_management"),
        avgValueForGraph: clampForGraph(findScore("water_management")),
        maxValue: typeMaxValues.water_management,
        remainingToMax: clampRemaining(
          findScore("water_management"),
          typeMaxValues.water_management
        ),
      },
      {
        category: "Wastewater Management",
        avgValue: findScore("wastewater_management"),
        avgValueForGraph: clampForGraph(findScore("wastewater_management")),
        maxValue: typeMaxValues.wastewater_management,
        remainingToMax: clampRemaining(
          findScore("wastewater_management"),
          typeMaxValues.wastewater_management
        ),
      },
      {
        category: "Waste Management",
        avgValue: findScore("waste_management"),
        avgValueForGraph: clampForGraph(findScore("waste_management")),
        maxValue: typeMaxValues.waste_management,
        remainingToMax: clampRemaining(
          findScore("waste_management"),
          typeMaxValues.waste_management
        ),
      },
      {
        category: "Energy Management",
        avgValue: findScore("energy_management"),
        avgValueForGraph: clampForGraph(findScore("energy_management")),
        maxValue: typeMaxValues.energy_management,
        remainingToMax: clampRemaining(
          findScore("energy_management"),
          typeMaxValues.energy_management
        ),
      },
    ];
  };

  const salesData = extractScores(data.sales_criteria, "Sales");
  const serviceData = extractScores(data.service_criteria, "Service");

  // Function to determine Y-Axis domain with a fixed max value (10)
  const getYAxisDomain = () => [0, 10];

  // Custom tick component for X-axis with two lines of text
  const CustomizedTick = ({ x, y, payload }) => {
    // Format special case for legal_compliance
    let displayText = payload.value;
    if (displayText === "legal_compliance") {
      displayText = "Legal Compliance";
    }

    // Split the text into two lines
    const words = displayText.split(' ');
    let firstLine, secondLine;

    if (words.length === 1) {
      // Single word - put on first line
      firstLine = words[0];
      secondLine = "";
    } else if (words.length === 2) {
      // Two words - one on each line
      firstLine = words[0];
      secondLine = words[1];
    } else {
      // More than two words - balance between lines
      const midpoint = Math.ceil(words.length / 2);
      firstLine = words.slice(0, midpoint).join(' ');
      secondLine = words.slice(midpoint).join(' ');
    }

    return (
      <g transform={`translate(${x},${y})`}>
        <text
          x={0}
          y={0}
          textAnchor="middle"
          fill="#666"
          fontSize={10}
        >
          <tspan x={0} dy="0.71em">{firstLine}</tspan>
          {secondLine && <tspan x={0} dy="1.2em">{secondLine}</tspan>}
        </text>
      </g>
    );
  };

  // Custom legend component
  const CustomLegend = (props) => {
    const { payload } = props;
    return (
      <ul
        style={{
          display: "flex",
          listStyleType: "none",
          justifyContent: "center",
          padding: 0,
        }}
      >
        {payload.map((entry, index) => (
          <li
            key={`item-${index}`}
            style={{
              color: entry.color,
              marginRight: "5px",
            }}
          >
            <span
              style={{
                color: entry.color,
                backgroundColor: entry.color,
                marginRight: 4,
                fontSize: "20px",
                width: "10px",
                height: "10px",
                borderRadius: "50%",
                display: "inline-block",
              }}
            ></span>
            <span style={{ color: "#555", fontSize: "14px" }}>
              {entry.value}
            </span>
          </li>
        ))}
      </ul>
    );
  };

  return (
    <div
      style={{
        display: "flex",
        justifyContent: "space-between",
        width: "100%",
        padding: "20px",
      }}
    >
      {/* Sales Chart */}
      <div style={{ width: "48%", height: 400 }}>
        <h3 style={{ textAlign: "center", marginBottom: 20, color: "#555" }}>
          Sales - Environment
        </h3>
        <ResponsiveContainer width="100%" height="100%">
          <BarChart
            data={salesData}
            barSize={60}
            margin={{ top: 20, right: 30, left: 20, bottom: 60 }}
          >
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis
              dataKey="category"
              tick={<CustomizedTick />}
              tickLine={true} // Ensures ticks are visible
              interval={0} // Forces all labels to be shown
              height={70} // Increase height to accommodate two lines of text
            />

            <YAxis domain={getYAxisDomain()} />
            <Tooltip
              formatter={(_, name, props) => {
                const { payload } = props;
                if (name === "Achieved") {
                  return [
                    `${payload.avgValue.toFixed(2)} (Max: ${payload.maxValue})`,
                    name,
                  ];
                }
                return [null, null];
              }}
            />
            <Legend content={CustomLegend} />
            <Bar
              dataKey="avgValueForGraph"
              stackId="progress"
              fill="#2C7C69"
              name="Achieved"
              label={{
                position: "insideTop",
                fill: "#fff",
                formatter: (value) => (value ? value.toFixed(1) : ""),
              }}
            />
            <Bar
              dataKey="remainingToMax"
              stackId="progress"
              fill="#7FC8A9"
              name="Maximum"
            />
          </BarChart>
        </ResponsiveContainer>
      </div>

      {/* Service Chart */}
      <div style={{ width: "48%", height: 400 }}>
        <h3 style={{ textAlign: "center", marginBottom: 20, color: "#555" }}>
          Service - Environment
        </h3>
        <ResponsiveContainer width="100%" height="100%">
          <BarChart
            data={serviceData}
            barSize={60}
            margin={{ top: 20, right: 30, left: 20, bottom: 60 }}
          >
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis
              dataKey="category"
              tick={<CustomizedTick />}
              tickLine={true} // Ensures ticks are visible
              interval={0} // Forces all labels to be shown
              height={70} // Increase height to accommodate two lines of text
            />

            <YAxis domain={getYAxisDomain()} />
            <Tooltip
              formatter={(_, name, props) => {
                const { payload } = props;
                if (name === "Achieved") {
                  return [
                    `${payload.avgValue.toFixed(2)} (Maximum: ${payload.maxValue})`,
                    name,
                  ];
                }
                return [null, null];
              }}
            />
            <Legend content={CustomLegend} />
            <Bar
              dataKey="avgValueForGraph"
              stackId="progress"
              fill="#2C7C69"
              name="Achieved"
              label={{
                position: "insideTop",
                fill: "#fff",
                formatter: (value) => (value !== 0 ? value.toFixed(1) : ""),
              }}
            />
            <Bar
              dataKey="remainingToMax"
              stackId="progress"
              fill="#7FC8A9"
              name="Maximum"
            />
          </BarChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
};

export default LegalComplianceChart;
