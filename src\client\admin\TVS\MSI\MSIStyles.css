
/* Add styles for the cursor pointer on clickable elements */
.cursor-pointer {
    cursor: pointer;
}

/* Add text-primary class if not already defined */
.text-primary {
    color: #007bff;
}

/* Status tag colors */
.status-tag-green {
    background-color: #28a745 !important;
    color: white !important;
}

.status-tag-blue {
    background-color: #007bff !important;
    color: white !important;
}

.status-tag-orange {
    background-color: #fd7e14 !important;
    color: white !important;
}

.status-tag-red {
    background-color: #dc3545 !important;
    color: white !important;
}

/* Box colors for action counts */
.redBox {
    background-color: #dc3545;
    color: white;
    padding: 5px 10px;
    border-radius: 4px;
    display: inline-block;
    min-width: 30px;
    text-align: center;
}

.orangeBox {
    background-color: #fd7e14;
    color: white;
    padding: 5px 10px;
    border-radius: 4px;
    display: inline-block;
    min-width: 30px;
    text-align: center;
}

.greenBox {
    background-color: #28a745;
    color: white;
    padding: 5px 10px;
    border-radius: 4px;
    display: inline-block;
    min-width: 30px;
    text-align: center;
}
