import React, { useEffect, useRef } from "react";
import * as d3 from "d3";
import { sankey, sankeyLinkHorizontal } from "d3-sankey";

const data = {
  nodes: [
    { id: "Complaints Filed" },
    { id: "Under Investigation" },
    { id: "Resolved" },
    { id: "Escalated" },
    { id: "Closed" },
  ],
  links: [
    { source: "Complaints Filed", target: "Under Investigation", value: 100 },
    { source: "Under Investigation", target: "Resolved", value: 60 },
    { source: "Under Investigation", target: "Escalated", value: 30 },
    { source: "Escalated", target: "Closed", value: 20 },
    { source: "Resolved", target: "Closed", value: 50 },
  ],
};

const ComplaintResolutionFlow = () => {
  const chartRef = useRef(null);

  useEffect(() => {
    if (!chartRef.current) return;
    renderSankeyChart();
  }, []);

  const renderSankeyChart = () => {
    const width = 700;
    const height = 400;
    const margin = { top: 20, right: 20, bottom: 20, left: 20 };

    const svg = d3
      .select(chartRef.current)
      .append("svg")
      .attr("width", width)
      .attr("height", height);

    const sankeyGenerator = sankey()
      .nodeWidth(20)
      .nodePadding(15)
      .extent([
        [margin.left, margin.top],
        [width - margin.right, height - margin.bottom],
      ]);

    const { nodes, links } = sankeyGenerator({
      nodes: data.nodes.map((d) => ({ ...d })),
      links: data.links.map((d) => ({ ...d })),
    });

    // Define a color scale
    const color = d3.scaleOrdinal(d3.schemeCategory10);

    // Draw links
    svg
      .append("g")
      .selectAll("path")
      .data(links)
      .join("path")
      .attr("d", sankeyLinkHorizontal())
      .attr("fill", "none")
      .attr("stroke", (d) => color(d.source.id))
      .attr("stroke-width", (d) => Math.max(1, d.width))
      .attr("opacity", 0.8);

    // Add link labels
    svg
      .append("g")
      .selectAll("text")
      .data(links)
      .join("text")
      .attr("x", (d) => (d.source.x1 + d.target.x0) / 2)
      .attr("y", (d) => (d.source.y0 + d.target.y0) / 2)
      .attr("text-anchor", "middle")
      .style("font-size", "12px")
      .style("fill", "#333")
      .text((d) => d.value);

    // Draw nodes
    svg
      .append("g")
      .selectAll("rect")
      .data(nodes)
      .join("rect")
      .attr("x", (d) => d.x0)
      .attr("y", (d) => d.y0)
      .attr("width", (d) => d.x1 - d.x0)
      .attr("height", (d) => d.y1 - d.y0)
      .attr("fill", (d) => color(d.id))
      .attr("stroke", "#000");

    // Add node labels
    svg
      .append("g")
      .selectAll("text")
      .data(nodes)
      .join("text")
      .attr("x", (d) => (d.x0 < width / 2 ? d.x0 - 10 : d.x1 + 10))
      .attr("y", (d) => (d.y1 + d.y0) / 2)
      .attr("text-anchor", (d) => (d.x0 < width / 2 ? "end" : "start"))
      .style("font-size", "14px")
      .style("fill", "#000")
      .text((d) => d.id);
  };

  return (
    <div>
      <h3 style={{ fontSize: "18px" }}>Complaint Resolution Flow</h3>
      <div>
        Visual representation of complaint stages, showcasing flows between
        categories and their respective values.
      </div>
      <div
        ref={chartRef}
        style={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          marginTop: "20px",
        }}
      />
    </div>
  );
};

export default ComplaintResolutionFlow;
