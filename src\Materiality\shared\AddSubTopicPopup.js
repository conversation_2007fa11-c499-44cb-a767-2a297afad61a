import React, { useEffect, useState } from 'react';
import ClientSelect from './ClientSelect';
import { API } from '../../constants/api_url';

const AddSubTopicPopup = ({ onSubmit, companies }) => {
  const [subTopicTitle, setSubTopicTitle] = useState('');
  const [selectedCompany, setSelectedCompany] = useState([]);
  const [client, setClients] = useState([])
  
  useEffect(() => {
    getOptions()
})

const  getOptions = async () => {
    const response = await fetch(API.UserProfile);
    if(response.ok) {
      const data = await response.json();
      setClients(data.filter(i=> i.role === "clientadmin").map((i) => {
            // return {label: i.information.companyname, value: i.id}
            return {label: i.information.enterpriseid, value: i.id}

        }))
      //   setTopics(data)
    }
  }


  const handleSubTopicSubmit = () => {
    onSubmit(subTopicTitle, selectedCompany);
    // Close the popup or reset form fields if needed
    setSubTopicTitle('');
    setSelectedCompany('');
  };
  
  const handleOnChangeClientValues = (val) => {
    setSelectedCompany (val)
  };


  return (
    <div className="popup">
      <h2>Add Subtopic</h2>
      <input
        type="text"
        placeholder="Subtopic Title"
        value={subTopicTitle}
        onChange={(e) => setSubTopicTitle(e.target.value)}
      />
      {/* <h5 className="mt-5">Select Clients <span>({getCompanyShortform()})</span></h5> */}
      <ClientSelect options={client} value={selectedCompany} onChangeValues={handleOnChangeClientValues} />
      <button onClick={handleSubTopicSubmit}>Add Subtopic</button>
    </div>
  );
};

export default AddSubTopicPopup;