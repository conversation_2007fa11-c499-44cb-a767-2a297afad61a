import Axios from "axios";
import React, { useEffect, useState, useMemo } from "react";
import { useSelector } from "react-redux";
import useForceUpdate from "use-force-update";
import { Dropdown } from "primereact/dropdown";
import { EditText } from "react-edit-text";
import { Button } from "primereact/button";
import { Calendar } from "primereact/calendar";
import { InputTextarea } from "primereact/inputtextarea";
import { Dialog } from "primereact/dialog";
import { InputText } from "primereact/inputtext";
import { CascadeSelect } from "primereact/cascadeselect";
import Swal from "sweetalert2";
import { Checkbox } from 'primereact/checkbox';
import $, { data } from "jquery";
import { API } from "../../constants/api_url";
import { RadioButton } from "primereact/radiobutton";
import LazyView from "../../components/LazyView";
import { MultiSelect } from 'primereact/multiselect';
import moment from "moment";
import { TabMenu } from "primereact/tabmenu";
import APIServices from "../../service/APIService";
import { Tag } from "primereact/tag";
import FileSaver from "file-saver";
import * as XLSX from "xlsx";
import { DateTime } from "luxon";
import { X } from "@mui/icons-material";
import { CustomDialog } from "../../components/CustomDialog/CustomDialog";
import { getRandomId } from "../../components/BGHF/helper";
import { DataTable } from "primereact/datatable";
import { Column } from "primereact/column";

window.jQuery = $;
window.$ = $;
let standAlone = [], levelCount = 0
const ClientIndicatorAssignment = () => {
    const selector = useSelector((state) => state.user.admindetail);
    const [key, setKey] = useState(null)
    const [clienttag, setClientTag] = useState([])
    const [user, setUser] = useState([]);
    const [list, setList] = useState({ category: null, topic: null, metric: null })
    const [selected, setSelected] = useState({ user: null, category: [], topic: [], metric: [] })
    const [activeItem, setActiveItem] = useState(1);
    const [selDataPoint, setSelDataPoint] = useState([])
    const configtype = [{ name: 'Location' }, { name: 'Data Point' }]
    const forceUpdate = useForceUpdate();
    const [editmode, setEditMode] = useState(false)
    const [editmodeid, setEditModeID] = useState(null)
    const [enable, setEnable] = useState(true)
    const [old, setOld] = useState([]);
    const [selectedlist, setSelectedList] = useState({ title: '', data: [] })
    const [prevSListdialog, setPrevSListDialog] = useState(false);
    const [metriclist, setMetricList] = useState([]);
    const [metricbk, setMetricBk] = useState([])
    const [raw, setRaw] = useState([])
    const [response, setResponse] = useState([])
    const [usermetric, setUserMetric] = useState([])


    const [rawdcf, setRawDCF] = useState([])
    const [dupdpiddialog, setDupDPIDDialog] = useState(false)
    const [dupdpid, setDupId] = useState({ data: [], msg: '' })
    const [selecteddcf, setSelectedDCF] = useState([]);
    const [selecteddcfbk, setSelectedDCFBK] = useState([]);
    const [dcfSelections, setDcfSelections] = useState({});
    // Track which datapoints have selected DCFs
    const [datapointDcfSelections, setDatapointDcfSelections] = useState({});

    // SAP Collections state management (similar to DCF)
    const [rawsap, setRawSAP] = useState([])
    const [selectedsap, setSelectedSAP] = useState([]);
    const [selectedsapbk, setSelectedSAPBK] = useState([]);
    const [sapSelections, setSapSelections] = useState({});
    // Track which datapoints have selected SAPs
    const [datapointSapSelections, setDatapointSapSelections] = useState({});
    const [cflist, setCFList] = useState([]);
    const [cfbklist, setCFBKList] = useState([]);
    const [dflist, setDFList] = useState([]);
    const [dfbklist, setDFBKList] = useState([]);
    const [selectedcf, setSelectedCF] = useState([]);
    const [selectedcfbk, setSelectedCFBK] = useState([]);
    const [selectedform, setSelectedForm] = useState([])
    const [prevdialog, setPrevDialog] = useState(false);
    const [dfreqlist, setDFReqList] = useState([]);
    const [dfreqlistbk, setDFReqListBK] = useState([]);
    const [dfass, setDFAss] = useState([]);
    const [dfassbk, setDFAssBK] = useState([]);
    const [prevSList2dialog, setPrevSList2Dialog] = useState(false);
    const [dfconfigdialog, setDfConfigDialog] = useState(false);
    const [dfconfig, setDfConfig] = useState({ apex: false, country: false, region: false, site: false });
    const [confirmSaveDialog, setConfirmSaveDialog] = useState(false);
    const [assignmentSummary, setAssignmentSummary] = useState({
        categories: [],
        topics: [],
        indicators: [],
        dcfs: [],
        saps: [],
        srfs: []
    });

    // State for indicator filter in DataTable
    const [selectedIndicatorFilter, setSelectedIndicatorFilter] = useState(null);

    // Track which indicator each DCF/SAP is mapped to
    const [dcfIndicatorMapping, setDcfIndicatorMapping] = useState({}); // { dcfId: indicatorId }
    const [sapIndicatorMapping, setSapIndicatorMapping] = useState({}); // { sapId: indicatorId }

    const [search, setSearch] = useState({ metric: '', dcf: '', sap: '', cf: '', df: '' })
    const [location, setLocation] = useState([]);
    const [overallmetric, setOverallMetric] = useState([]);
    const [userConfig, setUserConfig] = useState({
        name: "", type: "",
        location: ''
    });
    const [module, setModule] = useState({
        tier1: "",
        tier2: "",
        tier3: "",
    });
    const [cascade, setCascade] = useState("");
    const [showSave, setShowSave] = useState(0);

    const [tier2, setTier2] = useState([]);
    const [tier3, setTier3] = useState([]);
    const [moduleList, setModuleList] = useState({
        mod: [],
        title: [],
        topic: [],
    });

    useEffect(async () => {
        setOld(selector.information);

        APIServices.get(API.UserProfile).then((res) => {
            let locuser = [], tagList = []

            res.data.forEach((item) => {
                if (item.role === "clientadmin") {
                    tagList.push({
                        name: item.information.enterpriseid,
                        id: item.id,
                    });
                    locuser.push({
                        name: item.information.companyname,
                        id: item.id,
                    });
                }
            });
            setClientTag(tagList);
            setUser(locuser);
        });
        let uriString = {
            "include": [{ "relation": "newDataPoints" }]

        }

        APIServices.get(API.DCF).then((res) => {

            setRawDCF(res.data.filter((i) => { return (i.type === null || i.type === 1) }))

        })

        // Load SAP Collections similar to DCF
        APIServices.get(API.SapCollection).then((res) => {
            setRawSAP(res.data.map(x => ({
                name: x.sapId + ' : ' + x.title,
                id: x.id,
                sapId: x.sapId,
                title: x.title
            })))
        })
        APIServices.get(API.SRF).then((res) => {

            setCFBKList(res.data.map(i => ({ ...i, data1: JSON.parse(i.data1) })))
            setCFList(res.data.map(i => ({ ...i, data1: JSON.parse(i.data1) })))

        })
        APIServices.get(API.RF).then((res) => {

            setDFBKList(res.data)
            setDFList(res.data)

        })
        let uriString2 = {
            "include": [{ "relation": "newTopics", "scope": { "include": [{ "relation": "newMetrics", "scope": { "include": [{ "relation": "newDataPoints" }] } }] } }]


        }
        let Overall = API.Categories + `?filter=${encodeURIComponent(JSON.stringify(uriString2))}`;

        let url = API.Metric + `?filter=${encodeURIComponent(JSON.stringify(uriString))}`;
        APIServices.get(Overall).then(res => {
            res.data.forEach((item) => {
                item.newTopics && item.newTopics.forEach((topic) => {
                    topic.newMetrics && topic.newMetrics.forEach((metric) => {
                        metric.title = (Array.isArray(metric.data1) && metric.data1.length) ? metric.data1[0].title : metric.title
                    });
                });
            });
            setResponse(res.data)
            setEnable(false)
            let categoryList = [], metricList = [], topicList = []
            let loc = list
            res.data.forEach((cat) => {
                if (cat.newTopics !== undefined) {
                    categoryList.push({ id: cat.id, title: cat.title })
                    cat.newTopics.forEach((topic) => {

                        if (topic.newMetrics !== undefined) {
                            topicList.push({ name: topic.title, title: topic.title + "(" + cat.title + ")", id: topic.id })
                            topic.newMetrics.forEach((metric) => {
                                // if(Array.isArray(metric.data1) && metric.data1[0].title &&  metric.data1[0].title.trim().length !== 0 ){
                                metricList.push({ ...metric, cat_title: cat.title })
                                // }

                            })
                        }
                    })
                }


            })
            console.log(categoryList, topicList)
            loc.category = categoryList
            loc.metric = metricList
            loc.topic = topicList

            setList(loc)
            setOverallMetric(JSON.parse(JSON.stringify(metricList)))
            setRaw(metricList)
            setMetricBk(metricList.map(k => { return { title: k.title, id: k.id, selected: false } }))
            setMetricList(metricList.map(k => { return { title: k.title, id: k.id, cat_title: k.cat_title, selected: false } }))

        })




    }, [selector]);

    const renderIndicators = (item) => {
        let filteredData = item.filter((i) => { return Array.isArray(i.data1) && i.data1[0].type === 0 })



        filteredData.forEach(obj => {
            replaceIndicatorRecursive(obj, item)

        });

        function replaceIndicatorRecursive(obj) {
            if (obj.data1 && obj.data1[0] && obj.data1[0].source === 0) {
                obj.data1[0].indicator = obj.data1[0].indicator.map(index => {
                    const matchedObject = item.find(item => item.id === index);
                    if (matchedObject && matchedObject.data1 && matchedObject.data1[0]) {
                        replaceIndicatorRecursive(matchedObject);
                        return { ...matchedObject };
                    }
                    return index;
                });
            }
        }


        return filteredData
    }

    const updateSelectedIndicators = (sMetric, oMetric, preselectedDcfIds = [], preselectedSapIds = []) => {
        console.log("Starting updateSelectedIndicators with metrics:", sMetric);
        console.log("Preselected DCF IDs:", preselectedDcfIds);
        console.log("Preselected SAP IDs:", preselectedSapIds);

        let data = []
        let rejected = []

        // IMPORTANT: Start with the existing selected DCFs
        let selected_ = JSON.parse(JSON.stringify(selecteddcfbk))
        let dcfId = selected_.map(d => d.id)
        // Get the saved dcf_ids from the selectedDCFBK state
        const savedDcfIds = selecteddcfbk.map(dcf => dcf.id);
        console.log("Saved DCF IDs:", savedDcfIds);

        // IMPORTANT: Start with the existing selected SAPs
        let selectedSap_ = JSON.parse(JSON.stringify(selectedsapbk))
        let sapId = selectedSap_.map(s => s.id)
        // Get the saved sap_ids from the selectedSAPBK state
        const savedSapIds = selectedsapbk.map(sap => sap.id);
        console.log("Saved SAP IDs:", savedSapIds);

        // Log the current state for debugging
        console.log("Current selectedDCFBK:", selecteddcfbk.map(d => ({ id: d.id, title: d.title })));
        console.log("Current selectedDCF:", selecteddcf.map(d => ({ id: d.id, title: d.title })));
        console.log("Current selectedSAPBK:", selectedsapbk.map(s => ({ id: s.id, title: s.title })));
        console.log("Current selectedSAP:", selectedsap.map(s => ({ id: s.id, title: s.title })));

        if (oMetric.length !== 0) {
            // console.log(sMetric,sMetric.map( i => getStandAlone(oMetric.find(j => { return i === j.id}) ) ))
        }

        // Initialize DCF selections
        const newDcfSelections = {};

        // Initialize SAP selections
        const newSapSelections = {};

        // Initialize datapoint-DCF selections
        const newDatapointDcfSelections = { ...datapointDcfSelections };

        // Initialize datapoint-SAP selections
        const newDatapointSapSelections = { ...datapointSapSelections };

        oMetric.forEach((items) => {
            if (sMetric.includes(items.id)) {
                items.selected = true

                // Initialize DCF selections for this indicator
                newDcfSelections[items.id] = {
                    singleDcf: null,
                    multipleDcfs: []
                };

                // Check for dcfId in type 2 metrics
                if (Array.isArray(items.data1) && items.data1.length === 1 && items.data1[0].type === 2) {
                    // Handle single dcfId
                    if (items.data1[0].dcfId) {
                        let dcf_index = rawdcf.findIndex(i => i.id === items.data1[0].dcfId)

                        // If the DCF is not already in our list
                        if (!dcfId.includes(items.data1[0].dcfId)) {
                            // Check if this DCF is in the preselected list
                            const isPreselected = preselectedDcfIds.includes(items.data1[0].dcfId);

                            // Case 1: DCF found in rawdcf array
                            if (dcf_index !== -1 &&
                                (isPreselected || rawdcf[dcf_index].tags === null || rawdcf[dcf_index].tags.length === 0 || rawdcf[dcf_index].tags.includes(selected.user))) {

                                console.log(`Auto-selecting DCF ID ${items.data1[0].dcfId} from indicator ${items.id}`);
                                dcfId.push(items.data1[0].dcfId);
                                // Only add if not already in selected_ array (ensure uniqueness)
                                if (!selected_.some(dcf => dcf.id === items.data1[0].dcfId)) {
                                    selected_.push(rawdcf[dcf_index]);
                                }
                            }
                            // Case 2: DCF not found in rawdcf array but is in the indicator or preselected
                            else if (dcf_index === -1 && (isPreselected || items.data1[0].dcfId)) {
                                console.log(`DCF ID ${items.data1[0].dcfId} not found in rawdcf, creating placeholder`);
                                dcfId.push(items.data1[0].dcfId);
                                // Only add if not already in selected_ array (ensure uniqueness)
                                if (!selected_.some(dcf => dcf.id === items.data1[0].dcfId)) {
                                    selected_.push({
                                        id: items.data1[0].dcfId,
                                        title: `DCF ID: ${items.data1[0].dcfId}`,
                                        tags: null
                                    });
                                }
                            }

                            // Add to DCF selections
                            newDcfSelections[items.id].singleDcf = items.data1[0].dcfId;

                            // Add to datapoint-DCF selections for standalone indicators
                            const datapointKey = `standalone-${items.id}`;
                            newDatapointDcfSelections[datapointKey] = items.data1[0].dcfId;
                        }
                    }

                    // Handle dcf_ids array (multiple DCFs)
                    if (items.data1[0].dcf_ids && Array.isArray(items.data1[0].dcf_ids)) {
                        items.data1[0].dcf_ids.forEach(id => {
                            let dcf_index = rawdcf.findIndex(i => i.id === id)

                            // If the DCF is not already in our list
                            if (!dcfId.includes(id)) {
                                // Check if this DCF is in the preselected list
                                const isPreselected = preselectedDcfIds.includes(id);

                                // Case 1: DCF found in rawdcf array
                                if (dcf_index !== -1 &&
                                    (isPreselected || (savedDcfIds.includes(id) && (rawdcf[dcf_index].tags === null || rawdcf[dcf_index].tags.length === 0 || rawdcf[dcf_index].tags.includes(selected.user))))) {

                                    console.log(`Auto-selecting DCF ID ${id} from indicator ${items.id} (multiple DCFs)`);
                                    dcfId.push(id);
                                    // Only add if not already in selected_ array (ensure uniqueness)
                                    if (!selected_.some(dcf => dcf.id === id)) {
                                        selected_.push(rawdcf[dcf_index]);
                                    }
                                }
                                // Case 2: DCF not found in rawdcf array but is preselected
                                else if (dcf_index === -1 && isPreselected) {
                                    console.log(`DCF ID ${id} not found in rawdcf, creating placeholder (multiple DCFs)`);
                                    dcfId.push(id);
                                    // Only add if not already in selected_ array (ensure uniqueness)
                                    if (!selected_.some(dcf => dcf.id === id)) {
                                        selected_.push({
                                            id: id,
                                            title: `DCF ID: ${id}`,
                                            tags: null
                                        });
                                    }
                                }

                                // Add to DCF selections if DCF was included
                                if (dcfId.includes(id)) {
                                    newDcfSelections[items.id].multipleDcfs.push({
                                        id: id,
                                        selected: true
                                    });

                                    // Add to datapoint-DCF selections for standalone indicators
                                    // Only if no DCF is already selected for this datapoint
                                    const datapointKey = `standalone-${items.id}`;
                                    if (!newDatapointDcfSelections[datapointKey]) {
                                        newDatapointDcfSelections[datapointKey] = id;
                                    }
                                }
                            }
                        });
                    }
                } else if (items.newDataPoints !== undefined) {
                    items.newDataPoints.forEach((dp) => {
                        // Create a unique key for this datapoint
                        const datapointKey = `${items.id}-${dp.suffix || dp.title || dp.id}`;

                        // Handle single datasource
                        if (Array.isArray(dp.data1) && dp.data1.length !== 0 && dp.data1[0].datasource !== null && typeof dp.data1[0].datasource === 'number') {
                            let dcf_index = rawdcf.findIndex(i => i.id === dp.data1[0].datasource)

                            // If the DCF is not already in our list
                            if (!dcfId.includes(dp.data1[0].datasource)) {
                                // Check if this DCF is in the preselected list
                                const isPreselected = preselectedDcfIds.includes(dp.data1[0].datasource);

                                // Case 1: DCF found in rawdcf array
                                if (dcf_index !== -1 &&
                                    (isPreselected || (savedDcfIds.includes(dp.data1[0].datasource) && (rawdcf[dcf_index].tags === null || rawdcf[dcf_index].tags.length === 0 || rawdcf[dcf_index].tags.includes(selected.user))))) {

                                    console.log(`Auto-selecting DCF ID ${dp.data1[0].datasource} from datapoint ${datapointKey} (single datasource)`);
                                    dcfId.push(dp.data1[0].datasource);
                                    // Only add if not already in selected_ array (ensure uniqueness)
                                    if (!selected_.some(dcf => dcf.id === dp.data1[0].datasource)) {
                                        selected_.push(rawdcf[dcf_index]);
                                    }
                                }
                                // Case 2: DCF not found in rawdcf array but is preselected
                                else if (dcf_index === -1 && isPreselected) {
                                    console.log(`DCF ID ${dp.data1[0].datasource} not found in rawdcf, creating placeholder for datapoint ${datapointKey} (single datasource)`);
                                    dcfId.push(dp.data1[0].datasource);
                                    // Only add if not already in selected_ array (ensure uniqueness)
                                    if (!selected_.some(dcf => dcf.id === dp.data1[0].datasource)) {
                                        selected_.push({
                                            id: dp.data1[0].datasource,
                                            title: `DCF ID: ${dp.data1[0].datasource}`,
                                            tags: null
                                        });
                                    }
                                }

                                // Add to DCF selections if DCF was included
                                if (dcfId.includes(dp.data1[0].datasource)) {
                                    const existingDcfIndex = newDcfSelections[items.id].multipleDcfs.findIndex(d => d.id === dp.data1[0].datasource);
                                    if (existingDcfIndex === -1) {
                                        newDcfSelections[items.id].multipleDcfs.push({
                                            id: dp.data1[0].datasource,
                                            selected: true
                                        });
                                    }

                                    // Add to datapoint-DCF selections
                                    // Only if no DCF is already selected for this datapoint
                                    if (!newDatapointDcfSelections[datapointKey]) {
                                        newDatapointDcfSelections[datapointKey] = dp.data1[0].datasource;
                                    }
                                }
                            }
                        }

                        // Handle dcf_ids array (multiple DCFs)
                        if (Array.isArray(dp.data1) && dp.data1.length !== 0 && dp.data1[0].dcf_ids && Array.isArray(dp.data1[0].dcf_ids)) {
                            dp.data1[0].dcf_ids.forEach(id => {
                                let dcf_index = rawdcf.findIndex(i => i.id === id)

                                // If the DCF is not already in our list
                                if (!dcfId.includes(id)) {
                                    // Check if this DCF is in the preselected list
                                    const isPreselected = preselectedDcfIds.includes(id);

                                    // Case 1: DCF found in rawdcf array
                                    if (dcf_index !== -1 &&
                                        (isPreselected || (savedDcfIds.includes(id) && (rawdcf[dcf_index].tags === null || rawdcf[dcf_index].tags.length === 0 || rawdcf[dcf_index].tags.includes(selected.user))))) {

                                        console.log(`Auto-selecting DCF ID ${id} from datapoint ${datapointKey} (multiple DCFs)`);
                                        dcfId.push(id);
                                        // Only add if not already in selected_ array (ensure uniqueness)
                                        if (!selected_.some(dcf => dcf.id === id)) {
                                            selected_.push(rawdcf[dcf_index]);
                                        }
                                    }
                                    // Case 2: DCF not found in rawdcf array but is preselected
                                    else if (dcf_index === -1 && isPreselected) {
                                        console.log(`DCF ID ${id} not found in rawdcf, creating placeholder for datapoint ${datapointKey} (multiple DCFs)`);
                                        dcfId.push(id);
                                        // Only add if not already in selected_ array (ensure uniqueness)
                                        if (!selected_.some(dcf => dcf.id === id)) {
                                            selected_.push({
                                                id: id,
                                                title: `DCF ID: ${id}`,
                                                tags: null
                                            });
                                        }
                                    }

                                    // Add to DCF selections if DCF was included
                                    if (dcfId.includes(id)) {
                                        const existingDcfIndex = newDcfSelections[items.id].multipleDcfs.findIndex(d => d.id === id);
                                        if (existingDcfIndex === -1) {
                                            newDcfSelections[items.id].multipleDcfs.push({
                                                id: id,
                                                selected: true
                                            });
                                        }

                                        // Add to datapoint-DCF selections
                                        // Only if no DCF is already selected for this datapoint
                                        if (!newDatapointDcfSelections[datapointKey]) {
                                            newDatapointDcfSelections[datapointKey] = id;
                                        }
                                    }
                                }
                            });
                        }

                        // Handle SAP Collections (datasource2) - similar to dcf_ids handling
                        if (Array.isArray(dp.data1) && dp.data1.length !== 0 && dp.data1[0].datasource2 !== null) {
                            // Handle both single SAP (number) and multiple SAPs (array)
                            let sapIds = [];

                            if (typeof dp.data1[0].datasource2 === 'number') {
                                // Single SAP (backward compatibility)
                                sapIds = [dp.data1[0].datasource2];
                            } else if (Array.isArray(dp.data1[0].datasource2)) {
                                // Multiple SAPs (new format)
                                sapIds = dp.data1[0].datasource2;
                            }

                            sapIds.forEach(id => {
                                let sap_index = rawsap.findIndex(i => i.id === id)

                                // If the SAP is not already in our list
                                if (!sapId.includes(id)) {
                                    // Check if this SAP is in the preselected list
                                    const isPreselected = preselectedSapIds.includes(id);

                                    // Case 1: SAP found in rawsap array
                                    if (sap_index !== -1 &&
                                        (isPreselected || (savedSapIds.includes(id) && (rawsap[sap_index].tags === null || rawsap[sap_index].tags.length === 0 || rawsap[sap_index].tags.includes(selected.user))))) {

                                        console.log(`Auto-selecting SAP ID ${id} from datapoint ${datapointKey} (datasource2)`);
                                        sapId.push(id);
                                        // Only add if not already in selectedSap_ array (ensure uniqueness)
                                        if (!selectedSap_.some(sap => sap.id === id)) {
                                            selectedSap_.push(rawsap[sap_index]);
                                        }
                                    }
                                    // Case 2: SAP not found in rawsap array but is preselected
                                    else if (sap_index === -1 && isPreselected) {
                                        console.log(`SAP ID ${id} not found in rawsap, creating placeholder for datapoint ${datapointKey} (datasource2)`);
                                        sapId.push(id);
                                        // Only add if not already in selectedSap_ array (ensure uniqueness)
                                        if (!selectedSap_.some(sap => sap.id === id)) {
                                            selectedSap_.push({
                                                id: id,
                                                title: `SAP ID: ${id}`,
                                                sapId: `SAP${id}`
                                            });
                                        }
                                    }

                                    // Add to SAP selections if SAP was included
                                    if (sapId.includes(id)) {
                                        // Initialize SAP selections for this indicator if not exists
                                        if (!newSapSelections[items.id]) {
                                            newSapSelections[items.id] = {
                                                singleSap: null,
                                                multipleSaps: []
                                            };
                                        }

                                        const existingSapIndex = newSapSelections[items.id].multipleSaps.findIndex(s => s.id === id);
                                        if (existingSapIndex === -1) {
                                            newSapSelections[items.id].multipleSaps.push({
                                                id: id,
                                                selected: true
                                            });
                                        }

                                        // Add to datapoint-SAP selections
                                        // Only if no SAP is already selected for this datapoint
                                        if (!newDatapointSapSelections[datapointKey]) {
                                            newDatapointSapSelections[datapointKey] = id;
                                        }
                                    }
                                }
                            });
                        }
                    })
                }
            } else {
                items.selected = false
            }
        })

        // Update DCF selections state
        setDcfSelections(newDcfSelections);

        // Update datapoint-DCF selections state
        setDatapointDcfSelections(newDatapointDcfSelections);

        // Update SAP selections state
        setSapSelections(newSapSelections);

        // Update datapoint-SAP selections state
        setDatapointSapSelections(newDatapointSapSelections);

        // Collect all DCF IDs that should be selected (only add if not already in selected_)
        dcfId.forEach((dcfId) => {
            if (rawdcf.findIndex((i) => { return i.id === dcfId }) !== -1 && !selected_.some(dcf => dcf.id === dcfId)) {
                selected_.push(rawdcf[rawdcf.findIndex((i) => { return i.id === dcfId })])
            }
        })

        // Collect all SAP IDs that should be selected (only add if not already in selectedSap_)
        sapId.forEach((sapId) => {
            if (rawsap.findIndex((i) => { return i.id === sapId }) !== -1 && !selectedSap_.some(sap => sap.id === sapId)) {
                selectedSap_.push(rawsap[rawsap.findIndex((i) => { return i.id === sapId })])
            }
        })

        // Merge with any existing selected DCFs from saved assignments
        const existingDcfIds = selecteddcfbk.map(dcf => dcf.id);

        // Add any DCFs that are in the saved list but not yet in our selected list
        existingDcfIds.forEach(id => {
            if (!selected_.some(dcf => dcf.id === id)) {
                const dcfIndex = rawdcf.findIndex(i => i.id === id);
                if (dcfIndex !== -1) {
                    selected_.push(rawdcf[dcfIndex]);
                }
            }
        });

        // Merge with any existing selected SAPs from saved assignments
        const existingSapIds = selectedsapbk.map(sap => sap.id);

        // Add any SAPs that are in the saved list but not yet in our selected list
        existingSapIds.forEach(id => {
            if (!selectedSap_.some(sap => sap.id === id)) {
                const sapIndex = rawsap.findIndex(i => i.id === id);
                if (sapIndex !== -1) {
                    selectedSap_.push(rawsap[sapIndex]);
                }
            }
        });

    

        // Final deduplication step to ensure unique DCF IDs
        const uniqueSelected = [];
        const seenIds = new Set();
        selected_.forEach(dcf => {
            if (!seenIds.has(dcf.id)) {
                seenIds.add(dcf.id);
                uniqueSelected.push(dcf);
            }
        });

        // Final deduplication step to ensure unique SAP IDs
        const uniqueSelectedSap = [];
        const seenSapIds = new Set();
        selectedSap_.forEach(sap => {
            if (!seenSapIds.has(sap.id)) {
                seenSapIds.add(sap.id);
                uniqueSelectedSap.push(sap);
            }
        });

        // CLEANUP: Remove DCFs/SAPs that are no longer mapped to any selected indicators
        console.log("Starting DCF/SAP cleanup for deassigned indicators...");

        // Get all DCF IDs that are still needed by selected indicators
        const neededDcfIds = new Set();
        const neededSapIds = new Set();

        // Check each selected indicator to see what DCFs/SAPs it needs
        sMetric.forEach(metricId => {
            const metric = oMetric.find(m => m.id === metricId);
            if (!metric) return;

            // For standalone indicators (type 2)
            if (Array.isArray(metric.data1) && metric.data1.length === 1 && metric.data1[0].type === 2) {
                // Single DCF
                if (metric.data1[0].dcfId) {
                    neededDcfIds.add(metric.data1[0].dcfId);
                }
                // Multiple DCFs
                if (metric.data1[0].dcf_ids && Array.isArray(metric.data1[0].dcf_ids)) {
                    metric.data1[0].dcf_ids.forEach(id => neededDcfIds.add(id));
                }
            }

            // For indicators with datapoints
            if (metric.newDataPoints) {
                metric.newDataPoints.forEach(dp => {
                    if (Array.isArray(dp.data1) && dp.data1.length > 0) {
                        // Single DCF (datasource)
                        if (dp.data1[0].datasource && typeof dp.data1[0].datasource === 'number') {
                            neededDcfIds.add(dp.data1[0].datasource);
                        }
                        // Multiple DCFs (dcf_ids)
                        if (dp.data1[0].dcf_ids && Array.isArray(dp.data1[0].dcf_ids)) {
                            dp.data1[0].dcf_ids.forEach(id => neededDcfIds.add(id));
                        }
                        // SAP Collections (datasource2)
                        if (dp.data1[0].datasource2) {
                            if (typeof dp.data1[0].datasource2 === 'number') {
                                neededSapIds.add(dp.data1[0].datasource2);
                            } else if (Array.isArray(dp.data1[0].datasource2)) {
                                dp.data1[0].datasource2.forEach(id => neededSapIds.add(id));
                            }
                        }
                    }
                });
            }
        });

        console.log("DCFs needed by selected indicators:", Array.from(neededDcfIds));
        console.log("SAPs needed by selected indicators:", Array.from(neededSapIds));

        // Filter out DCFs that are no longer needed
        const cleanedDcfs = uniqueSelected.filter(dcf => neededDcfIds.has(dcf.id));
        const cleanedSaps = uniqueSelectedSap.filter(sap => neededSapIds.has(sap.id));

        // Log what was removed
        const removedDcfs = uniqueSelected.filter(dcf => !neededDcfIds.has(dcf.id));
        const removedSaps = uniqueSelectedSap.filter(sap => !neededSapIds.has(sap.id));

        if (removedDcfs.length > 0) {
            console.log("Removing unused DCFs:", removedDcfs.map(d => ({ id: d.id, title: d.title })));
        }
        if (removedSaps.length > 0) {
            console.log("Removing unused SAPs:", removedSaps.map(s => ({ id: s.id, title: s.title })));
        }

        // Clean up datapoint selections for removed DCFs/SAPs
        setDatapointDcfSelections(prev => {
            const cleaned = { ...prev };
            Object.keys(cleaned).forEach(dpKey => {
                if (!neededDcfIds.has(cleaned[dpKey])) {
                    delete cleaned[dpKey];
                }
            });
            return cleaned;
        });

        setDatapointSapSelections(prev => {
            const cleaned = { ...prev };
            Object.keys(cleaned).forEach(dpKey => {
                if (!neededSapIds.has(cleaned[dpKey])) {
                    delete cleaned[dpKey];
                }
            });
            return cleaned;
        });

        console.log("Final selected DCFs (after cleanup):", cleanedDcfs.map(d => d.id));
        console.log("Final selected SAPs (after cleanup):", cleanedSaps.map(s => s.id));

        setSelectedDCFBK(cleanedDcfs)
        setSelectedDCF(cleanedDcfs.filter((k) => { return k.title.trim().toLowerCase().includes(search.dcf.trim().toLowerCase()) }))
        setSelectedSAPBK(cleanedSaps)
        setSelectedSAP(cleanedSaps.filter((k) => { return k.title.trim().toLowerCase().includes(search.sap || '') }))
        setMetricList(oMetric.filter((k) => { return k.title.trim().toLowerCase().includes(search.metric.trim().toLowerCase()) }))

        // Force a re-render to update the disabled state of all radio buttons
        setTimeout(() => {
            forceUpdate();
        }, 0);
    }
    const updateSelected = (obj, val) => {
        let loc = selected;
        loc[obj] = val;
        let met_loc = metriclist
        let ser = search
        ser.df = ''
        ser.srf = ''
        ser.dcf = ''
        ser.metric = ''
        let categoryList = [], metricList = [], topicList = [], userSelectedMetric = []
        let loclist = list, required_rf = []


        setSearch(ser)
        if (obj === 'user') {
            try {
                // Show loading indicator
                Swal.fire({
                    title: 'Loading Client Data',
                    text: 'Please wait while we load the client data...',
                    allowOutsideClick: false,
                    didOpen: () => {
                        Swal.showLoading();
                    }
                });

                APIServices.get(API.AssignDCFClient_UP(val))
                    .then((res) => {
                        try {
                            console.log("Client selection response:", res.data);
                            if (res.data.length > 0) {
                                console.log("Client data details:");
                                console.log("- dcf_ids:", res.data[0].dcf_ids);
                                console.log("- datasource2 (datasource2):", res.data[0].sap_ids);
                                console.log("- metric_ids:", res.data[0].metric_ids);
                                console.log("- category_ids:", res.data[0].category_ids);
                                console.log("- topic_ids:", res.data[0].topic_ids);
                                console.log("- selected_ids:", res.data[0].selected_ids);
                                console.log("- dp_ids:", res.data[0].dp_ids);

                                // Log assignment summary
                                const clientData = res.data[0];
                                const categoryCount = clientData.category_ids ? clientData.category_ids.length : 0;
                                const topicCount = clientData.topic_ids ? clientData.topic_ids.length : 0;
                                const metricCount = clientData.metric_ids ? clientData.metric_ids.length : 0;
                                const dcfCount = clientData.dcf_ids ? clientData.dcf_ids.length : 0;
                                const dpCount = clientData.dp_ids ? clientData.dp_ids.length : 0;

                                console.log(`Assignment Summary - Categories: ${categoryCount}, Topics: ${topicCount}, Indicators: ${metricCount}, DCFs: ${dcfCount}, Datapoints: ${dpCount}`);



                                // Close loading indicator
                                Swal.close();
                            }
                            else {
                                // Close loading indicator
                                Swal.close();
                            }
                            setEditMode(res.data.length === 0 ? false : true);

                            if (res.data.length !== 0) {
                                setEditModeID(res.data[0].id);
                                response.forEach((cat) => {
                                    if (cat.newTopics !== undefined) {
                                        categoryList.push({ id: cat.id, title: cat.title });
                                        cat.newTopics.forEach((topic) => {
                                            if (topic.newMetrics !== undefined && (res.data[0].category_ids === null || res.data[0].category_ids.includes(cat.id)) && (topic.tag === null || parseFloat(topic.tag) === val)) {
                                                console.log(topic.id);
                                                topicList.push({ name: topic.title, title: topic.title + "(" + cat.title + ")", id: topic.id });
                                                topic.newMetrics.forEach((metric) => {
                                                    if ((res.data[0].topic_ids === null || res.data[0].topic_ids.includes(topic.id)) && (metric.tag === null || parseFloat(metric.tag) === val)) {
                                                        if (Array.isArray(metric.data1) && metric.data1[0].type !== undefined && (metric.data1[0].type === 0 || metric.data1[0].type === 2) && (metric.tag === null || metric.tag === val)) {
                                                            console.log(metric.id);
                                                            metricList.push({ ...metric, cat_title: cat.title });
                                                        }
                                                    }
                                                });
                                            }
                                        });
                                    }
                                });

                                if (res.data[0].cf_ids !== null && res.data[0].cf_ids.length !== 0) {
                                    let cfbk = cfbklist.map(item => ({
                                        ...item,
                                        selected: res.data[0].cf_ids.includes(item.id),
                                    }));
                                    setCFBKList(cfbk);
                                    setCFList(cfbk);
                                    forceUpdate();
                                } else {
                                    let cfbk = cfbklist.map(item => ({
                                        ...item,
                                        selected: false,
                                    }));
                                    setCFBKList(cfbk);
                                    setCFList(cfbk);
                                    forceUpdate();
                                }

                                // let loc = JSON.parse(JSON.stringify(metricList)).map(k => { return { title: k.title, id: k.id, selected: false } })
                                res.data.forEach((item) => {
                                    userSelectedMetric = item.metric_ids.filter((x) => metricList.map(y => y.id).includes(x));
                                });

                                console.log("Selected metrics:", userSelectedMetric);
                                setUserMetric(userSelectedMetric);
                                loclist.category = categoryList;
                                loclist.topic = topicList;
                                loclist.metric = metricList;
                                setMetricBk(metricList);

                                // STEP 1: Set the selected DCFs from the API response
                                if (res.data[0].dcf_ids && Array.isArray(res.data[0].dcf_ids)) {
                                    const savedDcfIds = res.data[0].dcf_ids;
                                    console.log("Saved DCF IDs:", savedDcfIds);

                                    // Find the DCF objects for the saved IDs
                                    const selectedDcfs = savedDcfIds.map(id => {
                                        const dcf = rawdcf.find(d => d.id === id);
                                        return dcf || null;
                                    }).filter(Boolean);

                                    console.log("Setting selected DCFs:", selectedDcfs.map(d => d.id));

                                    // IMPORTANT: Set these DCFs as selected globally
                                    setSelectedDCFBK(selectedDcfs);
                                    setSelectedDCF(selectedDcfs);

                                    // Log the selected DCFs for debugging
                                    console.log("Auto-selected DCFs:", selectedDcfs.map(d => ({ id: d.id, title: d.title })));

                                    // STEP 2: Initialize datapointDcfSelections for all datapoints that can use these DCFs
                                    try {
                                        // First, reset the state
                                        const newDatapointDcfSelections = {};

                                        // For each selected indicator (only those in metric_ids)
                                        metricList.filter(m => res.data[0].metric_ids.includes(m.id)).forEach(indicator => {
                                            console.log(`Processing indicator ${indicator.id} (${indicator.title})`);

                                            // For standalone indicators
                                            if (indicator.data1 && Array.isArray(indicator.data1) && indicator.data1.length > 0) {
                                                const datapointKey = `standalone-${indicator.id}`;

                                                // Check for single DCF
                                                if (indicator.data1[0].dcfId && savedDcfIds.includes(indicator.data1[0].dcfId)) {
                                                    console.log(`Auto-selecting DCF ${indicator.data1[0].dcfId} for standalone indicator ${indicator.id}`);
                                                    newDatapointDcfSelections[datapointKey] = indicator.data1[0].dcfId;
                                                }
                                                // Check for multiple DCFs
                                                else if (indicator.data1[0].dcf_ids && Array.isArray(indicator.data1[0].dcf_ids)) {
                                                    // Find all DCFs that are in the saved list
                                                    const availableDcfs = indicator.data1[0].dcf_ids.filter(id => savedDcfIds.includes(id));

                                                    if (availableDcfs.length > 0) {
                                                        // Select the first available DCF
                                                        console.log(`Auto-selecting DCF ${availableDcfs[0]} for standalone indicator ${indicator.id}`);
                                                        newDatapointDcfSelections[datapointKey] = availableDcfs[0];
                                                    }
                                                }
                                            }

                                            // For datapoints
                                            if (indicator.newDataPoints && indicator.newDataPoints.length > 0) {
                                                indicator.newDataPoints.forEach(datapoint => {
                                                    // Check if this datapoint is in the saved dp_ids
                                                    if (!res.data[0].dp_ids || !res.data[0].dp_ids.includes(datapoint.id)) {
                                                        return; // Skip datapoints that aren't in the saved list
                                                    }

                                                    // Create a unique key for this datapoint
                                                    const datapointKey = `${indicator.id}-${datapoint.suffix || datapoint.title || datapoint.id}`;

                                                    console.log(`Processing datapoint ${datapoint.id} (${datapoint.suffix || datapoint.title})`);

                                                    if (datapoint.data1 && Array.isArray(datapoint.data1) && datapoint.data1.length > 0) {
                                                        // Check for single DCF
                                                        if (datapoint.data1[0].datasource !== null &&
                                                            typeof datapoint.data1[0].datasource === 'number') {

                                                            if (savedDcfIds.includes(datapoint.data1[0].datasource)) {
                                                                console.log(`Auto-selecting DCF ${datapoint.data1[0].datasource} for datapoint ${datapointKey}`);
                                                                newDatapointDcfSelections[datapointKey] = datapoint.data1[0].datasource;
                                                            } else {
                                                                console.log(`DCF ${datapoint.data1[0].datasource} for datapoint ${datapointKey} is not in saved DCF IDs`);
                                                            }
                                                        }
                                                        // Check for multiple DCFs
                                                        else if (datapoint.data1[0].dcf_ids && Array.isArray(datapoint.data1[0].dcf_ids)) {
                                                            // Find all DCFs that are in the saved list
                                                            const availableDcfs = datapoint.data1[0].dcf_ids.filter(id => savedDcfIds.includes(id));

                                                            if (availableDcfs.length > 0) {
                                                                // Select the first available DCF
                                                                console.log(`Auto-selecting DCF ${availableDcfs[0]} for datapoint ${datapointKey}`);
                                                                newDatapointDcfSelections[datapointKey] = availableDcfs[0];
                                                            } else {
                                                                console.log(`No DCFs for datapoint ${datapointKey} are in saved DCF IDs`);
                                                            }
                                                        }
                                                    }
                                                });
                                            }
                                        });

                                        console.log("Final datapoint-DCF selections:", newDatapointDcfSelections);
                                        setDatapointDcfSelections(newDatapointDcfSelections);
                                    } catch (error) {
                                        console.error("Error initializing datapoint-DCF selections:", error);
                                    }
                                }

                                // STEP 1: Set the selected SAPs from the API response (similar to DCF handling)
                                if (res.data[0].sap_ids && Array.isArray(res.data[0].sap_ids)) {
                                    const savedSapIds = res.data[0].sap_ids;
                                    console.log("Saved SAP IDs:", savedSapIds);

                                    // Find the SAP objects for the saved IDs
                                    const selectedSaps = savedSapIds.map(id => {
                                        const sap = rawsap.find(s => s.id === id);
                                        return sap || null;
                                    }).filter(Boolean);

                                    console.log("Setting selected SAPs:", selectedSaps.map(s => s.id));

                                    // IMPORTANT: Set these SAPs as selected globally
                                    setSelectedSAPBK(selectedSaps);
                                    setSelectedSAP(selectedSaps);

                                    // Log the selected SAPs for debugging
                                    console.log("Auto-selected SAPs:", selectedSaps.map(s => ({ id: s.id, title: s.title })));

                                    // STEP 2: Initialize datapointSapSelections for all datapoints that can use these SAPs
                                    try {
                                        // First, reset the state
                                        const newDatapointSapSelections = {};

                                        // For each selected indicator (only those in metric_ids)
                                        metricList.filter(m => res.data[0].metric_ids.includes(m.id)).forEach(indicator => {
                                            console.log(`Processing indicator ${indicator.id} for SAP selection (${indicator.title})`);

                                            // For datapoints
                                            if (indicator.newDataPoints && indicator.newDataPoints.length > 0) {
                                                indicator.newDataPoints.forEach(datapoint => {
                                                    // Check if this datapoint is in the saved dp_ids
                                                    if (!res.data[0].dp_ids || !res.data[0].dp_ids.includes(datapoint.id)) {
                                                        return; // Skip datapoints that aren't in the saved list
                                                    }

                                                    // Create a unique key for this datapoint
                                                    const datapointKey = `${indicator.id}-${datapoint.suffix || datapoint.title || datapoint.id}`;

                                                    console.log(`Processing datapoint ${datapoint.id} for SAP selection (${datapoint.suffix || datapoint.title})`);

                                                    if (datapoint.data1 && Array.isArray(datapoint.data1) && datapoint.data1.length > 0) {
                                                        // Check for SAP (datasource2)
                                                        if (datapoint.data1[0].datasource2 !== null &&
                                                            typeof datapoint.data1[0].datasource2 === 'number') {

                                                            if (savedSapIds.includes(datapoint.data1[0].datasource2)) {
                                                                console.log(`Auto-selecting SAP ${datapoint.data1[0].datasource2} for datapoint ${datapointKey}`);
                                                                newDatapointSapSelections[datapointKey] = datapoint.data1[0].datasource2;
                                                            } else {
                                                                console.log(`SAP ${datapoint.data1[0].datasource2} for datapoint ${datapointKey} is not in saved SAP IDs`);
                                                            }
                                                        }
                                                    }
                                                });
                                            }
                                        });

                                        console.log("Final datapoint-SAP selections:", newDatapointSapSelections);
                                        setDatapointSapSelections(newDatapointSapSelections);
                                    } catch (error) {
                                        console.error("Error initializing datapoint-SAP selections:", error);
                                    }
                                }

                                // NEW: Load DCF/SAP indicator mapping data from database (if available)
                                // This restores the tracking information for existing assignments
                                if (res.data[0].dcf_indicator_mapping) {
                                    console.log("Loading DCF indicator mapping from database:", res.data[0].dcf_indicator_mapping);
                                    setDcfIndicatorMapping(res.data[0].dcf_indicator_mapping);
                                } else {
                                    console.log("No DCF indicator mapping found in database - will be created on save");
                                    setDcfIndicatorMapping({});
                                }

                                if (res.data[0].sap_indicator_mapping) {
                                    console.log("Loading SAP indicator mapping from database:", res.data[0].sap_indicator_mapping);
                                    setSapIndicatorMapping(res.data[0].sap_indicator_mapping);
                                } else {
                                    console.log("No SAP indicator mapping found in database - will be created on save");
                                    setSapIndicatorMapping({});
                                }

                                if (res.data[0].datapoint_dcf_mapping) {
                                    console.log("Loading datapoint DCF mapping from database:", res.data[0].datapoint_dcf_mapping);
                                    setDatapointDcfSelections(res.data[0].datapoint_dcf_mapping);
                                }

                                if (res.data[0].datapoint_sap_mapping) {
                                    console.log("Loading datapoint SAP mapping from database:", res.data[0].datapoint_sap_mapping);
                                    setDatapointSapSelections(res.data[0].datapoint_sap_mapping);
                                }

                                try {
                                    // CRITICAL: First, process indicators to collect all DCF IDs
                                    // This ensures we capture DCFs from indicators even if they're not in the assignment
                                    const allDcfIdsFromIndicators = [];

                                    // Collect DCF IDs from all selected indicators
                                    metricList.forEach(indicator => {
                                        if (userSelectedMetric.includes(indicator.id)) {
                                            if (Array.isArray(indicator.data1) &&
                                                indicator.data1.length === 1 &&
                                                indicator.data1[0].type === 2 &&
                                                indicator.data1[0].dcfId) {

                                                allDcfIdsFromIndicators.push(indicator.data1[0].dcfId);
                                            }
                                        }
                                    });

                                    console.log("DCF IDs from selected indicators:", allDcfIdsFromIndicators);

                                    // Combine DCF IDs from assignment and indicators
                                    let combinedDcfIds = [];

                                    // Add DCF IDs from assignment if available
                                    if (res.data[0].dcf_ids && Array.isArray(res.data[0].dcf_ids)) {
                                        combinedDcfIds = [...res.data[0].dcf_ids];
                                        console.log("DCF IDs from assignment:", res.data[0].dcf_ids);
                                    }

                                    // Add DCF IDs from indicators that aren't already in the list
                                    allDcfIdsFromIndicators.forEach(id => {
                                        if (!combinedDcfIds.includes(id)) {
                                            combinedDcfIds.push(id);
                                        }
                                    });

                                    console.log("Combined DCF IDs to select:", combinedDcfIds);

                                    if (combinedDcfIds.length > 0) {
                                        // Fetch the latest DCF data
                                        APIServices.get(API.DCF)
                                            .then(dcfResponse => {
                                                // Update the rawdcf array with the latest data
                                                const latestDcfs = dcfResponse.data.filter((i) => { return (i.type === null || i.type === 1) });
                                                setRawDCF(latestDcfs);

                                                // Create DCF objects for all IDs
                                                let selectedDcfs = combinedDcfIds
                                                    .map(id => {
                                                        const dcf = latestDcfs.find(d => d.id === id);
                                                        if (dcf) {
                                                            return dcf;
                                                        } else {
                                                            console.log(`DCF ID ${id} not found in rawdcf array, creating placeholder`);
                                                            return {
                                                                id: id,
                                                                title: `DCF ID: ${id}`,
                                                                tags: null
                                                            };
                                                        }
                                                    });

                                                console.log("Auto-selecting DCFs:", selectedDcfs.map(d => d.id));

                                                // IMPORTANT: Set the DCFs as selected BEFORE calling updateSelectedIndicators
                                                setSelectedDCFBK(selectedDcfs);
                                                setSelectedDCF(selectedDcfs);

                                                // Force update to ensure UI reflects the selection
                                                forceUpdate();

                                                // CRITICAL: Directly set the DCFs in the state
                                                // This ensures they're available immediately
                                                window.selectedDcfs = selectedDcfs; // Store for debugging

                                                // Wait a moment for state to update before continuing
                                                setTimeout(() => {
                                                    // Force another update to ensure UI reflects the selection
                                                    forceUpdate();

                                                    // Pass the selected DCF IDs and SAP IDs to updateSelectedIndicators
                                                    const combinedSapIds = res.data[0].sap_ids && Array.isArray(res.data[0].sap_ids) ? res.data[0].sap_ids : [];
                                                    console.log("Combined SAP IDs to select:", combinedSapIds);
                                                    updateSelectedIndicators(userSelectedMetric, metricList, combinedDcfIds, combinedSapIds);

                                                    // Set a final timeout to ensure everything is updated
                                                    setTimeout(() => {
                                                        forceUpdate();
                                                    }, 200);
                                                }, 300); // Increased timeout for more reliable state updates
                                            })
                                            .catch(error => {
                                                console.error("Error fetching DCFs:", error);
                                                updateSelectedIndicators(userSelectedMetric, metricList, [], []);
                                            });
                                    } else {
                                        updateSelectedIndicators(userSelectedMetric, metricList, [], []);
                                    }
                                } catch (error) {
                                    console.error("Error in updateSelectedIndicators:", error);
                                }

                                if (res.data[0].category_ids !== null) {
                                    loc.category = categoryList.filter((i) => { return res.data[0].category_ids.includes(i.id); }).map((j) => { return j.id; });
                                } else {
                                    loc.category = [];
                                }

                                if (res.data[0].topic_ids !== null) {
                                    loc.topic = topicList.filter((i) => { return res.data[0].topic_ids.includes(i.id); }).map((j) => { return j.id; });
                                    console.log(loc.topic);
                                    let met_list_final = [];
                                    response.forEach((cat) => {
                                        if (cat.newTopics) {
                                            cat.newTopics.forEach((topic) => {
                                                if (loc.topic.includes(topic.id) && topic.newMetrics) {
                                                    topic.newMetrics.forEach((metric) => {
                                                        if (Array.isArray(metric.data1) && metric.data1[0] !== undefined && metric.data1[0].type === 1 && dfbklist.map(i => i.id).includes(metric.data1[0].rf) && !required_rf.map(i => i.id).includes(metric.data1[0].rf) && (metric.tag === null || parseFloat(metric.tag) === val)) {
                                                            console.log(dfbklist.find(i => i.id === metric.data1[0].rf));
                                                            required_rf.push(dfbklist.find(i => i.id === metric.data1[0].rf));
                                                            // required_rf.push({ cat_id: cat.id, top_id: topic.id, overallTags: [metric.data1[0].tags1, metric.data1[0].tags2, metric.data1[0].tags3], cat_title: cat.title, top_title: topic.title, title: metric.title, ...metric })
                                                        }
                                                    });
                                                }
                                            });
                                        }
                                    });

                                    if (false) {
                                        setDFAss(res.data[0].df_ass);
                                        required_rf.forEach((i) => {
                                            let index = res.data[0].df_ass.findIndex((j) => { return j.dfid === i.id; });
                                            console.log(i.id, index);
                                            if (index !== -1) {
                                                i.config = res.data[0].df_ass[index];
                                            }
                                        });
                                        setDFReqListBK(required_rf);
                                        setDFReqList(required_rf);
                                    }

                                    console.log(required_rf);
                                } else {
                                    loc.metric = [];
                                    loc.topic = [];
                                }

                                setList(loclist);
                                forceUpdate();
                            } else {
                                // setDFReqListBK([])
                                // setDFReqList([])
                                // setDFAss([])
                                // setDfConfig({apex:false,country:false,region:false,site:false})
                                response.forEach((cat) => {
                                    if (cat.newTopics !== undefined) {
                                        categoryList.push({ id: cat.id, title: cat.title });
                                        cat.newTopics.forEach((topic) => {
                                            if (topic.newMetrics !== undefined && (topic.tag === null || parseFloat(topic.tag) === val)) {
                                                topicList.push({ name: topic.title, title: topic.title + "(" + cat.title + ")", id: topic.id });
                                                topic.newMetrics.forEach((metric) => {
                                                    if (Array.isArray(metric.data1) && metric.data1[0].type !== undefined && (metric.data1[0].type === 0 || metric.data1[0].type === 2) && (metric.tag === null || metric.tag === val)) {
                                                        metricList.push({ ...metric, cat_title: cat.title });
                                                    }
                                                });
                                            }
                                        });
                                    }
                                });

                                loclist.category = categoryList;
                                loc.category = [];
                                loc.metric = [];
                                loc.topic = [];
                                setList(loclist);
                                forceUpdate();
                            }
                        } catch (error) {
                            console.error("Error processing client selection:", error);
                        }
                    })
                    .catch(error => {
                        console.error("API error in client selection:", error);
                    });
            } catch (error) {
                console.error("Error in client selection:", error);
            }
        } else if (obj === 'category') {
            response.forEach((cat) => {
                if (cat.newTopics !== undefined && val.includes(cat.id)) {

                    cat.newTopics.forEach((topic) => {


                        if (topic.newMetrics !== undefined && (topic.tag === null || parseFloat(topic.tag) === loc.user)) {
                            topicList.push({ name: topic.title, title: topic.title + "(" + cat.title + ")", id: topic.id })
                            topic.newMetrics.forEach((metric) => {
                                if (Array.isArray(metric.data1) && metric.data1[0].type !== undefined && (metric.data1[0].type === 0 || metric.data1[0].type === 2) && (metric.tag === null || metric.tag === loc.user)) {
                                    metricList.push(metric)
                                }
                            })
                        }
                    })
                }


            })
            loc.topic = topicList.filter((i) => { return loc.topic.includes(i.id) }).map((j) => { return j.id })
            console.log(loc.topic)

            setDFReqListBK(required_rf)
            setDFReqList(required_rf)
            loclist.metric = metricList
            loclist.topic = topicList
            console.log(usermetric, metricList)
            setUserMetric(usermetric.filter(i => metricList.map(x => x.id).includes(i)))
            setMetricBk(metricList)
            updateSelectedIndicators(usermetric.filter(i => metricList.map(x => x.id).includes(i)), metricList, [], [])
            setList(loclist)
        } else if (obj === 'topic') {
            response.forEach((cat) => {
                if (cat.newTopics !== undefined) {

                    cat.newTopics.forEach((topic) => {

                        if (topic.newMetrics !== undefined && val.includes(topic.id) && (topic.tag === null || parseFloat(topic.tag) === loc.user)) {
                            topicList.push({ name: topic.title, title: topic.title + "(" + cat.title + ")", id: topic.id })
                            topic.newMetrics.forEach((metric) => {
                                if (Array.isArray(metric.data1) && metric.data1[0].type !== undefined && (metric.data1[0].type === 0 || metric.data1[0].type === 2) && (metric.tag === null || metric.tag === loc.user)) {
                                    metricList.push(metric)
                                }
                            })
                        }
                    })
                }


            })

            setDFReqListBK(required_rf)
            setDFReqList(required_rf)
            loclist.metric = metricList
            setUserMetric(usermetric.filter(i => metricList.map(x => x.id).includes(i)))
            setMetricBk(metricList)
            updateSelectedIndicators(usermetric.filter(i => metricList.map(x => x.id).includes(i)), metricList, [], [])
            setList(loclist)
        }


        setSelected(loc)
        forceUpdate();
    };




    const prevDialogFooter = (
        <>
            <Button label="Cancel" icon="pi pi-times" className="p-button-text" onClick={() => { setPrevDialog(false) }} />
        </>
    );
    const dupdpidDialogFooter = (
        <>
            <Button label="Cancel" icon="pi pi-times" className="p-button-text" onClick={() => { setDupDPIDDialog(false) }} />
        </>
    );
    const getStandAlone = (item) => {


        if (Array.isArray(item.data1) && item.data1.length !== 0) {
            if (item.data1[0].source === 1) {
                levelCount = levelCount + 1

            } else {
                if (standAlone.length === 0) {
                    item.data1[0].indicator.forEach((id, j) => { !standAlone.includes(id) && standAlone.push(id) })
                }
                item.data1[0].indicator.forEach((id, j) => {



                    let index = overallmetric.findIndex((i) => { return i.id === id })
                    if (index !== -1) {
                        !standAlone.includes(overallmetric[index].id) && standAlone.push(overallmetric[index].id)
                        getStandAlone(overallmetric[index])
                    }

                })
            }

        }
        console.log(standAlone)
        return standAlone
    }
    const getMetricLevel = (item) => {
        let data = 1
        levelCount = levelCount + 1
        if (Array.isArray(item.data1) && item.data1.length !== 0) {

            if (item.data1[0].source === 1) {

                data = 1
            } else {
                data = 2
                let derivedIds = JSON.parse(JSON.stringify(overallmetric)).filter((i) => { return Array.isArray(i.data1) && i.data1[0].length !== 0 && i.data1[0].source === 0 }).map((k) => { return k.id })
                item.data1[0].indicator.forEach((id, j) => {

                    if (derivedIds.includes(id) && j === 0) {

                        let index = overallmetric.findIndex((i) => { return i.id === id })
                        let item2 = overallmetric[index]
                        if (Array.isArray(item2.data1) && item2.data1.length !== 0) {

                            if (item2.data1[0].source === 1) {


                            } else {
                                data = 3
                                let derivedIds = JSON.parse(JSON.stringify(overallmetric)).filter((i) => { return Array.isArray(i.data1) && i.data1[0].length !== 0 && i.data1[0].source === 0 }).map((k) => { return k.id })
                                item2.data1[0].indicator.forEach((id, j) => {

                                    if (derivedIds.includes(id) && j === 0) {

                                        let index = overallmetric.findIndex((i) => { return i.id === id })
                                        let item3 = overallmetric[index]

                                        if (Array.isArray(item3.data1) && item3.data1.length !== 0) {

                                            if (item3.data1[0].source === 1) {


                                            } else {

                                                data = 4

                                                let derivedIds = JSON.parse(JSON.stringify(overallmetric)).filter((i) => { return Array.isArray(i.data1) && i.data1[0].length !== 0 && i.data1[0].source === 0 }).map((k) => { return k.id })
                                                item3.data1[0].indicator.forEach((id, j) => {

                                                    if (derivedIds.includes(id)) {



                                                    }
                                                })
                                            }

                                        }

                                    } else {

                                    }

                                })
                            }

                        }

                    }

                })
            }

        } else if (item.data1 === null) {

        }

        return data
    }
    const getLevel = (item) => {
        return null
    }

    // Render the Indicator-Datapoint-DCF table
    const renderIndicatorDatapointDCFTable = () => {
        // Get selected indicators - only standalone indicators (not derived)
        const selectedIndicators = metricbk.filter(indicator =>
            usermetric.includes(indicator.id) &&
            // Filter out derived indicators (type 0 and source 0 means derived)
            !(indicator.data1 && Array.isArray(indicator.data1) && indicator.data1.length > 0 &&
              indicator.data1[0].type === 0 && indicator.data1[0].source === 0)
        );

        if (!selectedIndicators || selectedIndicators.length === 0) {
            return <div className="p-3 text-center" style={{ height: '75vh' }}>No standalone indicators selected</div>;
        }

        // Prepare data for the table
        const tableData = [];

        // Don't initialize datapointDcfSelections here - we'll do it explicitly when a client is selected
        // This prevents auto-selection of DCFs that aren't in the saved dcf_ids array

        // Group datapoints across all indicators to identify duplicates
        const allDatapoints = {};

        // First pass: collect all datapoints
        selectedIndicators.forEach(indicator => {
            const indicatorDatapoints = indicator.newDataPoints || [];

            indicatorDatapoints.forEach(datapoint => {
                const datapointKey = datapoint.suffix || datapoint.title || datapoint.id;

                if (!allDatapoints[datapointKey]) {
                    allDatapoints[datapointKey] = {
                        datapoint: datapoint,
                        indicators: [],
                        dcfs: []
                    };
                }

                // Add this indicator to the datapoint's indicators
                if (!allDatapoints[datapointKey].indicators.includes(indicator.id)) {
                    allDatapoints[datapointKey].indicators.push(indicator.id);
                }

                // Find DCFs associated with this datapoint
                if (datapoint.data1 && Array.isArray(datapoint.data1) && datapoint.data1.length > 0) {
                    // Single DCF
                    if (datapoint.data1[0].datasource !== null && typeof datapoint.data1[0].datasource === 'number') {
                        const dcfId = datapoint.data1[0].datasource;
                        const dcf = rawdcf.find(d => d.id === dcfId);

                        if (dcf && !allDatapoints[datapointKey].dcfs.some(d => d.id === dcfId)) {
                            allDatapoints[datapointKey].dcfs.push(dcf);
                        }
                    }

                    // Multiple DCFs
                    if (datapoint.data1[0].dcf_ids && Array.isArray(datapoint.data1[0].dcf_ids)) {
                        datapoint.data1[0].dcf_ids.forEach(dcfId => {
                            const dcf = rawdcf.find(d => d.id === dcfId);

                            if (dcf && !allDatapoints[datapointKey].dcfs.some(d => d.id === dcfId)) {
                                allDatapoints[datapointKey].dcfs.push(dcf);
                            }
                        });
                    }
                }
            });
        });

        // For each selected indicator, find its datapoints and DCFs
        selectedIndicators.forEach(indicator => {
            // Check for standalone indicator DCFs
            let standaloneIndicatorDcfs = [];

            if (indicator.data1 && Array.isArray(indicator.data1) && indicator.data1.length > 0) {
                // Single DCF
                if (indicator.data1[0].dcfId) {
                    const dcf = rawdcf.find(d => d.id === indicator.data1[0].dcfId);
                    // Only include DCF if it's tagged with the client or has no tags (available to all)
                    if (dcf && (dcf.tags === null || dcf.tags.length === 0 || dcf.tags.includes(selected.user))) {
                        standaloneIndicatorDcfs.push(dcf);
                    }
                }

                // Multiple DCFs
                if (indicator.data1[0].dcf_ids && Array.isArray(indicator.data1[0].dcf_ids)) {
                    indicator.data1[0].dcf_ids.forEach(dcfId => {
                        const dcf = rawdcf.find(d => d.id === dcfId);
                        // Only include DCF if it's tagged with the client or has no tags (available to all)
                        if (dcf &&
                            (dcf.tags === null || dcf.tags.length === 0 || dcf.tags.includes(selected.user)) &&
                            !standaloneIndicatorDcfs.some(d => d.id === dcf.id)) {
                            standaloneIndicatorDcfs.push(dcf);
                        }
                    });
                }
            }

            // If this is a standalone indicator with DCFs, add rows for each DCF
            if (standaloneIndicatorDcfs.length > 0) {
                standaloneIndicatorDcfs.forEach(dcf => {
                    // Find associated SAP collections for this standalone indicator
                    const standaloneIndicatorSaps = [];

                    // Check if this standalone indicator has SAP associations in its data1
                    if (indicator.data1 && Array.isArray(indicator.data1) && indicator.data1.length > 0) {
                        const indicatorData = indicator.data1[0];

                        // Check for datasource2 (SAP) associations
                        if (indicatorData.datasource2 !== null && indicatorData.datasource2 !== undefined) {
                            let sapIds = [];

                            if (typeof indicatorData.datasource2 === 'number') {
                                // Single SAP (backward compatibility)
                                sapIds = [indicatorData.datasource2];
                            } else if (Array.isArray(indicatorData.datasource2)) {
                                // Multiple SAPs (new format)
                                sapIds = indicatorData.datasource2;
                            }

                            sapIds.forEach(sapId => {
                                // Look for SAP in rawsap (all available SAPs)
                                const sap = rawsap.find(s => s.id === sapId);
                                if (sap && !standaloneIndicatorSaps.some(s => s.id === sapId)) {
                                    standaloneIndicatorSaps.push(sap);
                                }
                            });
                        }
                    }

                    tableData.push({
                        indicator: indicator,
                        indicatorId: indicator.id,
                        indicatorName: indicator.title,
                        datapoint: null,
                        datapointName: 'Standalone',
                        datapointKey: `standalone-${indicator.id}`,
                        dcf: dcf,
                        dcfId: dcf.id,
                        dcfName: dcf.title || '',
                        saps: standaloneIndicatorSaps, // Array of SAP collections for standalone
                        sapIds: standaloneIndicatorSaps.map(s => s.id), // Array of SAP IDs
                        sapNames: standaloneIndicatorSaps.map(s => s.name || s.title || '').join(', ') || 'No SAP Collections'
                    });
                });
            }

            // Find datapoints associated with this indicator
            const indicatorDatapoints = indicator.newDataPoints || [];

            if (indicatorDatapoints.length === 0 && standaloneIndicatorDcfs.length === 0) {
                // If no datapoints and no standalone DCFs, still show the indicator with empty datapoint and DCF
                tableData.push({
                    indicator: indicator,
                    indicatorId: indicator.id,
                    indicatorName: indicator.title,
                    datapoint: null,
                    datapointName: 'No Datapoints',
                    datapointKey: `no-datapoints-${indicator.id}`,
                    dcf: null,
                    dcfName: 'No DCFs',
                    sap: null,
                    sapId: null,
                    sapName: 'No SAP Collections'
                });
            } else if (indicatorDatapoints.length > 0) {
                // For each datapoint, find its associated DCFs
                indicatorDatapoints.forEach(datapoint => {
                    const datapointKey = datapoint.suffix || datapoint.title || datapoint.id;

                    // Find DCFs associated with this datapoint
                    const datapointDcfs = rawdcf.filter(dcf => {
                        // First check if DCF is tagged with the client or has no tags (available to all)
                        if (!(dcf.tags === null || dcf.tags.length === 0 || dcf.tags.includes(selected.user))) {
                            return false; // Skip DCFs not tagged with this client
                        }

                        // Check if this datapoint has this DCF associated
                        if (datapoint.data1 && Array.isArray(datapoint.data1) && datapoint.data1.length > 0) {
                            // Check for single DCF
                            if (datapoint.data1[0].datasource === dcf.id) {
                                return true;
                            }

                            // Check for multiple DCFs
                            if (datapoint.data1[0].dcf_ids && Array.isArray(datapoint.data1[0].dcf_ids) &&
                                datapoint.data1[0].dcf_ids.includes(dcf.id)) {
                                return true;
                            }
                        }
                        return false;
                    });

                    if (datapointDcfs.length === 0) {
                        // Find associated SAP collections for this datapoint - handle both single and multiple SAPs
                        const datapointSaps = [];

                        if (datapoint.data1 && datapoint.data1.length > 0 && datapoint.data1[0].datasource2 !== null) {
                            // Handle both single SAP (number) and multiple SAPs (array)
                            let sapIds = [];

                            if (typeof datapoint.data1[0].datasource2 === 'number') {
                                // Single SAP (backward compatibility)
                                sapIds = [datapoint.data1[0].datasource2];
                            } else if (Array.isArray(datapoint.data1[0].datasource2)) {
                                // Multiple SAPs (new format)
                                sapIds = datapoint.data1[0].datasource2;
                            }

                            sapIds.forEach(sapId => {
                                // Look for SAP in rawsap (all available SAPs) instead of just selectedsap
                                const sap = rawsap.find(s => s.id === sapId);
                                if (sap && !datapointSaps.some(s => s.id === sapId)) {
                                    datapointSaps.push(sap);
                                }
                            });
                        }
                        console.log(`Datapoint: ${datapoint.suffix || datapoint.title} - SAPs:`, datapointSaps.map(s => s.name || s.title))

                        // If no DCFs, still show the indicator and datapoint with empty DCF
                        tableData.push({
                            indicator: indicator,
                            indicatorId: indicator.id,
                            indicatorName: indicator.title,
                            datapoint: datapoint,
                            datapointName: datapoint.suffix || datapoint.title || 'Datapoint',
                            datapointKey: datapointKey,
                            dcf: null,
                            dcfName: 'No DCFs',
                            saps: datapointSaps, // Array of SAP collections
                            sapIds: datapointSaps.map(s => s.id), // Array of SAP IDs
                            sapNames: datapointSaps.map(s => s.name || s.title || '').join(', ') || 'No SAP Collections'
                        });
                    } else {
                        // Find associated SAP collections for this datapoint ONCE - handle both single and multiple SAPs
                        const datapointSaps = [];

                        if (datapoint.data1 && datapoint.data1.length > 0 && datapoint.data1[0].datasource2 !== null) {
                            // Handle both single SAP (number) and multiple SAPs (array)
                            let sapIds = [];

                            if (typeof datapoint.data1[0].datasource2 === 'number') {
                                // Single SAP (backward compatibility)
                                sapIds = [datapoint.data1[0].datasource2];
                            } else if (Array.isArray(datapoint.data1[0].datasource2)) {
                                // Multiple SAPs (new format)
                                sapIds = datapoint.data1[0].datasource2;
                            }

                            sapIds.forEach(sapId => {
                                // Look for SAP in rawsap (all available SAPs) instead of just selectedsap
                                const sap = rawsap.find(s => s.id === sapId);
                                if (sap && !datapointSaps.some(s => s.id === sapId)) {
                                    datapointSaps.push(sap);
                                }
                            });
                        }

                        console.log(`Datapoint: ${datapoint.suffix || datapoint.title} with ${datapointDcfs.length} DCFs and ${datapointSaps.length} SAPs`)

                        // For each DCF, create a row
                        datapointDcfs.forEach((dcf, dcfIndex) => {
                            // Show SAPs only in the first row, "N/A" for subsequent rows
                            const shouldShowSaps = dcfIndex === 0;
                            const rowSaps = shouldShowSaps ? datapointSaps : [];

                            tableData.push({
                                indicator: indicator,
                                indicatorId: indicator.id,
                                indicatorName: indicator.title,
                                datapoint: datapoint,
                                datapointName: datapoint.suffix || datapoint.title || 'Datapoint',
                                datapointKey: datapointKey,
                                dcf: dcf,
                                dcfId: dcf.id,
                                dcfName: dcf.title || '',
                                saps: rowSaps, // Show SAPs only in first row
                                sapIds: rowSaps.map(s => s.id), // Array of SAP IDs
                                sapNames: rowSaps.length > 0 ? rowSaps.map(s => s.name || s.title || '').join(', ') : (shouldShowSaps ? 'No SAP Collections' : 'N/A'),
                                // Check if this datapoint appears in multiple indicators
                                isSharedDatapoint: allDatapoints[datapointKey]?.indicators.length > 1,
                                // Track if this is the first DCF row for SAP display logic
                                isFirstDcfRow: dcfIndex === 0,
                                dcfRowIndex: dcfIndex
                            });
                        });
                    }
                });
            }
        });

        // Indicator column template
        const indicatorTemplate = (rowData) => {
            return (
                <div className="indicator-cell">
                    <div>
                        <span className="font-bold">{rowData.indicatorId}</span>
                        <span className="ml-2">{rowData.indicatorName}</span>
                    </div>
                </div>
            );
        };

        // Datapoint column template
        const datapointTemplate = (rowData) => {
            return (
                <div className="datapoint-cell">
                    {rowData.datapointName === 'Standalone' ? (
                        <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded">Standalone</span>
                    ) : rowData.datapointName === 'No Datapoints' ? (
                        <span className="text-gray-500 italic">No Datapoints</span>
                    ) : (
                        <div>
                            <div className="font-medium">{rowData?.datapoint?.title || 'NA'}</div>
                            {rowData.datapoint && (rowData.datapoint.suffix || rowData.datapoint.id) && (
                                <div className="text-xs text-gray-500">
                                    {rowData.datapoint.suffix || rowData.datapoint.id}
                                </div>
                            )}
                        </div>
                    )}
                </div>
            );
        };

        // Handle SAP radio button change - TOGGLE BEHAVIOR
        const handleSapCheckboxChange = (e, rowData, sapId) => {
            // ALWAYS log this first to confirm handler is called
            console.log(`🔥🔥🔥 SAP ${sapId} CLICKED - Handler triggered!`);
            console.log(`Event details:`, { checked: e.checked, target: e.target });
            console.log(`Row data:`, { sapId, datapointKey: rowData.datapointKey });

            // Get the selected SAP
            const sap = rawsap.find(s => s.id === sapId);
            if (!sap) {
                console.log(`❌ SAP ${sapId} not found in rawsap`);
                return;
            }

            // Check current selection status BEFORE the event
            const isSapCurrentlySelected = selectedsap.some(s => s.id === sapId);

            console.log(`Current state analysis:`, {
                sapId,
                datapointKey: rowData.datapointKey,
                isSapCurrentlySelected,
                selectedsap_ids: selectedsap?.map(s => s.id) || [],
                currentDatapointSelections: datapointSapSelections,
                eventChecked: e.checked
            });

            // Use the synthetic event's checked property to determine action
            if (!e.checked) {
                // UNSELECT: The synthetic event indicates we should unselect
                console.log(`🔴 UNSELECTING SAP ${sapId} from ALL datapoints`);

                // Remove from selected SAPs list completely
                setSelectedSAPBK(prev => {
                    const filtered = prev.filter(s => s.id !== sapId);
                    console.log(`Updated selectedSAPBK: ${filtered.map(s => s.id).join(', ')}`);
                    return filtered;
                });

                setSelectedSAP(prev => {
                    const filtered = prev.filter(s => s.id !== sapId);
                    console.log(`Updated selectedSAP: ${filtered.map(s => s.id).join(', ')}`);
                    return filtered;
                });

                // Remove from all datapoint-SAP selections
                setDatapointSapSelections(prev => {
                    const newSelections = { ...prev };
                    const removedDatapoints = [];

                    Object.keys(newSelections).forEach(dpKey => {
                        if (newSelections[dpKey] === sapId) {
                            delete newSelections[dpKey];
                            removedDatapoints.push(dpKey);
                        }
                    });

                    console.log(`Removed SAP ${sapId} from datapoints:`, removedDatapoints);
                    console.log(`Updated datapointSapSelections:`, newSelections);
                    return newSelections;
                });

                // Remove from SAP-indicator mapping
                setSapIndicatorMapping(prev => {
                    const newMapping = { ...prev };
                    delete newMapping[sapId];
                    console.log(`Removed SAP ${sapId} from indicator mapping`);
                    return newMapping;
                });

                // Update SAP selections for all indicators
                setSapSelections(prev => {
                    const newSelections = { ...prev };
                    Object.keys(newSelections).forEach(indId => {
                        if (newSelections[indId] && newSelections[indId].multipleSaps) {
                            newSelections[indId].multipleSaps.forEach(sapObj => {
                                if (sapObj.id === sapId) {
                                    sapObj.selected = false;
                                    console.log(`Unselected SAP ${sapId} for indicator ${indId}`);
                                }
                            });
                        }
                    });
                    return newSelections;
                });

                // Force a re-render to update the disabled state of all radio buttons
                setTimeout(() => {
                    console.log(`🔄 Forcing re-render after unselecting SAP ${sapId}`);
                    forceUpdate();
                }, 100);

                return;
            }

            // SELECT: The synthetic event indicates we should select
            console.log(`🟢 SELECTING SAP ${sapId} for all datapoints`);

            // Extract the base datapoint key (without the indicator prefix)
            let baseDatapointKey = null;

            // If it's a standalone indicator, use the standalone key
            if (rowData.datapointKey.startsWith('standalone-')) {
                baseDatapointKey = 'standalone';
            }
            // If it's a regular datapoint, extract the datapoint name/id
            else if (rowData.datapoint) {
                baseDatapointKey = rowData.datapoint.suffix || rowData.datapoint.title || rowData.datapoint.id;
            }

            // Build a map of all datapoint keys to their base keys
            const allDatapointBaseKeys = {};
            tableData.forEach(row => {
                if (row.datapointKey) {
                    let rowBaseKey = null;

                    // For standalone indicators
                    if (row.datapointKey.startsWith('standalone-')) {
                        rowBaseKey = 'standalone';
                    }
                    // For regular datapoints, extract from the datapoint object
                    else if (row.datapoint) {
                        rowBaseKey = row.datapoint.suffix || row.datapoint.title || row.datapoint.id;
                    }

                    if (rowBaseKey) {
                        allDatapointBaseKeys[row.datapointKey] = rowBaseKey;
                    }
                }
            });

            // If we reach here, it means we're selecting a new SAP
            console.log(`Selecting SAP ${sapId} for all datapoints with base key ${baseDatapointKey}`);

            // 1. Add this SAP to the selected SAPs list if not already there
                setSelectedSAPBK(prev => {
                    if (!prev.some(s => s.id === sapId)) {
                        console.log(`Adding SAP ${sapId} to selected SAPs list`);
                        return [...prev, sap];
                    }
                    return prev;
                });

                setSelectedSAP(prev => {
                    if (!prev.some(s => s.id === sapId)) {
                        return [...prev, sap];
                    }
                    return prev;
                });

                // 1.5. Track which indicator this SAP is mapped to
                setSapIndicatorMapping(prev => {
                    const newMapping = { ...prev };
                    newMapping[sapId] = rowData.indicatorId;
                    console.log(`Mapping SAP ${sapId} to indicator ${rowData.indicatorId}`);
                    return newMapping;
                });

                // 2. Find all datapoints with the same base key
                const datapointsWithSameBaseKey = [];

                tableData.forEach(row => {
                    const rowBaseKey = allDatapointBaseKeys[row.datapointKey];

                    // If this row is for the same datapoint
                    if (rowBaseKey === baseDatapointKey) {
                        datapointsWithSameBaseKey.push({
                            datapointKey: row.datapointKey,
                            indicatorId: row.indicatorId,
                            saps: row.saps || []
                        });
                    }
                });

                // 3. First, unselect any other SAPs for this datapoint across all indicators
                // This ensures we only have one SAP selected per datapoint
                setSelectedSAPBK(prev => {
                    const currentlySelectedSaps = [...prev];
                    const sapsToRemove = [];

                    // Check each selected SAP
                    Object.entries(datapointSapSelections).forEach(([dpKey, selectedSapId]) => {
                        // If this is for the same base datapoint but not the SAP we're selecting now
                        if (allDatapointBaseKeys[dpKey] === baseDatapointKey && selectedSapId !== sapId) {
                            sapsToRemove.push(selectedSapId);
                            console.log(`Will unselect SAP ${selectedSapId} because we're selecting SAP ${sapId} for the same datapoint`);
                        }
                    });

                    // Remove these SAPs from the selected list
                    return currentlySelectedSaps.filter(s => !sapsToRemove.includes(s.id));
                });

                setSelectedSAP(prev => {
                    return prev.filter(s => {
                        // Check if this SAP is selected for this datapoint
                        let isSelectedForThisDatapoint = false;
                        Object.entries(datapointSapSelections).forEach(([dpKey, selectedSapId]) => {
                            if (allDatapointBaseKeys[dpKey] === baseDatapointKey && selectedSapId === s.id) {
                                isSelectedForThisDatapoint = true;
                            }
                        });

                        // Keep this SAP if it's not selected for this datapoint or if it's the one we're selecting now
                        return !isSelectedForThisDatapoint || s.id === sapId;
                    });
                });

                // 4. Update datapointSapSelections for all these datapoints
                setDatapointSapSelections(prev => {
                    const newSelections = { ...prev };

                    // First, remove any existing SAP selections for this datapoint
                    Object.keys(newSelections).forEach(dpKey => {
                        if (allDatapointBaseKeys[dpKey] === baseDatapointKey) {
                            delete newSelections[dpKey];
                        }
                    });

                    // For each datapoint with the same base key
                    datapointsWithSameBaseKey.forEach(dp => {
                        // Set the new SAP selection, but only if this datapoint has this SAP available
                        if (dp.saps.some(s => s.id === sapId)) {
                            newSelections[dp.datapointKey] = sapId;
                            console.log(`  - Setting SAP ${sapId} for datapoint ${dp.datapointKey}`);
                        }
                    });

                    return newSelections;
                });

                // 5. Update sapSelections for all indicators
                setSapSelections(prev => {
                    const newSelections = { ...prev };

                    // First, unselect any other SAPs for this datapoint across all indicators
                    Object.keys(newSelections).forEach(indId => {
                        if (newSelections[indId] && newSelections[indId].multipleSaps) {
                            // Find all SAPs that are currently selected for this datapoint
                            const sapsToUnselect = [];

                            // Check each datapoint-SAP selection
                            Object.entries(datapointSapSelections).forEach(([dpKey, selectedSapId]) => {
                                // If this is for the same base datapoint but not the SAP we're selecting now
                                if (allDatapointBaseKeys[dpKey] === baseDatapointKey && selectedSapId !== sapId) {
                                    sapsToUnselect.push(selectedSapId);
                                }
                            });

                            // Unselect these SAPs in this indicator
                            newSelections[indId].multipleSaps.forEach(sap => {
                                if (sapsToUnselect.includes(sap.id)) {
                                    sap.selected = false;
                                }
                            });
                        }
                    });

                    // Get unique indicator IDs
                    const uniqueIndicatorIds = [...new Set(datapointsWithSameBaseKey.map(dp => dp.indicatorId))];

                    console.log(`Updating SAP selections for ${uniqueIndicatorIds.length} indicators`);

                    // For each indicator
                    uniqueIndicatorIds.forEach(indId => {
                        // Initialize if not exists
                        if (!newSelections[indId]) {
                            newSelections[indId] = {
                                singleSap: null,
                                multipleSaps: []
                            };
                        }

                        // Find the SAP in multipleSaps
                        const sapIndex = newSelections[indId].multipleSaps.findIndex(s => s.id === sapId);

                        if (sapIndex === -1) {
                            // Add new SAP selection
                            newSelections[indId].multipleSaps.push({
                                id: sapId,
                                selected: true
                            });
                        } else {
                            // Update existing SAP selection
                            newSelections[indId].multipleSaps[sapIndex].selected = true;
                        }
                    });

                    return newSelections;
                });
        };

        // SAP column template with radio button functionality
        const sapTemplate = (rowData) => {
            // Handle N/A case for non-first DCF rows
            if (rowData.sapNames === 'N/A') {
                return (
                    <div className="sap-cell">
                        <span className="text-gray-500 italic">N/A</span>
                    </div>
                );
            }

            if (!rowData.saps || rowData.saps.length === 0) {
                return (
                    <div className="sap-cell">
                        <span className="text-gray-500 italic">No SAP Collections</span>
                    </div>
                );
            }

            // Extract the base datapoint key for this row
            let baseDatapointKey = null;
            if (rowData.datapointKey.startsWith('standalone-')) {
                baseDatapointKey = 'standalone';
            } else if (rowData.datapoint) {
                baseDatapointKey = rowData.datapoint.suffix || rowData.datapoint.title || rowData.datapoint.id;
            }

            // Build a map of all datapoint keys to their base keys
            const allDatapointBaseKeys = {};
            tableData.forEach(row => {
                if (row.datapointKey) {
                    let rowBaseKey = null;
                    if (row.datapointKey.startsWith('standalone-')) {
                        rowBaseKey = 'standalone';
                    } else if (row.datapoint) {
                        rowBaseKey = row.datapoint.suffix || row.datapoint.title || row.datapoint.id;
                    }
                    if (rowBaseKey) {
                        allDatapointBaseKeys[row.datapointKey] = rowBaseKey;
                    }
                }
            });

            // Create a map of base datapoint keys to their selected SAPs
            let datapointSapMap = {};
            Object.entries(datapointSapSelections).forEach(([dpKey, selectedSapId]) => {
                const otherBaseKey = allDatapointBaseKeys[dpKey];
                if (otherBaseKey) {
                    datapointSapMap[otherBaseKey] = selectedSapId;
                }
            });

            return (
                <div className="sap-cell" style={{
                    padding: '8px',
                    borderRadius: '4px',
                    backgroundColor: '#f0f9ff'
                }}>
                    <div className="sap-names">
                        {rowData.saps.map((sap, index) => {
                            // Check if this SAP is selected globally
                            const isSapSelectedGlobally = selectedsap.some(s => s.id === sap.id);

                            // Check if this SAP is mapped to a different indicator
                            const sapMappedToIndicator = sapIndicatorMapping[sap.id];
                            const isSapMappedToDifferentIndicator = sapMappedToIndicator && sapMappedToIndicator !== rowData.indicatorId;

                            // Check if any SAP is selected for this datapoint
                            const anySapSelectedForThisDatapoint = baseDatapointKey && datapointSapMap[baseDatapointKey];

                            // Check if this specific SAP is selected for this datapoint
                            const thisSpecificSapIsSelected = baseDatapointKey && datapointSapMap[baseDatapointKey] === sap.id;

                            // Determine if this checkbox should be disabled
                            let isDisabled = false;

                            // If this SAP is mapped to a different indicator, disable it
                            if (isSapMappedToDifferentIndicator) {
                                isDisabled = true;
                                console.log(`Disabling SAP ${sap.id} for indicator ${rowData.indicatorId} because it's mapped to indicator ${sapMappedToIndicator}`);
                            }

                            // If this datapoint has multiple SAPs, check if we should disable this one
                            if (rowData.saps.length > 1) {
                                // If any SAP is selected for this datapoint and it's not this one, disable this checkbox
                                if (anySapSelectedForThisDatapoint && !thisSpecificSapIsSelected && !isSapSelectedGlobally) {
                                    isDisabled = true;
                                }
                            }

                            // Important: Never disable a checkbox for a SAP that is already selected globally for THIS indicator
                            if (isSapSelectedGlobally && !isSapMappedToDifferentIndicator) {
                                isDisabled = false;
                            }

                            return (
                                <div key={sap.id} className="sap-item" style={{
                                    marginBottom: index < rowData.saps.length - 1 ? '4px' : '0',
                                    display: 'flex',
                                    alignItems: 'center',
                                    backgroundColor: isSapSelectedGlobally ? '#e6f7ff' : 'transparent',
                                    padding: '4px',
                                    borderRadius: '4px'
                                }}>
                                    <div
                                        onClick={(e) => {
                                            if (!isDisabled) {
                                                // Create a synthetic event object for compatibility
                                                const syntheticEvent = {
                                                    checked: !isSapSelectedGlobally, // Toggle the current state
                                                    target: e.target
                                                };
                                                handleSapCheckboxChange(syntheticEvent, rowData, sap.id);
                                            }
                                        }}
                                        style={{ cursor: isDisabled ? 'not-allowed' : 'pointer', display: 'inline-block' }}
                                    >
                                        <RadioButton
                                            inputId={`sap-${sap.id}-${rowData.indicatorId}-${rowData.datapointKey}`}
                                            checked={isSapSelectedGlobally}
                                            disabled={isDisabled}
                                            className="mr-2"
                                            style={{ pointerEvents: 'none' }} // Prevent the radio button's own click handling
                                        />
                                    </div>
                                    <span className="sap-name text-sm">{sap.name || sap.title || `SAP ID: ${sap.id}`}</span>
                                    {isSapMappedToDifferentIndicator && (
                                        <span className="ml-2 p-1 text-xs bg-red-100 text-red-800 rounded">
                                            Mapped to Indicator {sapMappedToIndicator}
                                        </span>
                                    )}
                                    {rowData.saps.length > 1 && (
                                        <span className="ml-1 p-1 text-xs bg-blue-100 text-blue-800 rounded">#{index + 1}</span>
                                    )}
                                </div>
                            );
                        })}
                    </div>
                    {rowData.isSharedDatapoint && (
                        <span className="ml-2 p-1 text-xs bg-green-100 text-green-800 rounded">Shared</span>
                    )}
                </div>
            );
        };

        // Handle DCF radio button change
        const handleDcfCheckboxChange = (e, rowData) => {
            const { dcfId, datapointKey } = rowData;

            // ALWAYS log this first to confirm handler is called
            console.log(`🔥🔥🔥 DCF ${dcfId} CLICKED - Handler triggered!`);
            console.log(`Event details:`, { checked: e.checked, target: e.target });
            console.log(`Row data:`, { dcfId, datapointKey, dcfName: rowData.dcfName });

            console.log(`Current state analysis:`, {
                dcfId,
                datapointKey,
                selecteddcf_ids: selecteddcf?.map(d => d.id) || [],
                selecteddcfbk_ids: selecteddcfbk?.map(d => d.id) || [],
                currentDatapointSelections: datapointDcfSelections,
                rawdcf_count: rawdcf?.length || 0
            });

            // Get the selected DCF
            const dcf = rawdcf.find(d => d.id === dcfId);
            if (!dcf) {
                console.log(`❌ DCF ${dcfId} not found in rawdcf`);
                return;
            }

            // Check current selection status
            const isDcfCurrentlySelected = selecteddcf.some(d => d.id === dcfId);

            console.log(`Selection status check:`, {
                isDcfCurrentlySelected,
                dcfId,
                eventChecked: e.checked,
                willToggleTo: e.checked ? 'SELECT' : 'UNSELECT'
            });

            // Use the synthetic event's checked property to determine action
            if (!e.checked) {
                // UNSELECT: The synthetic event indicates we should unselect
                console.log(`🔴 UNSELECTING DCF ${dcfId} from ALL datapoints`);

                // Remove from selected DCFs list completely
                setSelectedDCFBK(prev => {
                    const filtered = prev.filter(d => d.id !== dcfId);
                    console.log(`Updated selectedDCFBK: ${filtered.map(d => d.id).join(', ')}`);
                    return filtered;
                });

                setSelectedDCF(prev => {
                    const filtered = prev.filter(d => d.id !== dcfId);
                    console.log(`Updated selectedDCF: ${filtered.map(d => d.id).join(', ')}`);
                    return filtered;
                });

                // Remove from all datapoint-DCF selections
                setDatapointDcfSelections(prev => {
                    const newSelections = { ...prev };
                    const removedDatapoints = [];

                    Object.keys(newSelections).forEach(dpKey => {
                        if (newSelections[dpKey] === dcfId) {
                            delete newSelections[dpKey];
                            removedDatapoints.push(dpKey);
                        }
                    });

                    console.log(`Removed DCF ${dcfId} from datapoints:`, removedDatapoints);
                    console.log(`Updated datapointDcfSelections:`, newSelections);
                    return newSelections;
                });

                // Remove from DCF-indicator mapping
                setDcfIndicatorMapping(prev => {
                    const newMapping = { ...prev };
                    delete newMapping[dcfId];
                    console.log(`Removed DCF ${dcfId} from indicator mapping`);
                    return newMapping;
                });

                // Update DCF selections for all indicators
                setDcfSelections(prev => {
                    const newSelections = { ...prev };
                    Object.keys(newSelections).forEach(indId => {
                        if (newSelections[indId] && newSelections[indId].multipleDcfs) {
                            newSelections[indId].multipleDcfs.forEach(dcfObj => {
                                if (dcfObj.id === dcfId) {
                                    dcfObj.selected = false;
                                    console.log(`Unselected DCF ${dcfId} for indicator ${indId}`);
                                }
                            });
                        }
                    });
                    return newSelections;
                });

                // Force a re-render to update the disabled state of all radio buttons
                setTimeout(() => {
                    console.log(`🔄 Forcing re-render after unselecting DCF ${dcfId}`);
                    forceUpdate();
                }, 100);

                return;
            }

            // SELECT: The synthetic event indicates we should select
            console.log(`🟢 SELECTING DCF ${dcfId} for all datapoints`);

            // Extract the base datapoint key (without the indicator prefix)
            // This helps identify the same datapoint across different indicators
            let baseDatapointKey = null;

            // If it's a standalone indicator, use the standalone key
            if (datapointKey.startsWith('standalone-')) {
                baseDatapointKey = 'standalone';
            }
            // If it's a regular datapoint, extract the datapoint name/id
            else if (rowData.datapoint) {
                baseDatapointKey = rowData.datapoint.suffix || rowData.datapoint.title || rowData.datapoint.id;
            }

            // Build a map of all datapoint keys to their base keys
            const allDatapointBaseKeys = {};
            tableData.forEach(row => {
                if (row.datapointKey) {
                    let rowBaseKey = null;

                    // For standalone indicators
                    if (row.datapointKey.startsWith('standalone-')) {
                        rowBaseKey = 'standalone';
                    }
                    // For regular datapoints, extract from the datapoint object
                    else if (row.datapoint) {
                        rowBaseKey = row.datapoint.suffix || row.datapoint.title || row.datapoint.id;
                    }

                    if (rowBaseKey) {
                        allDatapointBaseKeys[row.datapointKey] = rowBaseKey;
                    }
                }
            });

            // If we reach here, it means we're selecting a new DCF
            console.log(`🟢 SELECTING DCF ${dcfId} for all datapoints with base key ${baseDatapointKey}`);

            // When a DCF is selected, select it for ALL datapoints with the same base key

            // 1. Add this DCF to the selected DCFs list if not already there
            setSelectedDCFBK(prev => {
                if (!prev.some(d => d.id === dcfId)) {
                    console.log(`Adding DCF ${dcfId} to selected DCFs list`);
                    return [...prev, dcf];
                }
                return prev;
            });

            setSelectedDCF(prev => {
                if (!prev.some(d => d.id === dcfId)) {
                    return [...prev, dcf];
                }
                return prev;
            });

            // 1.5. Track which indicator this DCF is mapped to
            setDcfIndicatorMapping(prev => {
                const newMapping = { ...prev };
                newMapping[dcfId] = rowData.indicatorId;
                console.log(`Mapping DCF ${dcfId} to indicator ${rowData.indicatorId}`);
                return newMapping;
            });

            // 2. Find all datapoints with the same base key and all DCFs for this datapoint
            const datapointsWithSameBaseKey = [];
            const allDcfsForThisDatapoint = new Set();

            // Go through tableData to find all datapoints with the same base key
            // and collect all DCFs available for this datapoint
            tableData.forEach(row => {
                const rowBaseKey = allDatapointBaseKeys[row.datapointKey];

                // If this row is for the same datapoint
                if (rowBaseKey === baseDatapointKey) {
                    datapointsWithSameBaseKey.push({
                        datapointKey: row.datapointKey,
                        indicatorId: row.indicatorId,
                        dcfId: row.dcfId
                    });

                    // Add this DCF to the set of all DCFs for this datapoint
                    if (row.dcfId) {
                        allDcfsForThisDatapoint.add(row.dcfId);
                    }
                }
            });

            console.log(`Found ${datapointsWithSameBaseKey.length} datapoints with base key ${baseDatapointKey}`);
            console.log(`Found ${allDcfsForThisDatapoint.size} DCFs for datapoint ${baseDatapointKey}: ${[...allDcfsForThisDatapoint].join(', ')}`);

            // 3. First, unselect any other DCFs for this datapoint across all indicators
            // This ensures we only have one DCF selected per datapoint
            setSelectedDCFBK(prev => {
                // Get all currently selected DCFs
                const currentlySelectedDcfs = [...prev];

                // Find all DCFs that are currently selected for this datapoint
                const dcfsToRemove = [];

                // Check each selected DCF
                Object.entries(datapointDcfSelections).forEach(([dpKey, selectedDcfId]) => {
                    // If this is for the same base datapoint but not the DCF we're selecting now
                    if (allDatapointBaseKeys[dpKey] === baseDatapointKey && selectedDcfId !== dcfId) {
                        // Add this DCF to the list to remove
                        dcfsToRemove.push(selectedDcfId);
                        console.log(`Will unselect DCF ${selectedDcfId} because we're selecting DCF ${dcfId} for the same datapoint`);
                    }
                });

                // Remove these DCFs from the selected list
                return currentlySelectedDcfs.filter(d => !dcfsToRemove.includes(d.id));
            });

            // Also update the filtered list
            setSelectedDCF(prev => {
                return prev.filter(d => {
                    // Check if this DCF is selected for this datapoint
                    let isSelectedForThisDatapoint = false;
                    Object.entries(datapointDcfSelections).forEach(([dpKey, selectedDcfId]) => {
                        if (allDatapointBaseKeys[dpKey] === baseDatapointKey && selectedDcfId === d.id) {
                            isSelectedForThisDatapoint = true;
                        }
                    });

                    // Keep this DCF if it's not selected for this datapoint or if it's the one we're selecting now
                    return !isSelectedForThisDatapoint || d.id === dcfId;
                });
            });

            // 4. Update datapointDcfSelections for all these datapoints
            setDatapointDcfSelections(prev => {
                const newSelections = { ...prev };

                // First, remove any existing DCF selections for this datapoint
                Object.keys(newSelections).forEach(dpKey => {
                    if (allDatapointBaseKeys[dpKey] === baseDatapointKey) {
                        delete newSelections[dpKey];
                    }
                });

                // For each datapoint with the same base key
                datapointsWithSameBaseKey.forEach(dp => {
                    // Set the new DCF selection, but only if this datapoint has this DCF available
                    if (dp.dcfId === dcfId) {
                        newSelections[dp.datapointKey] = dcfId;
                        console.log(`  - Setting DCF ${dcfId} for datapoint ${dp.datapointKey}`);
                    }
                });

                return newSelections;
            });

            // 5. Update dcfSelections for all indicators
            setDcfSelections(prev => {
                const newSelections = { ...prev };

                // First, unselect any other DCFs for this datapoint across all indicators
                Object.keys(newSelections).forEach(indId => {
                    if (newSelections[indId] && newSelections[indId].multipleDcfs) {
                        // Find all DCFs that are currently selected for this datapoint
                        const dcfsToUnselect = [];

                        // Check each datapoint-DCF selection
                        Object.entries(datapointDcfSelections).forEach(([dpKey, selectedDcfId]) => {
                            // If this is for the same base datapoint but not the DCF we're selecting now
                            if (allDatapointBaseKeys[dpKey] === baseDatapointKey && selectedDcfId !== dcfId) {
                                // Add this DCF to the list to unselect
                                dcfsToUnselect.push(selectedDcfId);
                            }
                        });

                        // Unselect these DCFs in this indicator
                        newSelections[indId].multipleDcfs.forEach(dcf => {
                            if (dcfsToUnselect.includes(dcf.id)) {
                                dcf.selected = false;
                            }
                        });
                    }
                });

                // Get unique indicator IDs
                const uniqueIndicatorIds = [...new Set(datapointsWithSameBaseKey.map(dp => dp.indicatorId))];

                console.log(`Updating DCF selections for ${uniqueIndicatorIds.length} indicators`);

                // For each indicator
                uniqueIndicatorIds.forEach(indId => {
                    // Initialize if not exists
                    if (!newSelections[indId]) {
                        newSelections[indId] = {
                            singleDcf: null,
                            multipleDcfs: []
                        };
                    }

                    // Find the DCF in multipleDcfs
                    const dcfIndex = newSelections[indId].multipleDcfs.findIndex(d => d.id === dcfId);

                    if (dcfIndex === -1) {
                        // Add new DCF selection
                        newSelections[indId].multipleDcfs.push({
                            id: dcfId,
                            selected: true
                        });
                    } else {
                        // Update existing DCF selection
                        newSelections[indId].multipleDcfs[dcfIndex].selected = true;
                    }
                });

                return newSelections;
            });
        };

        // DCF column template with radio button
        const dcfTemplate = (rowData) => {
            if (!rowData.dcf) {
                return (
                    <div className="dcf-cell">
                        <span className="text-gray-500 italic">No DCF</span>
                    </div>
                );
            }

            // Check if this DCF is selected globally (in the selected DCFs list)
            const isDcfSelectedGlobally = selecteddcf.some(d => d.id === rowData.dcfId);

            // Check if this DCF is mapped to a different indicator
            const dcfMappedToIndicator = dcfIndicatorMapping[rowData.dcfId];
            const isDcfMappedToDifferentIndicator = dcfMappedToIndicator && dcfMappedToIndicator !== rowData.indicatorId;

            // Determine if this DCF is the currently selected one for this datapoint
            const isDatapointSelected = datapointDcfSelections[rowData.datapointKey] === rowData.dcfId;

            // Extract the base datapoint key (without the indicator prefix)
            // This helps identify the same datapoint across different indicators
            let baseDatapointKey = null;

            // If it's a standalone indicator, use the standalone key
            if (rowData.datapointKey.startsWith('standalone-')) {
                baseDatapointKey = 'standalone';
            }
            // If it's a regular datapoint, extract the datapoint name/id
            else if (rowData.datapoint) {
                baseDatapointKey = rowData.datapoint.suffix || rowData.datapoint.title || rowData.datapoint.id;
            }

            // Get the base datapoint key for all datapoints in the table
            const allDatapointBaseKeys = {};

            // Build a map of all datapoint keys to their base keys
            tableData.forEach(row => {
                if (row.datapointKey) {
                    let rowBaseKey = null;

                    // For standalone indicators
                    if (row.datapointKey.startsWith('standalone-')) {
                        rowBaseKey = 'standalone';
                    }
                    // For regular datapoints, extract from the datapoint object
                    else if (row.datapoint) {
                        rowBaseKey = row.datapoint.suffix || row.datapoint.title || row.datapoint.id;
                    }

                    if (rowBaseKey) {
                        allDatapointBaseKeys[row.datapointKey] = rowBaseKey;
                    }
                }
            });

            // Create a map of base datapoint keys to their selected DCFs
            let datapointDcfMap = {};

            // Look through all datapoint-DCF selections to find which DCFs are selected for each base datapoint
            Object.entries(datapointDcfSelections).forEach(([dpKey, selectedDcfId]) => {
                const otherBaseKey = allDatapointBaseKeys[dpKey];

                // If we found a valid datapoint with a base key
                if (otherBaseKey) {
                    // Store the selected DCF for this base datapoint
                    datapointDcfMap[otherBaseKey] = selectedDcfId;
                }
            });

            // Determine if this radio button should be disabled
            let isDisabled = false;

            // Check if any DCF is selected for this datapoint across all indicators
            const anyDcfSelectedForThisDatapoint = baseDatapointKey && datapointDcfMap[baseDatapointKey];

            // Check if this specific DCF is selected for this datapoint
            const thisSpecificDcfIsSelected = baseDatapointKey &&
                                             datapointDcfMap[baseDatapointKey] === rowData.dcfId;

            // Check if this DCF is selected globally (in the selected DCFs list)
            const thisDcfIsSelectedGlobally = isDcfSelectedGlobally;

            // Find all DCFs available for this datapoint
            const allDcfsForThisDatapoint = [];
            tableData.forEach(row => {
                // Check if this row is for the same datapoint
                if (allDatapointBaseKeys[row.datapointKey] === baseDatapointKey && row.dcfId) {
                    allDcfsForThisDatapoint.push(row.dcfId);
                }
            });

            // Check if any DCF is selected in the global list for this datapoint
            const anyDcfSelectedGloballyForThisDatapoint = selecteddcf.some(d => {
                // Check if this DCF is used by any datapoint with the same base key
                return tableData.some(row => {
                    return allDatapointBaseKeys[row.datapointKey] === baseDatapointKey &&
                           row.dcfId === d.id;
                });
            });

            // If this datapoint has multiple DCFs, check if we should disable this one
            if (allDcfsForThisDatapoint.length > 1) {
                // If any DCF is selected for this datapoint (either in datapointDcfSelections or globally)
                // and it's not this one, disable this radio button
                if ((anyDcfSelectedForThisDatapoint || anyDcfSelectedGloballyForThisDatapoint) &&
                    !thisSpecificDcfIsSelected && !thisDcfIsSelectedGlobally) {
                    isDisabled = true;

                    // Find which DCF is selected for this datapoint
                    let selectedDcfId = datapointDcfMap[baseDatapointKey];

                    // If no DCF is found in datapointDcfMap, find it in the global selections
                    if (!selectedDcfId) {
                        selecteddcf.forEach(d => {
                            if (tableData.some(row => {
                                return allDatapointBaseKeys[row.datapointKey] === baseDatapointKey &&
                                       row.dcfId === d.id;
                            })) {
                                selectedDcfId = d.id;
                            }
                        });
                    }

                    console.log(`Disabling DCF ${rowData.dcfId} for datapoint ${baseDatapointKey} because DCF ${selectedDcfId} is already selected`);
                }
            }

            // If this DCF is mapped to a different indicator, disable it
            if (isDcfMappedToDifferentIndicator) {
                isDisabled = true;
                console.log(`Disabling DCF ${rowData.dcfId} for indicator ${rowData.indicatorId} because it's mapped to indicator ${dcfMappedToIndicator}`);
            }

            // Important: Never disable a radio button for a DCF that is already selected globally for THIS indicator
            if (thisDcfIsSelectedGlobally && !isDcfMappedToDifferentIndicator) {
                isDisabled = false;
            }

            // // Handle DCF ID 31 the same as other DCFs, but with a slight visual indicator
            // if (rowData.dcfId === 31) {
            //     return (
            //         <div className="dcf-cell" style={{
            //             padding: '8px',
            //             borderRadius: '4px',
            //             backgroundColor: isDcfSelectedGlobally ? '#e6f7ff' : 'transparent'
            //         }}>
            //             <div
            //                 onClick={(e) => {
            //                     if (!isDisabled) {
            //                         // Create a synthetic event object for compatibility
            //                         const syntheticEvent = {
            //                             checked: !isDcfSelectedGlobally, // Toggle the current state
            //                             target: e.target
            //                         };
            //                         handleDcfCheckboxChange(syntheticEvent, rowData);
            //                     }
            //                 }}
            //                 style={{ cursor: isDisabled ? 'not-allowed' : 'pointer', display: 'inline-block' }}
            //             >
            //                 <RadioButton
            //                     inputId={`dcf-31-${rowData.indicatorId}`}
            //                     checked={isDcfSelectedGlobally}
            //                     disabled={isDisabled}
            //                     className="mr-2"
            //                     style={{ pointerEvents: 'none' }} // Prevent the radio button's own click handling
            //                 />
            //             </div>
            //             <span className="dcf-name">
            //                 {rowData.dcfName || `DCF ID: 31`}
            //             </span>
            //             {isDcfMappedToDifferentIndicator && (
            //                 <span className="ml-2 p-1 text-xs bg-red-100 text-red-800 rounded">
            //                     Mapped to Indicator {dcfMappedToIndicator}
            //                 </span>
            //             )}
            //             {rowData.isSharedDatapoint && (
            //                 <span className="ml-2 p-1 text-xs bg-yellow-100 text-yellow-800 rounded">Shared</span>
            //             )}
            //         </div>
            //     );
            // }

            return (
                <div className="dcf-cell" style={{
                    padding: '8px',
                    borderRadius: '4px',
                    backgroundColor: isDcfSelectedGlobally ? '#e6f7ff' : 'transparent'
                }}>
                    <div
                        onClick={(e) => {
                            if (!isDisabled) {
                                // Create a synthetic event object for compatibility
                                const syntheticEvent = {
                                    checked: !isDcfSelectedGlobally, // Toggle the current state
                                    target: e.target
                                };
                                handleDcfCheckboxChange(syntheticEvent, rowData);
                            }
                        }}
                        style={{ cursor: isDisabled ? 'not-allowed' : 'pointer', display: 'inline-block' }}
                    >
                        <RadioButton
                            inputId={`dcf-${rowData.dcfId}-${rowData.indicatorId}`}
                            checked={isDcfSelectedGlobally}
                            disabled={isDisabled}
                            className="mr-2"
                            style={{ pointerEvents: 'none' }} // Prevent the radio button's own click handling
                        />
                    </div>
                    <span className="dcf-name">{rowData.dcfId +' : '+rowData.dcfName}</span>
                    {isDcfMappedToDifferentIndicator && (
                        <span className="ml-2 p-1 text-xs bg-red-100 text-red-800 rounded">
                            Mapped to Indicator {dcfMappedToIndicator}
                        </span>
                    )}
                    {rowData.isSharedDatapoint && (
                        <span className="ml-2 p-1 text-xs bg-yellow-100 text-yellow-800 rounded">Shared</span>
                    )}

                </div>
            );
        };

        // Calculate available indicators from the actual table data (without useMemo)
        const tableAvailableIndicators = (() => {
            const uniqueIndicators = [];
            const seenIds = new Set();

            tableData.forEach(row => {
                if (!seenIds.has(row.indicatorId)) {
                    seenIds.add(row.indicatorId);
                    uniqueIndicators.push({
                        id: row.indicatorId,
                        name: row.indicatorName,
                        label: `${row.indicatorId} - ${row.indicatorName}`
                    });
                }
            });

            return uniqueIndicators.sort((a, b) => a.id - b.id);
        })();

        // Filter table data based on selected indicator
        // Only show data when an indicator is selected
        const filteredTableData = selectedIndicatorFilter
            ? tableData.filter(row => row.indicatorId === selectedIndicatorFilter.id)
            : []; // Show empty table when no indicator is selected

        return (
            <div>
                {/* Indicator Filter Dropdown */}
                <div className="mb-4 p-3 surface-0 shadow-1 border-round">
                    <div className="flex align-items-center gap-3">
                        <i className="pi pi-filter text-primary" style={{ fontSize: '1.2rem' }}></i>
                        <label htmlFor="indicator-filter" className="font-bold text-primary">
                            Filter by Indicator:
                        </label>
                        <Dropdown
                            id="indicator-filter"
                            value={selectedIndicatorFilter}
                            options={tableAvailableIndicators}
                            onChange={(e) => setSelectedIndicatorFilter(e.value)}
                            optionLabel="label"
                            placeholder="Select an indicator to view its DCF/SAP mappings"
                            className="w-full md:w-20rem"
                            showClear
                            filter
                            filterBy="label"
                            emptyMessage="No indicators available"
                        />
                        {selectedIndicatorFilter ? (
                            <div className="text-sm text-600">
                                Showing {filteredTableData.length} datapoint(s) for indicator {selectedIndicatorFilter.id}
                            </div>
                        ) : (
                            <div className="text-sm text-orange-600 font-medium">
                                <i className="pi pi-info-circle mr-2"></i>
                                Please select an indicator to view and manage its DCF/SAP mappings
                            </div>
                        )}
                    </div>
                </div>

                {/* DataTable without Indicator column */}
                <DataTable
                    value={filteredTableData}
                    scrollable
                    scrollHeight="75vh"
                    className="p-datatable-sm"
                    emptyMessage={
                        selectedIndicatorFilter ?
                            `No datapoints found for indicator ${selectedIndicatorFilter.id} - ${selectedIndicatorFilter.name}` :
                            "Please select an indicator from the dropdown above to view and manage its DCF/SAP mappings"
                    }
                >
                    <Column
                        field="datapointName"
                        header="Associated Datapoint"
                        body={datapointTemplate}
                        style={{ width: '35%' }}
                    />
                    <Column
                        field="dcfName"
                        header="Mapped DCF"
                        body={dcfTemplate}
                        style={{ width: '32.5%' }}
                    />
                    <Column
                        field="sapName"
                        header="Mapped SAP"
                        body={sapTemplate}
                        style={{ width: '32.5%' }}
                    />
                </DataTable>
            </div>
        );
    };
    const checkDependency = (ind, sMetric, id) => {
        let sa = []
        standAlone = []
        metricbk.forEach((i) => {

            if (i.id !== id && sMetric.includes(i.id) && i.data1[0].type === 0 && i.data1[0].source === 0) {
                console.log(i)
                getStandAlone(i).forEach((met) => {
                    console.log(met, i)
                    let index = metricbk.findIndex((j) => { return j.id === met })
                    if (index !== -1 && metricbk[index].data1[0].type === 0 && metricbk[index].data1[0].source === 1) {

                        sa.push(met)
                    }

                })
            }
        })
        console.log(sa)
        return !sa.includes(sMetric[ind])
    }
    const checkDependency_ = (ind, sMetric, id) => {
        let sa = []
        metricbk.forEach((i) => {

            if (i.id !== id && sMetric.includes(i.id) && i.data1[0].type === 0 && i.data1[0].source === 0) {

                getStandAlone(i).forEach((met) => {
                    sa.push(met)
                })
            }
        })

        return !sa.includes(sMetric[ind])
    }
    const onCFChange = (e, item) => {
        item.selected = e.checked
        let index = cfbklist.findIndex((i) => { return item.id === i.id })
        cfbklist[index]['selected'] = e.checked

        forceUpdate()
        console.log(cflist)
    }
    const onMetricChange = (e, item, type) => {
        console.log(`Indicator selection changed: ${item.id} (${item.title}) - ${e.checked ? 'selected' : 'unselected'}`);

        let disabledIds = []
        if (type === 1 || type === 3) {
            item.selected = e.checked
            let indx = usermetric.findIndex((i) => { return i === item.id })
            if (e.checked) {
                if (!usermetric.includes(item.id)) { usermetric.push(item.id) }

                // IMPORTANT: When an indicator is selected, also select its DCF if it has one
                if (Array.isArray(item.data1) &&
                    item.data1.length === 1 &&
                    item.data1[0].type === 2 &&
                    item.data1[0].dcfId) {

                    // Check if this DCF is already selected
                    if (!selecteddcfbk.some(d => d.id === item.data1[0].dcfId)) {
                        console.log(`Auto-selecting DCF ID ${item.data1[0].dcfId} from newly selected indicator ${item.id}`);

                        // Find the DCF in rawdcf
                        const dcf_index = rawdcf.findIndex(i => i.id === item.data1[0].dcfId);

                        if (dcf_index !== -1) {
                            // DCF found in rawdcf
                            const newSelectedDcfs = [...selecteddcfbk, rawdcf[dcf_index]];
                            setSelectedDCFBK(newSelectedDcfs);
                            setSelectedDCF(newSelectedDcfs);
                        } else {
                            // DCF not found in rawdcf, create a placeholder
                            const placeholder = {
                                id: item.data1[0].dcfId,
                                title: `DCF ID: ${item.data1[0].dcfId}`,
                                tags: null
                            };
                            const newSelectedDcfs = [...selecteddcfbk, placeholder];
                            setSelectedDCFBK(newSelectedDcfs);
                            setSelectedDCF(newSelectedDcfs);
                        }
                    }
                }
            } else {
                if (indx !== -1) {
                    usermetric.splice(indx, 1)
                }
            }

            updateSelectedIndicators(usermetric, metricbk)

            // After updating selected indicators, force a re-evaluation of DCF disabled states
            setTimeout(() => {
                // Force a re-render to update the disabled state of all checkboxes
                forceUpdate();
            }, 100);

        } else if (type === 2) {
            standAlone = []
            item.data1[0].indicator.forEach((id, j) => { !standAlone.includes(id) && standAlone.push(id) })
            getStandAlone(item).forEach((id) => {

                if (!disabledIds.includes(id)) { disabledIds.push(id) }
                let indx = usermetric.findIndex((i) => { return i === id })
                let ind1 = metriclist.findIndex((i) => { return i.id === id })

                if (ind1 !== -1) {
                    metriclist[ind1].disabled = e.checked
                    forceUpdate()
                }

                if (e.checked) {
                    if (!usermetric.includes(id)) { usermetric.push(id) }
                } else {
                    if (indx !== -1 && checkDependency(indx, usermetric, item.id)) {
                        usermetric.splice(indx, 1)
                    }
                }
            })

            let indx = usermetric.findIndex((i) => { return i === item.id })

            if (e.checked) {
                if (!usermetric.includes(item.id)) { usermetric.push(item.id) }
            } else {
                if (indx !== -1) {
                    usermetric.splice(indx, 1)
                }
            }

            updateSelectedIndicators(usermetric, metricbk)

            // After updating selected indicators, force a re-evaluation of DCF disabled states
            setTimeout(() => {
                // Force a re-render to update the disabled state of all radio buttons
                forceUpdate();
            }, 100);
        }

        metricbk.forEach((i) => {
            if (usermetric.includes(i.id)) {
                i.selected = true
            } else {
                i.selected = false
            }
            if (disabledIds.includes(i.id)) {
                i.disabled = e.checked
            }
        })
        forceUpdate()
    }
    const checkDCF = (item) => {
        let result = true
        if (item.newDataPoints === undefined || item.newDataPoints === null || item.newDataPoints.length === 0) {
            result = true
        } else {
            if (item.newDataPoints.filter((k) => { return k.data1 !== null && Array.isArray(k.data1) && k.data1.length !== 0 && typeof k.data1[0].datasource === 'number' }).length !== 0) {
                result = false
            } else {
                result = true
            }
        }

        return result
    }
    const searchMetric = (e) => {
        let loc = search;
        loc.metric = e.target.value
        let mtr = JSON.parse(JSON.stringify(metricbk))

        setMetricList(mtr.filter((k) => { return (k.title.trim().toLowerCase().includes(e.target.value.trim().toLowerCase()) || (k.tag !== null && getTag(k.tag).trim().toLowerCase().includes(e.target.value.trim().toLowerCase()))) }))
        setSearch(loc)
    }
    const searchDCF = (e) => {
        let loc = search;
        loc.dcf = e.target.value
        let mtr = JSON.parse(JSON.stringify(selecteddcfbk))
        setSelectedDCF(mtr.filter((k) => { return k.title.trim().toLowerCase().includes(e.target.value.trim().toLowerCase()) }))

        setSearch(loc)
    }
    const searchCF = (e) => {
        let loc = search;
        loc.cf = e.target.value.trim()
        let mtr = JSON.parse(JSON.stringify(cfbklist))
        setCFList(mtr.filter((k) => { return k.title.trim().toLowerCase().includes(e.target.value.trim().toLowerCase()) }))

        setSearch(loc)
    }
    const searchDF = (e) => {
        let loc = search;
        loc.df = e.target.value.trim()
        let mtr = JSON.parse(JSON.stringify(cfbklist))
        setDFReqList(dfreqlistbk.filter((k) => { return k.title.trim().toLowerCase().includes(e.target.value.trim().toLowerCase()) }))

        setSearch(loc)
    }
    const removeHTMLTag = (html) => {
        return html.replace(/(<([^>]+)>)/gi, "")
            .replace(/\n/g, " ")
            .replace(/&nbsp;/g, " ")
    }
    const renderPreview = () => {


        if (prevdialog && selectedform.data1.length !== 0) {

            let data = selectedform.data1


            return data.map((field, ind) => {
                if (field.type === 'paragraph') {
                    return (
                        <div className="card   flex-wrap justify-content-center gap-3">

                            <p>{removeHTMLTag(field.label)}</p>
                        </div>
                    )
                } else if (field.type === 'date') {
                    return (
                        <div className="card   flex-wrap justify-content-center gap-3">
                            <label htmlFor={'date' + ind}>{removeHTMLTag(field.label)}</label>
                            <Calendar disabled showIcon ></Calendar>
                        </div>
                    )
                } else if (field.type === 'text') {
                    return (
                        <div className="card   flex-wrap justify-content-center gap-3">
                            <label htmlFor={'text' + ind}>{removeHTMLTag(field.label)}</label>
                            < InputText disabled></InputText>
                        </div>
                    )
                } else if (field.type === 'textarea') {
                    return (
                        <div className="card   flex-wrap justify-content-center gap-3">
                            <label htmlFor={'textarea' + ind}>{removeHTMLTag(field.label)}</label>
                            < InputTextarea disabled></InputTextarea>
                        </div>
                    )
                } else if (field.type === 'number') {
                    return (
                        <div className="card   flex-wrap justify-content-center gap-3">
                            <label htmlFor={'textno' + ind}>{removeHTMLTag(field.label)}</label>
                            < InputText keyfilter={'num'} disabled></InputText>
                        </div>
                    )
                } else if (field.type === 'select') {
                    return (
                        <div className="card   flex-wrap justify-content-center gap-3">
                            <label htmlFor={'select' + ind}>{removeHTMLTag(field.label)}</label>
                            <Dropdown options={field.values} ></Dropdown>
                        </div>
                    )
                } else if (field.type === 'radio-group') {
                    return (
                        <>

                            <div className="card   flex-wrap justify-content-center gap-3">
                                <label htmlFor={'radio' + ind} style={{ marginBottom: 15 }}>{removeHTMLTag(field.label)}</label>
                                {field.values.map((option) => {
                                    return (
                                        <div className="flex align-items-center">
                                            <RadioButton inputId="ingredient1" name={option.label} value={option.value} checked={option.selected} />
                                            <label htmlFor="ingredient1" className="ml-2">{option.label}</label>
                                        </div>
                                    )
                                })

                                }
                            </div>
                        </>
                    )
                } else if (field.type === 'checkbox-group') {
                    return (
                        <>

                            <div className="card   flex-wrap justify-content-center gap-3">
                                <label htmlFor={'checkbox' + ind} style={{ marginBottom: 15 }}>{removeHTMLTag(field.label)}</label>
                                {field.values.map((option) => {
                                    return (
                                        <div className="flex align-items-center">
                                            <Checkbox inputId="ingredient1" name={option.label} value={option.value} checked={option.selected} />
                                            <label htmlFor="ingredient1" className="ml-2">{option.value}</label>
                                        </div>
                                    )
                                })

                                }
                            </div>
                        </>

                    )
                }
            })
        }
    }
    const renderListPreview = () => {
        return (
            <div className="col-12 grid" >
                {selectedlist.data.map((i, j) => {
                    return (
                        <label className="col-12"><b>&nbsp; {i.id} - &nbsp; {i.title}</b> </label>
                    )
                })

                }
            </div>
        )
    }
    const renderListPreview_ = () => {

        return (
            <div className="col-12 grid" >
                {selectedlist.data.map((i, j) => {
                    return (
                        <label className="col-12"><b>&nbsp; {i.id} - &nbsp; {i.title}</b> </label>
                    )
                })

                }
            </div>
        )
    }
    const saveDFConfig = () => {
        let loc = JSON.parse(JSON.stringify(dfass))
        let index = loc.findIndex((i) => { return i.dfid === dfconfig.dfid })
        let loc2 = dfreqlistbk.findIndex((i) => { return i.id === dfconfig.dfid })
        let loc3 = dfreqlist.findIndex((i) => { return i.id === dfconfig.dfid })
        if (index === -1) {

            loc.push(dfconfig)
        } else {
            loc[index] = dfconfig
        }
        dfreqlistbk[loc2].config = dfconfig
        dfreqlist[loc3].config = dfconfig
        setDFAss(loc)
        setDfConfigDialog(false)
        forceUpdate()
    }
    function getDuplicates(arr) {
        const counts = arr.reduce((acc, str) => {
            acc[str] = (acc[str] || 0) + 1;
            return acc;
        }, {});

        return Object.keys(counts).filter((key) => counts[key] > 1);
    }

    const checkDCFForDPDuplication = () => {
        let metrics = JSON.parse(JSON.stringify(metricbk));
        let dup = JSON.parse(JSON.stringify(dupdpid));
        let selectedMetric = metrics.filter((i) => { return i.selected }).map((j) => { return j.id })
        let selectedDataPoints = []
        raw.forEach((k) => {
            if (selectedMetric.findIndex((ind) => { return ind === k.id }) !== -1) {
                if (k.newDataPoints !== undefined) {
                    k.newDataPoints.forEach((k) => {
                        selectedDataPoints.push(k.suffix)
                    })

                }
            }
        })
        let dps = [], duplicatedids = []
        selectedDataPoints.forEach((i) => {
            if (!dps.includes(i)) {
                dps.push(i)
            } else {
                if (!duplicatedids.includes(i)) {
                    duplicatedids.push(i)
                }
            }
        })

        if (dps.length === selectedDataPoints.length) {
            const { result, data } = getCFID()
            if (result) {
                let result_ = getDuplicates(dps.filter(value => data.map(i => i.trim().toLowerCase()).includes(value.trim().toLowerCase())))
                console.log(result_)
                if (result_.length !== 0) {
                    const set1 = new Set(dps.map(item => item.toLowerCase().trim()));
                    const set2 = new Set(data.map(item => item.toLowerCase().trim()));
                    const unionArray = [...new Set([...set1, ...set2])];
                    dup.msg = ' DCF & SRF'
                    dup.data = result_


                    setDupId(dup)

                    forceUpdate()
                    setDupDPIDDialog(true)
                }


            } else {
                dup.msg = 'SRF'
                dup.data = getDuplicates(data.map(i => i?.trim()?.toLowerCase()))

                setDupId(dup)

                setDupDPIDDialog(true)
            }

        } else {
            dup.msg = 'DCF'
            dup.data = duplicatedids


            setDupId(dup)

            setDupDPIDDialog(true)

        }

    }
    const getCFID = () => {
        let loc = JSON.parse(JSON.stringify(cfbklist)).filter((i) => { return i.selected === true })
        let dps = []
        console.log(loc)
        loc.forEach((i) => {
            i.data1.forEach((item) => {

                if (item.type !== 'paragraph' && item.type !== 'htmleditor') {
                    if (item.type !== 'table' && item.type !== 'tableadd') {
                        if (item.name) {
                            dps.push(item.name.trim().toLowerCase())
                        } else {
                            dps.push(null)
                        }

                    } else {
                        Object.values(item.data).forEach((i) => {
                            Object.values(i).forEach((j) => {
                                if (j.type !== 5) {
                                    dps.push(j.data.name.trim().toLowerCase())
                                }
                            })
                        })
                    }
                }
            })
        })

        return { result: [...new Set(dps.map(item => item.toLowerCase().trim()))].length === dps.length, data: dps }
    }
    const checkDCFForDPDuplication_ = () => {
        let metrics = JSON.parse(JSON.stringify(metricbk));
        let dcf = JSON.parse(JSON.stringify(selecteddcfbk));
        let selectedMetric = metrics.filter((i) => { return i.selected }).map((j) => { return j.id })

        let selectedDataPoints = []
        // raw.forEach((k) => {

        //     if (selectedMetric.findIndex((ind) => { return ind === k.id }) !== -1) {

        //         if (k.newDataPoints !== undefined) {
        //             k.newDataPoints.forEach((k) => {
        //                 if (Array.isArray(k.data1) && k.data1[0] !== undefined && k.data1[0].datasource !== undefined && k.data1[0].datasource !== null) {
        //                     let dcf_index = rawdcf.findIndex((dcf) => { return dcf.id === k.data1[0].datasource })
        //                     if (dcf_index !== -1) {
        //                         rawdcf[dcf_index].data1.forEach((item) => {
        //                             if (item.name !== undefined) {
        //                                 selectedDataPoints.push(item.name.trim().toLowerCase())
        //                             }
        //                         })
        //                     }
        //                 }

        //             })

        //         }
        //     }
        // })
        raw.forEach((k) => {
            if (selectedMetric.findIndex((ind) => { return ind === k.id }) !== -1) {
                if (k.newDataPoints !== undefined) {
                    k.newDataPoints.forEach((k) => {
                        selectedDataPoints.push(k.suffix)
                    })

                }
            }
        })
        let dps = [], duplicatedids = []
        selectedDataPoints.forEach((i) => {
            if (!dps.includes(i)) {
                dps.push(i)
            } else {
                if (!duplicatedids.includes(i)) {
                    duplicatedids.push(i)
                }
            }
        })

        if (dps.length === selectedDataPoints.length) {
            const { result, data } = getCFID()
            console.log(result, data)
            if (result) {
                let result_ = dps.filter(value => data.map(i => i.trim().toLowerCase()).includes(value.trim().toLowerCase())).length

                return { result: result_ === 0 ? true : false, msg: 'Duplicate ID found within SRF & DCF' }
            } else {
                return { result: false, msg: 'Duplicate ID found within SRF' }
            }

        } else {
            return { result: false, msg: 'Duplicate ID found within DCF' }
        }

    }
    // Function to prepare assignment summary for confirmation dialog
    const prepareAssignmentSummary = () => {
        let metrics = JSON.parse(JSON.stringify(metricbk));
        let dcf = JSON.parse(JSON.stringify(selecteddcfbk));
        let selectedMetric = metrics.filter((i) => { return i.selected }).map((j) => { return j.id })
        let selectedDataPoints = []

        raw.forEach((k) => {
            if (selectedMetric.findIndex((ind) => { return ind === k.id }) !== -1) {
                if (k.newDataPoints !== undefined) {
                    k.newDataPoints.forEach((k) => {
                        selectedDataPoints.push(k.id)
                    })
                }
            }
        })

        let selectedDCF = dcf.map((k) => { return k.id })
        let selectedSAP = selectedsapbk.map((k) => { return k.id })
        let selectedSRF = cfbklist.filter((i) => { return i.selected === true }).map(k => k.id)

        // Get category details
        const categoryDetails = list.category.filter(cat => selected.category.includes(cat.id));

        // Get topic details
        const topicDetails = list.topic.filter(topic => selected.topic.includes(topic.id));

        // Get indicator details
        const indicatorDetails = metrics.filter(metric => metric.selected);

        // Get DCF details
        const dcfDetails = selecteddcfbk;

        // Get SAP details
        const sapDetails = selectedsapbk;

        // Get SRF details
        const srfDetails = cfbklist.filter((i) => { return i.selected === true });

        return {
            categories: categoryDetails,
            topics: topicDetails,
            indicators: indicatorDetails,
            dcfs: dcfDetails,
            saps: sapDetails,
            srfs: srfDetails
        };
    };

    const saveAssignedDCF = async () => {
        setKey(null);
        let metrics = JSON.parse(JSON.stringify(metricbk));
        let dcf = JSON.parse(JSON.stringify(selecteddcfbk));
        let selectedMetric = metrics.filter((i) => { return i.selected }).map((j) => { return j.id })
        let selectedDataPoints = []
        const { result, msg } = checkDCFForDPDuplication_()
        console.log(result)
        raw.forEach((k) => {
            if (selectedMetric.findIndex((ind) => { return ind === k.id }) !== -1) {
                if (k.newDataPoints !== undefined) {
                    k.newDataPoints.forEach((k) => {
                        selectedDataPoints.push(k.id)
                    })

                }
            }
        })
        console.log(raw, selectedMetric)
        let selectedDCF = dcf.map((k) => { return k.id })
        let selectedSAP = selectedsapbk.map((k) => { return k.id })

        // Prepare assignment summary and show confirmation dialog
        const summary = prepareAssignmentSummary();
        setAssignmentSummary(summary);
        setConfirmSaveDialog(true);
    };

    const confirmSaveAssignment = async () => {
        let metrics = JSON.parse(JSON.stringify(metricbk));
        let dcf = JSON.parse(JSON.stringify(selecteddcfbk));
        let selectedMetric = metrics.filter((i) => { return i.selected }).map((j) => { return j.id })
        let selectedDataPoints = []
        const { result, msg } = checkDCFForDPDuplication_()

        raw.forEach((k) => {
            if (selectedMetric.findIndex((ind) => { return ind === k.id }) !== -1) {
                if (k.newDataPoints !== undefined) {
                    k.newDataPoints.forEach((k) => {
                        selectedDataPoints.push(k.id)
                    })
                }
            }
        })

        let selectedDCF = dcf.map((k) => { return k.id })
        let selectedSAP = selectedsapbk.map((k) => { return k.id })

        // Create the assignment data object
        const assignmentData = {
            requestKey: '',
            cf_ids: cfbklist.filter((i) => { return i.selected === true }).map(k => k.id),
            category_ids: selected.category,
            topic_ids: selected.topic,
            selected_ids: JSON.parse(JSON.stringify(metriclist)).filter((i) => { return i.selected && !i.disabled }).map((j) => { return j.id }),
            dcf_ids: selectedDCF,
            sap_ids: selectedSAP, // SAP collections stored as datasource2 array
            dp_ids: selectedDataPoints,
            metric_ids: selectedMetric,
            user_id: selector.id,
            modified: moment.utc(),

            // NEW: Add DCF/SAP indicator mapping data for tracking
            // These will be null for existing assignments (backward compatibility)
            // and populated for new assignments to enable cross-indicator conflict detection
            dcf_indicator_mapping: Object.keys(dcfIndicatorMapping).length > 0 ? dcfIndicatorMapping : null,
            sap_indicator_mapping: Object.keys(sapIndicatorMapping).length > 0 ? sapIndicatorMapping : null,
            datapoint_dcf_mapping: Object.keys(datapointDcfSelections).length > 0 ? datapointDcfSelections : null,
            datapoint_sap_mapping: Object.keys(datapointSapSelections).length > 0 ? datapointSapSelections : null
        }

        console.log(assignmentData)

        // Close the confirmation dialog
        setConfirmSaveDialog(false);

        if (selectedMetric.length !== 0 && selectedDCF.length !== 0 && result) {
            Swal.fire({
                title: 'Saving Assignment',
                text: 'Please wait while we save your assignment...',
                allowOutsideClick: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });

            try {
                if (editmode) {
                    // Update existing assignment
                    await APIServices.patch(API.AssignDCFClient_Edit(editmodeid), assignmentData);

                    Swal.fire({
                        position: "center",
                        icon: "success",
                        title: "Assignment Updated Successfully",
                        showConfirmButton: false,
                        timer: 1500
                    });
                } else {
                    // Create new assignment
                    const response = await APIServices.post(API.AssignDCFClient_UP(selected.user), {
                        ...assignmentData,
                        created: moment.utc()
                    });

                    setEditMode(true);
                    setEditModeID(response.data.id);

                    Swal.fire({
                        position: "center",
                        icon: "success",
                        title: "Assignment Saved Successfully",
                        showConfirmButton: false,
                        timer: 1500
                    });
                }
            } catch (error) {
                console.error("Error saving assignment:", error);
                Swal.fire({
                    position: "center",
                    icon: "error",
                    title: "Error Saving Assignment",
                    text: "Please try again later.",
                    showConfirmButton: true
                });
            }
        } else {
            // Show error message if validation fails
            let errorMessage = "Please check the following:";
            if (selectedMetric.length === 0) errorMessage += "\n- No indicators selected";
            if (selectedDCF.length === 0) errorMessage += "\n- No DCFs selected";
            if (!result) errorMessage += "\n- Duplicate datapoints detected";

            Swal.fire({
                position: "center",
                icon: "warning",
                title: "Cannot Save Assignment",
                text: errorMessage,
                showConfirmButton: true
            });
        }


//         if (selectedMetric.length !== 0 && selectedDCF.length !== 0 && result) {

//             const key = getRandomId(); setKey(key); await APIServices.post('https://api.eisqr.com/post-email', {
//                 to: '<EMAIL>', subject: 'Team Request for Indicator Assignment to ' + user.find(x => x.id === selected.user).name, body: ` <p>Dear User,</p>
// <p>
// Team requested to assign indicators to the client: <strong>${user.find(x => x.id === selected.user)?.name}</strong>.
// To proceed, please use the following key:
// </p>

// <p><strong> Key:</strong> ${key}</p>
// <p><strong> Indicator(s) listed below:</strong></p>
// <ol>${JSON.parse(JSON.stringify(metriclist)).filter((i) => { return i.selected && !i.disabled }).map(x =>
//                     `<li> ${x.title}</li>`
//                 )}
// </ol>
// <p>
// Enter this key in the system to complete the assignment process.
// </p>

// `
//             }).then(() => {
//                 CustomDialog.Set({
//                     title: 'Are you sure want to update?',
//                     message: 'Key has <NAME_EMAIL>, ask Elango/JC  <strong>' + selectedMetric.length + " Indicator </strong> to " + user.find(x => x.id === selected.user)?.name,
//                     inputType: 'text',
//                     validation: 'equals',
//                     validateValue: key, buttons: [
//                         {
//                             label: 'Submit',
//                             className: 'p-button-primary',
//                             onClick: (value) => {

//                                 if (editmode) {
//                                     APIServices.patch(API.AssignDCFClient_Edit(editmodeid), {
//                                         requestKey: value,
//                                         cf_ids: cfbklist.filter((i) => { return i.selected === true }).map(k => k.id),
//                                         category_ids: selected.category,
//                                         topic_ids: selected.topic,
//                                         selected_ids: JSON.parse(JSON.stringify(metriclist)).filter((i) => { return i.selected && !i.disabled }).map((j) => { return j.id }),
//                                         dcf_ids: selectedDCF,
//                                         dp_ids: selectedDataPoints,
//                                         metric_ids: selectedMetric,
//                                         user_id: selector.id,
//                                         modified: moment.utc()
//                                     }).then((a) => {
//                                         Swal.fire({
//                                             position: "center",
//                                             icon: "success",
//                                             title: `Data updated successfully`,
//                                             showConfirmButton: false,
//                                             timer: 1500,
//                                         });
//                                     })
//                                 } else {
//                                     APIServices.post(API.AssignDCFClient_UP(selected.user), {
//                                         requestKey: value,
//                                         cf_ids: cfbklist.filter((i) => { return i.selected === true }).map(k => k.id),
//                                         category_ids: selected.category,
//                                         topic_ids: selected.topic,
//                                         selected_ids: JSON.parse(JSON.stringify(metriclist)).filter((i) => { return i.selected && !i.disabled }).map((j) => { return j.id }),
//                                         dcf_ids: selectedDCF,
//                                         dp_ids: selectedDataPoints,
//                                         metric_ids: selectedMetric,
//                                         user_id: selector.id,
//                                         created: moment.utc()
//                                     }).then((a) => {
//                                         setEditMode(true)
//                                         setEditModeID(a.data.id)
//                                         Swal.fire({
//                                             position: "center",
//                                             icon: "success",
//                                             title: `Data saved successfully`,
//                                             showConfirmButton: false,
//                                             timer: 1500,
//                                         });
//                                     })
//                                 }

//                             }
//                         }
//                     ]
//                 })
//             })



//             // Swal.fire({
//             //     title: "Are you sure want to update?",
//             //     text: selectedMetric.length + " Indicator Assigned",
//             //     icon: "warning",
//             //     showCancelButton: true,
//             //     confirmButtonColor: "#3085d6",
//             //     cancelButtonColor: "#d33",
//             //     confirmButtonText: "Yes, Update"
//             // }).then((result) => {
//             //     if (result.isConfirmed) {
//             //         if (editmode) {
//             //             APIServices.patch(API.AssignDCFClient_Edit(editmodeid), { cf_ids: cfbklist.filter((i) => { return i.selected === true }).map(k => k.id), category_ids: selected.category, topic_ids: selected.topic, selected_ids: JSON.parse(JSON.stringify(metriclist)).filter((i) => { return i.selected && !i.disabled }).map((j) => { return j.id }), dcf_ids: selectedDCF, dp_ids: selectedDataPoints, metric_ids: selectedMetric, user_id: selector.id, modified: moment.utc() }).then((a) => {

//             //                 Swal.fire({
//             //                     position: "center",
//             //                     icon: "success",
//             //                     title: `Data updated successfully`,
//             //                     showConfirmButton: false,
//             //                     timer: 1500,
//             //                 });
//             //             })
//             //         } else {
//             //             APIServices.post(API.AssignDCFClient_UP(selected.user), { cf_ids: cfbklist.filter((i) => { return i.selected === true }).map(k => k.id), category_ids: selected.category, topic_ids: selected.topic, selected_ids: JSON.parse(JSON.stringify(metriclist)).filter((i) => { return i.selected && !i.disabled }).map((j) => { return j.id }), dcf_ids: selectedDCF, dp_ids: selectedDataPoints, metric_ids: selectedMetric, user_id: selector.id, created: moment.utc() }).then((a) => {
//             //                 setEditMode(true)
//             //                 setEditModeID(a.data.id)
//             //                 Swal.fire({
//             //                     position: "center",
//             //                     icon: "success",
//             //                     title: `Data saved successfully`,
//             //                     showConfirmButton: false,
//             //                     timer: 1500,
//             //                 });
//             //             })
//             //         }
//             //     }
//             // });

//         } else {
//             if (!result) {
//                 checkDCFForDPDuplication()
//             } else {
//                 Swal.fire({
//                     position: "center",
//                     icon: "error",
//                     title: `Unable to save, DCF not assigned / not empty `,
//                     showConfirmButton: false,
//                     timer: 1500,
//                 })
//             }
//         }


    }
    const listingMulitSelectTemplate = (option) => {
        if (option) {
            return (
                <div >
                    <span class="p-multiselect-token-label">{option.name}</span>
                    {/* <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg" class="p-icon p-multiselect-token-icon" aria-hidden="true"><g clip-path="url(#pr_icon_clip_2)"><path fill-rule="evenodd" clip-rule="evenodd" d="M7 14C5.61553 14 4.26215 13.5895 3.11101 12.8203C1.95987 12.0511 1.06266 10.9579 0.532846 9.67879C0.00303296 8.3997 -0.13559 6.99224 0.134506 5.63437C0.404603 4.2765 1.07129 3.02922 2.05026 2.05026C3.02922 1.07129 4.2765 0.404603 5.63437 0.134506C6.99224 -0.13559 8.3997 0.00303296 9.67879 0.532846C10.9579 1.06266 12.0511 1.95987 12.8203 3.11101C13.5895 4.26215 14 5.61553 14 7C14 8.85652 13.2625 10.637 11.9497 11.9497C10.637 13.2625 8.85652 14 7 14ZM7 1.16667C5.84628 1.16667 4.71846 1.50879 3.75918 2.14976C2.79989 2.79074 2.05222 3.70178 1.61071 4.76768C1.16919 5.83358 1.05367 7.00647 1.27876 8.13803C1.50384 9.26958 2.05941 10.309 2.87521 11.1248C3.69102 11.9406 4.73042 12.4962 5.86198 12.7212C6.99353 12.9463 8.16642 12.8308 9.23232 12.3893C10.2982 11.9478 11.2093 11.2001 11.8502 10.2408C12.4912 9.28154 12.8333 8.15373 12.8333 7C12.8333 5.45291 12.2188 3.96918 11.1248 2.87521C10.0308 1.78125 8.5471 1.16667 7 1.16667ZM4.66662 9.91668C4.58998 9.91704 4.51404 9.90209 4.44325 9.87271C4.37246 9.84333 4.30826 9.8001 4.2544 9.74557C4.14516 9.6362 4.0838 9.48793 4.0838 9.33335C4.0838 9.17876 4.14516 9.0305 4.2544 8.92113L6.17553 7L4.25443 5.07891C4.15139 4.96832 4.09529 4.82207 4.09796 4.67094C4.10063 4.51982 4.16185 4.37563 4.26872 4.26876C4.3756 4.16188 4.51979 4.10066 4.67091 4.09799C4.82204 4.09532 4.96829 4.15142 5.07887 4.25446L6.99997 6.17556L8.92106 4.25446C9.03164 4.15142 9.1779 4.09532 9.32903 4.09799C9.48015 4.10066 9.62434 4.16188 9.73121 4.26876C9.83809 4.37563 9.89931 4.51982 9.90198 4.67094C9.90464 4.82207 9.84855 4.96832 9.74551 5.07891L7.82441 7L9.74554 8.92113C9.85478 9.0305 9.91614 9.17876 9.91614 9.33335C9.91614 9.48793 9.85478 9.6362 9.74554 9.74557C9.69168 9.8001 9.62748 9.84333 9.55669 9.87271C9.4859 9.90209 9.40996 9.91704 9.33332 9.91668C9.25668 9.91704 9.18073 9.90209 9.10995 9.87271C9.03916 9.84333 8.97495 9.8001 8.9211 9.74557L6.99997 7.82444L5.07884 9.74557C5.02499 9.8001 4.96078 9.84333 4.88999 9.87271C4.81921 9.90209 4.74326 9.91704 4.66662 9.91668Z" fill="currentColor"></path></g><defs><clipPath id="pr_icon_clip_2"><rect width="14" height="14" fill="white"></rect></clipPath></defs></svg> */}
                </div>
            );
        }

        return 'Select SDGs';
    };
    const checkStandlone = (metric) => {
        if (Array.isArray(metric.data1) && metric.data1.length !== 0) {
            if (metric.data1[0].source === undefined) {
                return false
            } else if (metric.data1[0].source === 1) {
                return true
            }
        }
    }
    const checkChildrenSelected = (metric) => {


        if (Array.isArray(metric.data1) && metric.data1.length !== 0) {
            let children = metric.data1[0].indicator.filter((i) => { return usermetric.includes(i) })
            if (children.length !== 0) {
                if (!usermetric.includes(metric.id)) {
                    usermetric.push(metric.id)
                }
                return true
            } else {
                if (usermetric.includes(metric.id)) {
                    let index = usermetric.findIndex((i) => { return i === metric.id })
                    usermetric.splice(index, 1)
                }
            }
        }

        return false

    }
    const checkChildrenSelected_ = (metric) => {


        if (Array.isArray(metric.data1) && metric.data1.length !== 0) {
            let children = metric.data1[0].indicator.filter((i) => { return usermetric.includes(i) })
            if (children.length !== 0) {

                return true
            }
        }

        return false

    }
    const showList = (id) => {
        let loclist = selectedlist
        if (id === 1) {
            loclist.title = 'Selected Categories'
            loclist.data = list.category.filter((k) => { return selected.category.includes(k.id) })
        } else if (id === 2) {
            loclist.title = 'Selected Topics'
            loclist.data = list.topic.filter((k) => { return selected.topic.includes(k.id) })
        }
        setSelectedList(loclist)
        setPrevSListDialog(true)

    }
    const showList_ = (id) => {
        let loclist = selectedlist

        if (id === 1) {
            // Check if there are any selected indicators
            const selectedIndicators = metriclist.filter((k) => { return k.selected });
            if (selectedIndicators.length === 0) {
                // Don't show dialog if no indicators are selected
                return;
            }

            loclist.title = `Selected Indicators (${selectedIndicators.length})`
            loclist.data = selectedIndicators
        } else if (id === 2) {
            // Check if there are any selected DCFs
            if (selecteddcf.length === 0) {
                // Don't show dialog if no DCFs are selected
                return;
            }

            loclist.title = `Mapped DCFs (${selecteddcf.length})`
            loclist.data = JSON.parse(JSON.stringify(selecteddcf))
        } else if (id === 3) {
            // Check if there are any selected SRFs
            const selectedSRFs = cflist.filter((i) => { return i.selected === true });
            if (selectedSRFs.length === 0) {
                // Don't show dialog if no SRFs are selected
                return;
            }

            loclist.title = `Mapped SRF (${selectedSRFs.length})`
            loclist.data = JSON.parse(JSON.stringify(selectedSRFs))
        } else if (id === 4) {
            // Check if there are any required qualitative forms
            const requiredForms = dfreqlistbk.filter((i) => { return i.config !== undefined });
            if (requiredForms.length === 0) {
                // Don't show dialog if no forms are required
                return;
            }

            loclist.title = `Required Qualitative Form (${requiredForms.length})`
            loclist.data = JSON.parse(JSON.stringify(requiredForms))
        } else if (id === 5) {
            // Check if there are any selected SAP Collections
            if (selectedsapbk.length === 0) {
                // Don't show dialog if no SAP Collections are selected
                return;
            }

            loclist.title = `Mapped SAP Collections (${selectedsapbk.length})`
            loclist.data = JSON.parse(JSON.stringify(selectedsapbk))
        }

        setSelectedList(loclist)
        setPrevSList2Dialog(true)
    }
    const getTag = (item) => {
        let result = 'Default'

        if (item !== undefined && item !== null) {
            let tag = clienttag.findIndex(i => i.id === parseFloat(item))
            if (tag === -1) {
                result = 'No Tag'
            } else {
                result = clienttag[tag].name
            }


        }


        return result
    }
    const export2Excel = () => {
        console.log(response)
        let topic = [], metric = [], dcf = [], json = []
        let added = []
        // APIServices.get(API.AssignDCFClient ).then((res) => {
        //   for(let i=0;i<res.data.length;i++){
        //     let obj = res.data[i]
        //     if(!added.includes(obj.userProfileId)){
        //         console.log(obj)
        //         added.push(obj.userProfileId)

        //     }
        //   }
        // })
        response.forEach((cat) => {
            if (selected.category.includes(cat.id)) {

                if (cat.newTopics) {
                    cat.newTopics.forEach((top) => {
                        console.log(top.id === 65, selected.topic)
                        if (selected.topic.includes(top.id)) {
                            console.log(top.id === 65)
                            if (top.newMetrics) {
                                top.newMetrics.forEach((met) => {
                                    console.log(met.id === 755)
                                    if (metricbk.filter((k) => { return k.selected }).map(i => i.id).includes(met.id)) {
                                        if (met.newDataPoints) {
                                            met.newDataPoints.forEach((dp) => {
                                                console.log(dp.id === 2143)
                                                if (Array.isArray(dp.data1) && dp.data1[0] && dp.data1[0].datasource && typeof dp.data1[0].datasource === 'number' && selecteddcf.map(i => i.id).includes(dp.data1[0].datasource)) {
                                                    let dcf_index = selecteddcf.findIndex(x => x.id === dp.data1[0].datasource)

                                                    if (dcf_index !== -1) {
                                                        let cat_index = json.findIndex(i => i['Category Id'] === cat.id && i['Topic Id'] === top.id && i['Metric Id'] === met.id && i['DCF Id'] === dp.data1[0].datasource)
                                                        if (cat_index !== -1) {
                                                            json[cat_index] = { ...json[cat_index], 'DCF Id': dp.data1[0].datasource, 'DCF Title': selecteddcf[dcf_index].title }
                                                        } else {

                                                            json.push({ 'Category Id': cat.id, 'Category Title': cat.title, 'Topic Id': top.id, 'Topic Title': top.title, 'Metric Id': met.id, 'Metric Title': met.title, 'DCF Id': dp.data1[0].datasource, 'DCF Title': selecteddcf[dcf_index].title })
                                                        }
                                                    }

                                                }
                                            })
                                        }
                                    }


                                })

                            }
                        }

                    })
                }
            }
        })
        console.log(json)
        if (json.length !== 0) {
            const ws = XLSX.utils.json_to_sheet(json)
            let sheet_name = 'Assignment_' + user.find(i => i.id === selected.user).name
            const wb = {
                Sheets: { [sheet_name]: ws },
                SheetNames: [sheet_name],
            };

            const excelBuffer = XLSX.write(wb, { bookType: "xlsx", type: "array" }, { Props: { Author: "Eisqr Solutions", CreatedDate: DateTime.utc() } });

            const data = new Blob([excelBuffer], {
                type:
                    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8",
            });
            FileSaver.saveAs(data, sheet_name + ".xlsx");
        }
    }
    return (
        <div className="grid">
            <div className="col-12">
                <div className="card">
                    <div className="flex align-items-center justify-content-between mb-3">
                        <h2 className="m-0">Client DCF/SRF Assignment</h2>
                        {metricbk.filter((i) => { return i.selected }).length !== 0 &&
                            <Button
                                icon='pi pi-cloud-download'
                                className="p-button-outlined"
                                onClick={() => { export2Excel() }}
                            >
                                Export Assignment
                            </Button>
                        }
                    </div>

                    {selector.role === "eisqradmin" ?
                        <>
                            {user.length !== 0 ?
                                <>
                                    <div className="p-field mb-4">
                                        <label className="font-bold block mb-2">Select Enterprise / Client</label>
                                        <Dropdown
                                            disabled={enable}
                                            className="w-full"
                                            value={selected.user}
                                            options={user}
                                            optionValue="id"
                                            onChange={(e) => updateSelected("user", e.value)}
                                            optionLabel="name"
                                            filter
                                            filterBy="name"
                                            placeholder="Select Client"
                                        />
                                    </div>
                                    {selected.user !== null &&
                                        <>
                                            <TabMenu
                                                model={[
                                                    { label: 'DCF', icon: 'pi pi-file', command: () => setActiveItem(1) },
                                                    { label: 'SRF', icon: 'pi pi-file-excel', command: () => setActiveItem(2) }
                                                ]}
                                                activeIndex={activeItem - 1}
                                                className="mb-3"
                                            />

                                            {activeItem === 1 &&
                                                <div className="card p-3">
                                                    <div style={{ flexDirection: 'column', display: 'flex', marginTop: 10 }}>

                                                        <div className="grid">
                                                            <div className="col-6">
                                                                <div className="p-field ">
                                                                    <label className="font-bold mb-2 block">Select Category</label>
                                                                    <MultiSelect
                                                                        display="chip"
                                                                        value={selected.category}
                                                                        onChange={(e) => updateSelected("category", e.value)}
                                                                        options={list.category}
                                                                        optionLabel="title"
                                                                        optionValue="id"
                                                                        filter={true}
                                                                        placeholder="Select Category"
                                                                        className="w-full"
                                                                        panelClassName={'hidefilter'}
                                                                    />

                                                                </div>
                                                            </div>

                                                            <div className="col-6">
                                                                <div className="p-field">
                                                                    <label className={`font-bold mb-2 block ${selected.category.length === 0 ? 'text-gray-400' : ''}`}>
                                                                        Select Topic
                                                                    </label>
                                                                    <MultiSelect
                                                                        display="chip"
                                                                        value={selected.topic}
                                                                        onChange={(e) => updateSelected("topic", e.value)}
                                                                        options={list.topic}
                                                                        optionLabel="title"
                                                                        optionValue="id"
                                                                        filterBy="name"
                                                                        filter
                                                                        placeholder="Select Topic"
                                                                        className="w-full"
                                                                        panelClassName={'hidefilter'}
                                                                        disabled={selected.category.length === 0}
                                                                    />

                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div className="grid">

                                                            <div className="col-6"> {selected.category.length > 0 &&
                                                                <div className="flex justify-content-end mt-2">
                                                                    <a
                                                                        className="clr-navy cursor-pointer"
                                                                        onClick={() => { showList(1) }}
                                                                    >
                                                                        View selected ({selected.category.length})
                                                                    </a>
                                                                </div>
                                                            }</div>

                                                            <div className="col-6"> {selected.topic.length > 0 &&
                                                                <div className="flex justify-content-end mt-2">
                                                                    <a
                                                                        className="clr-navy cursor-pointer"
                                                                        onClick={() => { showList(2) }}
                                                                    >
                                                                        View selected ({selected.topic.length})
                                                                    </a>
                                                                </div>
                                                            }</div>
                                                        </div>

                                                        {selected.topic.length !== 0 &&
                                                            <div className="col-12">
                                                                <div className="flex align-items-center mb-3">

                                                                    <div className="flex-grow-1">
                                                                        <div className="flex flex-wrap">
                                                                            <div className="p-2" style={{ width: '16.66%' }}>
                                                                                <div className="border-1 border-round p-3 text-center h-full">

                                                                                    <div className="text-center">
                                                                                        <span className="block text-600 font-medium">Selected Categories</span>
                                                                                        <div className="text-900 font-bold text-xl mt-2">{selected.category.length} / {list.category.length}</div>
                                                                                    </div>
                                                                                </div>
                                                                            </div>

                                                                            <div className="p-2" style={{ width: '16.66%' }}>
                                                                                <div className="border-1 border-round p-3 text-center h-full">

                                                                                    <div className="text-center">
                                                                                        <span className="block text-600 font-medium">Selected Topics</span>
                                                                                        <div className="text-900 font-bold text-xl mt-2">{selected.topic.length} / {list.topic.length}</div>
                                                                                    </div>
                                                                                </div>
                                                                            </div>

                                                                            <div className="p-2" style={{ width: '16.66%' }}>
                                                                                <div className="border-1 border-round p-3 text-center h-full">

                                                                                    <div className="text-center">
                                                                                        <span className="block text-600 font-medium">Total Indicators</span>
                                                                                        <div className="text-900 font-bold text-xl mt-2">{metricbk.length}</div>
                                                                                    </div>
                                                                                </div>
                                                                            </div>

                                                                            <div className="p-2" style={{ width: '16.66%' }}>
                                                                                <div className="border-1 border-round p-3 text-center h-full">

                                                                                    <div className="text-center">
                                                                                        <span className="block text-600 font-medium">Indicators Assigned</span>
                                                                                        <div className="text-900 font-bold text-xl mt-2">{metricbk.filter((i) => { return i.selected }).length}</div>
                                                                                    </div>
                                                                                </div>
                                                                            </div>

                                                                            <div className="p-2" style={{ width: '16.66%' }}>
                                                                                <div className="border-1 border-round p-3 text-center h-full">

                                                                                    <div className="text-center">
                                                                                        <span className="block text-600 font-medium">DCF Assigned</span>
                                                                                        <div className="text-900 font-bold text-xl mt-2">{selecteddcfbk.length}</div>
                                                                                    </div>
                                                                                </div>
                                                                            </div>

                                                                            <div className="p-2" style={{ width: '16.66%' }}>
                                                                                <div className="border-1 border-round p-3 text-center h-full">

                                                                                    <div className="text-center">
                                                                                        <span className="block text-600 font-medium">SAP Assigned</span>
                                                                                        <div className="text-900 font-bold text-xl mt-2">{selectedsapbk.length}</div>
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        }
                                                    </div>

                                                    {selected.topic.length !== 0 &&
                                                        <>
                                                            <div className="grid mb-3">
                                                                <div className="col-12" style={{ width: '100%' }}>
                                                                    <div className="surface-0 shadow-1 p-3 border-round" style={{ width: '100%' }}>
                                                                        <div className="flex justify-content-between align-items-center mb-3" style={{ width: '100%' }}>
                                                                            <div className="flex align-items-center">
                                                                                <i className="pi pi-chart-line text-primary mr-2" style={{ fontSize: '1.2rem' }}></i>
                                                                                <h3 className="text-lg font-medium m-0">Indicators</h3>
                                                                            </div>
                                                                            <Button
                                                                                label={`View Selected${usermetric.length > 0 ? ` (${usermetric.length})` : ''}`}
                                                                                icon="pi pi-eye"
                                                                                className="p-button-outlined p-button-sm"
                                                                                onClick={() => { showList_(1) }}
                                                                                disabled={usermetric.length === 0}
                                                                            />
                                                                        </div>
                                                                        <span className="p-input-icon-left w-full mb-3">
                                                                            <i className="pi pi-search" />
                                                                            <InputText
                                                                                value={search.metric}
                                                                                onChange={searchMetric}
                                                                                placeholder="Search indicator by name/tag"
                                                                                className="w-full"
                                                                            />
                                                                        </span>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div className="grid">
                                                                <div className="col-12 mb-3">
                                                                    <div className="surface-0 shadow-2 border-round p-0" style={{ height: '40vh', overflow: 'hidden', display: 'flex', flexDirection: 'column', width: '100%' }}>
                                                                        <div className="p-3 font-medium bg-primary text-white flex align-items-center">
                                                                            <i className="pi pi-list mr-2"></i>
                                                                            <span>Indicators</span>
                                                                        </div>
                                                                        <div className="overflow-auto p-3" style={{ flex: 1, width: '100%' }}>
                                                                            {metriclist.map((metric) => {
                                                                                return (
                                                                                    <LazyView duration={0.5} key={metric.id}>
                                                                                        {(Array.isArray(metric.data1) && metric.data1.length === 1 && metric.data1[0]?.type === 2) ?
                                                                                            <div key={metric.id} style={{ padding: 10 }} className="flex align-items-center">
                                                                                                <Checkbox disabled={metric.disabled} inputId={metric.id} name="metric" value={metric.title} onChange={(e) => { onMetricChange(e, metric, 3) }} checked={metric.selected} />
                                                                                                <div className="ml-2">
                                                                                                    <label htmlFor={metric.id} style={{ cursor: 'pointer' }} > {metric.title} <span className={(metric.extra !== undefined && metric.extra !== null) ? 'status-tag-red ml-1' : 'status-tag-gray ml-1'}>{(metric.extra !== undefined && metric.extra !== null) ? getTag(metric.tag) : 'Default'}</span> </label>
                                                                                                    {/* <Tag className="status-tag-gray" style={{padding:3,marginLeft:5}} > {(metric.extra !== undefined && metric.extra !== null) ? metric.tag : 'Default' } </Tag>  */}
                                                                                                </div>


                                                                                            </div>


                                                                                            : (metric.data1 === null || metric.data1 === undefined || metric.data1.length === 0 || Object.keys(metric.data1[0]).length === 2 || (Object.keys(metric.data1[0]).length > 2 && metric.data1[0].source === 1)) ?
                                                                                                <div key={metric.id} style={{ padding: 10 }} className="flex align-items-center">
                                                                                                    <Checkbox disabled={metric.disabled} inputId={metric.id} name="metric" value={metric.title} onChange={(e) => { onMetricChange(e, metric, 1) }} checked={metric.selected} />
                                                                                                    <div className="ml-2">
                                                                                                        <label htmlFor={metric.id} style={{ cursor: 'pointer' }} >{metric.title} <span className={(metric.extra !== undefined && metric.extra !== null) ? 'status-tag-red ml-1' : 'status-tag-gray ml-1'}>{(metric.extra !== undefined && metric.extra !== null) ? getTag(metric.tag) : 'Default'}</span> </label>
                                                                                                        {/* <Tag className="status-tag-gray" style={{padding:3,marginLeft:5}} > {(metric.extra !== undefined && metric.extra !== null) ? metric.tag : 'Default' } </Tag>  */}
                                                                                                    </div>


                                                                                                </div>
                                                                                                :
                                                                                                <>
                                                                                                    <div key={metric.id} style={{ padding: 10 }} className="flex align-items-center">
                                                                                                        <Checkbox disabled={metric.disabled} inputId={metric.id} name="metric" value={metric.title} onChange={(e) => { onMetricChange(e, metric, 2) }} checked={metric.selected} />
                                                                                                        <label htmlFor={metric.id} style={{ cursor: 'pointer' }} className="ml-2">{metric.title}</label>
                                                                                                        <span className={(metric.extra !== undefined && metric.extra !== null) ? 'status-tag-red ml-1' : 'status-tag-gray ml-1'}>{(metric.extra !== undefined && metric.extra !== null) ? getTag(metric.tag) : 'Default'}</span>
                                                                                                    </div>
                                                                                                    <div className="col-12 "  >

                                                                                                        {/* <div className="col-2" style={{position:'relative'}} ><hr  class="tree-vertical-line" /></div> */}
                                                                                                        <div className="col-12" >
                                                                                                            {metric.data1[0].indicator.map((id) => {
                                                                                                                // let index2 = metriclist.findIndex((mid) => { return mid.id === id })
                                                                                                                let index = overallmetric.findIndex((mid) => { return mid.id === id })

                                                                                                                if (index !== -1) {
                                                                                                                    let item = overallmetric[index]

                                                                                                                    return (

                                                                                                                        <div key={'D' + item.id} style={{ position: 'relative', marginLeft: 30, marginBottom: 10 }} className="flex align-items-center">
                                                                                                                            {/* <img src={require('../assets/images/l_purple.png').default} width={30} style={{
                                                                                                position: 'absolute',
                                                                                                left: '-23px',
                                                                                                top: '-10px'
                                                                                            }} /> */}
                                                                                                                            {/* <hr className="tree-horizontal-line" /> */}
                                                                                                                            {!checkStandlone(item) ?
                                                                                                                                <>
                                                                                                                                    {getLevel(item)}
                                                                                                                                    {/* <Checkbox name="category" value={item.title} disabled={true} checked={usermetric.includes(item.id)} /> */}
                                                                                                                                    <label style={{ cursor: 'default' }} className="ml-2">{item.title} </label>
                                                                                                                                    <span className={(item.extra !== undefined && item.extra !== null) ? 'status-tag-red ml-1' : 'status-tag-gray ml-1'}>{(item.extra !== undefined && item.extra !== null) ? getTag(item.tag) : 'Default'}</span>
                                                                                                                                </>
                                                                                                                                :
                                                                                                                                <>
                                                                                                                                    {getLevel(item)}
                                                                                                                                    {/* <Checkbox inputId={'D' + item.id} name="category" value={item.title} onChange={(e) => { onCategoryChange(e, item, 3) }} checked={usermetric.includes(item.id)} />
                                                                                                            <label htmlFor={'D' + item.id} style={{ cursor: 'pointer' }} className="ml-2">{item.title}</label> */}
                                                                                                                                    {/* textDecoration: (checkChildrenSelected_(metric) && !usermetric.includes(item.id)) ? 'line-through' : 'none' */}
                                                                                                                                    <label style={{ cursor: 'default' }} className="ml-2">{item.title} </label>
                                                                                                                                    <span className={(item.extra !== undefined && item.extra !== null) ? 'status-tag-red ml-1' : 'status-tag-gray ml-1'}>{(item.extra !== undefined && item.extra !== null) ? getTag(item.tag) : 'Default'}</span>
                                                                                                                                </>
                                                                                                                            }


                                                                                                                        </div>
                                                                                                                    )
                                                                                                                }
                                                                                                            })
                                                                                                            }
                                                                                                        </div>
                                                                                                    </div>
                                                                                                </>

                                                                                        }
                                                                                    </LazyView>
                                                                                )
                                                                            })}
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                                <div className="col-6" >
                                                                    <div className="surface-0 shadow-1 p-3 border-round" style={{ width: '100%' }}>
                                                                        <div className="flex justify-content-between align-items-center mb-3" style={{ width: '100%' }}>
                                                                            <div className="flex align-items-center">
                                                                                <i className="pi pi-file-pdf text-primary mr-2" style={{ fontSize: '1.2rem' }}></i>
                                                                                <h3 className="text-lg font-medium m-0">DCF</h3>
                                                                            </div>
                                                                            <Button
                                                                                label={`View Mapped${selecteddcfbk.length > 0 ? ` (${selecteddcfbk.length})` : ''}`}
                                                                                icon="pi pi-eye"
                                                                                className="p-button-outlined p-button-sm"
                                                                                onClick={() => { showList_(2) }}
                                                                                disabled={selecteddcfbk.length === 0}
                                                                            />
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                                <div className="col-6">
                                                                    <div className="surface-0 shadow-1 p-3 border-round" style={{ width: '100%' }}>
                                                                        <div className="flex justify-content-between align-items-center mb-3" style={{ width: '100%' }}>
                                                                            <div className="flex align-items-center">
                                                                                <i className="pi pi-database text-primary mr-2" style={{ fontSize: '1.2rem' }}></i>
                                                                                <h3 className="text-lg font-medium m-0">SAP Collections</h3>
                                                                            </div>
                                                                            <Button
                                                                                label={`View Mapped${selectedsapbk.length > 0 ? ` (${selectedsapbk.length})` : ''}`}
                                                                                icon="pi pi-eye"
                                                                                className="p-button-outlined p-button-sm"
                                                                                onClick={() => { showList_(5) }}
                                                                                disabled={selectedsapbk.length === 0}
                                                                            />
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                                <div className="col-12" style={{ width: '100%' }}>
                                                                    <div className="surface-0 shadow-2 border-round p-0" style={{ height: '75vh', overflow: 'hidden', display: 'flex', flexDirection: 'column', width: '100%' }}>
                                                                        <div className="p-3 font-medium bg-primary text-white flex align-items-center" style={{ width: '100%' }}>
                                                                           <i className="pi pi-list mr-2"></i>
                                                                            <span>Indicator-Datapoint-DCF-SAP Mapping</span>
                                                                        </div>
                                                                        <div className="overflow-auto p-3" style={{ flex: 1, width: '100%' }}>
                                                                            {renderIndicatorDatapointDCFTable()}
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>

                                                            <div className="flex justify-content-end mt-3">
                                                                <Button
                                                                    label={editmode ? 'Update Assignment' : 'Save Assignment'}
                                                                    icon="pi pi-save"
                                                                    className="p-button-primary p-button-raised"
                                                                    onClick={() => { saveAssignedDCF() }}
                                                                    style={{ minWidth: '180px' }}
                                                                />
                                                            </div>

                                                        </>
                                                    }
                                                </div>
                                            }
                                            {activeItem === 2 &&
                                                <div className="card p-3">
                                                    <div className="surface-0 shadow-1 p-3 border-round mb-3">
                                                        <div className="flex justify-content-between align-items-center col-6">
                                                            <div className="flex align-items-center">
                                                                <i className="pi pi-file-excel text-primary mr-2" style={{ fontSize: '1.2rem' }}></i>
                                                                <h3 className="text-lg font-medium m-0">SRF</h3>
                                                            </div>
                                                            <Button
                                                                label={`View Mapped${cfbklist.filter((i) => { return i.selected === true }).length > 0 ? ` (${cfbklist.filter((i) => { return i.selected === true }).length})` : ''}`}
                                                                icon="pi pi-eye"
                                                                className="p-button-outlined p-button-sm"
                                                                onClick={() => { showList_(3) }}
                                                                disabled={cfbklist.filter((i) => { return i.selected === true }).length === 0}
                                                            />
                                                        </div>
                                                        <span className="p-input-icon-left w-full flex">
                                                            <i className="pi pi-search" />
                                                            <InputText
                                                                value={search.cf}
                                                                onChange={searchCF}
                                                                placeholder="Search SRF"
                                                                className="w-full"
                                                            />
                                                        </span>
                                                    </div>

                                                    <div className="surface-0 shadow-2 border-round p-0" style={{ height: '75vh', overflow: 'hidden', display: 'flex', flexDirection: 'column' }}>
                                                        <div className="p-3 font-medium bg-primary text-white flex align-items-center">
                                                            <i className="pi pi-file-excel mr-2"></i>
                                                            <span>Available SRF</span>
                                                        </div>
                                                        <div className="overflow-auto p-3" style={{ flex: 1 }}>
                                                            {cflist.filter(i => (i.tags === null || !i.tags.length || i.tags.includes(selected.user))).map((cf) => {
                                                                return (
                                                                    <LazyView duration={0.5} key={cf.id}>
                                                                        <div key={cf.id} style={{ padding: 10 }} className="flex align-items-center">
                                                                            <Checkbox inputId={cf.id} name="metric" value={cf.title} onChange={(e) => { onCFChange(e, cf) }} checked={cf.selected} />
                                                                            <label htmlFor={cf.id} style={{ cursor: 'pointer' }} className="ml-2">{cf.title} </label>
                                                                        </div>
                                                                    </LazyView>
                                                                )
                                                            })}
                                                        </div>
                                                    </div>

                                                    <div className="flex justify-content-end mt-3">
                                                        <Button
                                                            label={editmode ? 'Update Assignment' : 'Save Assignment'}
                                                            icon="pi pi-save"
                                                            className="p-button-primary p-button-raised"
                                                            onClick={() => { saveAssignedDCF() }}
                                                            style={{ minWidth: '180px' }}
                                                        />
                                                    </div>
                                                </div>
                                            }
                                            {false &&
                                                <div>
                                                    <div style={{ flexDirection: 'row', display: 'flex', marginTop: 10, justifyContent: 'space-between' }}>
                                                        <div className="col-6 grid" >
                                                            <div className="col-7" >
                                                                <span className="p-input-icon-left" style={{ width: '100%' }}>
                                                                    <i className="pi pi-search" />
                                                                    <InputText value={search.df} style={{ width: '100%' }} onChange={searchDF} placeholder="Search Qualitative" />
                                                                </span>
                                                            </div>
                                                            <div className="col-5" style={{ display: 'flex', justifyContent: 'flex-end' }}>
                                                                <Button onClick={() => { showList_(4) }} >{'Distributed Qualitative'} </Button>
                                                            </div>
                                                        </div>
                                                        <div className="col-4" style={{
                                                            display: 'flex',
                                                            alignItems: 'center',
                                                            justifyContent: 'flex-end'
                                                        }}>
                                                            <label
                                                                style={{
                                                                    marginRight: 2,
                                                                }}
                                                            >
                                                                Distributed Qualitative : <span style={{ color: '#005284' }}>{dfreqlistbk.filter((i) => { return i.config !== undefined }).length} / {dfreqlistbk.length}</span>
                                                            </label>
                                                        </div>

                                                    </div>
                                                    <div style={{ flexDirection: 'row', display: 'flex' }}>
                                                        <div className="col-12">


                                                            <div style={{ height: '45vh', overflow: 'scroll', border: '1px solid gray', borderRadius: '10px' }}>

                                                                {dfreqlist.map((df) => {

                                                                    return (
                                                                        <LazyView duration={0.5}>

                                                                            <div key={df.id} style={{ margin: 10, color: 'white' }} className=" flex align-items-center" >
                                                                                <div className="bg-navy br-1 p-1 flex col-12" >
                                                                                    <label htmlFor={df.id} style={{ cursor: 'pointer' }} className="col-11">{df.title} </label>
                                                                                    <i onClick={() => { df.config === undefined ? setDfConfig({ dfid: df.id, country: false, region: false, site: false }) : setDfConfig(df.config); setDfConfigDialog(true) }} className="pi pi-cog col-1 flex justify-content-end cur-pointer" style={{ fontSize: 20 }}></i>
                                                                                </div>

                                                                            </div>

                                                                        </LazyView>
                                                                    )
                                                                })}
                                                            </div>
                                                        </div>



                                                    </div>
                                                </div>
                                            }

                                        </>
                                    }
                                </>
                                :
                                <div className=" col-12">No Clients found, reload page </div>
                            }

                        </>


                        :
                        <div className=" col-12">You have no rights to access this page</div>

                    }
                </div>
            </div>
            <Dialog
                visible={prevdialog}
                style={{
                    width: "450px",
                }}
                header={selectedform.title + " preview"}
                modal
                className="p-fluid"
                footer={prevDialogFooter}
                onHide={() => { setPrevDialog(false) }}
            >
                {renderPreview()}
            </Dialog>
            <Dialog
                visible={dupdpiddialog}
                style={{
                    width: "450px",
                }}
                header={"Duplicated ID List in " + dupdpid.msg}
                modal
                className="p-fluid"
                footer={dupdpidDialogFooter}
                onHide={() => { setDupDPIDDialog(false) }}
            >
                {dupdpid.data.map((id) => {
                    return (<div style={{ margin: 10 }}>
                        <label style={{ padding: 10 }}>{id}</label>
                    </div>)
                })

                }
            </Dialog>
            <Dialog
                visible={prevSListdialog}
                style={{
                    width: "60%",
                }}
                header={selectedlist.title}
                modal
                className="p-fluid"

                onHide={() => { setPrevSListDialog(false) }}
            >
                {renderListPreview()}
            </Dialog>
            <Dialog
                visible={prevSList2dialog}
                style={{
                    width: "60%",
                }}
                header={selectedlist.title}
                modal
                className="p-fluid"

                onHide={() => { setPrevSList2Dialog(false) }}
            >
                {renderListPreview_()}
            </Dialog>
            <Dialog
                visible={dfconfigdialog}
                style={{
                    width: "60%",
                }}
                header={'Qualitative Configuration'}
                modal
                className="p-fluid"

                onHide={() => { setDfConfigDialog(false) }}
            >
                {
                    <div>
                        <div key={'apex'} style={{ padding: 10 }} className="flex align-items-center">
                            <Checkbox inputId={'apex'} name="metric" value={dfconfig.apex} onChange={(e) => { setDfConfig((prev) => { return { ...prev, apex: e.checked } }) }} checked={dfconfig.apex === true} />
                            <label htmlFor={'apex'} style={{ cursor: 'pointer' }} className="ml-2">Apex </label>
                        </div>
                        <div key={'country'} style={{ padding: 10 }} className="flex align-items-center">
                            <Checkbox inputId={'country'} name="metric" value={dfconfig.country} onChange={(e) => { setDfConfig((prev) => { return { ...prev, country: e.checked } }) }} checked={dfconfig.country === true} />
                            <label htmlFor={'country'} style={{ cursor: 'pointer' }} className="ml-2">Country </label>
                        </div>
                        <div key={'region'} style={{ padding: 10 }} className="flex align-items-center">
                            <Checkbox inputId={'region'} name="metric" value={dfconfig.region} onChange={(e) => { setDfConfig((prev) => { return { ...prev, region: e.checked } }) }} checked={dfconfig.region === true} />
                            <label htmlFor={'region'} style={{ cursor: 'pointer' }} className="ml-2">Region </label>
                        </div>
                        <div key={'site'} style={{ padding: 10 }} className="flex align-items-center">
                            <Checkbox inputId={'site'} name="metric" value={dfconfig.site} onChange={(e) => { setDfConfig((prev) => { return { ...prev, site: e.checked } }) }} checked={dfconfig.site === true} />
                            <label htmlFor={'site'} style={{ cursor: 'pointer' }} className="ml-2">Site </label>
                        </div>

                        <div>
                            <Button label="Save & Exit" onClick={() => { saveDFConfig() }} />
                        </div>
                    </div>

                }

            </Dialog>
            <Dialog
                visible={confirmSaveDialog}
                style={{
                    width: "70%",
                    maxHeight: "80vh"
                }}
                header="Assignment Summary"
                modal
                className="p-fluid"
                footer={
                    <div className="flex justify-content-end gap-2">
                        <Button
                            label="Cose"
                            className="p-button-outlined"
                            onClick={() => setConfirmSaveDialog(false)}
                        />
                        {/* <Button
                            label={editmode ? 'Update Assignment' : 'Save Assignment'}
                            icon="pi pi-save"
                            className="p-button-primary"
                            onClick={confirmSaveAssignment}
                        /> */}
                    </div>
                }
                onHide={() => { setConfirmSaveDialog(false) }}
            >
                <div className="assignment-summary" style={{ maxHeight: '60vh', overflow: 'auto' }}>
                    {/* Categories Section */}
                    <div className="surface-0 shadow-1 p-3 border-round">
                        <div className="flex align-items-center mb-3">
                            <i className="pi pi-folder text-primary mr-2" style={{ fontSize: '1.2rem' }}></i>
                            <h4 className="m-0 text-primary">Categories ({assignmentSummary.categories.length})</h4>
                        </div>
                        {assignmentSummary.categories.length > 0 ? (
                            <div className="grid mt-2">
                                <div className="col-2 font-bold text-primary border-bottom-1 border-300 pb-2 mb-2">ID</div>
                                <div className="col-10 font-bold text-primary border-bottom-1 border-300 pb-2 mb-2">Name</div>
                                {assignmentSummary.categories.map((category) => (
                                    <React.Fragment key={category.id}>
                                        <div className="col-2 py-2 border-bottom-1 border-100">{category.id}</div>
                                        <div className="col-10 py-2 border-bottom-1 border-100">{category.title}</div>
                                    </React.Fragment>
                                ))}
                            </div>
                        ) : (
                            <span className="text-gray-500 italic">No categories selected</span>
                        )}
                    </div>

                    {/* Topics Section */}
                    <div className="surface-0 shadow-1 p-3 border-round">
                        <div className="flex align-items-center mb-3">
                            <i className="pi pi-bookmark text-primary mr-2" style={{ fontSize: '1.2rem' }}></i>
                            <h4 className="m-0 text-primary">Topics ({assignmentSummary.topics.length})</h4>
                        </div>
                        {assignmentSummary.topics.length > 0 ? (
                            <div className="grid mt-2">
                                <div className="col-2 font-bold text-primary border-bottom-1 border-300 pb-2 mb-2">ID</div>
                                <div className="col-10 font-bold text-primary border-bottom-1 border-300 pb-2 mb-2">Name</div>
                                {assignmentSummary.topics.map((topic) => (
                                    <React.Fragment key={topic.id}>
                                        <div className="col-2 py-2 border-bottom-1 border-100">{topic.id}</div>
                                        <div className="col-10 py-2 border-bottom-1 border-100">{topic.title}</div>
                                    </React.Fragment>
                                ))}
                            </div>
                        ) : (
                            <span className="text-gray-500 italic">No topics selected</span>
                        )}
                    </div>

                    {/* Indicators Section */}
                    <div className="surface-0 shadow-1 p-3 border-round ">
                        <div className="flex align-items-center mb-3">
                            <i className="pi pi-chart-line text-primary mr-2" style={{ fontSize: '1.2rem' }}></i>
                            <h4 className="m-0 text-primary">Indicators ({assignmentSummary.indicators.length})</h4>
                        </div>
                        {assignmentSummary.indicators.length > 0 ? (
                            <div className="grid mt-2">
                                <div className="col-2 font-bold text-primary border-bottom-1 border-300 pb-2 mb-2">ID</div>
                                <div className="col-10 font-bold text-primary border-bottom-1 border-300 pb-2 mb-2">Name</div>
                                {assignmentSummary.indicators.map((indicator) => (
                                    <React.Fragment key={indicator.id}>
                                        <div className="col-2 py-2 border-bottom-1 border-100">{indicator.id}</div>
                                        <div className="col-10 py-2 border-bottom-1 border-100">{indicator.title}</div>
                                    </React.Fragment>
                                ))}
                            </div>
                        ) : (
                            <span className="text-gray-500 italic">No indicators selected</span>
                        )}
                    </div>

                    {/* DCFs Section */}
                    <div className="surface-0 shadow-1 p-3 border-round ">
                        <div className="flex align-items-center mb-3">
                            <i className="pi pi-file-pdf text-primary mr-2" style={{ fontSize: '1.2rem' }}></i>
                            <h4 className="m-0 text-primary">DCFs ({assignmentSummary.dcfs.length})</h4>
                        </div>
                        {assignmentSummary.dcfs.length > 0 ? (
                            <div className="grid mt-2">
                                <div className="col-2 font-bold text-primary border-bottom-1 border-300 pb-2 mb-2">ID</div>
                                <div className="col-10 font-bold text-primary border-bottom-1 border-300 pb-2 mb-2">Name</div>
                                {assignmentSummary.dcfs.map((dcf) => (
                                    <React.Fragment key={dcf.id}>
                                        <div className="col-2 py-2 border-bottom-1 border-100">{dcf.id}</div>
                                        <div className="col-10 py-2 border-bottom-1 border-100">{dcf.title}</div>
                                    </React.Fragment>
                                ))}
                            </div>
                        ) : (
                            <span className="text-gray-500 italic">No DCFs selected</span>
                        )}
                    </div>

                    {/* SAPs Section */}
                    <div className="surface-0 shadow-1 p-3 border-round ">
                        <div className="flex align-items-center mb-3">
                            <i className="pi pi-database text-primary mr-2" style={{ fontSize: '1.2rem' }}></i>
                            <h4 className="m-0 text-primary">SAP ({assignmentSummary.saps.length})</h4>
                        </div>
                        {assignmentSummary.saps.length > 0 ? (
                            <div className="grid mt-2">
                                <div className="col-2 font-bold text-primary border-bottom-1 border-300 pb-2 mb-2">ID</div>
                                <div className="col-10 font-bold text-primary border-bottom-1 border-300 pb-2 mb-2">Name</div>
                                {assignmentSummary.saps.map((sap) => (
                                    <React.Fragment key={sap.id}>
                                        <div className="col-2 py-2 border-bottom-1 border-100">{sap.id}</div>
                                        <div className="col-10 py-2 border-bottom-1 border-100">{sap.name || sap.title}</div>
                                    </React.Fragment>
                                ))}
                            </div>
                        ) : (
                            <span className="text-gray-500 italic">No SAP Collections selected</span>
                        )}
                    </div>

                    {/* SRFs Section */}
                    <div className="surface-0 shadow-1 p-3 border-round ">
                        <div className="flex align-items-center mb-3">
                            <i className="pi pi-file-excel text-primary mr-2" style={{ fontSize: '1.2rem' }}></i>
                            <h4 className="m-0 text-primary">SRFs ({assignmentSummary.srfs.length})</h4>
                        </div>
                        {assignmentSummary.srfs.length > 0 ? (
                            <div className="grid mt-2">
                                <div className="col-2 font-bold text-primary border-bottom-1 border-300 pb-2 mb-2">ID</div>
                                <div className="col-10 font-bold text-primary border-bottom-1 border-300 pb-2 mb-2">Name</div>
                                {assignmentSummary.srfs.map((srf) => (
                                    <React.Fragment key={srf.id}>
                                        <div className="col-2 py-2 border-bottom-1 border-100">{srf.id}</div>
                                        <div className="col-10 py-2 border-bottom-1 border-100">{srf.title}</div>
                                    </React.Fragment>
                                ))}
                            </div>
                        ) : (
                            <span className="text-gray-500 italic">No SRFs selected</span>
                        )}
                    </div>
                </div>
            </Dialog>
        </div>
    );
};

const comparisonFn = function (prevProps, nextProps) {
    return prevProps.location.pathname === nextProps.location.pathname;
};

export default React.memo(ClientIndicatorAssignment, comparisonFn);
