import React from 'react';
import { Panel } from 'primereact/panel';
import { ScrollPanel } from 'primereact/scrollpanel';
import { Accordion, AccordionTab } from 'primereact/accordion';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { Button } from 'primereact/button';

/**
 * SubmissionAccordionView Component
 * 
 * A reusable component that displays submission data in an accordion format
 * with a data table for each accordion tab.
 * 
 * @param {Object} props - Component props
 * @param {Object} props.data - The data to display in the accordion
 * @param {Function} props.onUpload - Function to call when the upload button is clicked
 * @param {string} props.panelHeader - Header text for the panel (default: "Output from excel")
 * @param {number} props.scrollHeight - Height of the scroll panel (default: 400)
 * @param {string} props.uploadButtonLabel - Label for the upload button (default: "Upload")
 * @param {string} props.filterType - Type of data to filter in the accordion (default: "checklist-group")
 */
const SubmissionAccordionView = ({
  data,
  onUpload,
  panelHeader = "Output from excel",
  scrollHeight = 400,
  uploadButtonLabel = "Upload",
  filterType = "checklist-group"
}) => {
  // Extract the response data from the provided data object
  const responseData = data?.response || [];
  
  // Check if there is any data to display
  const hasData = responseData.length > 0;

  return (
    <Panel header={panelHeader} toggleable>
      <ScrollPanel style={{ width: '100%', height: `${scrollHeight}px` }}>
        <pre style={{ whiteSpace: 'pre-wrap', wordBreak: 'break-word' }}>
          <Accordion>
            {responseData
              ?.filter(x => x.type === filterType)
              ?.map((item, index) => (
                <AccordionTab 
                  key={index} 
                  header={item.label.replace(/(<([^>]+)>)/gi, "") + " - " + item.score}
                >
                  <DataTable 
                    value={item?.questions?.filter(x => x.label !== "checkpoints") || []}
                  >
                    <Column field={'label'} header={'Question'} style={{ width: '80%' }} />
                    <Column 
                      header={'Response'} 
                      body={(rowData) => { 
                        return rowData?.options 
                          ? rowData?.options?.find(x => x.checked)?.label || '-' 
                          : 'No Option' 
                      }} 
                    />
                    <Column field={'score'} header={'Score'} />
                  </DataTable>
                </AccordionTab>
              ))
            }
          </Accordion>
        </pre>
      </ScrollPanel>
      
      {hasData && onUpload && (
        <div className='col-12 flex justify-content-end'>
          <Button label={uploadButtonLabel} onClick={onUpload} />
        </div>
      )}
    </Panel>
  );
};

export default SubmissionAccordionView;
