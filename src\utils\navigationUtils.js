/**
 * Utility functions for safely handling navigation operations
 */

/**
 * Safely navigates to a new route with error handling
 * @param {Object} history - React Router history object
 * @param {string} path - Path to navigate to
 * @param {Object} state - State to pass to the new route
 * @returns {boolean} - Whether the navigation was successful
 */
export const safelyNavigate = (history, path, state = {}) => {
  if (!history || typeof history.push !== 'function') return false;
  
  try {
    // Perform any cleanup needed before navigation
    
    // Navigate to the new route
    history.push(path, state);
    return true;
  } catch (error) {
    console.warn('Error navigating to path:', path, error);
    return false;
  }
};

/**
 * Safely performs cleanup before navigation
 * @param {Function} cleanupFn - Function to call for cleanup
 * @returns {boolean} - Whether the cleanup was successful
 */
export const safelyCleanupBeforeNavigation = (cleanupFn) => {
  if (typeof cleanupFn !== 'function') return false;
  
  try {
    cleanupFn();
    return true;
  } catch (error) {
    console.warn('Error during navigation cleanup:', error);
    return false;
  }
};
