import React from "react";
import {
  <PERSON><PERSON><PERSON>,
  Bar,
  XAxis,
  <PERSON>A<PERSON>s,
  Tooltip,
  Cell,
  ResponsiveContainer,
  CartesianGrid,
} from "recharts";

const ESG_Sales_Service = ({ data }) => {
  console.log(data);

  const maxValues = {
    Environment: 40,
    Social: 40,
    Governance: 15,
    General: 5,
  };

  // Helper function to clamp values between 0 and maxValue for graph display
  const clampForGraph = (value) => Math.max(0, value);
  const clampRemaining = (avgValue, maxValue) =>
    Math.max(0, Math.min(maxValue, maxValue - avgValue));

  const extractScores = (criteriaData) => [
    {
      category: "Environment",
      avgValue: criteriaData?.environment?.summary_score || 0,
      avgValueForGraph: clampForGraph(criteriaData?.environment?.summary_score || 0),
      maxValue: maxValues.Environment,
      remainingToMax: clampRemaining(
        criteriaData?.environment?.summary_score || 0,
        maxValues.Environment
      ),
      achievedColor: "#2C7C69",
      maxColor: "#7FC8A9",
    },
    {
      category: "Social",
      avgValue: criteriaData?.social?.summary_score || 0,
      avgValueForGraph: clampForGraph(criteriaData?.social?.summary_score || 0),
      maxValue: maxValues.Social,
      remainingToMax: clampRemaining(
        criteriaData?.social?.summary_score || 0,
        maxValues.Social
      ),
      achievedColor: "#FC6E51",
      maxColor: "#FEB2A8",
    },
    {
      category: "Governance",
      avgValue: criteriaData?.governance?.summary_score || 0,
      avgValueForGraph: clampForGraph(criteriaData?.governance?.summary_score || 0),
      maxValue: maxValues.Governance,
      remainingToMax: clampRemaining(
        criteriaData?.governance?.summary_score || 0,
        maxValues.Governance
      ),
      achievedColor: "#4A90E2",
      maxColor: "#AFCBFF",
    },
    {
      category: "General",
      avgValue: criteriaData?.general?.summary_score || 0,
      avgValueForGraph: clampForGraph(criteriaData?.general?.summary_score || 0),
      maxValue: maxValues.General,
      remainingToMax: clampRemaining(
        criteriaData?.general?.summary_score || 0,
        maxValues.General
      ),
      achievedColor: "#b0b0b0",
      maxColor: "#e3e3e3",
    },
  ];

  const salesData = extractScores(data.sales_criteria);
  const serviceData = extractScores(data.service_criteria);

  return (
    <div
      style={{
        display: "flex",
        justifyContent: "space-between",
        width: "100%",
        height: 500,
        marginTop:'50px'
      }}
    >
      {/* Sales Chart */}
      <div style={{ width: "48%", height: 400 }}>
        <h3 style={{ textAlign: "center", marginBottom: 20, color: "#555" }}>
          Sales - ESG Score
        </h3>
        <ResponsiveContainer width="100%" height="100%">
          <BarChart
            data={salesData}
            barSize={60}
            margin={{ top: 20, right: 30, left: 20, bottom: 15 }}
          >
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="category" tick={{ fontSize: 12 }} />
            <YAxis domain={[0, 40]} />
            <Tooltip
              formatter={(value, name, props) => {
                const { payload } = props;
                if (name === "Achieved") {
                  return [
                    `${payload.avgValue.toFixed(2)} (Max: ${payload.maxValue})`,
                    name,
                  ];
                }
                if (name === "Remaining") {
                  return [
                    `Remaining: ${payload.remainingToMax.toFixed(2)}`,
                    name,
                  ];
                }
                return [value, name];
              }}
            />
            <Bar
              dataKey="avgValueForGraph"
              stackId="score"
              name="Achieved"
              label={{
                position: "insideTop",
                fill: "#fff",
                formatter: (value) => value.toFixed(1),
              }}
            >
              {salesData.map((entry, index) => (
                <Cell
                  key={`cell-achieved-${index}`}
                  fill={entry.achievedColor}
                />
              ))}
            </Bar>
            <Bar dataKey="remainingToMax" stackId="score" name="Remaining">
              {salesData.map((entry, index) => (
                <Cell key={`cell-remaining-${index}`} fill={entry.maxColor} />
              ))}
            </Bar>
          </BarChart>
        </ResponsiveContainer>
      </div>

      {/* Service Chart */}
      <div style={{ width: "48%", height: 400 }}>
        <h3 style={{ textAlign: "center", marginBottom: 20, color: "#555" }}>
          Service - ESG Score
        </h3>
        <ResponsiveContainer width="100%" height="100%">
          <BarChart
            data={serviceData}
            barSize={60}
            margin={{ top: 20, right: 30, left: 20, bottom: 15 }}
          >
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="category" tick={{ fontSize: 12 }} />
            <YAxis domain={[0, 40]} />
            <Tooltip
              formatter={(value, name, props) => {
                const { payload } = props;
                if (name === "Achieved") {
                  return [
                    `${payload.avgValue.toFixed(2)} (Max: ${payload.maxValue})`,
                    name,
                  ];
                }
                if (name === "Remaining") {
                  return [
                    `Remaining: ${payload.remainingToMax.toFixed(2)}`,
                    name,
                  ];
                }
                return [value, name];
              }}
            />
            <Bar
              dataKey="avgValueForGraph"
              stackId="score"
              name="Achieved"
              label={{
                position: "insideTop",
                fill: "#fff",
                formatter: (value) => value.toFixed(1),
              }}
            >
              {serviceData.map((entry, index) => (
                <Cell
                  key={`cell-achieved-${index}`}
                  fill={entry.achievedColor}
                />
              ))}
            </Bar>
            <Bar dataKey="remainingToMax" stackId="score" name="Remaining">
              {serviceData.map((entry, index) => (
                <Cell key={`cell-remaining-${index}`} fill={entry.maxColor} />
              ))}
            </Bar>
          </BarChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
};

export default ESG_Sales_Service;
