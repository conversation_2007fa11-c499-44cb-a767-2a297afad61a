import React, { useEffect, useState } from "react";
import APIServices from "../../../../service/APIService";
import { API } from "../../../../constants/api_url";
import { useSelector } from "react-redux";
import useForceUpdate from "use-force-update";
import { Button } from "primereact/button";
import { MultiSelect } from "primereact/multiselect";
import { Dialog } from "primereact/dialog";
import { Dropdown } from "primereact/dropdown";
import { InputText } from "primereact/inputtext";

import { saveAs } from "file-saver";
import { DateTime } from "luxon";
import { DataTable } from "primereact/datatable";
import { Column } from "primereact/column";
import Swal from "sweetalert2";
import { Calendar } from "primereact/calendar";

const SupplierAssessmentAssignmentTVS = () => {
    const [assignments, setAssignments] = useState([])
    const [asssupplierlist, setAssSupplierList] = useState([])
    const [activeAss, setActiveAss] = useState(null)
    const [asssrflist, setAssSrfList] = useState([])

    const userList = useSelector(state => state.userlist.userList)
    const supplierList = useSelector(state => state.userlist.supplierList)

    const admin_data = useSelector((state) => state.user.admindetail);
    const login_data = useSelector((state) => state.user.userdetail);
    const [assobj, setAssObj] = useState({ supplierId: null, auditor_ids: [], assessmentStartDate: null, assessmentEndDate: null, auditStartDate: null, auditEndDate: null, srfId: null })
    const [asssupplierobj, setAssSupplierObj] = useState({ supplierId: null })


    const [supplierlist, setSupplierList] = useState([])
    const [auditorlist, setAuditorList] = useState([])
    const [adddialog, setAddDialog] = useState(false)
    const [addsupplierdialog, setAddSupplierDialog] = useState(false)
    const [submitted, setSubmitted] = useState(false)
    const [supplierdialog, setSupplierDialog] = useState(false)

    const forceUpdate = useForceUpdate()


    useEffect(() => {
        let uriString = {

            "include": ["supplierAssignmentSubmission" ]

        };
        const promise0 = APIServices.get(API.SupplierAssessmentAss_Up(admin_data.id))
        const promise1 = APIServices.get(API.SupplierAssessmentAss_Up(admin_data.id) + `?filter=${encodeURIComponent(JSON.stringify(uriString))}`)
        const promise2 = APIServices.get(
            API.AssignDCFClient_UP(admin_data.id)
        );
        const promise3 = APIServices.get(API.SRF)
        const promise4 = APIServices.get(API.GetRole_Up(admin_data.id))

        Promise.all([promise0, promise1, promise2, promise3, promise4]).then((values) => {
            let srf_list = values[3].data
            setAssignments(values[1].data)
           
                setSupplierList(supplierList.filter(i => i.role === 'clientsupplier'))
                setAuditorList(userList.filter(i => values[4].data.some(x => x.user_id === i.id && x.roles && x.roles.includes(17))))

            
        })
    }, [])
    useEffect(() => {
        console.log(supplierlist, auditorlist)
    }, [supplierlist, auditorlist])

    const addfooter = () => {
        return (
            <Button label="Save" onClick={saveAssignment} />
        )
    }
    const addSupplierfooter = () => {
        return (
            <Button label="Save" onClick={saveAssignmentSupplier} />
        )
    }
    const saveAssignmentSupplier = () => {
        setSubmitted(true)
        if (asssupplierobj.supplierId) {
            let newObj = {
                supplierId: asssupplierobj.supplierId
            }
            APIServices.post(API.SupplierList_supass(activeAss.id), { ...newObj, srfId: activeAss.srfId, mailSent: 1, created_on: DateTime.utc(), created_by: login_data.id }).then((res) => {
                let loc = JSON.parse(JSON.stringify(assignments))
                let index = loc.findIndex(i => i.id === activeAss.id)
                if (index !== -1) {
                    if (loc.assessmentSupplierLists) {
                        loc[index].assessmentSupplierLists.push(res.data)
                    } else {
                        loc[index].assessmentSupplierLists = [res.data]
                    }
                }

                setAssignments(loc)

            })
        }

    }
    const saveAssignment = () => {
        setSubmitted(true)
        if (assobj.supplierId && assobj.assessmentStartDate && assobj.assessmentEndDate && assobj.auditStartDate &&assobj.auditor_ids.length) {
            let newObj = {
                assessmentStartDate: assobj.assessmentStartDate,
                assessmentEndDate: assobj.assessmentEndDate,
                auditStartDate: assobj.auditStartDate,
                auditEndDate: assobj.auditEndDate,
                auditor_ids: assobj.auditor_ids,
                
                supplierId: assobj.supplierId
            }
            console.log(assobj.id)
            if (assobj.id) {
                
                APIServices.patch(API.SupplierAssessmentAss_Edit(assobj.id), { ...newObj, modified_on: DateTime.utc(), modified_by: login_data.id }).then((res) => {
                    let loc = JSON.parse(JSON.stringify(assignments))
                    let index = loc.findIndex(i => i.id === assobj.id)
                    if (index !== -1) {
                        loc[index] = { ...assobj, modified_on: DateTime.utc(), modified_by: login_data.id }
                        setAssignments(loc)
                    }
                    setAddDialog(false)
                })
            } else {
                APIServices.post(API.SupplierAssessmentAss_Up(admin_data.id), { ...newObj,id:assobj.id, created_on: DateTime.utc(), created_by: login_data.id }).then((res) => {
                    let loc = JSON.parse(JSON.stringify(assignments))
                    loc.push(res.data)
                    setAssignments(loc)
                    setAddDialog(false)
                })

            }

        }
    }
    const addNewAssignment = () => {
        setSubmitted(false)
        setAssObj({ supplierId: null, auditor_ids: [], assessmentStartDate: null, assessmentEndDate: null, auditStartDate: null, auditEndDate: null, srfId: null })
        setAddDialog(true)

    }
    const addNewAssignmentSupplier = () => {
        setSubmitted(false)
        setAssSupplierObj({ supplierId: null })
        setAddSupplierDialog(true)
    }
    const updateAssObj = (obj, val) => {
        let loc = assobj
        loc[obj] = val
        setAssObj(loc)
        forceUpdate()

    }
    const IdTemplate = (rowData)=>{
        return (<div className ='clr-navy cur-pointer text-underline fw-5' onClick={()=>{editAssignment(rowData)}}> {rowData.id} </div>)
    }
    const editAssignment= (item)=>{
        let items = JSON.parse(JSON.stringify(item));
        if(items.assessmentStartDate){
            items.assessmentStartDate = DateTime.fromISO(items.assessmentStartDate ,{zone:'utc'}).toJSDate()
        }
        if(items.assessmentEndDate){
            items.assessmentEndDate = DateTime.fromISO(items.assessmentEndDate ,{zone:'utc'}).toJSDate()
        }
        if(items.auditStartDate){
            items.auditStartDate = DateTime.fromISO(items.auditStartDate ,{zone:'utc'}).toJSDate()
        }
        if(items.auditEndDate){
            items.auditEndDate = DateTime.fromISO(items.auditEndDate ,{zone:'utc'}).toJSDate()
        }
        setAssObj(items)
        setAddDialog(true)
    }
    const supplierNameTemplate = (rowData) => {

        let txt = ''
        let index =  supplierList.find(i => i.id === rowData.supplierId)
        if(index){
            txt = index.information.supplierName
        }
        console.log(txt)
        return (<>{!txt ? '' : txt} </>)

    }
    const getDate = (date, format) => {

        if (!date) {
            return 'Not Set'
        } if (typeof date === 'string') {
            return DateTime.fromISO(date, { zone: 'utc' }).toLocal().toFormat(format ? format : 'dd-MM-yyyy')
        } else if (DateTime.isDateTime(date)) {
            return date.toFormat(format ? format : 'dd-MM-yyyy')
        } else {
            return DateTime.fromJSDate(date).toLocal().toFormat(format ? format : 'dd-MM-yyyy')
        }

    };
    const dateTemplate = (date) => {
        console.log(getDate(date))
        return getDate(date)

    }
    const resendMail = (rowData) => {
        let assessment = assignments.find(i => i.id === activeAss.id)
        let txt = supplierList.find(i => rowData.supplierId === i.id)
        if (assessment) {
            let body = `<p>Hi ${txt.information.empname}</p>  <p>You have received mail in order to fill your Questionary Response, <a href="${window.location.origin}/supplier/assessment/${rowData.id}">click here </a> to open Supplier Assessment Questionary Form</p><hr/><p style='font-style:italic'>This email is an automated notification. Please do not reply to this message</p>`
            APIServices.post(API.SubmissionMail, { email: [txt.email], subject: 'Supplier Assessment Form - ' + assessment.title, body: body }).then(res => {
                Swal.fire({
                    position: "center",
                    icon: "warning",
                    title: "Mail Sent Successfully",
                    showConfirmButton: false,
                    timer: 1500,
                });
            })

        }


    }
    const actionTemplate = (rowData) => {
        return (<div className='clr-navy text-underline fw-5' onClick={() => { resendMail(rowData) }}>Send Mail</div>)
    }
    const statusTemplate = (rowData) => {
        return (<div className={rowData.supplierAssignmentSubmission ? 'clr-navy text-underline fw-5' : 'fw-5'}>{rowData.supplierAssignmentSubmission ? 'Submitted' : 'NA'}</div>)
    }
    const questionaryTemplate = (rowData) => {

        let txt = asssrflist.find(i => rowData.srfId === i.id)
        console.log(txt)
        return (<>{!txt ? '' : txt.title} </>)

    }
    const openSupplierList = (rowData) => {
        setActiveAss(rowData)
        setAssSupplierList(rowData.assessmentSupplierLists ? rowData.assessmentSupplierLists : [])
        setSupplierDialog(true)
    }
    const suppliersTemplate = (rowData) => {

        return (
            <div className="clr-navy fw-7 text-underline" onClick={() => { openSupplierList(rowData) }} >{rowData.assessmentSupplierLists ? rowData.assessmentSupplierLists.length : 0}</div>
        )
    }
    return (
        <div className="col-12">
            <div className="col-12">
            <div className="col-12 flex align-items-center" >
             <span className="text-big-one"> Hello  {login_data?.role === 'clientadmin' ?  login_data?.information?.companyname : login_data?.information?.empname} ! </span>   <span className="ml-1">{`<${login_data.email}>`} </span>   
                    </div>

          <div
            className="flex col-12 flex-start"
            style={{ flexDirection: "column" }}
          >
            <span className="text-big-one">
            Schedule Supplier Assessment & Audit
            </span>
            <p className="ml-1">Schedule Supplier Self Assessment & track thier actions </p>
            {/* <Tag className="ml-3 p-tag-blue">
              {" "}
              {login_data.role === "clientadmin"
                ? "Enterprise Admin"
                : getRoles(login_data.information)}{" "}
            </Tag> */}
          </div>
                <div className="col-12">
                    <div className="flex justify-content-end">
                        <Button label="Schedule Assessment" onClick={addNewAssignment} />
                    </div>
                </div>
                <div className="col-12">
                    <DataTable value={assignments} >
                    <Column header="Assessment Id" body={IdTemplate} />
                        <Column header="Supplier Name" body={supplierNameTemplate} />
                        
                        <Column header="Assessment Start Date" body={(rowData) => { return dateTemplate(rowData.assessmentStartDate) }} />
                        <Column header="Assessment End Date" body={(rowData) => { return dateTemplate(rowData.assessmentEndDate) }} />
                        <Column header="Audit Start Date" body={(rowData) => { return dateTemplate(rowData.auditStartDate) }} />
                        <Column header="Audit End Date" body={(rowData) => { return dateTemplate(rowData.auditEndDate) }} />



                    </DataTable>
                </div>

                <Dialog header={'Schedule Supplier Assessment & Audit'} style={{ width: '75%' }} visible={adddialog} footer={addfooter} onHide={() => setAddDialog(false)}>
                    <div className="p-fluid grid m-0 p-3">
                        <div className="p-field col-12 ">
                            <label htmlFor="status">Select Supplier<span className="mandatory"> *</span>  </label>
                            <Dropdown id="status" disabled={assobj.id} className='mt-2' value={assobj.supplierId} optionLabel="information.supplierName" optionValue='id' options={supplierList} onChange={(e) => { updateAssObj("supplierId", e.target.value) }} placeholder="Select Supplier" />

                            {submitted && !assobj.supplierId && (
                                <small
                                    className="p-invalid"
                                    style={{
                                        color: "red",
                                    }}
                                >

                                    Select Supplier
                                </small>
                            )}
                        </div>
                       
                        <div className="p-field col-12 ">
                            <label htmlFor="status">Assign Auditors<span className="mandatory"> *</span>  </label>
                            <MultiSelect id="status" className='mt-2' value={assobj.auditor_ids} optionLabel="information.empname" optionValue='id' options={auditorlist} onChange={(e) => { updateAssObj("auditor_ids", e.target.value) }} placeholder="Assign Auditor(s)" />

                            {submitted && !assobj.auditor_ids.length && (
                                <small
                                    className="p-invalid"
                                    style={{
                                        color: "red",
                                    }}
                                >

                                    Select Auditor(s)
                                </small>
                            )}
                        </div>
                        <div className="p-field col-6 ">
                            <label htmlFor="status">Self-Assessment Start Date<span className="mandatory"> *</span>  </label>
                            <Calendar  dateFormat="dd/mm/yy" id="status" className='mt-2' value={assobj.assessmentStartDate} onChange={(e) => { updateAssObj("assessmentStartDate", e.target.value) }} placeholder="Select Self-Assessment Start Date" />

                            {submitted && !assobj.assessmentStartDate && (
                                <small
                                    className="p-invalid"
                                    style={{
                                        color: "red",
                                    }}
                                >

                                    Set Self-Assessment Start Date
                                </small>
                            )}
                        </div>
                        <div className="p-field col-6 ">
                            <label htmlFor="status">Self-Assessment End Date </label>
                            <Calendar id="status"  dateFormat="dd/mm/yy" className='mt-2' value={assobj.assessmentEndDate} onChange={(e) => { updateAssObj("assessmentEndDate", e.target.value) }} placeholder="Select Self-Assessment End Date" />
                            {submitted && !assobj.assessmentEndDate && (
                                <small
                                    className="p-invalid"
                                    style={{
                                        color: "red",
                                    }}
                                >

                                    Set Self-Assessment End Date
                                </small>
                            )}
                        </div>
                        <div className="p-field col-6 ">
                            <label htmlFor="status">Audit Start Date </label>
                            <Calendar id="status"  dateFormat="dd/mm/yy" className='mt-2' value={assobj.auditStartDate} onChange={(e) => { updateAssObj("auditStartDate", e.target.value) }} placeholder="Select Audit Start Date" />
                            {submitted && !assobj.auditStartDate && (
                                <small
                                    className="p-invalid"
                                    style={{
                                        color: "red",
                                    }}
                                >

                                    Set Audit Start Date
                                </small>
                            )}
                        </div>
                        <div className="p-field col-6 ">
                            <label htmlFor="status">Audit End Date </label>
                            <Calendar id="status"  dateFormat="dd/mm/yy" className='mt-2' value={assobj.auditEndDate} onChange={(e) => { updateAssObj("auditEndDate", e.target.value) }} placeholder="Select Audit End Date" />

                        </div>
                    </div>
                </Dialog>
                <Dialog style={{ width: '75%' }} visible={supplierdialog} onHide={() => setSupplierDialog(false)}>
                    <div className="col-12">
                        <div className="flex justify-content-end">
                            <Button label="Add Supplier" onClick={addNewAssignmentSupplier} />
                        </div>
                    </div>
                    <DataTable value={asssupplierlist} >
                        <Column header="Supplier Name" body={supplierNameTemplate} />
                        <Column header="Assessment Start Date" body={(rowData) => { dateTemplate(rowData.assessmentStartDate) }} />
                        <Column header="Assessment End Date" body={(rowData) => { dateTemplate(rowData.assessmentEndDate) }} />
                        <Column header="Assessment Status" />                        
                        <Column header="Supplier Score" />
                        <Column header="Audit Start Date" body={(rowData) => { dateTemplate(rowData.auditStartDate) }} />
                        <Column header="Audit End Date" body={(rowData) => { dateTemplate(rowData.auditEndDate) }} />
                        <Column header="Audit Score" />
                        <Column header="Audit Report" />

                    </DataTable>
                </Dialog>
                <Dialog style={{ width: '75%' }} visible={addsupplierdialog} footer={addSupplierfooter} onHide={() => setAddSupplierDialog(false)}>
                    <div className="p-fluid grid m-0 p-3">
                        <div className="p-field col-12 ">
                            <label htmlFor="status">Select Suppliers<span className="mandatory"> *</span>  </label>
                            <Dropdown id="status" className='mt-2' value={asssupplierobj.supplierId} optionLabel="information.supplierName" optionValue='id' options={supplierlist} onChange={(e) => { setAssSupplierObj((prev) => ({ ...prev, supplierId: e.target.value })) }} placeholder="Select Supplier" />

                            {submitted && !asssupplierobj.supplierId && (
                                <small
                                    className="p-invalid"
                                    style={{
                                        color: "red",
                                    }}
                                >

                                    Select Supplier
                                </small>
                            )}
                        </div>
                    </div>
                </Dialog>

            </div>

        </div>
    )
}

const comparisonFn = function (prevProps, nextProps) {
    return prevProps.location.pathname === nextProps.location.pathname;
};

export default React.memo(SupplierAssessmentAssignmentTVS, comparisonFn);