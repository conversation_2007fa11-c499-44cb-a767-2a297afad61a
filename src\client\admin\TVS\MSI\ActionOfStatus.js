import React from 'react'
import { Accordion, AccordionTab } from 'primereact/accordion';
import Typography from '@mui/material/Typography'
import { useSelector } from 'react-redux';
import moment from 'moment';
import { AttachmentComponent } from '../../../../components/Attachment';
import { Card } from 'primereact/card';


function ActionOfStatus({ report }) {
  const select = useSelector((state) => state.userlist.userList);

  const customFontStyle = {
    fontFamily: 'Lato, sans-serif',

  }
  const getName = (id) => {
    const user = select.find(user => user.id === id);
    return user?.email || '';
  };
  // Get all actions for reference
  const allActions = report.supplierActions || [];

  console.log(allActions, ' Actions')
  // Filter to show only Non-compliance actions (category 3)
  const category3Items = allActions.filter(item => item.categoryOfFinding === 3);
  const type3Count = category3Items.filter(item => item.type === 3);

  // Display only Non-compliance actions
  const displayActions = category3Items;

  const renderMetaInformation = () => {
    return (
      <div className="grid">
        {/* Action Information Card */}
        <div className="md:col-6">
          <Card className="shadow-2 mt-3 mb-3 h-full"
            title={
              <div className="flex align-items-center">
                <i className="pi pi-info-circle mr-2" style={{ color: '#315975' }}></i>
                <span className="font-bold">Action Information</span>
              </div>
            }
          >
            <div>
              <div className="mb-3">
                <p className="m-0">{report?.description || 'Water monitoring flow meter need to calibrate by external agency.'}</p>
                <span className="bg-primary text-white px-2 py-1 rounded-sm text-xs">Assigned</span>
              </div>

              <div className="grid">
                <div className="col-6">
                  <div className="field mb-2">
                    <label className="block text-sm font-medium text-gray-700">ID</label>
                    <div>{report?.actionId || '788'}</div>
                  </div>
                </div>

                <div className="col-6">
                  <div className="field mb-2">
                    <label className="block text-sm font-medium text-gray-700">Category</label>
                    <div>Opportunity for Improvement</div>
                  </div>
                </div>

                <div className="col-6">
                  <div className="field mb-2">
                    <label className="block text-sm font-medium text-gray-700">Due Date</label>
                    <div>NA</div>
                  </div>
                </div>

                <div className="col-6">
                  <div className="field mb-2">
                    <label className="block text-sm font-medium text-gray-700">Created On</label>
                    <div>{moment(report?.created_on).format('DD-MM-YYYY') || '29-04-2025'}</div>
                  </div>
                </div>

                <div className="col-6">
                  <div className="field mb-2">
                    <label className="block text-sm font-medium text-gray-700">Created By</label>
                    <div>{report?.created_by || 'Prabhakaran'}</div>
                  </div>
                </div>

                <div className="col-12">
                  <div className="field mb-2">
                    <label className="block text-sm font-medium text-gray-700">Description</label>
                    <div className="p-2 border-1 border-gray-300 border-round bg-gray-50">
                      NA
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </Card>
        </div>

        {/* Supplier Information Card */}
        <div className="md:col-6">
          <Card className="shadow-2 mt-3 mb-3 h-full"
            title={
              <div className="flex align-items-center">
                <i className="pi pi-building mr-2" style={{ color: '#315975' }}></i>
                <span className="font-bold">Supplier Information</span>
              </div>
            }
          >
            <div className="grid">
              <div className="col-6">
                <div className="field mb-2">
                  <label className="block text-sm font-medium text-gray-700">Supplier</label>
                  <div>{report?.supplierName || 'NATIONAL PLASTIC TECHNOLOGIES LIMIT'}</div>
                </div>
              </div>

              <div className="col-6">
                <div className="field mb-2">
                  <label className="block text-sm font-medium text-gray-700">Location</label>
                  <div>{report?.location || 'HOSUR'}</div>
                </div>
              </div>

              <div className="col-6">
                <div className="field mb-2">
                  <label className="block text-sm font-medium text-gray-700">MSI ID</label>
                  <div>{report?.msiId || 'MSI-21480-19032025'}</div>
                </div>
              </div>

              <div className="col-6">
                <div className="field mb-2">
                  <label className="block text-sm font-medium text-gray-700">Stat</label>
                  <div>{report?.stat || 'In Progress'}</div>
                </div>
              </div>

              <div className="col-12">
                <h5 className="mt-2 mb-2 flex align-items-center">
                  <i className="pi pi-calendar mr-2" style={{ color: '#315975' }}></i>
                  Audit & Assessment Periods
                </h5>
              </div>

              <div className="col-6">
                <div className="field mb-2">
                  <label className="block text-sm font-medium text-gray-700">Audit Period</label>
                  <div>{moment(report?.auditStartDate).format('DD-MM-YYYY') || '28-04-2025'} to {moment(report?.auditEndDate).format('DD-MM-YYYY') || '29-04-2025'}</div>
                </div>
              </div>

              <div className="col-6">
                <div className="field mb-2">
                  <label className="block text-sm font-medium text-gray-700">Assessment Period</label>
                  <div>{moment(report?.assessmentStartDate).format('DD-MM-YYYY') || '19-03-2025'} to {moment(report?.assessmentEndDate).format('DD-MM-YYYY') || '10-04-2025'}</div>
                </div>
              </div>

              <div className="col-12">
                <h5 className="mt-2 mb-2 flex align-items-center">
                  <i className="pi pi-users mr-2" style={{ color: '#315975' }}></i>
                  Team Members
                </h5>
              </div>

              <div className="col-6">
                <div className="field mb-2">
                  <label className="block text-sm font-medium text-gray-700">Team 1</label>
                  <div>{report?.team1Members?.length ? report.team1Members.join(', ') : 'Not Assigned'}</div>
                </div>
              </div>

              <div className="col-6">
                <div className="field mb-2">
                  <label className="block text-sm font-medium text-gray-700">Team 2</label>
                  <div>{report?.team2Members?.length ? report.team2Members.join(', ') : 'Not Assigned'}</div>
                </div>
              </div>

              <div className="col-6">
                <div className="field mb-2">
                  <label className="block text-sm font-medium text-gray-700">Team 3</label>
                  <div>{report?.team3Members?.length ? report.team3Members.join(', ') : 'Not Assigned'}</div>
                </div>
              </div>

              <div className="col-6">
                <div className="field mb-2">
                  <label className="block text-sm font-medium text-gray-700">Team 4</label>
                  <div>{report?.team4Members?.length ? report.team4Members.join(', ') : 'Not Assigned'}</div>
                </div>
              </div>
            </div>
          </Card>
        </div>
      </div>
    );
  };

  const getStatusAction = (item, outerIndex) => {
    const latestActionIndex = item?.supplierActionHistories?.length || 0;
    const latestActionNumber = `${outerIndex + 1}.${latestActionIndex > 0 ? latestActionIndex - 1 : 0}`; // e.g., 1.0, 1.1, etc.

    // Get category label
    const getCategoryLabel = (categoryId) => {
      switch(categoryId) {
        case 1: return "Good Practices";
        case 2: return "Opportunity of Improvement";
        case 3: return "Non-compliance";
        default: return "Unknown";
      }
    };

    // Get non-compliance type label if applicable
    const getNonComplianceTypeLabel = (typeId) => {
      if (item.categoryOfFinding !== 3) return null;

      switch(typeId) {
        case 1: return "Regulatory (Major)";
        case 2: return "Regulatory (Minor)";
        case 3: return "Minor";
        default: return null;
      }
    };

    // Determine badge color based on category
    const getBadgeClass = (categoryId) => {
      switch(categoryId) {
        case 1: return "status-tag-green"; // Good Practices
        case 2: return "status-tag-blue";  // Opportunity of Improvement
        case 3: return "status-tag-orange"; // Non-compliance
        default: return "status-tag-orange";
      }
    };

    return (
      <Typography variant="body1" style={customFontStyle}>
        <div className='row d-flex mb-2'>
          <div className='col-6'>
            <h4>{`Action ${latestActionNumber}`}</h4>
            <p className='p-0' style={{ fontSize: '14px' }}><strong>Finding : </strong>{item.finding}</p>
            <p className='p-0' style={{ fontSize: '14px' }}><strong>Category : </strong>{getCategoryLabel(item.categoryOfFinding)}</p>
            {item.categoryOfFinding === 3 && item.nonComplianceType && (
              <p className='p-0' style={{ fontSize: '14px' }}><strong>Non-compliance Type : </strong>{getNonComplianceTypeLabel(item.nonComplianceType)}</p>
            )}
          </div>
          <div className='col-6'>
            <span className={`badge fw-bold ${getBadgeClass(item.categoryOfFinding)}`}>
              {(item.type === 1 || item.type === null)
                ? "Action Assigned"
                : item.type === 2
                  ? 'Action Returned'
                  : item.type === 3
                    ? "Action Completed"
                    : ''}
            </span>

          </div>
        </div>
        <div className='row d-flex'>
          <div className='col-12'>
            <p className='p-0' style={{ fontSize: '14px' }}>{item.description}</p>
            <strong className='p-0' style={{ fontSize: '14px' }}>{item.actionId}</strong>
          </div>
          <div className=''>
            <p className='p-0' style={{ fontSize: '14px' }}> <strong> Created on :</strong> {moment(item.approved_on || item.created_on).format('DD-MM-YYYY')}</p>
            <p className='p-0' style={{ fontSize: '14px' }}> <strong> Status :</strong> {item.status ? item.status.charAt(0).toUpperCase() + item.status.slice(1) : 'Initiated'}</p>
          </div>
        </div>
      </Typography>
    );
  };


  const getCMData = (item, outerIndex) => {

    return (
      <>
        {item?.supplierActionHistories?.map((history, index) => {
          return (
            <div key={index} style={{ boxShadow: '0px 0px 4px 4px #f5f5f5', padding: 10, marginBottom: 15 }}>
              <p className='obs-title'>Action {outerIndex + 1}.{index}</p>
              <div className='row' style={{ marginBottom: 10 }}>
                <p className='obs-title'>Root Cause</p>
                <p className='obs-content' dangerouslySetInnerHTML={{ __html: history.rootCause }}></p>
              </div>
              <div className='row' style={{ marginBottom: 10 }}>
                <p className='obs-title'>Corrective Action</p>
                <p className='obs-content' dangerouslySetInnerHTML={{ __html: history.correctiveAction }}></p>
              </div>
              <div className='row' style={{ marginBottom: 10 }}>
                <p className='obs-title'>Action Taken</p>
                <p className='obs-content' dangerouslySetInnerHTML={{ __html: history.actionTaken }}></p>
              </div>
              <div className='row' style={{ marginBottom: 10 }}>
                <p className='obs-title'>Action Taken Date</p>

                <p className='obs-content'>{moment(history.supplier_submitted_on).format('DD-MM-YYYY hh:mm A')}</p>
              </div>

              <AttachmentComponent mandatory={true} edit={0} documents={history.supplierAttachments
              } labels={'uploads'} hint={''} />

              {history.reject === 1 &&

                <>
                  <div className='row' style={{ marginBottom: 10 }}>
                    <p className='obs-title'>Approver Comments</p>
                    <p className='obs-content'>{history.approverComments}</p>
                  </div>

                  <div className='row' style={{ marginBottom: 10 }}>
                    <p className='obs-title'>Rejected Date</p>
                    <p className='obs-content'>{moment(history.returned_on).format('DD-MM-YYYY hh:mm A')}</p>
                  </div>
                  <div className='row' style={{ marginBottom: 10 }}>
                    <p className='obs-title'>Rejected By</p>
                    <p className='obs-content'>{getName(history.returned_by)}</p>
                  </div>

                </>}

              {(history.reject === 0 && history.type === 2) &&

                <>
                  <div className='row' style={{ marginBottom: 10 }}>
                    <p className='obs-title'>Approver Comments</p>
                    <p className='obs-content'>{history.approverComments}</p>
                  </div>
                  <div className='row' style={{ marginBottom: 10 }}>
                    <p className='obs-title'>Approved On</p>
                    <p className='obs-content'>{moment(history.approved_on).format('DD-MM-YYYY hh:mm A')}</p>
                  </div>
                  <div className='row' style={{ marginBottom: 10 }}>
                    <p className='obs-title'>Approved By</p>
                    <p className='obs-content'>{getName(history.approved_by)}</p>
                  </div>
                </>}

              {(history.reject === 0 && history.type === 3) &&

                <>
                  <div className='row' style={{ marginBottom: 10 }}>
                    <p className='obs-title'>Approver Comments</p>
                    <p className='obs-content'>{history.approverComments}</p>
                  </div>
                  <div className='row' style={{ marginBottom: 10 }}>
                    <p className='obs-title'>Approved On</p>
                    <p className='obs-content'>{moment(history.approved_on).format('DD-MM-YYYY hh:mm A')}</p>
                  </div>
                  <div className='row' style={{ marginBottom: 10 }}>
                    <p className='obs-title'>Approved By</p>
                    <p className='obs-content'>{getName(history.approved_by)}</p>
                  </div>
                </>}


            </div>
          );
        })}
      </>
    );
  };


  // Calculate action counts
  const nonComplianceCount = displayActions.length;

  return (
    <div>
      {renderMetaInformation()}

      <h4 className="mt-3">Non-compliance Actions ({nonComplianceCount})</h4>

      <div className="alert alert-warning">
        <i className="pi pi-info-circle me-2"></i>
        Only Non-compliance actions are displayed below.
      </div>

      <Accordion>
        {displayActions.map((item, i) => {
          return (
            <AccordionTab
              header={getStatusAction(item, i)}
              headerClassName='test-header'
              key={i}
            >
              {getCMData(item, i)}
            </AccordionTab>
          )
        })}
      </Accordion>

      {/* Show message if no non-compliance actions */}
      {displayActions.length === 0 && (
        <div className="alert alert-info mt-3">
          <p className="mb-0">No Non-compliance actions found. There may be Good Practices or Opportunity of Improvement actions that are not displayed here.</p>
          <p className="mb-0">Total actions: {allActions.length}</p>
        </div>
      )}

    </div>
  )
}

export default ActionOfStatus
