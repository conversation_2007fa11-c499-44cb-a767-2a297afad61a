import React from 'react';

const headers = [
  "Policy & Commitment",
  "Water Risk Assessment",
  "Efficiency & Reduction Initiatives",
  "Wastewater Treatment & Discharge",
  "Water Stewardship & Community Engagement",
  "Targets & Performance Tracking",
  "Water accounting or auditing performed",
  "Technologies or practices to recycle or reuse water",
  "Product Water footprint"
];

const TableHeader = () => {
  return (
    <thead>
      <tr>
        <th className="header-cell">User</th>
        {headers.map((header, index) => (
          <th key={index} className="header-cell">{header}</th>
        ))}
      </tr>
    </thead>
  );
};

export default TableHeader;
