import React from 'react';
import { Dropdown } from 'primereact/dropdown';

/**
 * A reusable dropdown component for sorting chart data
 * 
 * @param {Object} props - Component props
 * @param {string} props.value - Current sort value
 * @param {Array} props.options - Array of sort options
 * @param {Function} props.onChange - Function to call when sort option changes
 * @param {Object} props.style - Additional styles for the dropdown
 * @returns {JSX.Element} - Dropdown component for sorting
 */
export const ChartSortDropdown = ({ 
  value, 
  options, 
  onChange, 
  style = {} 
}) => {
  return (
    <div className="chart-sort-dropdown" style={{ marginRight: "10px", ...style }}>
      <Dropdown
        value={value}
        options={options}
        onChange={onChange}
        placeholder="Sort by"
        style={{ width: "200px", height: "30px", ...style }}
      />
    </div>
  );
};

/**
 * Creates standard sort options for chart data
 * 
 * @param {Array} fields - Array of field objects with name and label properties
 * @returns {Array} - Array of sort options
 */
export const createSortOptions = (fields) => {
  const options = [{ label: 'None', value: 'none' }];
  
  fields.forEach(field => {
    if (field.type === 'string') {
      options.push({ 
        label: `${field.label} (A-Z)`, 
        value: `${field.name}_asc` 
      });
      options.push({ 
        label: `${field.label} (Z-A)`, 
        value: `${field.name}_desc` 
      });
    } else {
      options.push({ 
        label: `${field.label} (High-Low)`, 
        value: `${field.name}_desc` 
      });
      options.push({ 
        label: `${field.label} (Low-High)`, 
        value: `${field.name}_asc` 
      });
    }
  });
  
  return options;
};

/**
 * Sorts data based on the selected sort field and direction
 * 
 * @param {Array} data - Array of data objects to sort
 * @param {string} sortField - Field to sort by in format 'fieldName_direction'
 * @returns {Array} - Sorted array of data objects
 */
export const sortData = (data, sortField) => {
  if (!sortField || sortField === 'none') return data;
  
  const [fieldName, direction] = sortField.split('_');
  const sortMultiplier = direction === 'asc' ? 1 : -1;
  
  return [...data].sort((a, b) => {
    // Handle string fields (use localeCompare)
    if (typeof a[fieldName] === 'string') {
      return sortMultiplier * a[fieldName].localeCompare(b[fieldName]);
    } 
    // Handle numeric fields
    else {
      return sortMultiplier * (a[fieldName] - b[fieldName]);
    }
  });
};

export default ChartSortDropdown;
