import React, { useEffect, useState, useRef } from "react";
import { Card } from "primereact/card";
import { <PERSON>b<PERSON>ie<PERSON>, Tab<PERSON>anel } from "primereact/tabview";
import { DataTable } from "primereact/datatable";
import { Column } from "primereact/column";
import { MultiSelect } from "primereact/multiselect";
import { InputText } from "primereact/inputtext";
import { Dialog } from "primereact/dialog";
import { But<PERSON> } from "primereact/button";
import DocViewer, { DocViewerRenderers } from "@cyntler/react-doc-viewer";
import * as XLSX from "xlsx";
import FileSaver from "file-saver";
import Swal from "sweetalert2";

import QualitativeResponse from "./QualitativeResponse";
import APIServices from '../../../service/APIService';
import { API } from '../../../constants/api_url';
import { useSelector } from "react-redux";
import moment from "moment";
const QualitativeDataResponse = () => {
    const admin_data = useSelector((state) => state.user.admindetail)
    const [rawsitelist, setRawSitelist] = useState([]);
    const [activeTabIndex, setActiveTabIndex] = useState(0);

    const [assignedData, setAssignedData] = useState([]);
    const [consolidatordata, setConsolidatorData] = useState([]);
    const [categoryData, setCategoryData] = useState([]);
    const [allDocuments, setAllDocuments] = useState([]);

    const userList = useSelector(state => state.userlist.userList)

    const getUser = (id) => {
        let user_name = 'Not Found'
        let index = userList.findIndex(i => i.id === Number(id))
        if (index !== -1) {
            user_name = userList[index].information.empname
        }
        return user_name
    }
    const getCoverageText = (rowData, rawsitelist) => {
        let text = "";

        if (rowData.level === 0) {
            text = "Corporate";
        } else if (rowData.level === 1) {
            let country_index = rawsitelist.findIndex(
                (i) => i.id === rowData.locationId
            );
            if (country_index !== -1) {
                text = rawsitelist[country_index].name;
            }
        } else if (rowData.level === 2) {
            let city_index = rawsitelist
                .flatMap((i) =>
                    i.locationTwos.flatMap((j) =>
                        j.locationThrees.map((k) => {
                            return {
                                site_id: k.id,
                                site_name: k.name,
                                city_id: j.id,
                                city_name: j.name,
                                country_id: i.id,
                                country_name: i.name,
                            };
                        })
                    )
                )
                .findIndex((i) => {
                    return i.city_id === rowData.locationId;
                });
            if (city_index !== -1) {
                text = rawsitelist.flatMap((i) =>
                    i.locationTwos.flatMap((j) =>
                        j.locationThrees.map((k) => {
                            return {
                                site_id: k.id,
                                site_name: k.name,
                                city_id: j.id,
                                city_name: j.name,
                                country_id: i.id,
                                country_name: i.name,
                            };
                        })
                    )
                )[city_index].city_name;
            }
        } else if (rowData.level === 3) {
            let site_index = rawsitelist
                .flatMap((i) =>
                    i.locationTwos.flatMap((j) =>
                        j.locationThrees.map((k) => {
                            return {
                                site_id: k.id,
                                site_name: k.name,
                                city_id: j.id,
                                city_name: j.name,
                                country_id: i.id,
                                country_name: i.name,
                            };
                        })
                    )
                )
                .findIndex((i) => {
                    return i.site_id === rowData.locationId;
                });
            if (site_index !== -1) {
                text = rawsitelist.flatMap((i) =>
                    i.locationTwos.flatMap((j) =>
                        j.locationThrees.map((k) => {
                            return {
                                site_id: k.id,
                                site_name: k.name,
                                city_id: j.id,
                                city_name: j.name,
                                country_id: i.id,
                                country_name: i.name,
                            };
                        })
                    )
                )[site_index].site_name;
            }
        }
        return text;
    };


    function mergeGroupedSectionData(apiData, localData) {
        const updatedLocalData = {};

        Object.entries(localData).forEach(([categoryName, entries]) => {
            const matchedCategory = apiData.find(cat => cat.name === categoryName);
            if (!matchedCategory) {
                updatedLocalData[categoryName] = entries;
                return;
            }

            updatedLocalData[categoryName] = entries.map(entry => {
                const matchedTopic = matchedCategory.qTopics.find(topic => topic.name === entry.subHeading);
                if (!matchedTopic) return entry;

                const sectionNames = matchedTopic.qSections.map(section => section.name);
                const sectionMap = {};

                // Loop through each user in data
                Object.entries(entry.data).forEach(([userId, userSections]) => {
                    const enrichedSections = {};
                    const enrichedConsolidatedSections = {};

                    sectionNames.forEach(sectionName => {
                        const consolidatedData = consolidatordata.find(x => x.qSectionId === (matchedTopic.qSections.find(s => s.name === sectionName)?.id || null));
                        const assignmentData = assignedData.find(x => x.qSectionId === matchedTopic.qSections.find(s => s.name === sectionName)?.id);

                        enrichedConsolidatedSections[sectionName] = (userId === 'Consolidate' && userSections[sectionName]) || {
                            name: sectionName,
                            status: consolidatedData?.response ? 'Started' : 'Not Started',
                            isLocked: false,
                            sectionId: matchedTopic.qSections.find(s => s.name === sectionName)?.id,
                            assignmentId: consolidatedData?.id,
                            form: assignmentData?.qSection?.srf?.data1
                                ? JSON.parse(assignmentData?.qSection?.srf.data1).map((field) => {
                                    const responseValue = consolidatedData?.response?.[field.name] || null

                                    // Handle radio-group
                                    if (field.type === "radio-group" && Array.isArray(field.values)) {
                                        return {
                                            ...field,
                                            values: field.values.map(option => ({
                                                ...option,
                                                selected: option.value === responseValue
                                            })),
                                            value: responseValue || null
                                        };
                                    }

                                    // Handle checkbox-group
                                    if (field.type === "checkbox-group" && Array.isArray(field.values)) {
                                        const selectedValues = Array.isArray(responseValue) ? responseValue : [];
                                        return {
                                            ...field,
                                            values: field.values.map(option => ({
                                                ...option,
                                                selected: selectedValues.includes(option.value)
                                            })),
                                            value: selectedValues
                                        };
                                    }

                                    // Handle text, textarea, file
                                    if (["text", "textarea", "file"].includes(field.type)) {
                                        return {
                                            ...field,
                                            value: responseValue || ""
                                        };
                                    }

                                    // Default fallback
                                    return {
                                        ...field,
                                        value: responseValue || ""
                                    };
                                })
                                : null
                        };

                        enrichedSections[sectionName] = userSections[sectionName] || {
                            id: matchedTopic.qSections.find(s => s.name === sectionName)?.id || null,
                            name: sectionName,
                            status: 'Blocked',
                            dueDate: null,
                            isLocked: false,
                            form: [],
                        };
                    });
                    console.log(enrichedConsolidatedSections)
                    sectionMap['Consolidate'] = enrichedConsolidatedSections;
                    sectionMap[userId] = enrichedSections;
                });
                return {
                    ...entry,
                    data: sectionMap
                };
            });
        });

        return updatedLocalData;
    }

    // Removed unused function mergeGroupedSectionData_





    const transformAssignedDataByCategory = (assignedData, consolidatorData) => {
        const grouped = {};

        assignedData.forEach((item) => {
            console.log(item)
            const title = item.qCategory.name;
            const subHeading = item.qTopic.name;
            const location = getCoverageText(item, rawsitelist) || 'N/A';

            const sectionName = item?.qSection?.name ?? '';
            const dueDate = moment.utc(item.due_date).local().format("DD/MM/YYYY");
            const key = `${title}__${subHeading}__${location}`;
            if (!grouped[title]) grouped[title] = {};
            if (!grouped[title][key]) {
                grouped[title][key] = {
                    title,
                    subHeading,
                    sectionId: item.qSectionId,
                    location,
                    data: {} // per-user responses
                };
            }

            const allUserResponses = item.response || {};

            Object.entries(allUserResponses).forEach(([userId, userResp]) => {
                if (!grouped[title][key].data[userId]) {
                    grouped[title][key].data[userId] = {};
                }

                grouped[title][key].data[userId][sectionName] = {
                    name: sectionName,
                    status: userResp.status || "Not Started",
                    dueDate,
                    assignment: item,
                    sectionId: item.qSectionId,
                    reporter: getUser(userId),
                    isLocked: false,
                    id: item.id, isConsolidator: userId === 'Consolidate',
                    assignmentId: consolidatorData.find(x => x.qSectionId === item.qSectionId)?.id,
                    form: item?.qSection?.srf?.data1
                        ? JSON.parse(item.qSection.srf.data1).map((field) => {
                            const responseValue = userResp?.[field.name];

                            // Handle radio-group
                            if (field.type === "radio-group" && Array.isArray(field.values)) {
                                return {
                                    ...field,
                                    values: field.values.map(option => ({
                                        ...option,
                                        selected: option.value === responseValue
                                    })),
                                    value: responseValue || null
                                };
                            }

                            // Handle checkbox-group
                            if (field.type === "checkbox-group" && Array.isArray(field.values)) {
                                const selectedValues = Array.isArray(responseValue) ? responseValue : [];
                                return {
                                    ...field,
                                    values: field.values.map(option => ({
                                        ...option,
                                        selected: selectedValues.includes(option.value)
                                    })),
                                    value: selectedValues
                                };
                            }

                            // Handle text, textarea, file
                            if (["text", "textarea", "file"].includes(field.type)) {
                                return {
                                    ...field,
                                    value: responseValue || ""
                                };
                            }

                            // Default fallback
                            return {
                                ...field,
                                value: responseValue || ""
                            };
                        })
                        : null
                };
            });
        });

        // Flatten grouped structure to final output
        const finalGroups = {};

        Object.entries(grouped).forEach(([category, group]) => {
            finalGroups[category] = [];

            Object.values(group).forEach(({ title, subHeading, location, data }) => {
                finalGroups[category].push({ title, subHeading, location, data });
            });
        });

        const merged = mergeGroupedSectionData(categoryData, finalGroups);
        console.log(merged)
        return merged
    };













    const [groupedData, setGroupedData] = useState({});

    useEffect(() => {
        if (consolidatordata.length) {
            const grouped = transformAssignedDataByCategory(assignedData, consolidatordata);
            console.log("Grouped Data", grouped);
            setGroupedData(grouped);

            // Extract all documents from the grouped data
            collectAllDocuments(grouped);
        }
    }, [assignedData, consolidatordata]);

    // Function to collect all documents from the grouped data
    const collectAllDocuments = (groupedData) => {
        const documents = [];

        // Loop through all categories
        Object.entries(groupedData).forEach(([_, categoryItems]) => {
            // Loop through all items in the category
            categoryItems.forEach(item => {
                const { title, subHeading, location } = item;

                // Create a map of all sections
                const sectionMap = {};
                Object.entries(item.data).forEach(([userId, sections]) => {
                    Object.entries(sections).forEach(([sectionName, sectionData]) => {
                        if (!sectionMap[sectionName]) sectionMap[sectionName] = [];
                        sectionMap[sectionName].push({
                            ...sectionData,
                            userId,
                            location,
                            form: sectionData.form || []
                        });
                    });
                });

                // Extract documents from each section
                Object.entries(sectionMap).forEach(([sectionName, responses]) => {
                    responses.forEach(res => {
                        if (res.form) {
                            res.form
                                .filter(field => field.type === "file" && !!field.value)
                                .forEach(fileField => {
                                    const userId = res.userId;
                                    documents.push({
                                        category: title,
                                        topic: subHeading,
                                        section: sectionName,
                                        user: typeof userId === 'number' || !isNaN(Number(userId)) ? getUser(userId) : userId,
                                        label: fileField.label,
                                        file: fileField.value,
                                        fileName: fileField.value?.split("/").pop() || '',
                                        location: location
                                    });
                                });
                        }
                    });
                });
            });
        });

        setAllDocuments(documents);
    };


    const getAssignedQualitativeData = async () => {

        const response = await APIServices.post(API.GetAssignedQualitative_consolidator, {
            userId: 0,
            userProfileId: admin_data.id
        });

        if (response.status === 200 && response.data.status) {
            console.log(response.data)
            setAssignedData(response.data?.reporter?.filter(x => x.qSection && x.qTopic && x.qSection)?.map((i) => ({ ...i, response: { ...i?.response || {}, ...(response.data.consolidation.find(x => x.qCategoryId === i.qCategoryId && x.qTopicId === i.qTopicId && x.qSectionId === i.qSectionId)?.response || {}) } })) || []);
            setConsolidatorData(response.data?.consolidation || []);
        }
    };

    useEffect(async () => {
        let uriString = {
            include: [
                {
                    relation: "locationTwos",
                    scope: { include: [{ relation: "locationThrees" }] },
                },
            ],
        };
        const locationData = await APIServices.get(
            API.LocationOne_UP(admin_data.id) +
            `?filter=${encodeURIComponent(JSON.stringify(uriString))}`
        );
        const categoryData = await APIServices.get(API.getAllQCategory)
        let shapedQlCategory = categoryData.data
            .map((item) => {
                if (item.qTopics) {
                    item.qTopics = item?.qTopics.filter(
                        (locationTwo) =>
                            locationTwo?.qSections &&
                            locationTwo.qSections.length > 0
                    );
                }
                return item;
            })
            .filter((item) => item?.qTopics && item?.qTopics.length > 0);

        setCategoryData(shapedQlCategory)
        const shapedSite = locationData?.data?.map((item) => {
            if (item.locationTwos) {
                item.locationTwos = item.locationTwos.filter(
                    (locationTwo) =>
                        locationTwo.locationThrees &&
                        locationTwo.locationThrees.length > 0
                );
            }
            return item;
        }).filter((item) => item.locationTwos && item.locationTwos.length > 0);
        setRawSitelist(shapedSite)
        getAssignedQualitativeData();
    }, []);

    // Removed unused overlay panel refs


    // State for document filtering
    const [globalFilterValue, setGlobalFilterValue] = useState('');
    const [filters, setFilters] = useState({
        category: { value: null, matchMode: 'in' },
        topic: { value: null, matchMode: 'in' },
        section: { value: null, matchMode: 'in' },
        location: { value: null, matchMode: 'in' },
        user: { value: null, matchMode: 'in' }
    });

    // State for document viewer dialog
    const [docViewerVisible, setDocViewerVisible] = useState(false);
    const [selectedDocument, setSelectedDocument] = useState(null);

    // Reference to the DataTable
    const dt = useRef(null);

    // Function to export Document Repository to Excel
    const exportDocumentRepository = () => {
        // Get the filtered data from the DataTable using the ref
        const filteredData = dt.current ? dt.current.getVirtualScroller().props.items : [];

        if (!filteredData || filteredData.length === 0) {
            Swal.fire({
                icon: 'warning',
                title: 'No Data',
                text: 'No documents to export',
                timer: 2000
            });
            return;
        }

        try {
            // Create a workbook and worksheet
            const workbook = XLSX.utils.book_new();

            // Prepare data for export (excluding View and Download columns)
            const exportData = filteredData.map(doc => ({
                Category: doc.category || '',
                Topic: doc.topic || '',
                Section: doc.section || '',
                Location: doc.location || '',
                User: doc.user || '',
                Question: doc.label || '',
                File: doc.fileName || '',
                FileURL: doc.file?.includes('api.eisqr') ? doc.file : API.Docs + doc.file
            }));

            // Create worksheet with data
            const worksheet = XLSX.utils.json_to_sheet(exportData);

            // Set column widths
            const columnWidths = [
                { wch: 20 }, // Category
                { wch: 25 }, // Topic
                { wch: 25 }, // Section
                { wch: 20 }, // Location
                { wch: 20 }, // User
                { wch: 30 }, // Question
                { wch: 30 }, // File
                { wch: 50 }  // FileURL
            ];
            worksheet['!cols'] = columnWidths;

            // Add hyperlinks to the File column
            exportData.forEach((row, rowIndex) => {
                // Excel rows are 1-based with header at row 1
                const cellRef = XLSX.utils.encode_cell({ r: rowIndex + 1, c: 6 }); // Column G (File)

                // Create hyperlink formula
                if (row.File && row.FileURL) {
                    worksheet[cellRef] = {
                        f: `HYPERLINK("${row.FileURL}","${row.File}")`,
                        t: 's'
                    };
                }
            });

            // Hide the FileURL column (it's only used for the hyperlinks)
            if (!worksheet['!cols']) worksheet['!cols'] = [];
            worksheet['!cols'][7] = { hidden: true };

            // Add worksheet to workbook
            XLSX.utils.book_append_sheet(workbook, worksheet, 'Document Repository');

            // Generate and save the Excel file
            const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });
            FileSaver.saveAs(new Blob([excelBuffer]), `Document_Repository_${new Date().toISOString().slice(0, 10)}.xlsx`);


        } catch (error) {
            console.error('Error exporting to Excel:', error);
            Swal.fire({
                icon: 'error',
                title: 'Export Failed',
                text: 'Failed to export data. Please try again.'
            });
        }
    };

    // Handle global filter change
    const onGlobalFilterChange = (e) => {
        const value = e.target.value;
        setGlobalFilterValue(value);
    };

    // Function to render the document repository tab
    const renderDocumentRepository = () => {

        // Create the header with global search and export button
        const header = (
            <div className="d-flex justify-content-between align-items-center">
                 <Button
                    disabled={allDocuments.length === 0}
                    onClick={exportDocumentRepository}
                    label="Export"
                    icon="pi pi-download"
                    className="p-button-primary"
                />
                <span className="p-input-icon-left">
                    <i className="pi pi-search" />
                    <InputText
                        value={globalFilterValue}
                        onChange={onGlobalFilterChange}
                        placeholder="Search User, Question, File"
                        style={{ width: '300px' }}
                    />
                </span>

            </div>
        );

        return (
            <div className="p-4 border rounded-lg shadow-sm bg-white mb-4">
                <div style={{ overflowX: "auto" }}>
                    <DataTable
                        ref={dt}
                        value={allDocuments}
                        paginator
                        rows={10}
                        rowsPerPageOptions={[5, 10, 20, 50]}
                        emptyMessage="No documents found"
                        className="p-datatable-sm"
                        header={header}
                        filters={filters}
                        filterDisplay="menu"
                        globalFilter={globalFilterValue}
                        globalFilterFields={['user', 'label', 'fileName']}
                        onFilter={(e) => setFilters(e.filters)}
                    >
                        <Column
                            field="category"
                            header="Category"
                            filter
                            showFilterMatchModes={false}
                            filterElement={(options) => (
                                <MultiSelect
                                    value={options.value}
                                    options={[...new Set(allDocuments.map(doc => doc.category))].filter(Boolean)}
                                    onChange={(e) => options.filterCallback(e.value)}
                                    placeholder="Select Categories"
                                    className="p-column-filter"
                                    display="chip"
                                    showClear
                                />
                            )}
                        />
                        <Column
                            field="topic"
                            header="Topic"
                            filter
                            showFilterMatchModes={false}
                            filterElement={(options) => (
                                <MultiSelect
                                    value={options.value}
                                    options={[...new Set(allDocuments.map(doc => doc.topic))].filter(Boolean)}
                                    onChange={(e) => options.filterCallback(e.value)}
                                    placeholder="Select Topics"
                                    className="p-column-filter"
                                    display="chip"
                                    showClear
                                />
                            )}
                        />
                        <Column
                            field="section"
                            header="Section"
                            filter
                            showFilterMatchModes={false}
                            filterElement={(options) => (
                                <MultiSelect
                                    value={options.value}
                                    options={[...new Set(allDocuments.map(doc => doc.section))].filter(Boolean)}
                                    onChange={(e) => options.filterCallback(e.value)}
                                    placeholder="Select Sections"
                                    className="p-column-filter"
                                    display="chip"
                                    showClear
                                />
                            )}
                        />
                        <Column
                            field="location"
                            header="Location"
                            filter
                            showFilterMatchModes={false}
                            filterElement={(options) => (
                                <MultiSelect
                                    value={options.value}
                                    options={[...new Set(allDocuments.map(doc => doc.location))].filter(Boolean)}
                                    onChange={(e) => options.filterCallback(e.value)}
                                    placeholder="Select Locations"
                                    className="p-column-filter"
                                    display="chip"
                                    showClear
                                />
                            )}
                        />
                        <Column
                            field="user"
                            header="User"
                            filter
                            showFilterMatchModes={false}
                            filterElement={(options) => (
                                <MultiSelect
                                    value={options.value}
                                    options={[...new Set(allDocuments.map(doc => doc.user))].filter(Boolean)}
                                    onChange={(e) => options.filterCallback(e.value)}
                                    placeholder="Select Users"
                                    className="p-column-filter"
                                    display="chip"
                                    showClear
                                />
                            )}
                        />
                        <Column field="label" header="Question" />
                        <Column field="fileName" header="File" />
                        <Column
                            header="View"
                            body={(rowData) => (
                                <Button
                                    icon="pi pi-eye"
                                    className="p-button-rounded p-button-text"
                                    onClick={() => {
                                        setSelectedDocument({
                                            uri: rowData.file?.includes('api.eisqr') ? rowData.file : API.Docs + rowData.file,
                                            fileName: rowData.fileName
                                        });
                                        setDocViewerVisible(true);
                                    }}
                                />
                            )}
                            style={{ width: '5rem', textAlign: 'center' }}
                        />
                        <Column
                            header="Download"
                            body={(rowData) => (
                                <button
                                    style={{textDecoration:'none'}}
                                    className="p-button p-button-rounded p-button-text"
                                    onClick={(e) => {
                                        e.preventDefault();
                                        // Create a function to handle direct download
                                        const downloadFile = async () => {
                                            try {
                                                // Get the file URL
                                                const fileUrl = rowData.file?.includes('api.eisqr') ? rowData.file : API.Docs + rowData.file;

                                                // Fetch the file as a blob
                                                const response = await fetch(fileUrl);
                                                const blob = await response.blob();

                                                // Create a temporary URL for the blob
                                                const url = window.URL.createObjectURL(blob);

                                                // Create a temporary link element
                                                const link = document.createElement('a');
                                                link.href = url;
                                                link.download = rowData.fileName || 'download';

                                                // Append to the document, click it, and remove it
                                                document.body.appendChild(link);
                                                link.click();
                                                document.body.removeChild(link);

                                                // Clean up the URL object
                                                window.URL.revokeObjectURL(url);
                                            } catch (error) {
                                                console.error('Error downloading file:', error);
                                                // Optionally show an error message to the user
                                                alert('Failed to download file. Please try again.');
                                            }
                                        };

                                        // Execute the download function
                                        downloadFile();
                                    }}
                                >
                                    <i className="pi pi-download"></i>
                                </button>
                            )}
                            style={{ width: '5rem', textAlign: 'center' }}
                        />
                    </DataTable>
                </div>
            </div>
        );
    };

    return (
        <div className="p-4 max-w-4xl mx-auto">
            <Card className="mb-4">
                <div className="d-flex justify-content-between align-items-center mt-3 p-3">
                    <div className="d-flex gap-3 align-items-center">
                        <span className="d-flex align-items-center gap-2">
                            <span className="rounded-circle bg-secondary" style={{ width: "12px", height: "12px" }}></span> Information required
                        </span>
                        <span className="d-flex align-items-center gap-2">
                            <span className="rounded-circle bg-warning" style={{ width: "12px", height: "12px" }}></span> Draft
                        </span>
                        <span className="d-flex align-items-center gap-2">
                            <span className="rounded-circle bg-success" style={{ width: "12px", height: "12px" }}></span> Data finalised
                        </span>
                        <span className="d-flex align-items-center gap-2">
                            <span className="rounded-circle bg-danger" style={{ width: "12px", height: "12px" }}></span> Overdue submission
                        </span>
                    </div>
                    <div className="d-flex gap-4 align-items-center">
                        <span className="d-flex align-items-center gap-2">
                            <i className="pi pi-lock"></i> Locked for submission
                        </span>
                        <span className="d-flex align-items-center gap-2">
                            <i className="pi pi-lock-open"></i> Unlocked for updates
                        </span>
                    </div>
                </div>
            </Card>

            <TabView activeIndex={activeTabIndex} onTabChange={(e) => setActiveTabIndex(e.index)}>
                <TabPanel header="Response">
                    <div className="w-100">
                        {
                            categoryData.map((item) => {
                                return (
                                    groupedData[item.name] && <QualitativeResponse refresh={() => { getAssignedQualitativeData() }} categoryData={categoryData} data={groupedData[item.name] || []} />
                                )
                            })
                        }
                    </div>
                </TabPanel>
                <TabPanel header="Document Repository">
                    {renderDocumentRepository()}
                </TabPanel>
            </TabView>

            {/* Document Viewer Dialog */}
            <Dialog
                visible={docViewerVisible}
                style={{ width: '90%', height: '90vh' }}
                onHide={() => setDocViewerVisible(false)}
                header={selectedDocument?.fileName}
                maximizable
            >
                {selectedDocument && (
                    <DocViewer
                        documents={[selectedDocument]}
                        pluginRenderers={DocViewerRenderers}
                        style={{ height: 'calc(90vh - 120px)' }}
                    />
                )}
            </Dialog>
        </div>
    );
};

export default QualitativeDataResponse;