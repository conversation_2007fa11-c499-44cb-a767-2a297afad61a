import React from "react";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  ResponsiveContainer,
  CartesianGrid,
} from "recharts";

const FireSafetyChart = ({ data }) => {
  // Define max values for each category
  const maxValues = {
    fire_safety: 10,
    road_safety: 10,
    electrical_safety: 5,
    office_safety: 5,
    sustainability_ambassador_program: 10,
  };

  const extractScores = (criteria = {}) => {
    const subCriteria = criteria?.social?.subCriteria || [];

    const getScore = (name) => {
      const found = subCriteria.find((item) => item.name === name);
      return Math.max(0, found?.score || 0); // Avoid negative or null
    };

    // Helper function to clamp values between 0 and maxValue for graph display
    const clampForGraph = (value) => Math.max(0, value);
    const clampRemaining = (value, maxValue) => Math.max(0, Math.min(maxValue, maxValue - value));

    return [
      {
        category: "Fire Safety",
        avgValue: getScore("fire_safety"),
        avgValueForGraph: clampForGraph(getScore("fire_safety")),
        remainingToMax: clampRemaining(
          getScore("fire_safety"),
          maxValues.fire_safety
        ),
        maxValue: maxValues.fire_safety,
      },
      {
        category: "Road Safety",
        avgValue: getScore("road_safety"),
        avgValueForGraph: clampForGraph(getScore("road_safety")),
        remainingToMax: clampRemaining(
          getScore("road_safety"),
          maxValues.road_safety
        ),
        maxValue: maxValues.road_safety,
      },
      {
        category: "Electrical Safety",
        avgValue: getScore("electrical_safety"),
        avgValueForGraph: clampForGraph(getScore("electrical_safety")),
        remainingToMax: clampRemaining(
          getScore("electrical_safety"),
          maxValues.electrical_safety
        ),
        maxValue: maxValues.electrical_safety,
      },
      {
        category: "Office Safety",
        avgValue: getScore("office_safety"),
        avgValueForGraph: clampForGraph(getScore("office_safety")),
        remainingToMax: clampRemaining(
          getScore("office_safety"),
          maxValues.office_safety
        ),
        maxValue: maxValues.office_safety,
      },
      {
        category: "SAP",
        avgValue: getScore("sustainability_ambassador_program"),
        avgValueForGraph: clampForGraph(getScore("sustainability_ambassador_program")),
        remainingToMax: clampRemaining(
          getScore("sustainability_ambassador_program"),
          maxValues.sustainability_ambassador_program
        ),
        maxValue: maxValues.sustainability_ambassador_program,
      },
    ];
  };

  const salesData = extractScores(data.sales_criteria);
  const serviceData = extractScores(data.service_criteria);

  // Function to determine Y-Axis domain with a fixed max value (10)
  const getYAxisDomain = () => [0, 10];

  const salesYAxisDomain = getYAxisDomain();
  const serviceYAxisDomain = getYAxisDomain();

  // Custom tick component for X-axis with two lines of text
  const CustomizedTick = ({ x, y, payload }) => {
    // Format special cases
    let displayText = payload.value;
    if (displayText === "fire_safety") {
      displayText = "Fire Safety";
    } else if (displayText === "road_safety") {
      displayText = "Road Safety";
    } else if (displayText === "electrical_safety") {
      displayText = "Electrical Safety";
    } else if (displayText === "office_safety") {
      displayText = "Office Safety";
    } else if (displayText === "sustainability_ambassador_program") {
      displayText = "Sustainability Ambassador Program";
    }

    // Split the text into two lines
    const words = displayText.split(' ');
    let firstLine, secondLine;

    if (words.length === 1) {
      // Single word - put on first line
      firstLine = words[0];
      secondLine = "";
    } else if (words.length === 2) {
      // Two words - one on each line
      firstLine = words[0];
      secondLine = words[1];
    } else {
      // More than two words - balance between lines
      const midpoint = Math.ceil(words.length / 2);
      firstLine = words.slice(0, midpoint).join(' ');
      secondLine = words.slice(midpoint).join(' ');
    }

    return (
      <g transform={`translate(${x},${y})`}>
        <text
          x={0}
          y={0}
          textAnchor="middle"
          fill="#666"
          fontSize={10}
        >
          <tspan x={0} dy="0.71em">{firstLine}</tspan>
          {secondLine && <tspan x={0} dy="1.2em">{secondLine}</tspan>}
        </text>
      </g>
    );
  };

  // Custom legend component
  const CustomLegend = (props) => {
    const { payload } = props;
    return (
      <ul
        style={{
          display: "flex",
          listStyleType: "none",
          justifyContent: "center",
          padding: 0,
        }}
      >
        {payload.map((entry, index) => (
          <li
            key={`item-${index}`}
            style={{
              color: entry.color,
              marginRight: "5px",
            }}
          >
            <span
              style={{
                color: entry.color,
                backgroundColor: entry.color,
                marginRight: 4,
                fontSize: "20px",
                width: "10px",
                height: "10px",
                borderRadius: "50%",
                display: "inline-block",
              }}
            ></span>
            <span style={{ color: "#555", fontSize: "14px" }}>
              {entry.value}
            </span>
          </li>
        ))}
      </ul>
    );
  };

  return (
    <div
      style={{
        display: "flex",
        justifyContent: "space-between",
        width: "100%",
        padding: "20px",
      }}
    >
      {/* Sales Chart */}
      <div style={{ width: "48%", height: 400 }}>
        <h3 style={{ textAlign: "center", marginBottom: 20, color: "#555" }}>
          Sales - Social
        </h3>
        <ResponsiveContainer width="100%" height="100%">
          <BarChart
            data={salesData}
            barSize={60}
            margin={{ top: 20, right: 30, left: 20, bottom: 60 }}
          >
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis
              dataKey="category"
              tick={<CustomizedTick />}
              tickLine={true} // Ensures ticks are visible
              interval={0} // Forces all labels to be shown
              height={70} // Increase height to accommodate two lines of text
            />
            <YAxis domain={salesYAxisDomain} />
            <Tooltip
              formatter={(_, name, props) => {
                const { payload } = props;
                if (name === "Achieved") {
                  return [
                    `${payload.avgValue.toFixed(2)} (Max: ${payload.maxValue})`,
                    name,
                  ];
                }
                return [null, null];
              }}
            />
            <Legend content={CustomLegend} />
            <Bar
              dataKey="avgValueForGraph"
              stackId="score"
              fill="#FC6E51"
              name="Achieved"
              label={{
                position: "insideTop",
                fill: "#fff",
                formatter: (value) => value.toFixed(1),
              }}
            />
            <Bar
              dataKey="remainingToMax"
              stackId="score"
              fill="#FEB2A8"
              name="Maximum"
            />
          </BarChart>
        </ResponsiveContainer>
      </div>

      {/* Service Chart */}
      <div style={{ width: "48%", height: 400 }}>
        <h3 style={{ textAlign: "center", marginBottom: 20, color: "#555" }}>
          Service - Social
        </h3>
        <ResponsiveContainer width="100%" height="100%">
          <BarChart
            data={serviceData}
            barSize={60}
            margin={{ top: 20, right: 30, left: 20, bottom: 60 }}
          >
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis
              dataKey="category"
              tick={<CustomizedTick />}
              tickLine={true} // Ensures ticks are visible
              interval={0} // Forces all labels to be shown
              height={70} // Increase height to accommodate two lines of text
            />
            <YAxis domain={serviceYAxisDomain} />
            <Tooltip
              formatter={(_, name, props) => {
                const { payload } = props;
                if (name === "Achieved") {
                  return [
                    `${payload.avgValue.toFixed(2)} (Max: ${payload.maxValue})`,
                    name,
                  ];
                }
                return [null, null];
              }}
            />
            <Legend content={CustomLegend} />
            <Bar
              dataKey="avgValueForGraph"
              stackId="score"
              fill="#FC6E51"
              name="Achieved"
              label={{
                position: "insideTop",
                fill: "#fff",
                formatter: (value) => value.toFixed(1),
              }}
            />
            <Bar
              dataKey="remainingToMax"
              stackId="score"
              fill="#FEB2A8"
              name="Maximum"
            />
          </BarChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
};

export default FireSafetyChart;
