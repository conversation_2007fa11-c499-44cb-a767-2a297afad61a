import React, { useEffect, useState } from 'react';
import APIServices from '../../../../service/APIService';
import { API } from '../../../../constants/api_url';
import DealerCompletedReport from '../MSI/DealerCompletedReport';

const CryptoJS = require("crypto-js");

const PublicDealerReportView = () => {
    const urlParams = new URLSearchParams(window.location.search);
    const token = decodeURIComponent(urlParams.get('token'));

    const SECRET_KEY = "e!sq6kee4deaassid";

    const [show, setShow] = useState(false);
    const [load, setLoad] = useState(true);
    const [auditreport, setAuditReport] = useState({});

    useEffect(() => {
        if (!token) {
            setLoad(false);
            return;
        }

        const fetchData = async () => {
            const decryptedId = decryptToken(token, SECRET_KEY);

            if (!decryptedId) {
                console.error("Failed to decrypt token.");
                setLoad(false);
                return;
            }

            let dealerAssUri = {
                "include": ['dealer', 'dealerAuditorChecklistSubmission', 'vendor', 'actions']
            }


            try {

                const response = await APIServices.get(API.DealerAssessmentAss_Edit(decryptedId) + `?filter=${encodeURIComponent(JSON.stringify(dealerAssUri))}`)

                console.log(response)
                if (response.status === 200) {
                    setAuditReport(response.data);
                    setShow(true);
                } else {
                    setShow(false);
                }
            } catch (e) {
                console.error("API Error:", e);
                setShow(false);
            } finally {
                setLoad(false);
            }
        };

        fetchData();
    }, [token]);

    function decryptToken(encryptedText, secret) {
        try {
            const bytes = CryptoJS.AES.decrypt(encryptedText, secret);
            return bytes.toString(CryptoJS.enc.Utf8);
        } catch (error) {
            console.error("Decryption failed:", error);
            return '';
        }
    }

    return (
        <div>
            {load ? (
                <div className="col-12 flex align-items-center justify-content-center">
                    <div> <span className="loader"></span> </div>
                    <label className="col-12 fs-16 fw-5" style={{ display: 'flex' }}>
                        Generating Report Preview, please wait...
                    </label>
                </div>
            ) : (
                show ? <div className='flex justify-content-center'> <div style={{ width: '90%' }}>    <DealerCompletedReport report={auditreport} /> </div>  </div> : <div>Something went wrong while previewing the report. Report this error with MSI <NAME_EMAIL> </div>
            )}
        </div>
    );
};

export default PublicDealerReportView;