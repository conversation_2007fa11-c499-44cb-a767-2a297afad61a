import React from 'react';
import { Card } from 'primereact/card';
import { Divider } from 'primereact/divider';
import { Tag } from 'primereact/tag';
import { DateTime } from 'luxon';

const ActionInformationCard = ({ action, getCategoryLabel, getNonComplianceLabel, getStatusLabel, getUserName }) => {
    const dateTemplate = (rowData, field) => {
        const dateStr = rowData?.[field];
        return dateStr ? DateTime.fromISO(dateStr).toFormat('dd-MM-yyyy') : 'NA';
    };

    return (
        <Card className="h-full shadow-2"
            title={
                <div className="flex align-items-center">
                    <i className="pi pi-info-circle mr-2" style={{ color: '#315975' }}></i>
                    <span className="font-bold">Action Information</span>
                </div>
            }
        >
            <div className="grid">
                <div className="col-12">
                    <div className="flex justify-content-between align-items-center mb-3">
                        <h3 className="m-0 text-lg">{action.finding}</h3>
                        <Tag
                            value={getStatusLabel(action.status)}
                            severity={
                                action.status === 'completed' ? 'success' :
                                action.status === 'submitted' ? 'success' :
                                action.status === 'in progress' ? 'warning' : 'info'
                            }
                        />
                    </div>
                    <Divider />
                </div>

                <div className="col-6">
                    <div className="field mb-2">
                        <label className="block text-sm font-medium text-gray-700">ID</label>
                        <div className="text-lg font-semibold">{action.id}</div>
                    </div>
                </div>

                <div className="col-6">
                    <div className="field mb-2">
                        <label className="block text-sm font-medium text-gray-700">Category</label>
                        <div className="text-lg font-semibold">{getCategoryLabel(action.categoryOfFinding)}</div>
                    </div>
                </div>

                {action.nonComplianceType && (
                    <div className="col-6">
                        <div className="field mb-2">
                            <label className="block text-sm font-medium text-gray-700">Non-Compliance Type</label>
                            <div className="text-lg font-semibold">{getNonComplianceLabel(action.nonComplianceType)}</div>
                        </div>
                    </div>
                )}

                <div className="col-6">
                    <div className="field mb-2">
                        <label className="block text-sm font-medium text-gray-700">Due Date</label>
                        <div className="text-lg font-semibold">{dateTemplate(action, 'actionDueDate')}</div>
                    </div>
                </div>

                <div className="col-6">
                    <div className="field mb-2">
                        <label className="block text-sm font-medium text-gray-700">Created On</label>
                        <div>{dateTemplate(action, 'created_on')}</div>
                    </div>
                </div>

                <div className="col-6">
                    <div className="field mb-2">
                        <label className="block text-sm font-medium text-gray-700">Created By</label>
                        <div>{getUserName(action.created_by)}</div>
                    </div>
                </div>

                <div className="col-12">
                    <div className="field mb-2">
                        <label className="block text-sm font-medium text-gray-700">Description</label>
                        <div className="p-2 border-1 border-gray-300 border-round bg-gray-50">
                            {action.description || 'NA'}
                        </div>
                    </div>
                </div>
            </div>
        </Card>
    );
};

export default ActionInformationCard;
