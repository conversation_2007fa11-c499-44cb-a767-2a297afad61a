@import url('https://fonts.googleapis.com/css2?family=Lato:wght@400;700&display=swap');

.bg-esg-blue {
    background: #12344B;

}

.clr-esg-blue {
    color: #12344B;

}
.font-lato {
    font-family: 'Lato', sans-serif !important;
}
.text-big-one {

 
    font-size: 20px;
    font-style: normal;
    font-weight: 600;
    line-height: 38px;
    leading-trim: both;

    text-edge: cap;
}

/* body, html {
    font-family: 'Lato', sans-serif;
    -moz-osx-font-smoothing: grayscale;
    -webkit-font-smoothing: antialiased;
    color: var(--gray-900, #101828);
} */
.p-tag-blue {
    background: #31597590;
    color: #ffffff;
    font-size: 0.75rem;
    font-weight: 700;
    padding: 0.25rem 0.4rem;
    border-radius: 6px;
}

.clr-navy {

    color: var(--Navy, #315975);
}

.text-bold-500 {
    
    font-weight: 500;
}

.text-bold {
    
    font-weight: bold;
}

.fs-10 {
    
    font-size: 10px;
}

.fs-12 {
    
    font-size: 12px;
}

.fs-14 {
    
    font-size: 14px;
}

.fs-16 {
    
    font-size: 16px;
}

.fs-36 {
    
    font-size: 36px;
}
.fs-38 {
    
    font-size: 38px;
}
.fs-18 {
    
    font-size: 18px;
}
.fs-20 {
    
    font-size: 20px;
}
.lh-38{
    line-height: 38px;
}
.lh-20{
    line-height: 20px;
}
.lh-24{
    line-height: 20px;
}
.text-micro {
    
    color: var(--Gray-2, #4F4F4F);
    leading-trim: both;
    text-edge: cap;

    font-size: 12px;
    font-style: normal;
    font-weight: 400;

}

.hover-blue:hover {
    background: #31597550;
    cursor: pointer;
    color: white;

    font-weight: bold;
}



.cur-pointer {
    cursor: pointer !important;
}

.bg-smoke {
    background-color: #F9F9F9;
}

.bg-white {
    background-color: white;
}

.bg-g5 {
    background-color: #E0E0E0;
}
.fw-7 {
    font-weight: 700;
}
.fw-6 {
    font-weight: 600;
}
.fw-5 {
    font-weight: 500;
}
.fw-4 {
    font-weight: 400;
}
.clr-blk {
    color: #222
}
.clr-gray-900 {
    color: #101828
}
.status-tag-green {
    background: rgba(111, 207, 151, 0.20);
    color: #219653;
}

.status-tag-red {
    background: rgba(255, 0, 0, 0.20);
    color: #ff0000;
}

.p-datatable-wrapper {
    background: white;
}

.clr-gray-3 {
    color: #828282
}

.clr-navy-light {
    color: #315975;
    opacity: 0.1;
}

.bg-navy-light {
    background: #31597520;

}
.p-datepicker table td.p-datepicker-today > span.p-highlight {
    color: #315975;
    background: #EEF2FF;
}
.p-datepicker-title .p-datepicker-month {
    color:#315975;
    font-weight: bold;
}
.p-datepicker-title .p-datepicker-year {
    color:#315975
}
.p-datepicker .p-monthpicker .p-monthpicker-month.p-highlight {
    color: #315975;
    background: #31597510;
    font-weight: bold;
}
.big-number-navy {
    color: var(--Primary, #222);
    leading-trim: both;
    text-edge: cap;

    font-size: 38px;
    font-style: normal;
    font-weight: 700;
    line-height: 38px;
    /* 100% */
}

.box-shadow-one {
    box-shadow: rgba(0, 0, 0, 0.35) 0px 5px 15px !important;
}

.clr-delete {
    color: #EE5724;
    
}

.icon-layout {
    width: 44px;
    height: 37.923px;
    flex-shrink: 0;
    border-radius: 4px;
    display: flex;
    justify-content: center;
    align-items: center;

}

.admin-card {
    padding: 20px;
    height: 180px;
    width: 100%;
    flex-direction: column;
    background: rgb(255, 255, 255);
    cursor: pointer;
    border-radius: 4px;
}
.text-link{
    text-decoration: underline !important;
}
.text-none{
    text-decoration: none !important;
}


/* Media query for small screens (phones) */
@media only screen and (max-width: 767px) {
  /* Styles for small screens */
  .admin-card {
    height: 230px;
}
}
/* Media query for medium screens (tablets) */
@media only screen and (min-width: 768px) and (max-width: 1023px) {
    .admin-card {
        height: 230px;
    }
  }
  
  /* Media query for large screens (desktops) */
  @media only screen and (min-width: 1024px) {
    .admin-card {
        height: 230px;
    }
  }
  
  @import url('https://fonts.googleapis.com/css2?family=Lato:wght@400;700&display=swap');

  .bg-esg-blue {
      background: #12344B;
  
  }
  
  .clr-esg-blue {
      color: #12344B;
  
  }
  
  .font-lato {
      font-family: 'Lato', sans-serif !important;
  }
  
  .text-big-one {
  
  
      font-size: 20px;
      font-style: normal;
      font-weight: 600;
      line-height: 38px;
      leading-trim: both;
  
      text-edge: cap;
  }
  
  /* body, html {
      font-family: 'Lato', sans-serif;
      -moz-osx-font-smoothing: grayscale;
      -webkit-font-smoothing: antialiased;
      color: var(--gray-900, #101828);
  } */
  .p-tag-blue {
      background: #31597590;
      color: #ffffff;
      font-size: 0.75rem;
      font-weight: 700;
      padding: 0.25rem 0.4rem;
      border-radius: 6px;
  }
  
  .clr-navy {
  
      color: var(--Navy, #315975);
  }
  
  .text-bold-500 {
  
      font-weight: 500;
  }
  
  .text-bold {
  
      font-weight: bold;
  }
  
  .fs-10 {
  
      font-size: 10px;
  }
  
  .fs-12 {
  
      font-size: 12px;
  }
  
  .fs-14 {
  
      font-size: 14px;
  }
  
  .fs-16 {
  
      font-size: 16px;
  }
  
  .fs-36 {
  
      font-size: 36px;
  }
  
  .fs-38 {
  
      font-size: 38px;
  }
  
  .fs-18 {
  
      font-size: 18px;
  }
  
  .fs-20 {
  
      font-size: 20px;
  }
  
  .lh-38 {
      line-height: 38px;
  }
  
  .lh-20 {
      line-height: 20px;
  }
  
  .lh-24 {
      line-height: 20px;
  }
  
  .text-micro {
  
      color: var(--Gray-2, #4F4F4F);
      leading-trim: both;
      text-edge: cap;
  
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
  
  }
  
  .hover-blue:hover {
    background:linear-gradient(97deg, rgba(49,89,117,1) 0%, rgba(255,255,255,1) 100%);
    cursor: pointer;
    color: white;

    font-weight: bold;
}
  
  
  .cur-pointer {
      cursor: pointer !important;
  }
  
  .bg-smoke {
      background-color: #F9F9F9;
  }
  
  .bg-white {
      background-color: white;
  }
  
  .bg-g5 {
      background-color: #E0E0E0;
  }
  
  .fw-7 {
      font-weight: 700;
  }
  
  .fw-6 {
      font-weight: 600;
  }
  
  .fw-5 {
      font-weight: 500;
  }
  
  .fw-4 {
      font-weight: 400;
  }
  
  .clr-blk {
      color: #222
  }
  
  .clr-gray-900 {
      color: #101828
  }
  
  .status-tag-green {
      background: rgba(111, 207, 151, 0.20);
      color: #219653;
  }
  
  .status-tag-red {
      background: rgba(255, 0, 0, 0.20);
      color: #ff0000;
  }
  
  .p-datatable-wrapper {
      background: white;
  }
  
  .clr-gray-3 {
      color: #828282
  }
  
  .clr-navy-light {
      color: #315975;
      opacity: 0.1;
  }
  
  .bg-navy-light {
      background: #31597520;
  
  }
  
  .p-datepicker table td.p-datepicker-today>span.p-highlight {
      color: #315975;
      background: #EEF2FF;
  }
  
  .p-datepicker-title .p-datepicker-month {
      color: #315975;
      font-weight: bold;
  }
  
  .p-datepicker-title .p-datepicker-year {
      color: #315975
  }
  
  .p-datepicker .p-monthpicker .p-monthpicker-month.p-highlight {
      color: #315975;
      background: #31597510;
      font-weight: bold;
  }
  
  .big-number-navy {
      color: var(--Primary, #222);
      leading-trim: both;
      text-edge: cap;
  
      font-size: 38px;
      font-style: normal;
      font-weight: 700;
      line-height: 38px;
      /* 100% */
  }
  
  .box-shadow-one {
      box-shadow: rgba(0, 0, 0, 0.35) 0px 5px 15px !important;
  }
  
  .clr-delete {
      color: #EE5724;
  
  }
  
  .icon-layout {
      width: 44px;
      height: 37.923px;
      flex-shrink: 0;
      border-radius: 4px;
      display: flex;
      justify-content: center;
      align-items: center;
  
  }
  
  .admin-card {
      padding: 20px;
      height: 180px;
      width: 100%;
      flex-direction: column;
      background: rgb(255, 255, 255);
      cursor: pointer;
      border-radius: 4px;
  }
  
  .text-link {
      text-decoration: underline !important;
  }
  
  .text-none {
      text-decoration: none !important;
  }
  
  
  /* Media query for small screens (phones) */
  @media only screen and (max-width: 767px) {
  
      /* Styles for small screens */
      .admin-card {
          height: 230px;
      }
  }
  
  /* Media query for medium screens (tablets) */
  @media only screen and (min-width: 768px) and (max-width: 1023px) {
      .admin-card {
          height: 230px;
      }
  }
  
  /* Media query for large screens (desktops) */
  @media only screen and (min-width: 1024px) {
      .admin-card {
          height: 230px;
      }
  }
  
  .pencil-icon form a {
      visibility: hidden;
  }
  
  .pencil-icon form a::before {
      visibility: initial;
      content: "\e3c9";
      font-family: 'Material Icons';
  }
  
  .border-right {
      border-right: 1px solid #d1d1d1;
  }
  
  .mail-list .form-inline {
      display: flex;
      justify-content: space-between;
      padding: 8px;
      /* background: #eee; */
      border-bottom: 1px solid #d1d1d1;
  }
  
  .mail-list.active {
      background-color: #eee !important;
  }
  
  .p-field label {
      font-weight: bold;
  }
  
  .attachment-badge {
      text-decoration: none;
      /* Optional: if you want to remove the underline from links */
  }
  
  .badge {
      display: inline-block;
      padding: 0.25em 0.6em;
      font-size: 75%;
      font-weight: 700;
      line-height: 1;
      text-align: center;
      white-space: nowrap;
      vertical-align: baseline;
      border-radius: 0.25rem;
      transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out,
          border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  }
  
  .badge-completed {
      color: #fff;
      background-color: #28a745;
  }
  
  .badge-planned {
      color: #fff;
      background-color: #17a2b8;
  }
  
  .badge-in-progress {
      color: #212529;
      background-color: #ffc107;
  }
  
  .badge-attachment {
      color: #fff;
      background-color: #007bff;
  }
  
  /* This will ensure the link doesn't have text decoration */
  .badge-attachment a {
      text-decoration: none;
      color: inherit;
      /* This will ensure the link has the same text color as the badge */
  }
  
  .custom-radio-grid {
      display: flex;
      /* Uses flexbox to align children horizontally */
      flex-wrap: wrap;
      /* Allows items to wrap if there isn't enough space */
      align-items: center;
      /* Aligns items vertically at the center */
      justify-content: flex-start;
      /* Aligns items to the start of the flex container */
      gap: 20px;
      /* Adds space between each item */
  }
  
  .custom-radio-grid .p-field-radiobutton {
      display: flex;
      /* Ensures label and input are aligned horizontally */
      align-items: center;
      /* Center align the items vertically */
  }
  
  .custom-radio-grid .p-radiobutton .p-radiobutton-box {
      border-radius: 50%;
      /* Circular radio buttons */
      width: 20px;
      height: 20px;
  }
  
  .custom-radio-grid .p-radiobutton .p-radiobutton-box.p-highlight {
      background-color: #007ad9;
      /* Active color */
      border-color: #007ad9;
      /* Active border color */
  }
  
  .custom-radio-grid label.custom-label {
      margin-left: 10px;
      font-weight: normal;
      cursor: pointer;
      transition: font-weight 0.3s ease;
      /* Smooth transition for font-weight */
  }
  
  .custom-radio-grid label.custom-label.active {
      font-weight: bold;
      /* Bold for active label */
  }
  .p-tooltip {
      width: 300px;           /* Fixed width */
      white-space: normal;    /* Ensures text wraps if necessary */
  
  }
  
  
  
  