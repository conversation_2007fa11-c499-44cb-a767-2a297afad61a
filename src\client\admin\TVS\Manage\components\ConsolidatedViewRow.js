import React from 'react';

const cellColors = [
  "", "", "#E5F5EA", "#FEF5E5", "#E5F5EA", "", "#E5F5EA", "#FEF5E5", "#E5F5EA"
];

const ConsolidatedViewRow = ({ avatar }) => {
  return (
    <tr>
      <td className="user-cell">
        <img src={avatar} alt="Consolidated View" className="avatar-img" />
        Consolidated View
      </td>
      {cellColors.map((color, idx) => (
        <td key={idx} style={{ backgroundColor: color }}>
          {idx === 8 && <a href="#">Product Water footprint</a>}
        </td>
      ))}
    </tr>
  );
};

export default ConsolidatedViewRow;
