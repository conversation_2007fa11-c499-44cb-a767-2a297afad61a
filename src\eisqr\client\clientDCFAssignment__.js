import Axios from "axios";
import React, { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import useForceUpdate from "use-force-update";
import { Dropdown } from "primereact/dropdown";
import { EditText } from "react-edit-text";
import { Button } from "primereact/button";
import { Calendar } from "primereact/calendar";
import { InputTextarea } from "primereact/inputtextarea";
import { Dialog } from "primereact/dialog";
import { InputText } from "primereact/inputtext";
import { CascadeSelect } from "primereact/cascadeselect";
import Swal from "sweetalert2";
import { Checkbox } from 'primereact/checkbox';
import $, { data } from "jquery";
import { API } from "../../constants/api_url";
import { RadioButton } from "primereact/radiobutton";
import LazyView from "../../components/LazyView";
import { MultiSelect } from 'primereact/multiselect';
import moment from "moment";
import { TabMenu } from "primereact/tabmenu";
import APIServices from "../../service/APIService";
import { Tag } from "primereact/tag";
import FileSaver from "file-saver";
import * as XLSX from "xlsx";
import { DateTime } from "luxon";
import { X } from "@mui/icons-material";
import { CustomDialog } from "../../components/CustomDialog/CustomDialog";
import { getRandomId } from "../../components/BGHF/helper";

window.jQuery = $;
window.$ = $;
let standAlone = [], levelCount = 0
const ClientDCFAssignment = () => {
    const selector = useSelector((state) => state.user.admindetail);
    const [key, setKey] = useState(null)
    const [clienttag, setClientTag] = useState([])
    const [user, setUser] = useState([]);
    const [list, setList] = useState({ category: null, topic: null, metric: null })
    const [selected, setSelected] = useState({ user: null, category: [], topic: [], metric: [] })
    const [activeItem, setActiveItem] = useState(1);
    const [selDataPoint, setSelDataPoint] = useState([])
    const configtype = [{ name: 'Location' }, { name: 'Data Point' }]
    const forceUpdate = useForceUpdate();
    const [editmode, setEditMode] = useState(false)
    const [editmodeid, setEditModeID] = useState(null)
    const [enable, setEnable] = useState(true)
    const [old, setOld] = useState([]);
    const [selectedlist, setSelectedList] = useState({ title: '', data: [] })
    const [prevSListdialog, setPrevSListDialog] = useState(false);
    const [metriclist, setMetricList] = useState([]);
    const [metricbk, setMetricBk] = useState([])
    const [raw, setRaw] = useState([])
    const [response, setResponse] = useState([])
    const [usermetric, setUserMetric] = useState([])


    const [rawdcf, setRawDCF] = useState([])
    const [dupdpiddialog, setDupDPIDDialog] = useState(false)
    const [dupdpid, setDupId] = useState({ data: [], msg: '' })
    const [selecteddcf, setSelectedDCF] = useState([]);
    const [selecteddcfbk, setSelectedDCFBK] = useState([]);
    const [cflist, setCFList] = useState([]);
    const [cfbklist, setCFBKList] = useState([]);
    const [dflist, setDFList] = useState([]);
    const [dfbklist, setDFBKList] = useState([]);
    const [selectedcf, setSelectedCF] = useState([]);
    const [selectedcfbk, setSelectedCFBK] = useState([]);
    const [selectedform, setSelectedForm] = useState([])
    const [prevdialog, setPrevDialog] = useState(false);
    const [dfreqlist, setDFReqList] = useState([]);
    const [dfreqlistbk, setDFReqListBK] = useState([]);
    const [dfass, setDFAss] = useState([]);
    const [dfassbk, setDFAssBK] = useState([]);
    const [prevSList2dialog, setPrevSList2Dialog] = useState(false);
    const [dfconfigdialog, setDfConfigDialog] = useState(false);
    const [dfconfig, setDfConfig] = useState({ apex: false, country: false, region: false, site: false });

    const [search, setSearch] = useState({ metric: '', dcf: '', cf: '', df: '' })
    const [location, setLocation] = useState([]);
    const [overallmetric, setOverallMetric] = useState([]);
    const [userConfig, setUserConfig] = useState({
        name: "", type: "",
        location: ''
    });
    const [module, setModule] = useState({
        tier1: "",
        tier2: "",
        tier3: "",
    });
    const [cascade, setCascade] = useState("");
    const [showSave, setShowSave] = useState(0);

    const [tier2, setTier2] = useState([]);
    const [tier3, setTier3] = useState([]);
    const [moduleList, setModuleList] = useState({
        mod: [],
        title: [],
        topic: [],
    });

    useEffect(async () => {
        setOld(selector.information);

        APIServices.get(API.UserProfile).then((res) => {
            let locuser = [], tagList = []

            res.data.forEach((item) => {
                if (item.role === "clientadmin") {
                    tagList.push({
                        name: item.information.enterpriseid,
                        id: item.id,
                    });
                    locuser.push({
                        name: item.information.companyname,
                        id: item.id,
                    });
                }
            });
            setClientTag(tagList);
            setUser(locuser);
        });
        let uriString = {
            "include": [{ "relation": "newDataPoints" }]

        }

        APIServices.get(API.DCF).then((res) => {

            setRawDCF(res.data.filter((i) => { return (i.type === null || i.type === 1) }))

        })
        APIServices.get(API.SRF).then((res) => {

            setCFBKList(res.data.map(i => ({ ...i, data1: JSON.parse(i.data1) })))
            setCFList(res.data.map(i => ({ ...i, data1: JSON.parse(i.data1) })))

        })
        APIServices.get(API.RF).then((res) => {

            setDFBKList(res.data)
            setDFList(res.data)

        })
        let uriString2 = {
            "include": [{ "relation": "newTopics", "scope": { "include": [{ "relation": "newMetrics", "scope": { "include": [{ "relation": "newDataPoints" }] } }] } }]


        }
        let Overall = API.Categories + `?filter=${encodeURIComponent(JSON.stringify(uriString2))}`;

        let url = API.Metric + `?filter=${encodeURIComponent(JSON.stringify(uriString))}`;
        APIServices.get(Overall).then(res => {
            res.data.forEach((item) => {
                item.newTopics && item.newTopics.forEach((topic) => {
                    topic.newMetrics && topic.newMetrics.forEach((metric) => {
                        metric.title = (Array.isArray(metric.data1) && metric.data1.length) ? metric.data1[0].title : metric.title
                    });
                });
            });
            setResponse(res.data)
            setEnable(false)
            let categoryList = [], metricList = [], topicList = []
            let loc = list
            res.data.forEach((cat) => {
                if (cat.newTopics !== undefined) {
                    categoryList.push({ id: cat.id, title: cat.title })
                    cat.newTopics.forEach((topic) => {

                        if (topic.newMetrics !== undefined) {
                            topicList.push({ name: topic.title, title: topic.title + "(" + cat.title + ")", id: topic.id })
                            topic.newMetrics.forEach((metric) => {
                                // if(Array.isArray(metric.data1) && metric.data1[0].title &&  metric.data1[0].title.trim().length !== 0 ){
                                metricList.push({ ...metric, cat_title: cat.title })
                                // }

                            })
                        }
                    })
                }


            })
            console.log(categoryList, topicList)
            loc.category = categoryList
            loc.metric = metricList
            loc.topic = topicList

            setList(loc)
            setOverallMetric(JSON.parse(JSON.stringify(metricList)))
            setRaw(metricList)
            setMetricBk(metricList.map(k => { return { title: k.title, id: k.id, selected: false } }))
            setMetricList(metricList.map(k => { return { title: k.title, id: k.id, cat_title: k.cat_title, selected: false } }))

        })




    }, [selector]);

    const renderIndicators = (item) => {
        let filteredData = item.filter((i) => { return Array.isArray(i.data1) && i.data1[0].type === 0 })



        filteredData.forEach(obj => {
            replaceIndicatorRecursive(obj, item)

        });

        function replaceIndicatorRecursive(obj) {
            if (obj.data1 && obj.data1[0] && obj.data1[0].source === 0) {
                obj.data1[0].indicator = obj.data1[0].indicator.map(index => {
                    const matchedObject = item.find(item => item.id === index);
                    if (matchedObject && matchedObject.data1 && matchedObject.data1[0]) {
                        replaceIndicatorRecursive(matchedObject);
                        return { ...matchedObject };
                    }
                    return index;
                });
            }
        }


        return filteredData
    }

    const updateSelectedIndicators = (sMetric, oMetric) => {
        let data = []
        let rejected = []
        let selected_ = []
        let dcfId = []
        if (oMetric.length !== 0) {
            // console.log(sMetric,sMetric.map( i => getStandAlone(oMetric.find(j => { return i === j.id}) ) ))
        }

        oMetric.forEach((items) => {

            if (sMetric.includes(items.id)) {
                items.selected = true
                if (Array.isArray(items.data1) && items.data1.length === 1 && items.data1[0].type === 2) {
                    if (items.data1[0].dcfId) {
                         let dcf_index = rawdcf.findIndex(i => i.id === items.data1[0].dcfId)

                            if (!dcfId.includes(items.data1[0].dcfId) && dcf_index !== -1) {
                                 if (rawdcf[dcf_index].tags === null || !rawdcf[dcf_index].tags.length || rawdcf[dcf_index].tags.includes(selected.user)) {
                                          dcfId.push(items.data1[0].dcfId)
                            }
                        }
              
                    }

                } else if (items.newDataPoints !== undefined) {
                    items.newDataPoints.forEach((dp) => {
                        if (Array.isArray(dp.data1) && dp.data1.length !== 0 && dp.data1[0].datasource !== null && typeof dp.data1[0].datasource === 'number') {
                            let dcf_index = rawdcf.findIndex(i => i.id === dp.data1[0].datasource)

                            if (!dcfId.includes(dp.data1[0].datasource) && dcf_index !== -1) {

                                if (rawdcf[dcf_index].tags === null || !rawdcf[dcf_index].tags.length || rawdcf[dcf_index].tags.includes(selected.user)) {
                                    dcfId.push(dp.data1[0].datasource)
                                    console.log(dp.data1[0].datasource)
                                } else {
                                    console.log(rawdcf[dcf_index].tags, selected)
                                }

                            } else {
                                console.log(dp.data1[0].datasource)
                            }
                        }
                    })
                }
            } else {
                items.selected = false
            }


        })

        dcfId.forEach((dcfId) => {
            if (rawdcf.findIndex((i) => { return i.id === dcfId }) !== -1) {
                selected_.push(rawdcf[rawdcf.findIndex((i) => { return i.id === dcfId })])
            }
        })
        console.log(dcfId)
        setSelectedDCFBK(selected_)
        setSelectedDCF(selected_.filter((k) => { return k.title.trim().toLowerCase().includes(search.dcf.trim().toLowerCase()) }))
        setMetricList(oMetric.filter((k) => { return k.title.trim().toLowerCase().includes(search.metric.trim().toLowerCase()) }))

        forceUpdate()
    }
    const updateSelected = (obj, val) => {
        let loc = selected;
        loc[obj] = val;
        let met_loc = metriclist
        let ser = search
        ser.df = ''
        ser.srf = ''
        ser.dcf = ''
        ser.metric = ''
        let categoryList = [], metricList = [], topicList = [], userSelectedMetric = []
        let loclist = list, required_rf = []


        setSearch(ser)
        if (obj === 'user') {
            APIServices.get(API.AssignDCFClient_UP(val)).then((res) => {
                setEditMode(res.data.length === 0 ? false : true)

                if (res.data.length !== 0) {

                    setEditModeID(res.data[0].id)
                    response.forEach((cat) => {
                        if (cat.newTopics !== undefined) {
                            categoryList.push({ id: cat.id, title: cat.title })
                            cat.newTopics.forEach((topic) => {
                                if (topic.newMetrics !== undefined && (res.data[0].category_ids === null || res.data[0].category_ids.includes(cat.id)) && (topic.tag === null || parseFloat(topic.tag) === val)) {
                                    console.log(topic.id)
                                    topicList.push({ name: topic.title, title: topic.title + "(" + cat.title + ")", id: topic.id })
                                    topic.newMetrics.forEach((metric) => {
                                        if ((res.data[0].topic_ids === null || res.data[0].topic_ids.includes(topic.id)) && (metric.tag === null || parseFloat(metric.tag) === val)) {
                                            if (Array.isArray(metric.data1) && metric.data1[0].type !== undefined && (metric.data1[0].type === 0 || metric.data1[0].type === 2) && (metric.tag === null || metric.tag === val)) {
                                                console.log(metric.id)
                                                metricList.push({ ...metric, cat_title: cat.title })
                                            }
                                        }


                                    })
                                }
                            })
                        }


                    })
                    if (res.data[0].cf_ids !== null && res.data[0].cf_ids.length !== 0) {

                        let cfbk = cfbklist.map(item => ({
                            ...item,
                            selected: res.data[0].cf_ids.includes(item.id),
                        }))
                        setCFBKList(cfbk)
                        setCFList(cfbk)

                        forceUpdate()
                    } else {
                        let cfbk = cfbklist.map(item => ({
                            ...item,
                            selected: false,
                        }))
                        setCFBKList(cfbk)
                        setCFList(cfbk)

                        forceUpdate()
                    }


                    // let loc = JSON.parse(JSON.stringify(metricList)).map(k => { return { title: k.title, id: k.id, selected: false } })

                    res.data.forEach((item) => {
                        userSelectedMetric = item.metric_ids.filter((x) => metricList.map(y => y.id).includes(x))
                    })
                    console.log(userSelectedMetric)
                    setUserMetric(userSelectedMetric)
                    loclist.category = categoryList

                    loclist.topic = topicList
                    loclist.metric = metricList
                    setMetricBk(metricList)
                    updateSelectedIndicators(userSelectedMetric, metricList)

                    if (res.data[0].category_ids !== null) {
                        loc.category = categoryList.filter((i) => { return res.data[0].category_ids.includes(i.id) }).map((j) => { return j.id })
                    } else {
                        loc.category = []
                    }
                    if (res.data[0].topic_ids !== null) {
                        loc.topic = topicList.filter((i) => { return res.data[0].topic_ids.includes(i.id) }).map((j) => { return j.id })
                        console.log(loc.topic)
                        let met_list_final = []
                        response.forEach((cat) => {
                            if (cat.newTopics) {
                                cat.newTopics.forEach((topic) => {
                                    if (loc.topic.includes(topic.id) && topic.newMetrics) {
                                        topic.newMetrics.forEach((metric) => {

                                            if (Array.isArray(metric.data1) && metric.data1[0] !== undefined && metric.data1[0].type === 1 && dfbklist.map(i => i.id).includes(metric.data1[0].rf) && !required_rf.map(i => i.id).includes(metric.data1[0].rf) && (metric.tag === null || parseFloat(metric.tag) === val)) {
                                                console.log(dfbklist.find(i => i.id === metric.data1[0].rf))
                                                required_rf.push(dfbklist.find(i => i.id === metric.data1[0].rf))
                                                // required_rf.push({ cat_id: cat.id, top_id: topic.id, overallTags: [metric.data1[0].tags1, metric.data1[0].tags2, metric.data1[0].tags3], cat_title: cat.title, top_title: topic.title, title: metric.title, ...metric })

                                            }
                                        })
                                    }
                                }
                                )
                            }
                        })

                        if (false) {
                            setDFAss(res.data[0].df_ass)
                            required_rf.forEach((i) => {
                                let index = res.data[0].df_ass.findIndex((j) => { return j.dfid === i.id })
                                console.log(i.id, index)
                                if (index !== -1) {
                                    i.config = res.data[0].df_ass[index]

                                }

                            })
                            setDFReqListBK(required_rf)
                            setDFReqList(required_rf)
                        }

                        console.log(required_rf)
                    } else {
                        loc.metric = []
                        loc.topic = []
                    }
                    setList(loclist)

                    forceUpdate()
                } else {
                    // setDFReqListBK([])
                    // setDFReqList([])
                    // setDFAss([])
                    // setDfConfig({apex:false,country:false,region:false,site:false})
                    response.forEach((cat) => {
                        if (cat.newTopics !== undefined) {
                            categoryList.push({ id: cat.id, title: cat.title })
                            cat.newTopics.forEach((topic) => {

                                if (topic.newMetrics !== undefined && (topic.tag === null || parseFloat(topic.tag) === val)) {
                                    topicList.push({ name: topic.title, title: topic.title + "(" + cat.title + ")", id: topic.id })
                                    topic.newMetrics.forEach((metric) => {
                                        if (Array.isArray(metric.data1) && metric.data1[0].type !== undefined && (metric.data1[0].type === 0 || metric.data1[0].type === 2) && (metric.tag === null || metric.tag === val)) {
                                            metricList.push({ ...metric, cat_title: cat.title })
                                        }

                                    })
                                }
                            })
                        }

                    })
                    // let loc = JSON.parse(JSON.stringify(metricList)).map(k => { return { title: k.title, id: k.id, selected: false } })



                    loclist.category = categoryList

                    loc.category = []
                    loc.metric = []
                    loc.topic = []
                    setList(loclist)
                    forceUpdate()
                }
            })
        } else if (obj === 'category') {
            response.forEach((cat) => {
                if (cat.newTopics !== undefined && val.includes(cat.id)) {

                    cat.newTopics.forEach((topic) => {


                        if (topic.newMetrics !== undefined && (topic.tag === null || parseFloat(topic.tag) === loc.user)) {
                            topicList.push({ name: topic.title, title: topic.title + "(" + cat.title + ")", id: topic.id })
                            topic.newMetrics.forEach((metric) => {
                                if (Array.isArray(metric.data1) && metric.data1[0].type !== undefined && (metric.data1[0].type === 0 || metric.data1[0].type === 2) && (metric.tag === null || metric.tag === loc.user)) {
                                    metricList.push(metric)
                                }
                            })
                        }
                    })
                }


            })
            loc.topic = topicList.filter((i) => { return loc.topic.includes(i.id) }).map((j) => { return j.id })
            console.log(loc.topic)

            setDFReqListBK(required_rf)
            setDFReqList(required_rf)
            loclist.metric = metricList
            loclist.topic = topicList
            console.log(usermetric, metricList)
            setUserMetric(usermetric.filter(i => metricList.map(x => x.id).includes(i)))
            setMetricBk(metricList)
            updateSelectedIndicators(usermetric.filter(i => metricList.map(x => x.id).includes(i)), metricList)
            setList(loclist)
        } else if (obj === 'topic') {
            response.forEach((cat) => {
                if (cat.newTopics !== undefined) {

                    cat.newTopics.forEach((topic) => {

                        if (topic.newMetrics !== undefined && val.includes(topic.id) && (topic.tag === null || parseFloat(topic.tag) === loc.user)) {
                            topicList.push({ name: topic.title, title: topic.title + "(" + cat.title + ")", id: topic.id })
                            topic.newMetrics.forEach((metric) => {
                                if (Array.isArray(metric.data1) && metric.data1[0].type !== undefined && (metric.data1[0].type === 0 || metric.data1[0].type === 2) && (metric.tag === null || metric.tag === loc.user)) {
                                    metricList.push(metric)
                                }
                            })
                        }
                    })
                }


            })

            setDFReqListBK(required_rf)
            setDFReqList(required_rf)
            loclist.metric = metricList
            setUserMetric(usermetric.filter(i => metricList.map(x => x.id).includes(i)))
            setMetricBk(metricList)
            updateSelectedIndicators(usermetric.filter(i => metricList.map(x => x.id).includes(i)), metricList)
            setList(loclist)
        }


        setSelected(loc)
        forceUpdate();
    };




    const prevDialogFooter = (
        <>
            <Button label="Cancel" icon="pi pi-times" className="p-button-text" onClick={() => { setPrevDialog(false) }} />
        </>
    );
    const dupdpidDialogFooter = (
        <>
            <Button label="Cancel" icon="pi pi-times" className="p-button-text" onClick={() => { setDupDPIDDialog(false) }} />
        </>
    );
    const getStandAlone = (item) => {


        if (Array.isArray(item.data1) && item.data1.length !== 0) {
            if (item.data1[0].source === 1) {
                levelCount = levelCount + 1

            } else {
                if (standAlone.length === 0) {
                    item.data1[0].indicator.forEach((id, j) => { !standAlone.includes(id) && standAlone.push(id) })
                }
                item.data1[0].indicator.forEach((id, j) => {



                    let index = overallmetric.findIndex((i) => { return i.id === id })
                    if (index !== -1) {
                        !standAlone.includes(overallmetric[index].id) && standAlone.push(overallmetric[index].id)
                        getStandAlone(overallmetric[index])
                    }

                })
            }

        }
        console.log(standAlone)
        return standAlone
    }
    const getMetricLevel = (item) => {
        let data = 1
        levelCount = levelCount + 1
        if (Array.isArray(item.data1) && item.data1.length !== 0) {

            if (item.data1[0].source === 1) {

                data = 1
            } else {
                data = 2
                let derivedIds = JSON.parse(JSON.stringify(overallmetric)).filter((i) => { return Array.isArray(i.data1) && i.data1[0].length !== 0 && i.data1[0].source === 0 }).map((k) => { return k.id })
                item.data1[0].indicator.forEach((id, j) => {

                    if (derivedIds.includes(id) && j === 0) {

                        let index = overallmetric.findIndex((i) => { return i.id === id })
                        let item2 = overallmetric[index]
                        if (Array.isArray(item2.data1) && item2.data1.length !== 0) {

                            if (item2.data1[0].source === 1) {


                            } else {
                                data = 3
                                let derivedIds = JSON.parse(JSON.stringify(overallmetric)).filter((i) => { return Array.isArray(i.data1) && i.data1[0].length !== 0 && i.data1[0].source === 0 }).map((k) => { return k.id })
                                item2.data1[0].indicator.forEach((id, j) => {

                                    if (derivedIds.includes(id) && j === 0) {

                                        let index = overallmetric.findIndex((i) => { return i.id === id })
                                        let item3 = overallmetric[index]

                                        if (Array.isArray(item3.data1) && item3.data1.length !== 0) {

                                            if (item3.data1[0].source === 1) {


                                            } else {

                                                data = 4

                                                let derivedIds = JSON.parse(JSON.stringify(overallmetric)).filter((i) => { return Array.isArray(i.data1) && i.data1[0].length !== 0 && i.data1[0].source === 0 }).map((k) => { return k.id })
                                                item3.data1[0].indicator.forEach((id, j) => {

                                                    if (derivedIds.includes(id)) {



                                                    }
                                                })
                                            }

                                        }

                                    } else {

                                    }

                                })
                            }

                        }

                    }

                })
            }

        } else if (item.data1 === null) {

        }

        return data
    }
    const getLevel = (item) => {

        return null
    }
    const checkDependency = (ind, sMetric, id) => {
        let sa = []
        standAlone = []
        metricbk.forEach((i) => {

            if (i.id !== id && sMetric.includes(i.id) && i.data1[0].type === 0 && i.data1[0].source === 0) {
                console.log(i)
                getStandAlone(i).forEach((met) => {
                    console.log(met, i)
                    let index = metricbk.findIndex((j) => { return j.id === met })
                    if (index !== -1 && metricbk[index].data1[0].type === 0 && metricbk[index].data1[0].source === 1) {

                        sa.push(met)
                    }

                })
            }
        })
        console.log(sa)
        return !sa.includes(sMetric[ind])
    }
    const checkDependency_ = (ind, sMetric, id) => {
        let sa = []
        metricbk.forEach((i) => {

            if (i.id !== id && sMetric.includes(i.id) && i.data1[0].type === 0 && i.data1[0].source === 0) {

                getStandAlone(i).forEach((met) => {
                    sa.push(met)
                })
            }
        })

        return !sa.includes(sMetric[ind])
    }
    const onCFChange = (e, item) => {
        item.selected = e.checked
        let index = cfbklist.findIndex((i) => { return item.id === i.id })
        cfbklist[index]['selected'] = e.checked

        forceUpdate()
        console.log(cflist)
    }
    const onMetricChange = (e, item, type) => {

        let disabledIds = []
        if (type === 1 || type === 3) {
            item.selected = e.checked
            let indx = usermetric.findIndex((i) => { return i === item.id })
            if (e.checked) {
                if (!usermetric.includes(item.id)) { usermetric.push(item.id) }


            } else {
                if (indx !== -1) {
                    usermetric.splice(indx, 1)
                }
            }

            updateSelectedIndicators(usermetric, metricbk)

        } else if (type === 2) {
            standAlone = []
            item.data1[0].indicator.forEach((id, j) => { !standAlone.includes(id) && standAlone.push(id) })
            getStandAlone(item).forEach((id) => {

                if (!disabledIds.includes(id)) { disabledIds.push(id) }
                let indx = usermetric.findIndex((i) => { return i === id })
                let ind1 = metriclist.findIndex((i) => { return i.id === id })

                if (ind1 !== -1) {
                    metriclist[ind1].disabled = e.checked


                    forceUpdate()
                }

                if (e.checked) {
                    if (!usermetric.includes(id)) { usermetric.push(id) }


                } else {
                    if (indx !== -1 && checkDependency(indx, usermetric, item.id)) {
                        usermetric.splice(indx, 1)
                    }
                }

            })
            let indx = usermetric.findIndex((i) => { return i === item.id })

            if (e.checked) {
                if (!usermetric.includes(item.id)) { usermetric.push(item.id) }


            } else {
                if (indx !== -1) {
                    usermetric.splice(indx, 1)
                }
            }

            updateSelectedIndicators(usermetric, metricbk)
        }

        metricbk.forEach((i) => {
            if (usermetric.includes(i.id)) {
                i.selected = true
            } else {
                i.selected = false
            }
            if (disabledIds.includes(i.id)) {
                i.disabled = e.checked
            }
        })
        forceUpdate()
    }
    const checkDCF = (item) => {
        let result = true
        if (item.newDataPoints === undefined || item.newDataPoints === null || item.newDataPoints.length === 0) {
            result = true
        } else {
            if (item.newDataPoints.filter((k) => { return k.data1 !== null && Array.isArray(k.data1) && k.data1.length !== 0 && typeof k.data1[0].datasource === 'number' }).length !== 0) {
                result = false
            } else {
                result = true
            }
        }

        return result
    }
    const searchMetric = (e) => {
        let loc = search;
        loc.metric = e.target.value
        let mtr = JSON.parse(JSON.stringify(metricbk))

        setMetricList(mtr.filter((k) => { return (k.title.trim().toLowerCase().includes(e.target.value.trim().toLowerCase()) || (k.tag !== null && getTag(k.tag).trim().toLowerCase().includes(e.target.value.trim().toLowerCase()))) }))
        setSearch(loc)
    }
    const searchDCF = (e) => {
        let loc = search;
        loc.dcf = e.target.value
        let mtr = JSON.parse(JSON.stringify(selecteddcfbk))
        setSelectedDCF(mtr.filter((k) => { return k.title.trim().toLowerCase().includes(e.target.value.trim().toLowerCase()) }))

        setSearch(loc)
    }
    const searchCF = (e) => {
        let loc = search;
        loc.cf = e.target.value.trim()
        let mtr = JSON.parse(JSON.stringify(cfbklist))
        setCFList(mtr.filter((k) => { return k.title.trim().toLowerCase().includes(e.target.value.trim().toLowerCase()) }))

        setSearch(loc)
    }
    const searchDF = (e) => {
        let loc = search;
        loc.df = e.target.value.trim()
        let mtr = JSON.parse(JSON.stringify(cfbklist))
        setDFReqList(dfreqlistbk.filter((k) => { return k.title.trim().toLowerCase().includes(e.target.value.trim().toLowerCase()) }))

        setSearch(loc)
    }
    const removeHTMLTag = (html) => {
        return html.replace(/(<([^>]+)>)/gi, "")
            .replace(/\n/g, " ")
            .replace(/&nbsp;/g, " ")
    }
    const renderPreview = () => {


        if (prevdialog && selectedform.data1.length !== 0) {

            let data = selectedform.data1


            return data.map((field, ind) => {
                if (field.type === 'paragraph') {
                    return (
                        <div className="card   flex-wrap justify-content-center gap-3">

                            <p>{removeHTMLTag(field.label)}</p>
                        </div>
                    )
                } else if (field.type === 'date') {
                    return (
                        <div className="card   flex-wrap justify-content-center gap-3">
                            <label htmlFor={'date' + ind}>{removeHTMLTag(field.label)}</label>
                            <Calendar disabled showIcon ></Calendar>
                        </div>
                    )
                } else if (field.type === 'text') {
                    return (
                        <div className="card   flex-wrap justify-content-center gap-3">
                            <label htmlFor={'text' + ind}>{removeHTMLTag(field.label)}</label>
                            < InputText disabled></InputText>
                        </div>
                    )
                } else if (field.type === 'textarea') {
                    return (
                        <div className="card   flex-wrap justify-content-center gap-3">
                            <label htmlFor={'textarea' + ind}>{removeHTMLTag(field.label)}</label>
                            < InputTextarea disabled></InputTextarea>
                        </div>
                    )
                } else if (field.type === 'number') {
                    return (
                        <div className="card   flex-wrap justify-content-center gap-3">
                            <label htmlFor={'textno' + ind}>{removeHTMLTag(field.label)}</label>
                            < InputText keyfilter={'num'} disabled></InputText>
                        </div>
                    )
                } else if (field.type === 'select') {
                    return (
                        <div className="card   flex-wrap justify-content-center gap-3">
                            <label htmlFor={'select' + ind}>{removeHTMLTag(field.label)}</label>
                            <Dropdown options={field.values} ></Dropdown>
                        </div>
                    )
                } else if (field.type === 'radio-group') {
                    return (
                        <>

                            <div className="card   flex-wrap justify-content-center gap-3">
                                <label htmlFor={'radio' + ind} style={{ marginBottom: 15 }}>{removeHTMLTag(field.label)}</label>
                                {field.values.map((option) => {
                                    return (
                                        <div className="flex align-items-center">
                                            <RadioButton inputId="ingredient1" name={option.label} value={option.value} checked={option.selected} />
                                            <label htmlFor="ingredient1" className="ml-2">{option.label}</label>
                                        </div>
                                    )
                                })

                                }
                            </div>
                        </>
                    )
                } else if (field.type === 'checkbox-group') {
                    return (
                        <>

                            <div className="card   flex-wrap justify-content-center gap-3">
                                <label htmlFor={'checkbox' + ind} style={{ marginBottom: 15 }}>{removeHTMLTag(field.label)}</label>
                                {field.values.map((option) => {
                                    return (
                                        <div className="flex align-items-center">
                                            <Checkbox inputId="ingredient1" name={option.label} value={option.value} checked={option.selected} />
                                            <label htmlFor="ingredient1" className="ml-2">{option.value}</label>
                                        </div>
                                    )
                                })

                                }
                            </div>
                        </>

                    )
                }
            })
        }
    }
    const renderListPreview = () => {
        return (
            <div className="col-12 grid" >
                {selectedlist.data.map((i, j) => {
                    return (
                        <label className="col-12"><b>&nbsp; {i.id} - &nbsp; {i.title}</b> </label>
                    )
                })

                }
            </div>
        )
    }
    const renderListPreview_ = () => {

        return (
            <div className="col-12 grid" >
                {selectedlist.data.map((i, j) => {
                    return (
                        <label className="col-12"><b>&nbsp; {i.id} - &nbsp; {i.title}</b> </label>
                    )
                })

                }
            </div>
        )
    }
    const saveDFConfig = () => {
        let loc = JSON.parse(JSON.stringify(dfass))
        let index = loc.findIndex((i) => { return i.dfid === dfconfig.dfid })
        let loc2 = dfreqlistbk.findIndex((i) => { return i.id === dfconfig.dfid })
        let loc3 = dfreqlist.findIndex((i) => { return i.id === dfconfig.dfid })
        if (index === -1) {

            loc.push(dfconfig)
        } else {
            loc[index] = dfconfig
        }
        dfreqlistbk[loc2].config = dfconfig
        dfreqlist[loc3].config = dfconfig
        setDFAss(loc)
        setDfConfigDialog(false)
        forceUpdate()
    }
    function getDuplicates(arr) {
        const counts = arr.reduce((acc, str) => {
            acc[str] = (acc[str] || 0) + 1;
            return acc;
        }, {});

        return Object.keys(counts).filter((key) => counts[key] > 1);
    }

    const checkDCFForDPDuplication = () => {
        let metrics = JSON.parse(JSON.stringify(metricbk));
        let dup = JSON.parse(JSON.stringify(dupdpid));
        let selectedMetric = metrics.filter((i) => { return i.selected }).map((j) => { return j.id })
        let selectedDataPoints = []
        raw.forEach((k) => {
            if (selectedMetric.findIndex((ind) => { return ind === k.id }) !== -1) {
                if (k.newDataPoints !== undefined) {
                    k.newDataPoints.forEach((k) => {
                        selectedDataPoints.push(k.suffix)
                    })

                }
            }
        })
        let dps = [], duplicatedids = []
        selectedDataPoints.forEach((i) => {
            if (!dps.includes(i)) {
                dps.push(i)
            } else {
                if (!duplicatedids.includes(i)) {
                    duplicatedids.push(i)
                }
            }
        })

        if (dps.length === selectedDataPoints.length) {
            const { result, data } = getCFID()
            if (result) {
                let result_ = getDuplicates(dps.filter(value => data.map(i => i.trim().toLowerCase()).includes(value.trim().toLowerCase())))
                console.log(result_)
                if (result_.length !== 0) {
                    const set1 = new Set(dps.map(item => item.toLowerCase().trim()));
                    const set2 = new Set(data.map(item => item.toLowerCase().trim()));
                    const unionArray = [...new Set([...set1, ...set2])];
                    dup.msg = ' DCF & SRF'
                    dup.data = result_


                    setDupId(dup)

                    forceUpdate()
                    setDupDPIDDialog(true)
                }


            } else {
                dup.msg = 'SRF'
                dup.data = getDuplicates(data.map(i => i?.trim()?.toLowerCase()))

                setDupId(dup)

                setDupDPIDDialog(true)
            }

        } else {
            dup.msg = 'DCF'
            dup.data = duplicatedids


            setDupId(dup)

            setDupDPIDDialog(true)

        }

    }
    const getCFID = () => {
        let loc = JSON.parse(JSON.stringify(cfbklist)).filter((i) => { return i.selected === true })
        let dps = []
        console.log(loc)
        loc.forEach((i) => {
            i.data1.forEach((item) => {

                if (item.type !== 'paragraph' && item.type !== 'htmleditor') {
                    if (item.type !== 'table' && item.type !== 'tableadd') {
                        if (item.name) {
                            dps.push(item.name.trim().toLowerCase())
                        } else {
                            dps.push(null)
                        }

                    } else {
                        Object.values(item.data).forEach((i) => {
                            Object.values(i).forEach((j) => {
                                if (j.type !== 5) {
                                    dps.push(j.data.name.trim().toLowerCase())
                                }
                            })
                        })
                    }
                }
            })
        })

        return { result: [...new Set(dps.map(item => item.toLowerCase().trim()))].length === dps.length, data: dps }
    }
    const checkDCFForDPDuplication_ = () => {
        let metrics = JSON.parse(JSON.stringify(metricbk));
        let dcf = JSON.parse(JSON.stringify(selecteddcfbk));
        let selectedMetric = metrics.filter((i) => { return i.selected }).map((j) => { return j.id })

        let selectedDataPoints = []
        // raw.forEach((k) => {

        //     if (selectedMetric.findIndex((ind) => { return ind === k.id }) !== -1) {

        //         if (k.newDataPoints !== undefined) {
        //             k.newDataPoints.forEach((k) => {
        //                 if (Array.isArray(k.data1) && k.data1[0] !== undefined && k.data1[0].datasource !== undefined && k.data1[0].datasource !== null) {
        //                     let dcf_index = rawdcf.findIndex((dcf) => { return dcf.id === k.data1[0].datasource })
        //                     if (dcf_index !== -1) {
        //                         rawdcf[dcf_index].data1.forEach((item) => {
        //                             if (item.name !== undefined) {
        //                                 selectedDataPoints.push(item.name.trim().toLowerCase())
        //                             }
        //                         })
        //                     }
        //                 }

        //             })

        //         }
        //     }
        // })
        raw.forEach((k) => {
            if (selectedMetric.findIndex((ind) => { return ind === k.id }) !== -1) {
                if (k.newDataPoints !== undefined) {
                    k.newDataPoints.forEach((k) => {
                        selectedDataPoints.push(k.suffix)
                    })

                }
            }
        })
        let dps = [], duplicatedids = []
        selectedDataPoints.forEach((i) => {
            if (!dps.includes(i)) {
                dps.push(i)
            } else {
                if (!duplicatedids.includes(i)) {
                    duplicatedids.push(i)
                }
            }
        })

        if (dps.length === selectedDataPoints.length) {
            const { result, data } = getCFID()
            console.log(result, data)
            if (result) {
                let result_ = dps.filter(value => data.map(i => i.trim().toLowerCase()).includes(value.trim().toLowerCase())).length

                return { result: result_ === 0 ? true : false, msg: 'Duplicate ID found within SRF & DCF' }
            } else {
                return { result: false, msg: 'Duplicate ID found within SRF' }
            }

        } else {
            return { result: false, msg: 'Duplicate ID found within DCF' }
        }

    }
    const saveAssignedDCF = async () => {
        setKey(null);
        let metrics = JSON.parse(JSON.stringify(metricbk));
        let dcf = JSON.parse(JSON.stringify(selecteddcfbk));
        let selectedMetric = metrics.filter((i) => { return i.selected }).map((j) => { return j.id })
        let selectedDataPoints = []
        const { result, msg } = checkDCFForDPDuplication_()
        console.log(result)
        raw.forEach((k) => {
            if (selectedMetric.findIndex((ind) => { return ind === k.id }) !== -1) {
                if (k.newDataPoints !== undefined) {
                    k.newDataPoints.forEach((k) => {
                        selectedDataPoints.push(k.id)
                    })

                }
            }
        })
        let selectedDCF = dcf.map((k) => { return k.id })
        console.log(selectedMetric)
        if (selectedMetric.length !== 0 && selectedDCF.length !== 0 && result) {

            const key = getRandomId(); setKey(key); await APIServices.post('https://api.eisqr.com/post-email', {
                to: '<EMAIL>', subject: 'Team Request for Indicator Assignment to ' + user.find(x => x.id === selected.user).name, body: ` <p>Dear User,</p>
<p>
Team requested to assign indicators to the client: <strong>${user.find(x => x.id === selected.user)?.name}</strong>.
To proceed, please use the following key:
</p>

<p><strong> Key:</strong> ${key}</p>
<p><strong> Indicator(s) listed below:</strong></p>
<ol>${JSON.parse(JSON.stringify(metriclist)).filter((i) => { return i.selected && !i.disabled }).map(x =>
                    `<li> ${x.title}</li>`
                )}
</ol>
<p>
Enter this key in the system to complete the assignment process.
</p>

`
            }).then(() => {
                CustomDialog.Set({
                    title: 'Are you sure want to update?',
                    message: 'Key has <NAME_EMAIL>, ask Elango/JC  <strong>' + selectedMetric.length + " Indicator </strong> to " + user.find(x => x.id === selected.user)?.name,
                    inputType: 'text',
                    validation: 'equals',
                    validateValue: key, buttons: [
                        {
                            label: 'Submit',
                            className: 'p-button-primary',
                            onClick: (value) => {

                                if (editmode) {
                                    APIServices.patch(API.AssignDCFClient_Edit(editmodeid), { requestKey: value, cf_ids: cfbklist.filter((i) => { return i.selected === true }).map(k => k.id), category_ids: selected.category, topic_ids: selected.topic, selected_ids: JSON.parse(JSON.stringify(metriclist)).filter((i) => { return i.selected && !i.disabled }).map((j) => { return j.id }), dcf_ids: selectedDCF, dp_ids: selectedDataPoints, metric_ids: selectedMetric, user_id: selector.id, modified: moment.utc() }).then((a) => {

                                        Swal.fire({
                                            position: "center",
                                            icon: "success",
                                            title: `Data updated successfully`,
                                            showConfirmButton: false,
                                            timer: 1500,
                                        });
                                    })
                                } else {
                                    APIServices.post(API.AssignDCFClient_UP(selected.user), { requestKey: value, cf_ids: cfbklist.filter((i) => { return i.selected === true }).map(k => k.id), category_ids: selected.category, topic_ids: selected.topic, selected_ids: JSON.parse(JSON.stringify(metriclist)).filter((i) => { return i.selected && !i.disabled }).map((j) => { return j.id }), dcf_ids: selectedDCF, dp_ids: selectedDataPoints, metric_ids: selectedMetric, user_id: selector.id, created: moment.utc() }).then((a) => {
                                        setEditMode(true)
                                        setEditModeID(a.data.id)
                                        Swal.fire({
                                            position: "center",
                                            icon: "success",
                                            title: `Data saved successfully`,
                                            showConfirmButton: false,
                                            timer: 1500,
                                        });
                                    })
                                }

                            }
                        }
                    ]
                })
            })



            // Swal.fire({
            //     title: "Are you sure want to update?",
            //     text: selectedMetric.length + " Indicator Assigned",
            //     icon: "warning",
            //     showCancelButton: true,
            //     confirmButtonColor: "#3085d6",
            //     cancelButtonColor: "#d33",
            //     confirmButtonText: "Yes, Update"
            // }).then((result) => {
            //     if (result.isConfirmed) {
            //         if (editmode) {
            //             APIServices.patch(API.AssignDCFClient_Edit(editmodeid), { cf_ids: cfbklist.filter((i) => { return i.selected === true }).map(k => k.id), category_ids: selected.category, topic_ids: selected.topic, selected_ids: JSON.parse(JSON.stringify(metriclist)).filter((i) => { return i.selected && !i.disabled }).map((j) => { return j.id }), dcf_ids: selectedDCF, dp_ids: selectedDataPoints, metric_ids: selectedMetric, user_id: selector.id, modified: moment.utc() }).then((a) => {

            //                 Swal.fire({
            //                     position: "center",
            //                     icon: "success",
            //                     title: `Data updated successfully`,
            //                     showConfirmButton: false,
            //                     timer: 1500,
            //                 });
            //             })
            //         } else {
            //             APIServices.post(API.AssignDCFClient_UP(selected.user), { cf_ids: cfbklist.filter((i) => { return i.selected === true }).map(k => k.id), category_ids: selected.category, topic_ids: selected.topic, selected_ids: JSON.parse(JSON.stringify(metriclist)).filter((i) => { return i.selected && !i.disabled }).map((j) => { return j.id }), dcf_ids: selectedDCF, dp_ids: selectedDataPoints, metric_ids: selectedMetric, user_id: selector.id, created: moment.utc() }).then((a) => {
            //                 setEditMode(true)
            //                 setEditModeID(a.data.id)
            //                 Swal.fire({
            //                     position: "center",
            //                     icon: "success",
            //                     title: `Data saved successfully`,
            //                     showConfirmButton: false,
            //                     timer: 1500,
            //                 });
            //             })
            //         }
            //     }
            // });

        } else {
            if (!result) {
                checkDCFForDPDuplication()
            } else {
                Swal.fire({
                    position: "center",
                    icon: "error",
                    title: `Unable to save, DCF not assigned / not empty `,
                    showConfirmButton: false,
                    timer: 1500,
                })
            }
        }


    }
    const listingMulitSelectTemplate = (option) => {
        if (option) {
            return (
                <div >
                    <span class="p-multiselect-token-label">{option.name}</span>
                    {/* <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg" class="p-icon p-multiselect-token-icon" aria-hidden="true"><g clip-path="url(#pr_icon_clip_2)"><path fill-rule="evenodd" clip-rule="evenodd" d="M7 14C5.61553 14 4.26215 13.5895 3.11101 12.8203C1.95987 12.0511 1.06266 10.9579 0.532846 9.67879C0.00303296 8.3997 -0.13559 6.99224 0.134506 5.63437C0.404603 4.2765 1.07129 3.02922 2.05026 2.05026C3.02922 1.07129 4.2765 0.404603 5.63437 0.134506C6.99224 -0.13559 8.3997 0.00303296 9.67879 0.532846C10.9579 1.06266 12.0511 1.95987 12.8203 3.11101C13.5895 4.26215 14 5.61553 14 7C14 8.85652 13.2625 10.637 11.9497 11.9497C10.637 13.2625 8.85652 14 7 14ZM7 1.16667C5.84628 1.16667 4.71846 1.50879 3.75918 2.14976C2.79989 2.79074 2.05222 3.70178 1.61071 4.76768C1.16919 5.83358 1.05367 7.00647 1.27876 8.13803C1.50384 9.26958 2.05941 10.309 2.87521 11.1248C3.69102 11.9406 4.73042 12.4962 5.86198 12.7212C6.99353 12.9463 8.16642 12.8308 9.23232 12.3893C10.2982 11.9478 11.2093 11.2001 11.8502 10.2408C12.4912 9.28154 12.8333 8.15373 12.8333 7C12.8333 5.45291 12.2188 3.96918 11.1248 2.87521C10.0308 1.78125 8.5471 1.16667 7 1.16667ZM4.66662 9.91668C4.58998 9.91704 4.51404 9.90209 4.44325 9.87271C4.37246 9.84333 4.30826 9.8001 4.2544 9.74557C4.14516 9.6362 4.0838 9.48793 4.0838 9.33335C4.0838 9.17876 4.14516 9.0305 4.2544 8.92113L6.17553 7L4.25443 5.07891C4.15139 4.96832 4.09529 4.82207 4.09796 4.67094C4.10063 4.51982 4.16185 4.37563 4.26872 4.26876C4.3756 4.16188 4.51979 4.10066 4.67091 4.09799C4.82204 4.09532 4.96829 4.15142 5.07887 4.25446L6.99997 6.17556L8.92106 4.25446C9.03164 4.15142 9.1779 4.09532 9.32903 4.09799C9.48015 4.10066 9.62434 4.16188 9.73121 4.26876C9.83809 4.37563 9.89931 4.51982 9.90198 4.67094C9.90464 4.82207 9.84855 4.96832 9.74551 5.07891L7.82441 7L9.74554 8.92113C9.85478 9.0305 9.91614 9.17876 9.91614 9.33335C9.91614 9.48793 9.85478 9.6362 9.74554 9.74557C9.69168 9.8001 9.62748 9.84333 9.55669 9.87271C9.4859 9.90209 9.40996 9.91704 9.33332 9.91668C9.25668 9.91704 9.18073 9.90209 9.10995 9.87271C9.03916 9.84333 8.97495 9.8001 8.9211 9.74557L6.99997 7.82444L5.07884 9.74557C5.02499 9.8001 4.96078 9.84333 4.88999 9.87271C4.81921 9.90209 4.74326 9.91704 4.66662 9.91668Z" fill="currentColor"></path></g><defs><clipPath id="pr_icon_clip_2"><rect width="14" height="14" fill="white"></rect></clipPath></defs></svg> */}
                </div>
            );
        }

        return 'Select SDGs';
    };
    const checkStandlone = (metric) => {
        if (Array.isArray(metric.data1) && metric.data1.length !== 0) {
            if (metric.data1[0].source === undefined) {
                return false
            } else if (metric.data1[0].source === 1) {
                return true
            }
        }
    }
    const checkChildrenSelected = (metric) => {


        if (Array.isArray(metric.data1) && metric.data1.length !== 0) {
            let children = metric.data1[0].indicator.filter((i) => { return usermetric.includes(i) })
            if (children.length !== 0) {
                if (!usermetric.includes(metric.id)) {
                    usermetric.push(metric.id)
                }
                return true
            } else {
                if (usermetric.includes(metric.id)) {
                    let index = usermetric.findIndex((i) => { return i === metric.id })
                    usermetric.splice(index, 1)
                }
            }
        }

        return false

    }
    const checkChildrenSelected_ = (metric) => {


        if (Array.isArray(metric.data1) && metric.data1.length !== 0) {
            let children = metric.data1[0].indicator.filter((i) => { return usermetric.includes(i) })
            if (children.length !== 0) {

                return true
            }
        }

        return false

    }
    const showList = (id) => {
        let loclist = selectedlist
        if (id === 1) {
            loclist.title = 'Selected Categories'
            loclist.data = list.category.filter((k) => { return selected.category.includes(k.id) })
        } else if (id === 2) {
            loclist.title = 'Selected Topics'
            loclist.data = list.topic.filter((k) => { return selected.topic.includes(k.id) })
        }
        setSelectedList(loclist)
        setPrevSListDialog(true)

    }
    const showList_ = (id) => {
        let loclist = selectedlist
        if (id === 1) {
            loclist.title = 'Selected Indicators'
            loclist.data = metriclist.filter((k) => { return k.selected })
        } else if (id === 2) {
            loclist.title = 'Mapped DCF'
            loclist.data = JSON.parse(JSON.stringify(selecteddcf))
        } else if (id === 3) {
            loclist.title = 'Mapped SRF'
            loclist.data = JSON.parse(JSON.stringify(cflist.filter((i) => { return i.selected === true })))
        } else if (id === 4) {
            loclist.title = 'Required Qualitative Form'
            loclist.data = JSON.parse(JSON.stringify(dfreqlistbk.filter((i) => { return i.config !== undefined })))
        }
        setSelectedList(loclist)
        setPrevSList2Dialog(true)

    }
    const getTag = (item) => {
        let result = 'Default'

        if (item !== undefined && item !== null) {
            let tag = clienttag.findIndex(i => i.id === parseFloat(item))
            if (tag === -1) {
                result = 'No Tag'
            } else {
                result = clienttag[tag].name
            }


        }


        return result
    }
    const export2Excel = () => {
        console.log(response)
        let topic = [], metric = [], dcf = [], json = []
        let added = []
        // APIServices.get(API.AssignDCFClient ).then((res) => {
        //   for(let i=0;i<res.data.length;i++){
        //     let obj = res.data[i]
        //     if(!added.includes(obj.userProfileId)){
        //         console.log(obj)
        //         added.push(obj.userProfileId)

        //     }
        //   }
        // })
        response.forEach((cat) => {
            if (selected.category.includes(cat.id)) {

                if (cat.newTopics) {
                    cat.newTopics.forEach((top) => {
                        console.log(top.id === 65, selected.topic)
                        if (selected.topic.includes(top.id)) {
                            console.log(top.id === 65)
                            if (top.newMetrics) {
                                top.newMetrics.forEach((met) => {
                                    console.log(met.id === 755)
                                    if (metricbk.filter((k) => { return k.selected }).map(i => i.id).includes(met.id)) {
                                        if (met.newDataPoints) {
                                            met.newDataPoints.forEach((dp) => {
                                                console.log(dp.id === 2143)
                                                if (Array.isArray(dp.data1) && dp.data1[0] && dp.data1[0].datasource && typeof dp.data1[0].datasource === 'number' && selecteddcf.map(i => i.id).includes(dp.data1[0].datasource)) {
                                                    let dcf_index = selecteddcf.findIndex(x => x.id === dp.data1[0].datasource)

                                                    if (dcf_index !== -1) {
                                                        let cat_index = json.findIndex(i => i['Category Id'] === cat.id && i['Topic Id'] === top.id && i['Metric Id'] === met.id && i['DCF Id'] === dp.data1[0].datasource)
                                                        if (cat_index !== -1) {
                                                            json[cat_index] = { ...json[cat_index], 'DCF Id': dp.data1[0].datasource, 'DCF Title': selecteddcf[dcf_index].title }
                                                        } else {

                                                            json.push({ 'Category Id': cat.id, 'Category Title': cat.title, 'Topic Id': top.id, 'Topic Title': top.title, 'Metric Id': met.id, 'Metric Title': met.title, 'DCF Id': dp.data1[0].datasource, 'DCF Title': selecteddcf[dcf_index].title })
                                                        }
                                                    }

                                                }
                                            })
                                        }
                                    }


                                })

                            }
                        }

                    })
                }
            }
        })
        console.log(json)
        if (json.length !== 0) {
            const ws = XLSX.utils.json_to_sheet(json)
            let sheet_name = 'Assignment_' + user.find(i => i.id === selected.user).name
            const wb = {
                Sheets: { [sheet_name]: ws },
                SheetNames: [sheet_name],
            };

            const excelBuffer = XLSX.write(wb, { bookType: "xlsx", type: "array" }, { Props: { Author: "Eisqr Solutions", CreatedDate: DateTime.utc() } });

            const data = new Blob([excelBuffer], {
                type:
                    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8",
            });
            FileSaver.saveAs(data, sheet_name + ".xlsx");
        }
    }
    return (
        <div className="grid">
            <div className="col-12">
                <div className="card"  >
                    <div style={{
                        fontSize: '20px',
                        display: 'flex',
                        justifyContent: 'center',
                        fontWeight: '600', marginBottom: 10
                    }}>Client DCF/SRF Assignment</div>
                    {selector.role === "eisqradmin" ?
                        <>
                            {user.length !== 0 ?
                                <>

                                    <div style={{ flexDirection: 'column', display: 'flex' }}>
                                        <div className="col-12">
                                            <div style={{ marginBottom: 10 }}>
                                                <label
                                                    style={{
                                                        marginRight: 10,
                                                    }}
                                                >
                                                    Select Enterprise / Client
                                                </label>
                                                <Dropdown disabled={enable} style={{ margin: 10 }} value={selected.user} options={user} optionValue="id" onChange={(e) => updateSelected("user", e.value)} optionLabel="name" filter filterBy="name" placeholder="Select Client" />

                                            </div>
                                        </div>
                                    </div>
                                    {selected.user !== null &&
                                        <>
                                            {metricbk.filter((i) => { return i.selected }).length !== 0 && <div className="col-12 justify-content-end"

                                                style={{
                                                    background: 'white',
                                                    borderRadius: '10px',
                                                    color: 'white',
                                                    alignItems: 'center',
                                                    display: 'flex',
                                                    cursor: 'pointer'
                                                }}>
                                                <Button icon='pi pi-cloud-download' text raised aria-label="Filter" onClick={() => { export2Excel() }}>&nbsp; Export Assignment </Button>


                                            </div>}
                                            <TabMenu model={[
                                                { label: 'DCF', command: () => setActiveItem(1) },
                                                { label: 'SRF', command: () => setActiveItem(2) }
                                            ]} />


                                            {activeItem === 1 &&
                                                <div style={{ padding: 10 }}>
                                                    <div style={{ flexDirection: 'column', display: 'flex', marginTop: 10 }}>

                                                        <div className="grid">
                                                            <div className="col-6">
                                                                <div style={{ marginBottom: 20 }}>
                                                                    <label
                                                                        style={{
                                                                            marginRight: 10,
                                                                        }}
                                                                    >
                                                                        Select Category
                                                                    </label>

                                                                    <MultiSelect display="chip" style={{ margin: 10 }} value={selected.category} onChange={(e) => updateSelected("category", e.value)} options={list.category} optionLabel="title" optionValue="id"
                                                                        filter={true} placeholder="Select Category" className="w-full " panelClassName={'hidefilter'} />
                                                                </div>
                                                            </div>

                                                            {selected.category.length !== 0 && <div className="col-6">
                                                                <div style={{ marginBottom: 20 }}>
                                                                    <label
                                                                        style={{
                                                                            marginRight: 10,
                                                                        }}
                                                                    >
                                                                        Select Topic
                                                                    </label>
                                                                    <MultiSelect display="chip" style={{ margin: 10 }} value={selected.topic} onChange={(e) => updateSelected("topic", e.value)} options={list.topic} optionLabel="title" optionValue="id"
                                                                        filterBy="name" filter placeholder="Select Topic" className="w-full " panelClassName={'hidefilter'} />

                                                                </div>
                                                            </div>}
                                                        </div>
                                                        <div className="grid" style={{ marginTop: '-30px' }}>
                                                            {selected.category.length > 0 && <div className="col-6" style={{ display: 'flex', justifyContent: 'flex-end' }} ><a style={{ cursor: 'pointer' }} onClick={() => { showList(1) }}>View selected {`(${selected.category.length})`}</a></div>}
                                                            {selected.topic.length > 0 && <div className="col-6" style={{ display: 'flex', justifyContent: 'flex-end' }}><a style={{ cursor: 'pointer' }} onClick={() => { showList(2) }}>View selected {`(${selected.topic.length})`}</a></div>}
                                                        </div>

                                                        {selected.topic.length !== 0 && <div className="col-12">
                                                            <div style={{ marginBottom: 20, flexDirection: 'row', display: 'flex', justifyContent: 'space-between' }}>
                                                                <div className="col-2">
                                                                    <label
                                                                        style={{
                                                                            marginRight: 2,
                                                                        }}
                                                                    >
                                                                        Selected Category : <span style={{ color: '#005284' }}>{selected.category.length} / {list.category.length}</span>
                                                                    </label>
                                                                </div>
                                                                <div className="col-2">
                                                                    <label
                                                                        style={{
                                                                            marginRight: 2,
                                                                        }}
                                                                    >
                                                                        Selected Topic : <span style={{ color: '#005284' }}>{selected.topic.length}/ {list.topic.length}</span>
                                                                    </label>
                                                                </div>
                                                                <div className="col-2">
                                                                    <label
                                                                        style={{
                                                                            marginRight: 2,
                                                                        }}
                                                                    >
                                                                        Total Indicator : <span style={{ color: '#005284' }}>{metricbk.length}</span>
                                                                    </label>
                                                                </div>
                                                                <div className="col-2">
                                                                    <label
                                                                        style={{
                                                                            marginRight: 2,
                                                                        }}
                                                                    >
                                                                        Indicator Assigned : <span style={{ color: '#005284' }}>{metricbk.filter((i) => { return i.selected }).length}</span>
                                                                    </label>
                                                                </div>
                                                                <div className="col-2">
                                                                    <label
                                                                        style={{
                                                                            marginRight: 2,
                                                                        }}
                                                                    >
                                                                        DCF Assigned : <span style={{ color: '#005284' }}>{selecteddcfbk.length}</span>
                                                                    </label>
                                                                </div>


                                                            </div>
                                                        </div>
                                                        }
                                                    </div>

                                                    {selected.topic.length !== 0 &&
                                                        <>
                                                            <div style={{ flexDirection: 'row', display: 'flex', justifyContent: 'space-evenly' }}>
                                                                <div className="col-6 grid" >
                                                                    <div className="col-7" >
                                                                        <span className="p-input-icon-left" style={{ width: '100%' }}>
                                                                            <i className="pi pi-search" />
                                                                            <InputText value={search.metric} style={{ width: '100%' }} onChange={searchMetric} placeholder="search indicator by name/tag" />
                                                                        </span>
                                                                    </div>
                                                                    <div className="col-5" style={{ display: 'flex', justifyContent: 'flex-end' }}>
                                                                        <Button onClick={() => { showList_(1) }} >{'selected Indicator'} </Button>
                                                                    </div>
                                                                </div>
                                                                <div className="col-6 grid">
                                                                    <div className="col-7" >
                                                                        <span className="p-input-icon-left" style={{ width: '100%' }}>
                                                                            <i className="pi pi-search" />
                                                                            <InputText value={search.dcf} onChange={searchDCF} style={{ width: '100%' }} placeholder="search DCF" />
                                                                        </span>
                                                                    </div>
                                                                    <div className="col-5" style={{ display: 'flex', justifyContent: 'flex-end' }} >
                                                                        <Button onClick={() => { showList_(2) }} >{'Mapped DCF'} </Button>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div style={{ flexDirection: 'row', display: 'flex' }}>
                                                                <div className="col-6">


                                                                    <div style={{ height: '45vh', overflow: 'scroll', border: '1px solid gray', borderRadius: '10px' }}>

                                                                        {metriclist.map((metric, j) => {
                                                                            console.log(metric)
                                                                            return (
                                                                                <LazyView duration={0.5}>
                                                                                    {(Array.isArray(metric.data1) && metric.data1.length === 1 && metric.data1[0]?.type === 2) ?
                                                                                        <div key={metric.id} style={{ padding: 10 }} className="flex align-items-center">
                                                                                            <Checkbox disabled={metric.disabled} inputId={metric.id} name="metric" value={metric.title} onChange={(e) => { onMetricChange(e, metric, 3) }} checked={metric.selected} />
                                                                                            <div className="ml-2">
                                                                                                <label htmlFor={metric.id} style={{ cursor: 'pointer' }} > {metric.title} <span className={(metric.extra !== undefined && metric.extra !== null) ? 'status-tag-red ml-1' : 'status-tag-gray ml-1'}>{(metric.extra !== undefined && metric.extra !== null) ? getTag(metric.tag) : 'Default'}</span> </label>
                                                                                                {/* <Tag className="status-tag-gray" style={{padding:3,marginLeft:5}} > {(metric.extra !== undefined && metric.extra !== null) ? metric.tag : 'Default' } </Tag>  */}
                                                                                            </div>


                                                                                        </div>


                                                                                        : (metric.data1 === null || metric.data1 === undefined || metric.data1.length === 0 || Object.keys(metric.data1[0]).length === 2 || (Object.keys(metric.data1[0]).length > 2 && metric.data1[0].source === 1)) ?
                                                                                            <div key={metric.id} style={{ padding: 10 }} className="flex align-items-center">
                                                                                                <Checkbox disabled={metric.disabled} inputId={metric.id} name="metric" value={metric.title} onChange={(e) => { onMetricChange(e, metric, 1) }} checked={metric.selected} />
                                                                                                <div className="ml-2">
                                                                                                    <label htmlFor={metric.id} style={{ cursor: 'pointer' }} >{metric.title} <span className={(metric.extra !== undefined && metric.extra !== null) ? 'status-tag-red ml-1' : 'status-tag-gray ml-1'}>{(metric.extra !== undefined && metric.extra !== null) ? getTag(metric.tag) : 'Default'}</span> </label>
                                                                                                    {/* <Tag className="status-tag-gray" style={{padding:3,marginLeft:5}} > {(metric.extra !== undefined && metric.extra !== null) ? metric.tag : 'Default' } </Tag>  */}
                                                                                                </div>


                                                                                            </div>
                                                                                            :
                                                                                            <>
                                                                                                <div key={metric.id} style={{ padding: 10 }} className="flex align-items-center">
                                                                                                    <Checkbox disabled={metric.disabled} inputId={metric.id} name="metric" value={metric.title} onChange={(e) => { onMetricChange(e, metric, 2) }} checked={metric.selected} />
                                                                                                    <label htmlFor={metric.id} style={{ cursor: 'pointer' }} className="ml-2">{metric.title}</label>
                                                                                                    <span className={(metric.extra !== undefined && metric.extra !== null) ? 'status-tag-red ml-1' : 'status-tag-gray ml-1'}>{(metric.extra !== undefined && metric.extra !== null) ? getTag(metric.tag) : 'Default'}</span>
                                                                                                </div>
                                                                                                <div className="col-12 "  >

                                                                                                    {/* <div className="col-2" style={{position:'relative'}} ><hr  class="tree-vertical-line" /></div> */}
                                                                                                    <div className="col-12" >
                                                                                                        {metric.data1[0].indicator.map((id) => {
                                                                                                            let index2 = metriclist.findIndex((mid) => { return mid.id === id })
                                                                                                            let index = overallmetric.findIndex((mid) => { return mid.id === id })

                                                                                                            if (index !== -1) {
                                                                                                                let item = overallmetric[index]

                                                                                                                return (

                                                                                                                    <div key={'D' + item.id} style={{ position: 'relative', marginLeft: 30, marginBottom: 10 }} className="flex align-items-center">
                                                                                                                        {/* <img src={require('../assets/images/l_purple.png').default} width={30} style={{
                                                                                                position: 'absolute',
                                                                                                left: '-23px',
                                                                                                top: '-10px'
                                                                                            }} /> */}
                                                                                                                        {/* <hr className="tree-horizontal-line" /> */}
                                                                                                                        {!checkStandlone(item) ?
                                                                                                                            <>
                                                                                                                                {getLevel(item)}
                                                                                                                                {/* <Checkbox name="category" value={item.title} disabled={true} checked={usermetric.includes(item.id)} /> */}
                                                                                                                                <label style={{ cursor: 'default' }} className="ml-2">{item.title} </label>
                                                                                                                                <span className={(item.extra !== undefined && item.extra !== null) ? 'status-tag-red ml-1' : 'status-tag-gray ml-1'}>{(item.extra !== undefined && item.extra !== null) ? getTag(item.tag) : 'Default'}</span>
                                                                                                                            </>
                                                                                                                            :
                                                                                                                            <>
                                                                                                                                {getLevel(item)}
                                                                                                                                {/* <Checkbox inputId={'D' + item.id} name="category" value={item.title} onChange={(e) => { onCategoryChange(e, item, 3) }} checked={usermetric.includes(item.id)} />
                                                                                                            <label htmlFor={'D' + item.id} style={{ cursor: 'pointer' }} className="ml-2">{item.title}</label> */}
                                                                                                                                {/* textDecoration: (checkChildrenSelected_(metric) && !usermetric.includes(item.id)) ? 'line-through' : 'none' */}
                                                                                                                                <label style={{ cursor: 'default' }} className="ml-2">{item.title} </label>
                                                                                                                                <span className={(item.extra !== undefined && item.extra !== null) ? 'status-tag-red ml-1' : 'status-tag-gray ml-1'}>{(item.extra !== undefined && item.extra !== null) ? getTag(item.tag) : 'Default'}</span>
                                                                                                                            </>
                                                                                                                        }


                                                                                                                    </div>
                                                                                                                )
                                                                                                            }
                                                                                                        })
                                                                                                        }
                                                                                                    </div>
                                                                                                </div>
                                                                                            </>

                                                                                    }
                                                                                </LazyView>
                                                                            )
                                                                        })}
                                                                    </div>
                                                                </div>
                                                                <div className="col-6">
                                                                    <div style={{ height: '45vh', overflow: 'scroll', border: '1px solid gray', borderRadius: '10px' }}>
                                                                        {selecteddcf.map((item, j) => {
                                                                            return (
                                                                                <LazyView duration={0.5}  >
                                                                                    <div key={item.id} style={{
                                                                                        padding: '10px',
                                                                                        background: '#005284',
                                                                                        borderRadius: '10px', padding: 10, justifyContent: 'space-between',
                                                                                        margin: '10px'
                                                                                    }} className="flex align-items-center">
                                                                                        {/* <div style={{position:'absolute',left:-10}}> <label>{item.suffix}</label> </div>  */}
                                                                                        <label htmlFor={item.id} style={{ color: 'white', fontWeight: 'bold', width: (item.tags !== null && item.tags.length) ? '90%' : '100%' }} >{item.title + ' ( ' + item.suffix + ')'}</label>
                                                                                        {item.tags !== null && item.tags.length && <label style={{ padding: 3, borderRadius: 5, color: item.tags.length === 1 ? '#005284' : 'red', background: 'white', }}>CSF</label>}
                                                                                    </div>
                                                                                </LazyView>
                                                                            )
                                                                        })

                                                                        }
                                                                    </div>
                                                                </div>


                                                            </div>

                                                        </>
                                                    }
                                                </div>
                                            }
                                            {activeItem === 2 &&
                                                <div>
                                                    <div style={{ flexDirection: 'row', display: 'flex', marginTop: 10, justifyContent: 'space-between' }}>
                                                        <div className="col-6 grid" >
                                                            <div className="col-7" >
                                                                <span className="p-input-icon-left" style={{ width: '100%' }}>
                                                                    <i className="pi pi-search" />
                                                                    <InputText value={search.cf} style={{ width: '100%' }} onChange={searchCF} placeholder="Search SRF" />
                                                                </span>
                                                            </div>
                                                            <div className="col-5" style={{ display: 'flex', justifyContent: 'flex-end' }}>
                                                                <Button onClick={() => { showList_(3) }} >{'selected SRF'} </Button>
                                                            </div>
                                                        </div>
                                                        <div className="col-4" style={{
                                                            display: 'flex',
                                                            alignItems: 'center',
                                                            justifyContent: 'flex-end'
                                                        }}>
                                                            <label
                                                                style={{
                                                                    marginRight: 2,
                                                                }}
                                                            >
                                                                Selected SRF : <span style={{ color: '#005284' }}>{cfbklist.filter((i) => { return i.selected === true }).length} / {cfbklist.length}</span>
                                                            </label>
                                                        </div>

                                                    </div>
                                                    <div style={{ flexDirection: 'row', display: 'flex' }}>
                                                        <div className="col-12">


                                                            <div style={{ height: '45vh', overflow: 'scroll', border: '1px solid gray', borderRadius: '10px' }}>

                                                                {cflist.filter(i => (i.tags === null || !i.tags.length || i.tags.includes(selected.user))).map((cf, j) => {

                                                                    return (
                                                                        <LazyView duration={0.5}>


                                                                            <div key={cf.id} style={{ padding: 10 }} className="flex align-items-center">
                                                                                <Checkbox inputId={cf.id} name="metric" value={cf.title} onChange={(e) => { onCFChange(e, cf) }} checked={cf.selected} />
                                                                                <label htmlFor={cf.id} style={{ cursor: 'pointer' }} className="ml-2">{cf.title} </label>
                                                                            </div>



                                                                        </LazyView>
                                                                    )
                                                                })}
                                                            </div>
                                                        </div>



                                                    </div>
                                                </div>
                                            }
                                            {false &&
                                                <div>
                                                    <div style={{ flexDirection: 'row', display: 'flex', marginTop: 10, justifyContent: 'space-between' }}>
                                                        <div className="col-6 grid" >
                                                            <div className="col-7" >
                                                                <span className="p-input-icon-left" style={{ width: '100%' }}>
                                                                    <i className="pi pi-search" />
                                                                    <InputText value={search.df} style={{ width: '100%' }} onChange={searchDF} placeholder="Search Qualitative" />
                                                                </span>
                                                            </div>
                                                            <div className="col-5" style={{ display: 'flex', justifyContent: 'flex-end' }}>
                                                                <Button onClick={() => { showList_(4) }} >{'Distributed Qualitative'} </Button>
                                                            </div>
                                                        </div>
                                                        <div className="col-4" style={{
                                                            display: 'flex',
                                                            alignItems: 'center',
                                                            justifyContent: 'flex-end'
                                                        }}>
                                                            <label
                                                                style={{
                                                                    marginRight: 2,
                                                                }}
                                                            >
                                                                Distributed Qualitative : <span style={{ color: '#005284' }}>{dfreqlistbk.filter((i) => { return i.config !== undefined }).length} / {dfreqlistbk.length}</span>
                                                            </label>
                                                        </div>

                                                    </div>
                                                    <div style={{ flexDirection: 'row', display: 'flex' }}>
                                                        <div className="col-12">


                                                            <div style={{ height: '45vh', overflow: 'scroll', border: '1px solid gray', borderRadius: '10px' }}>

                                                                {dfreqlist.map((df, j) => {

                                                                    return (
                                                                        <LazyView duration={0.5}>

                                                                            <div key={df.id} style={{ margin: 10, color: 'white' }} className=" flex align-items-center" >
                                                                                <div className="bg-navy br-1 p-1 flex col-12" >
                                                                                    <label htmlFor={df.id} style={{ cursor: 'pointer' }} className="col-11">{df.title} </label>
                                                                                    <i onClick={() => { df.config === undefined ? setDfConfig({ dfid: df.id, country: false, region: false, site: false }) : setDfConfig(df.config); setDfConfigDialog(true) }} className="pi pi-cog col-1 flex justify-content-end cur-pointer" style={{ fontSize: 20 }}></i>
                                                                                </div>

                                                                            </div>

                                                                        </LazyView>
                                                                    )
                                                                })}
                                                            </div>
                                                        </div>



                                                    </div>
                                                </div>
                                            }
                                            <div style={{ margin: 10, display: 'flex', justifyContent: 'flex-end' }} >


                                                <Button onClick={() => { saveAssignedDCF() }} >{editmode ? 'Update' : 'Save'} </Button>
                                            </div>
                                        </>
                                    }
                                </>
                                :
                                <div className=" col-12">No Clients found, reload page </div>
                            }

                        </>


                        :
                        <div className=" col-12">You have no rights to access this page</div>

                    }
                </div>
            </div>
            <Dialog
                visible={prevdialog}
                style={{
                    width: "450px",
                }}
                header={selectedform.title + " preview"}
                modal
                className="p-fluid"
                footer={prevDialogFooter}
                onHide={() => { setPrevDialog(false) }}
            >
                {renderPreview()}
            </Dialog>
            <Dialog
                visible={dupdpiddialog}
                style={{
                    width: "450px",
                }}
                header={"Duplicated ID List in " + dupdpid.msg}
                modal
                className="p-fluid"
                footer={dupdpidDialogFooter}
                onHide={() => { setDupDPIDDialog(false) }}
            >
                {dupdpid.data.map((id) => {
                    return (<div style={{ margin: 10 }}>
                        <label style={{ padding: 10 }}>{id}</label>
                    </div>)
                })

                }
            </Dialog>
            <Dialog
                visible={prevSListdialog}
                style={{
                    width: "60%",
                }}
                header={selectedlist.title}
                modal
                className="p-fluid"

                onHide={() => { setPrevSListDialog(false) }}
            >
                {renderListPreview()}
            </Dialog>
            <Dialog
                visible={prevSList2dialog}
                style={{
                    width: "60%",
                }}
                header={selectedlist.title}
                modal
                className="p-fluid"

                onHide={() => { setPrevSList2Dialog(false) }}
            >
                {renderListPreview_()}
            </Dialog>
            <Dialog
                visible={dfconfigdialog}
                style={{
                    width: "60%",
                }}
                header={'Qualitative Configuration'}
                modal
                className="p-fluid"

                onHide={() => { setDfConfigDialog(false) }}
            >
                {
                    <div>
                        <div key={'apex'} style={{ padding: 10 }} className="flex align-items-center">
                            <Checkbox inputId={'apex'} name="metric" value={dfconfig.apex} onChange={(e) => { setDfConfig((prev) => { return { ...prev, apex: e.checked } }) }} checked={dfconfig.apex === true} />
                            <label htmlFor={'apex'} style={{ cursor: 'pointer' }} className="ml-2">Apex </label>
                        </div>
                        <div key={'country'} style={{ padding: 10 }} className="flex align-items-center">
                            <Checkbox inputId={'country'} name="metric" value={dfconfig.country} onChange={(e) => { setDfConfig((prev) => { return { ...prev, country: e.checked } }) }} checked={dfconfig.country === true} />
                            <label htmlFor={'country'} style={{ cursor: 'pointer' }} className="ml-2">Country </label>
                        </div>
                        <div key={'region'} style={{ padding: 10 }} className="flex align-items-center">
                            <Checkbox inputId={'region'} name="metric" value={dfconfig.region} onChange={(e) => { setDfConfig((prev) => { return { ...prev, region: e.checked } }) }} checked={dfconfig.region === true} />
                            <label htmlFor={'region'} style={{ cursor: 'pointer' }} className="ml-2">Region </label>
                        </div>
                        <div key={'site'} style={{ padding: 10 }} className="flex align-items-center">
                            <Checkbox inputId={'site'} name="metric" value={dfconfig.site} onChange={(e) => { setDfConfig((prev) => { return { ...prev, site: e.checked } }) }} checked={dfconfig.site === true} />
                            <label htmlFor={'site'} style={{ cursor: 'pointer' }} className="ml-2">Site </label>
                        </div>

                        <div>
                            <Button label="Save & Exit" onClick={() => { saveDFConfig() }} />
                        </div>
                    </div>

                }

            </Dialog>
        </div>
    );
};

const comparisonFn = function (prevProps, nextProps) {
    return prevProps.location.pathname === nextProps.location.pathname;
};

export default React.memo(ClientDCFAssignment, comparisonFn);
