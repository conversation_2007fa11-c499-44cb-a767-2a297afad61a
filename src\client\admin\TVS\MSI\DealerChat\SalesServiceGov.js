import React from "react";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  ResponsiveContainer,
  CartesianGrid,
} from "recharts";

const GovernanceChart = ({ data }) => {
  // Define max values for each category
  const maxValues = {
    legal_requirement: 5,
    fair_business_practices: 5,
    data_privacy: 5,
  };

  const extractScores = (criteria = {}) => {
    const subCriteria = criteria?.governance?.subCriteria || [];

    const getScore = (name) => {
      const found = subCriteria.find((item) => item.name === name);
      return Math.max(0, found?.score || 0); // Prevent negative
    };

    // Helper function to clamp values between 0 and maxValue for graph display
    const clampForGraph = (value) => Math.max(0, value);
    const clampRemaining = (value, maxValue) => Math.max(0, Math.min(maxValue, maxValue - value));

    return [
      {
        category: "Legal Requirement",
        avgValue: getScore("legal_requirement"),
        avgValueForGraph: clampForGraph(getScore("legal_requirement")),
        remainingToMax: clampRemaining(
          getScore("legal_requirement"),
          maxValues.legal_requirement
        ),
        maxValue: maxValues.legal_requirement,
      },
      {
        category: "Fair Business Practices",
        avgValue: getScore("fair_business_practices"),
        avgValueForGraph: clampForGraph(getScore("fair_business_practices")),
        remainingToMax: clampRemaining(
          getScore("fair_business_practices"),
          maxValues.fair_business_practices
        ),
        maxValue: maxValues.fair_business_practices,
      },
      {
        category: "Data Privacy",
        avgValue: getScore("data_privacy"),
        avgValueForGraph: clampForGraph(getScore("data_privacy")),
        remainingToMax: clampRemaining(
          getScore("data_privacy"),
          maxValues.data_privacy
        ),
        maxValue: maxValues.data_privacy,
      },
    ];
  };

  const salesData = extractScores(data.sales_criteria);
  const serviceData = extractScores(data.service_criteria);

  // Function to determine Y-Axis domain based on values
  const getYAxisDomain = (data) => {
    const allValues = data.flatMap((item) => [
      item.avgValueForGraph,
      item.maxValue,
    ]);
    const maxValue = Math.max(...allValues);
    return [0, maxValue];
  };

  const salesYAxisDomain = getYAxisDomain(salesData);
  const serviceYAxisDomain = getYAxisDomain(serviceData);

  // Custom tick component for X-axis with two lines of text
  const CustomizedTick = ({ x, y, payload }) => {
    // Format special cases
    let displayText = payload.value;
    if (displayText === "legal_requirement") {
      displayText = "Legal Requirement";
    } else if (displayText === "fair_business_practices") {
      displayText = "Fair Business Practices";
    } else if (displayText === "data_privacy") {
      displayText = "Data Privacy";
    }

    // Split the text into two lines
    const words = displayText.split(' ');
    let firstLine, secondLine;

    if (words.length === 1) {
      // Single word - put on first line
      firstLine = words[0];
      secondLine = "";
    } else if (words.length === 2) {
      // Two words - one on each line
      firstLine = words[0];
      secondLine = words[1];
    } else {
      // More than two words - balance between lines
      const midpoint = Math.ceil(words.length / 2);
      firstLine = words.slice(0, midpoint).join(' ');
      secondLine = words.slice(midpoint).join(' ');
    }

    return (
      <g transform={`translate(${x},${y})`}>
        <text
          x={0}
          y={0}
          textAnchor="middle"
          fill="#666"
          fontSize={10}
        >
          <tspan x={0} dy="0.71em">{firstLine}</tspan>
          {secondLine && <tspan x={0} dy="1.2em">{secondLine}</tspan>}
        </text>
      </g>
    );
  };

  // Custom legend component
  const CustomLegend = (props) => {
    const { payload } = props;
    return (
      <ul
        style={{
          display: "flex",
          listStyleType: "none",
          justifyContent: "center",
          padding: 0,
        }}
      >
        {payload.map((entry, index) => (
          <li
            key={`item-${index}`}
            style={{
              color: entry.color,
              marginRight: "5px",
            }}
          >
            <span
              style={{
                color: entry.color,
                backgroundColor: entry.color,
                marginRight: 4,
                fontSize: "20px",
                width: "10px",
                height: "10px",
                borderRadius: "50%",
                display: "inline-block",
              }}
            ></span>
            <span style={{ color: "#555", fontSize: "14px" }}>
              {entry.value}
            </span>
          </li>
        ))}
      </ul>
    );
  };

  return (
    <div
      style={{
        display: "flex",
        justifyContent: "space-between",
        width: "100%",
        padding: "20px",
      }}
    >
      {/* Sales Chart */}
      <div style={{ width: "48%", height: 400 }}>
        <h3 style={{ textAlign: "center", marginBottom: 20, color: "#555" }}>
          Sales - Governance
        </h3>
        <ResponsiveContainer width="100%" height="100%">
          <BarChart
            data={salesData}
            barSize={60}
            margin={{ top: 20, right: 30, left: 20, bottom: 60 }}
          >
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis
              dataKey="category"
              tick={<CustomizedTick />}
              tickLine={true} // Ensures ticks are visible
              interval={0} // Forces all labels to be shown
              height={70} // Increase height to accommodate two lines of text
            />
            <YAxis domain={salesYAxisDomain} />
            <Tooltip
              formatter={(_, name, props) => {
                const { payload } = props;
                if (name === "Achieved") {
                  return [
                    `${payload.avgValue.toFixed(2)} (Max: ${payload.maxValue})`,
                    name,
                  ];
                }
                return [null, null];
              }}
            />
            <Legend content={CustomLegend} />
            <Bar
              dataKey="avgValueForGraph"
              stackId="score"
              fill="#4A90E2"
              name="Achieved"
              label={{
                position: "insideTop",
                fill: "#fff",
                formatter: (value) => value.toFixed(1),
              }}
            />
            <Bar
              dataKey="remainingToMax"
              stackId="score"
              fill="#AFCBFF"
              name="Maximum"
            />
          </BarChart>
        </ResponsiveContainer>
      </div>

      {/* Service Chart */}
      <div style={{ width: "48%", height: 400 }}>
        <h3 style={{ textAlign: "center", marginBottom: 20, color: "#555" }}>
          Service - Governance
        </h3>
        <ResponsiveContainer width="100%" height="100%">
          <BarChart
            data={serviceData}
            barSize={60}
            margin={{ top: 20, right: 30, left: 20, bottom: 60 }}
          >
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis
              dataKey="category"
              tick={<CustomizedTick />}
              tickLine={true} // Ensures ticks are visible
              interval={0} // Forces all labels to be shown
              height={70} // Increase height to accommodate two lines of text
            />
            <YAxis domain={serviceYAxisDomain} />
            <Tooltip
              formatter={(_, name, props) => {
                const { payload } = props;
                if (name === "Achieved") {
                  return [
                    `${payload.avgValue.toFixed(2)} (Max: ${payload.maxValue})`,
                    name,
                  ];
                }
                return [null, null];
              }}
            />
            <Legend content={CustomLegend} />
            <Bar
              dataKey="avgValueForGraph"
              stackId="score"
              fill="#4A90E2"
              name="Achieved"
              label={{
                position: "insideTop",
                fill: "#fff",
                formatter: (value) => value.toFixed(1),
              }}
            />
            <Bar
              dataKey="remainingToMax"
              stackId="score"
              fill="#AFCBFF"
              name="Maximum"
            />
          </BarChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
};

export default GovernanceChart;
