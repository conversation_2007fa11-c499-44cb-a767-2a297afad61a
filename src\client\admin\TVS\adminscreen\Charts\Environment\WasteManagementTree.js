import React, { useEffect, useRef, useState } from "react";
import * as d3 from "d3";
import { Checkbox, FormControlLabel, FormGroup } from "@mui/material";
import WasteManagementHierarchy from "./WasteManagmentHierarchy";

const WasteManagementTree = () => {
  const graphRef = useRef(null);

  const [visiblePaths, setVisiblePaths] = useState({
    Recycling: true,
    Composting: true,
    Landfill: true,
  });

  // Sample data for product end-of-life flow
  const data = {
    name: "Product End-of-Life Flow",
    children: [
      {
        name: "Recycling",
        children: [
          { name: "<PERSON>", value: 30 },
          { name: "Plastic", value: 40 },
        ],
      },
      {
        name: "Composting",
        children: [{ name: "Organic Waste", value: 50 }],
      },
      {
        name: "Landfill",
        children: [{ name: "Non-recyclable Waste", value: 60 }],
      },
    ],
  };

  const colors = d3.scaleOrdinal(["#cce7e8", "#b0e0b0", "#f2b9b9"]); // Subtle colors for different paths

  useEffect(() => {
    renderGraph();
  }, [visiblePaths]);

  const renderGraph = () => {
    const width = 800;
    const height = 400;
    const margin = { top: 50, right: 50, bottom: 50, left: 80 };

    d3.select(graphRef.current).selectAll("*").remove();

    const svg = d3
      .select(graphRef.current)
      .append("svg")
      .attr("width", width)
      .attr("height", height)
      .style("display", "block")
      .style("margin", "0 auto");

    // Filter data based on visible paths
    const filteredData = {
      ...data,
      children: data.children.filter((d) => visiblePaths[d.name]),
    };

    const treemap = d3
      .treemap()
      .size([
        width - margin.left - margin.right,
        height - margin.top - margin.bottom,
      ])
      .padding(1);

    const root = d3
      .hierarchy(filteredData)
      .sum((d) => d.value)
      .sort((a, b) => b.value - a.value);

    treemap(root);

    const cell = svg
      .selectAll("g")
      .data(root.leaves())
      .enter()
      .append("g")
      .attr(
        "transform",
        (d) => `translate(${d.x0 + margin.left},${d.y0 + margin.top})`
      );

    cell
      .append("rect")
      .attr("id", (d) => d.data.name)
      .attr("width", (d) => d.x1 - d.x0)
      .attr("height", (d) => d.y1 - d.y0)
      .attr("fill", (d) => colors(d.parent.data.name));

    cell
      .append("text")
      .attr("x", (d) => (d.x1 - d.x0) / 2)
      .attr("y", (d) => (d.y1 - d.y0) / 2)
      .attr("dy", ".35em")
      .attr("text-anchor", "middle")
      .text((d) => d.data.name)
      .attr("fill", "#000")
      .attr("font-size", "12px");

    cell
      .append("title")
      .text(
        (d) =>
          `${d.data.name}\nPath: ${d.parent.data.name}\nAmount: ${d.data.value}`
      );
  };

  const handleTogglePath = (path) => {
    setVisiblePaths((prev) => ({ ...prev, [path]: !prev[path] }));
  };

  return (
    <div>
      <div>
        <h3 style={{ margin: "0", fontSize: "18px" }}>
          Product End-of-Life Flow
        </h3>
      </div>
      <div ref={graphRef} style={{ textAlign: "center" }}></div>
      <div style={{ textAlign: "center", marginTop: "20px" }}>
        <FormGroup row style={{ justifyContent: "center" }}>
          {Object.keys(visiblePaths).map((path) => (
            <FormControlLabel
              key={path}
              control={
                <Checkbox
                  checked={visiblePaths[path]}
                  onChange={() => handleTogglePath(path)}
                  style={{
                    color: colors(path),
                  }}
                />
              }
              label={path}
            />
          ))}
        </FormGroup>
      </div>
    </div>
  );
};

export default WasteManagementTree;
