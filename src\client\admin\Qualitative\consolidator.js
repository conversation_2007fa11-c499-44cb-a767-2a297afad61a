import React, { useEffect, useState } from 'react';
import SectionBox from './SectionBox';
import APIServices from '../../../service/APIService';
import { API } from '../../../constants/api_url';
import { useSelector } from 'react-redux';





const initialFormData = (form, userResponse) => {
  return JSON.parse(form.data1).map((question) => {
    const responses = Object.entries(userResponse).map(([user, answers]) => {
      let rawValue = answers[question.name];
      let displayValue = rawValue;
      let comment = answers[`${question.name}_comments`];
      if ((question.type === "radio-group" || question.type === "select") && question.values) {
        const matched = question.values.find(v => v.value === rawValue);
        displayValue = matched ? matched.label : rawValue;
      }

      if (question.type === "checkbox-group" && Array.isArray(rawValue)) {
        displayValue = rawValue
          .map(val => {
            const matched = question.values.find(v => v.value === val);
            return matched ? matched.label : null;
          })
          .filter(Boolean);
      }

      if (rawValue !== undefined) {
        return {
          user,
          answer: displayValue,
          comment
        };
      }

      return null;
    }).filter(Boolean);

    return { ...question, response: responses };
  });
}

function ConsolidatorNew() {
  const [formData, setFormData] = useState([]);
  const [form, setForm] = useState({})
  const [useresponse,setUserReponse]= useState({})
  const [header, setHeader] = useState({})
  const login_data = useSelector((state) => state.user.userdetail)
  const admin_data = useSelector((state) => state.user.admindetail)
   const userList = useSelector(state => state.userlist.userList)
  

  useEffect(async () => {
    const { assignmentId, sectionId } = JSON.parse(sessionStorage.getItem('sectionId'))
    if (assignmentId && sectionId) {
      APIServices.post(API.GetAssignedQualitative_Consolidator, { userProfileId: admin_data.id, userId: login_data.id }).then(async (res) => {
        const userResonse = res?.data?.reporter?.filter(x => x.qSectionId === sectionId).map(x => x?.response || {}).reduce((a, b) => { return { ...a, ...b }, {} })
        const response = await APIServices.get(API.GetQualitativeConsolidate(assignmentId) +
          `?filter=${encodeURIComponent(JSON.stringify({ include: [{relation:'qTopic'},{relation:'qCategory'}, { relation: 'qSection', scope: { include: [{ relation: 'srf' }] } }] }))}`)
        let srfForm = response?.data?.qSection?.srf || {}

        setHeader({category:response?.data?.qCategory?.name || '',topic:response?.data?.qTopic?.name || '',section:response?.data?.qSection?.name || '' })
        if (srfForm) {
          let loc = JSON.parse(JSON.stringify(srfForm))
          if (loc.data1) {
            console.log(loc)
            setForm(loc)
            setUserReponse(userResonse)
            setFormData(initialFormData(loc, {...userResonse, ...(response?.data?.response || {})}))

          }

        }
      })

    }
    console.log(assignmentId, sectionId)
  }, [])
  const handleUpdateResponse = ( updatedResponses) => {
    // const updated = formData.map((item) =>
    //   item.name === name ? { ...item, response: updatedResponses } : item
    // );
    const { assignmentId, sectionId } = JSON.parse(sessionStorage.getItem('sectionId'))
    console.log(updatedResponses)
    APIServices.patch(API.SaveQualitativeConsolidateResponse(assignmentId),{response:updatedResponses}).then((res)=>{
   setFormData( initialFormData(form,{...useresponse,...res.data.response}))
    })
    
  };

  return (
    <div className="container my-4">
      <div className='col-12 grid mb-3'>
        <div className='col-4'>Category: <strong>{header.category}</strong> </div>
        <div className='col-4'>Topic: <strong>{header.topic}</strong></div>
        <div className='col-4'>Section: <strong>{header.section}</strong></div>
      </div>
      <h4 className="mb-3">Form #{form?.id} : {form?.title}</h4>
      {formData.map((item, index) => (
        <SectionBox
          key={index}
          data={item}
          onUpdateResponse={handleUpdateResponse}
          userlist={userList}
        />
      ))}
    </div>
  );
}

export default ConsolidatorNew;
