import React, { useEffect, useState } from "react";
import { Checkbox } from "@mui/material";
import Gauge<PERSON>hart from "react-gauge-chart";

const dummyData = {
  baseline: 1000, // baseline emissions in the baseline year
  currentEmissions: 600, // current emissions
};

const OverviewGHGProgress = () => {
  const [visibleSeries, setVisibleSeries] = useState({
    scope1: true,
    scope2: true,
    scope3: true,
  });

  const progress =
    ((dummyData.baseline - dummyData.currentEmissions) / dummyData.baseline) *
    100;

  return (
    <div style={{ fontFamily: "Lato", padding: "10px" }}>
      <div
        style={{
          fontSize: "14px",
          fontWeight: 700,
          lineHeight: "19.2px",
          textAlign: "left",
          marginBottom: "5px",
        }}
      >
        GHG Reduction Progress
        <div style={{ fontWeight: 200, fontSize: "12px" }}>
          Track the progress of GHG reduction relative to baseline year
          emissions.
        </div>
      </div>

      {/* Gauge Chart */}
      <div
        style={{
          textAlign: "center",
          marginBottom: "20px",
          display: "flex",
          justifyContent: "center",
        }}
      >
        <GaugeChart
          id="ghg-progress-gauge"
          nrOfLevels={30}
          arcsLength={[0.5, 0.3, 0.2]} // Define the color zones
          colors={["#e74c3c", "#f39c12", "#16a085"]} // Red, Yellow, Green
          percent={progress / 100} // Progress as a percentage (0-1 scale)
          needleColor="#000000"
          arcPadding={0.02}
          style={{ width: "800px", height: "350px" }} // Reduced size for the gauge chart
        />
      </div>

      {/* Legends */}
      {/* Optionally, you can include the checkboxes here, but they're commented out to save space */}
      {/* <div style={{ textAlign: "center", marginTop: "10px" }}>
        <div style={{ display: "inline-block", marginRight: "15px" }}>
          <Checkbox
            checked={visibleSeries["scope1"]}
            onChange={() => handleCheckboxChange("scope1")}
            style={{
              color: "#e38322",
              marginRight: 4,
              fontSize: "18px",
            }}
          />
          <span style={{ color: "#555", fontSize: "12px" }}>Scope 1</span>
        </div>
        <div style={{ display: "inline-block", marginRight: "15px" }}>
          <Checkbox
            checked={visibleSeries["scope2"]}
            onChange={() => handleCheckboxChange("scope2")}
            style={{
              color: "#f2c79c",
              marginRight: 4,
              fontSize: "18px",
            }}
          />
          <span style={{ color: "#555", fontSize: "12px" }}>Scope 2</span>
        </div>
        <div style={{ display: "inline-block" }}>
          <Checkbox
            checked={visibleSeries["scope3"]}
            onChange={() => handleCheckboxChange("scope3")}
            style={{
              color: "#166963",
              marginRight: 4,
              fontSize: "18px",
            }}
          />
          <span style={{ color: "#555", fontSize: "12px" }}>Scope 3</span>
        </div>
      </div> */}
    </div>
  );
};

export default OverviewGHGProgress;
