import React, { useState } from 'react';
import { Panel } from 'primereact/panel';
import { ScrollPanel } from 'primereact/scrollpanel';
import { Accordion, AccordionTab } from 'primereact/accordion';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { Button } from 'primereact/button';
import * as XLSX from 'xlsx';
import { saveAs } from 'file-saver';
import { DateTime } from 'luxon';
import { API } from '../../../../constants/api_url';

const DealerSubmissionView = ({ excelData, dealerInfo }) => {

    console.log(excelData, dealerInfo, ' Check here');

    const [expandedRows, setExpandedRows] = useState(null);

    const groupedData = {
        sales: excelData?.filter((x) => x.type === 'checklist-group' && x.section === 1) || [],
        service: excelData?.filter((x) => x.type === 'checklist-group' && x.section === 2) || [],
    };

    // Function to get section scores
    const getSectionScores = () => {
        const scores = {};

        Object.entries(groupedData).forEach(([sectionName, sectionData]) => {
            let totalScore = 0;
            let totalMaxScore = 0;

            sectionData.forEach(group => {
                try {
                    if (group.score) {
                        // Check if score is already a number
                        if (typeof group.score === 'number') {
                            totalScore += group.score;
                            totalMaxScore += 100; // Assuming max score is 100 if not specified
                        }
                        // Check if score is a string with format "score/maxScore"
                        else if (typeof group.score === 'string' && group.score.includes('/')) {
                            const parts = group.score.split('/');
                            if (parts.length === 2) {
                                const score = parseFloat(parts[0] || 0);
                                const maxScore = parseFloat(parts[1] || 0);

                                if (!isNaN(score)) totalScore += score;
                                if (!isNaN(maxScore)) totalMaxScore += maxScore;
                            }
                        }
                        // If score is just a string number
                        else if (typeof group.score === 'string') {
                            const score = parseFloat(group.score);
                            if (!isNaN(score)) {
                                totalScore += score;
                                totalMaxScore += 100; // Assuming max score is 100 if not specified
                            }
                        }
                    }

                    // Also check if there are questions with scores
                    if (group.questions && Array.isArray(group.questions)) {
                        group.questions.forEach(question => {
                            if (question.options) {
                                const checkedOption = question.options.find(opt => opt.checked === 1);
                                if (checkedOption && checkedOption.score) {
                                    const score = parseFloat(checkedOption.score);
                                    if (!isNaN(score)) {
                                        totalScore += score;
                                        totalMaxScore += 1; // Assuming each question has a max score of 1
                                    }
                                }
                            }
                        });
                    }
                } catch (error) {
                    console.error('Error processing group score:', error, group);
                }
            });

            scores[sectionName] = {
                score: totalScore,
                maxScore: totalMaxScore || 1, // Avoid division by zero
                percentage: totalMaxScore > 0 ? (totalScore / totalMaxScore * 100).toFixed(2) : 0
            };
        });

        return scores;
    };

    // Function to export data to Excel
    const exportToExcel = () => {
        if (!excelData || excelData.length === 0) {
            alert('No data to export');
            return;
        }

        // Create workbook
        const wb = XLSX.utils.book_new();

        // Get section scores
        const sectionScores = getSectionScores();

        // Create summary sheet with dealer info and section scores
        const summarySheetData = [];

        // Add dealer meta information if available
        if (dealerInfo) {
            summarySheetData.push(['Dealer Information', '']);
            summarySheetData.push(['Calibration ID', dealerInfo.calibrationId || 'NA']);
            summarySheetData.push(['Dealer Name', dealerInfo.dealerName || 'NA']);
            summarySheetData.push(['Location', dealerInfo.location || 'NA']);
            summarySheetData.push(['Zone', dealerInfo.zone || 'NA']);
            summarySheetData.push(['Category', dealerInfo.category || 'NA']);
            summarySheetData.push(['Self-assessment Month', dealerInfo.selfAssessmentMonth || 'NA']);
            summarySheetData.push(['Self-assessment Score', dealerInfo.selfAssessmentScore || 'NA']);
            summarySheetData.push(['MSI Calibration Score', dealerInfo.calibrationScore || 'NA']);
            summarySheetData.push(['MSI Rating', dealerInfo.msiRating || 'NA']);
            summarySheetData.push(['Calibration Submission Date', dealerInfo.calibrationSubmissionDate || 'NA']);
            summarySheetData.push(['Calibration Team Member', dealerInfo.calibrationTeamMember || 'NA']);
            summarySheetData.push(['Status of Actions', dealerInfo.statusOfActions || 'NA']);
            summarySheetData.push(['', '']);
        }

        // Add section scores

        summarySheetData.push(['', '', '']);

        // Add findings section
        summarySheetData.push(['Findings', '', '']);
        summarySheetData.push(['Section', 'Group', 'Question', 'Response', 'Score', 'Remarks', 'Action To Be Taken', 'Person Responsible', 'Due Date']);

        // Add findings data
        Object.entries(groupedData).forEach(([sectionName, sectionData]) => {
            sectionData.forEach(group => {
                const groupName = group.label.replace(/(<([^>]+)>)/gi, '');

                group.questions?.filter(q => q.label !== 'checkpoints').forEach(question => {
                    const response = question.options
                        ? question.options.find(opt => opt.checked)?.label || '-'
                        : 'No Option';

                    const score = question.options
                        ? question.options.find(opt => opt.checked === 1)?.score ?? '-'
                        : '-';

                    // Get remarks and actions data
                    const remarks = question.remarks || '-';
                    const action = question.actions || {};
                    const actionToBeTaken = action.actionToBeTaken || '-';
                    const personResponsible = action.personResponsible || '-';
                    const dueDate = action.dueDate ? new Date(action.dueDate).toLocaleDateString() : '-';

                    summarySheetData.push([
                        sectionName.charAt(0).toUpperCase() + sectionName.slice(1), // Capitalize section name
                        groupName,
                        question.label,
                        response,
                        score,
                        remarks,
                        actionToBeTaken,
                        personResponsible,
                        dueDate
                    ]);
                });
            });
        });

        // Create summary sheet
        const summaryWs = XLSX.utils.aoa_to_sheet(summarySheetData);

        // Set column widths for better readability
        const summaryColWidths = [
            { wch: 20 }, // Column A - Section
            { wch: 30 }, // Column B - Group
            { wch: 40 }, // Column C - Question
            { wch: 30 }, // Column D - Response
            { wch: 10 }, // Column E - Score
            { wch: 30 }, // Column F - Remarks
            { wch: 30 }, // Column G - Action To Be Taken
            { wch: 20 }, // Column H - Person Responsible
            { wch: 15 }  // Column I - Due Date
        ];
        summaryWs['!cols'] = summaryColWidths;

        XLSX.utils.book_append_sheet(wb, summaryWs, 'Summary');

        // Create separate sheets for each section
        let submissionCounter = 1; // Counter for non-Authorized Main Dealer sheets
        Object.entries(groupedData).forEach(([sectionName, sectionData]) => {
            if (sectionData.length > 0) {
                const sectionSheetData = [];

                // Add section score at the top

                sectionSheetData.push(['', '', '']);

                // Add headers
                sectionSheetData.push(['Group', 'Question', 'Response', 'Score', 'Remarks', 'Action To Be Taken', 'Person Responsible', 'Due Date']);

                // Add data rows
                sectionData.forEach(group => {
                    const groupName = group.label.replace(/(<([^>]+)>)/gi, '');

                    // Add group score
                    if (group.score) {
                        let scoreDisplay = '';
                        try {
                            if (typeof group.score === 'number') {
                                scoreDisplay = `Score: ${group.score}`;
                            } else if (typeof group.score === 'string') {
                                scoreDisplay = `Score: ${group.score}`;
                            } else {
                                scoreDisplay = `Score: ${JSON.stringify(group.score)}`;
                            }
                        } catch (error) {
                            console.error('Error formatting group score:', error);
                            scoreDisplay = 'Score: Error';
                        }
                        sectionSheetData.push([groupName, scoreDisplay, '', '', '', '', '', '']);
                    } else {
                        sectionSheetData.push([groupName, '', '', '', '', '', '', '']);
                    }

                    group.questions?.filter(q => q.label !== 'checkpoints').forEach(question => {
                        const response = question.options
                            ? question.options.find(opt => opt.checked)?.label || '-'
                            : 'No Option';

                        const score = question.options
                            ? question.options.find(opt => opt.checked === 1)?.score ?? '-'
                            : '-';

                        // Get remarks and actions data
                        const remarks = question.remarks || '-';
                        const action = question.actions || {};
                        const actionToBeTaken = action.actionToBeTaken || '-';
                        const personResponsible = action.personResponsible || '-';
                        const dueDate = action.dueDate ? new Date(action.dueDate).toLocaleDateString() : '-';

                        sectionSheetData.push([
                            '',  // Leave group name blank for questions
                            question.label,
                            response,
                            score,
                            remarks,
                            actionToBeTaken,
                            personResponsible,
                            dueDate
                        ]);
                    });

                    // Add empty row after each group
                    sectionSheetData.push(['', '', '', '', '', '', '', '']);
                });

                // Create section sheet
                const sectionWs = XLSX.utils.aoa_to_sheet(sectionSheetData);

                // Set column widths for better readability
                const sectionColWidths = [
                    { wch: 30 }, // Column A - Group
                    { wch: 40 }, // Column B - Question
                    { wch: 30 }, // Column C - Response
                    { wch: 10 }, // Column D - Score
                    { wch: 30 }, // Column E - Remarks
                    { wch: 30 }, // Column F - Action To Be Taken
                    { wch: 20 }, // Column G - Person Responsible
                    { wch: 15 }  // Column H - Due Date
                ];
                sectionWs['!cols'] = sectionColWidths;

                // Set sheet name based on dealer category
                let sheetName;
                if (dealerInfo && dealerInfo.category === "Authorized Main Dealer") {
                    sheetName = sectionName.charAt(0).toUpperCase() + sectionName.slice(1); // "Sales" or "Service"
                } else {
                    sheetName = `Submissions ${submissionCounter}`;
                    submissionCounter++; // Increment counter for next sheet
                }
                XLSX.utils.book_append_sheet(wb, sectionWs, sheetName);
            }
        });

        // Generate Excel file
        const excelBuffer = XLSX.write(wb, { bookType: 'xlsx', type: 'array' });
        const dataBlob = new Blob([excelBuffer], { type: 'application/octet-stream' });

        // Use dealer name in filename if available
        const fileName = dealerInfo && dealerInfo.dealerName
            ? `MSI_Submission_${dealerInfo.dealerName.replace(/[^a-zA-Z0-9]/g, '_')}_${DateTime.now().toFormat('yyyyMMdd_HHmmss')}.xlsx`
            : `MSI_Submission_${DateTime.now().toFormat('yyyyMMdd_HHmmss')}.xlsx`;

        saveAs(dataBlob, fileName);
    };

    const renderActions = (rowData) => {
        const action = rowData.actions;
        console.log(action, 'Actions')
        const remarks = rowData.remarks;

        const uploads = rowData.uploads;

        if (!action && !remarks && !uploads) return null;

        return (
            <div style={{ padding: '1rem' }}>
                {uploads && uploads.length > 0 && (
                    <>
                        <p><strong>Attachments:</strong></p>
                        <div className="row">
                            {uploads.map((file, i) => (
                                <div key={i} className="col-md-3 mb-2">
                                    <a href={API.Docs + file.value} target="_blank" rel="noopener noreferrer">
                                        {file.value || `File ${i + 1}`}
                                    </a>
                                </div>
                            ))}
                        </div>
                    </>)
                }
                {remarks && <p><strong>Remarks:</strong> {remarks}</p>}
                {action &&
                    <>
                        <p><strong>Action To Be Taken:</strong> {action.actionToBeTaken || '-'}</p>
                        <p><strong>Person Responsible:</strong> {action.personResponsible || '-'}</p>
                        <p><strong>Due Date:</strong> {action.dueDate ? new Date(action.dueDate).toLocaleDateString() : '-'}</p>
                        <p><strong>Attachments:</strong> {action.evidence?.length > 0
                            ? action.evidence.map((file, i) => (
                                <div key={i}>
                                    <a href={API.Docs + file.value} target="_blank" rel="noopener noreferrer">
                                        {file.value || `File ${i + 1}`}
                                    </a>
                                </div>
                            ))
                            : 'None'}
                        </p>
                    </>
                }
            </div>
        );
    };


    const renderSection = (title, data) => (
        <>
            {dealerInfo && dealerInfo.category === "Authorized Main Dealer" && (
                <h3 style={{ marginTop: '1rem' }}>{title}</h3>
            )}
            <Accordion>
                {data.map((item, index) => (
                    <AccordionTab
                        key={index}
                        header={`${item.label.replace(/(<([^>]+)>)/gi, '')} - ${item.score}`}
                    >
                        <DataTable
                            value={item?.questions?.filter((q) => q.label !== 'checkpoints') || []}
                            expandedRows={expandedRows}
                            onRowToggle={(e) => setExpandedRows(e.data)}
                            rowExpansionTemplate={renderActions}
                            dataKey="name"
                            rowClassName={(rowData) => {
                                // Highlight row if it has actions
                                const hasActions = rowData.actions && (
                                    rowData.actions.actionToBeTaken ||
                                    rowData.actions.personResponsible ||
                                    rowData.actions.dueDate
                                );
                                return hasActions ? 'row-with-actions' : '';
                            }}
                        >
                            <Column expander style={{ width: '3em' }} />
                            <Column field="label" header="Question" style={{ width: '40%' }} />
                            <Column
                                header="Response"
                                body={(rowData) =>
                                    rowData?.options
                                        ? rowData.options.find((opt) => opt.checked)?.label || '-'
                                        : 'No Option'
                                }
                                style={{ width: '20%' }}
                            />
                            <Column
                                field="score"
                                header="Score"
                                body={(rowData) => {
                                    const checkedOption = rowData.options?.find((opt) => opt.checked === 1);
                                    return checkedOption?.score ?? '-';
                                }}
                                style={{ width: '10%' }}
                            />
                            <Column
                                header="Attachments"
                                body={(rowData) => {
                                    const uploads = rowData.uploads;
                                    const actionEvidence = rowData.actions?.evidence;

                                    let totalAttachments = 0;
                                    if (uploads && uploads.length > 0) {
                                        totalAttachments += uploads.length;
                                    }
                                    if (actionEvidence && actionEvidence.length > 0) {
                                        totalAttachments += actionEvidence.length;
                                    }

                                    if (totalAttachments > 0) {
                                        return (
                                            <div style={{ display: 'flex', alignItems: 'center', gap: '5px' }}>
                                                <i className="pi pi-paperclip" style={{ color: '#007ad9' }}></i>
                                                <span style={{ color: '#007ad9', fontWeight: 'bold' }}>
                                                    {totalAttachments}
                                                </span>
                                            </div>
                                        );
                                    }
                                    return '-';
                                }}
                                style={{ width: '15%', textAlign: 'center' }}
                            />
                        </DataTable>

                    </AccordionTab>
                ))}
            </Accordion>
        </>
    );

    // Custom header with export button
    const panelHeader = (
        <div className="flex justify-content-between align-items-center">
            <span>Submission Details</span>
            <Button
                label="Export to Excel"
                icon="pi pi-file-excel"
                className="mx-3 p-button-success"
                onClick={exportToExcel}
            />
        </div>
    );

    return (
        <>
            <style>
                {`
                    .row-with-actions {
                        background-color: #fff9c4 !important;
                    }
                    .row-with-actions:hover {
                        background-color: #fff3a0 !important;
                    }
                    .p-datatable .p-datatable-tbody > tr.row-with-actions > td {
                        background-color: #fff9c4 !important;
                    }
                    .p-datatable .p-datatable-tbody > tr.row-with-actions:hover > td {
                        background-color: #fff3a0 !important;
                    }
                `}
            </style>
            <Panel header={panelHeader} toggleable>
                <ScrollPanel style={{ width: '100%', height: '400px' }}>
                    <div style={{ whiteSpace: 'pre-wrap', wordBreak: 'break-word' }}>
                        {groupedData.sales.length > 0 && renderSection('Sales', groupedData.sales)}
                        {groupedData.service.length > 0 && renderSection('Service', groupedData.service)}
                    </div>
                </ScrollPanel>
            </Panel>
        </>
    );
};

export default DealerSubmissionView;
