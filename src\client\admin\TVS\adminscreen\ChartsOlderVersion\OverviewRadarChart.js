import React, { useEffect, useRef, useState } from "react";
import * as d3 from "d3";
import { Checkbox } from "@mui/material";

const dummyData = {
  name: "Total Emissions",
  children: [
    {
      name: "Scope 1",
      children: [
        { name: "Stationary", value: 30 },
        { name: "Mobile", value: 20 },
        { name: "Fugitive", value: 10 },
      ],
    },
    {
      name: "Scope 2",
      value: 40,
    },
    {
      name: "Scope 3",
      children: [
        { name: "Category 1", value: 50 },
        { name: "Category 11", value: 40 },
        { name: "Category 12", value: 60 },
      ],
    },
  ],
};

const OverviewRadarChart = () => {
  const [visibleSeries, setVisibleSeries] = useState({
    scope1: true,
    scope2: true,
    scope3: true,
  });
  const chartRef = useRef(null);

  useEffect(() => {
    renderRadarChart();
  }, [visibleSeries]);

  const renderRadarChart = () => {
    const width = 600;
    const height = 500;
    const margin = { top: 40, right: 40, bottom: 40, left: 40 };
    const chartWidth = width - margin.left - margin.right;
    const chartHeight = height - margin.top - margin.bottom;

    // Clear any previous SVG
    d3.select(chartRef.current).select("svg").remove();

    const svg = d3
      .select(chartRef.current)
      .append("svg")
      .attr("width", width)
      .attr("height", height)
      .append("g")
      .attr("transform", `translate(${chartWidth / 2},${chartHeight / 2})`);

    const color = d3
      .scaleOrdinal()
      .domain(["Scope 1", "Scope 2", "Scope 3"])
      .range(["#e74c3c", "#f39c12", "#16a085"]);

    const radarData = [
      { name: "Scope 1", value: getValueForScope("scope1") },
      { name: "Scope 2", value: getValueForScope("scope2") },
      { name: "Scope 3", value: getValueForScope("scope3") },
    ];

    const maxValue = d3.max(radarData, (d) => d.value);
    const radius = Math.min(chartWidth, chartHeight) / 2 - 40; // Adjust radius to fit the chart within the available space

    const angles = d3
      .scaleBand()
      .domain(radarData.map((d) => d.name))
      .range([0, 2 * Math.PI]);

    const line = d3
      .lineRadial()
      .radius((d) => (d.value / maxValue) * radius)
      .angle((d) => angles(d.name));

    // Create radial grid lines
    const grid = svg.append("g").attr("class", "grid");
    grid
      .selectAll(".grid-line")
      .data(d3.range(1, 6))
      .enter()
      .append("circle")
      .attr("class", "grid-line")
      .attr("r", (d) => (radius / 5) * d)
      .style("fill", "none")
      .style("stroke", "#ddd")
      .style("stroke-width", 1)
      .style("stroke-dasharray", "4 2");

    // Create axis lines
    const axis = svg.append("g").attr("class", "axis");
    axis
      .selectAll(".axis-line")
      .data(radarData)
      .enter()
      .append("line")
      .attr("class", "axis-line")
      .attr("x1", 0)
      .attr("y1", 0)
      .attr("x2", (d) => radius * Math.cos(angles(d.name) - Math.PI / 2))
      .attr("y2", (d) => radius * Math.sin(angles(d.name) - Math.PI / 2))
      .style("stroke", "#555")
      .style("stroke-width", 2);

    // Create axis labels
    svg
      .selectAll(".axis-label")
      .data(radarData)
      .enter()
      .append("text")
      .attr("class", "axis-label")
      .attr("x", (d) => (radius + 20) * Math.cos(angles(d.name) - Math.PI / 2))
      .attr("y", (d) => (radius + 20) * Math.sin(angles(d.name) - Math.PI / 2))
      .style("text-anchor", "middle")
      .style("font-size", "14px")
      .text((d) => d.name);

    // Create the radar chart area (polygon)
    svg
      .selectAll(".area")
      .data([radarData])
      .join("path")
      .attr("class", "area")
      .attr("d", line)
      .style("fill", (d) => color(d[0].name))
      .style("fill-opacity", 0.4)
      .style("stroke", (d) => color(d[0].name))
      .style("stroke-width", 2);

    function getValueForScope(scope) {
      let total = 0;
      if (scope === "scope1" && visibleSeries.scope1) {
        dummyData.children[0].children.forEach((item) => {
          total += item.value;
        });
      }
      if (scope === "scope2" && visibleSeries.scope2) {
        total += dummyData.children[1].value;
      }
      if (scope === "scope3" && visibleSeries.scope3) {
        dummyData.children[2].children.forEach((item) => {
          total += item.value;
        });
      }
      return total;
    }
  };

  const handleCheckboxChange = (key) => {
    setVisibleSeries((prevState) => ({
      ...prevState,
      [key]: !prevState[key],
    }));
  };

  return (
    <div>
      <div
        style={{
          fontFamily: "Lato",
          fontSize: "16px",
          fontWeight: 700,
          lineHeight: "19.2px",
          textAlign: "left",
          marginBottom: "10px",
        }}
      >
        Radar Chart for Emissions
        <div style={{ fontWeight: 200, fontSize: "14px" }}>
          View emissions from Scope 1, Scope 2, and Scope 3 relative to
          reduction targets.
        </div>
      </div>
      <div ref={chartRef} style={{ textAlign: "center" }} />

      {/* Legends */}
      <div style={{ textAlign: "center", marginTop: "5px" }}>
        <div style={{ display: "inline-block", marginRight: "20px" }}>
          <Checkbox
            checked={visibleSeries["scope1"]}
            onChange={() => handleCheckboxChange("scope1")}
            style={{
              color: "#e74c3c",
              marginRight: 4,
              fontSize: "20px",
            }}
          />
          <span style={{ color: "#555", fontSize: "14px" }}>Scope 1</span>
        </div>
        <div style={{ display: "inline-block", marginRight: "20px" }}>
          <Checkbox
            checked={visibleSeries["scope2"]}
            onChange={() => handleCheckboxChange("scope2")}
            style={{
              color: "#f39c12",
              marginRight: 4,
              fontSize: "20px",
            }}
          />
          <span style={{ color: "#555", fontSize: "14px" }}>Scope 2</span>
        </div>
        <div style={{ display: "inline-block", marginRight: "20px" }}>
          <Checkbox
            checked={visibleSeries["scope3"]}
            onChange={() => handleCheckboxChange("scope3")}
            style={{
              color: "#16a085",
              marginRight: 4,
              fontSize: "20px",
            }}
          />
          <span style={{ color: "#555", fontSize: "14px" }}>Scope 3</span>
        </div>
      </div>
    </div>
  );
};

export default OverviewRadarChart;
