import React, { useEffect, useRef, useState } from "react";
import * as d3 from "d3";
import { Checkbox } from "@mui/material";

// Sample data for Gender and Age Distribution
const genderData = [
  { name: "Male", value: 10 },
  { name: "Female", value: 5 },
  { name: "Other", value: 2 },
];

const ageData = [
  { name: "Under 30", value: 3 },
  { name: "30-50", value: 8 },
  { name: "50+", value: 6 },
];

const DiversityPieChart = () => {
  const chartRef = useRef(null);
  const [selectedChart, setSelectedChart] = useState("gender");

  useEffect(() => {
    renderPieChart();
  }, [selectedChart]);

  const renderPieChart = () => {
    const width = 400;
    const height = 400;
    const margin = { top: 20, right: 30, bottom: 40, left: 40 };
    const radius = Math.min(width, height) / 2 - margin.top;

    // Clear any existing SVG
    d3.select(chartRef.current).select("svg").remove();

    const svg = d3
      .select(chartRef.current)
      .append("svg")
      .attr("width", width)
      .attr("height", height)
      .append("g")
      .attr("transform", `translate(${width / 2},${height / 2})`);

    // Choose the data to display based on selected chart type (Gender or Age)
    const data = selectedChart === "gender" ? genderData : ageData;

    // Set up color scale with more subtle shades
    const color = d3
      .scaleOrdinal()
      .domain(data.map((d) => d.name))
      .range([
        "#A8DADC", // Light blue for Male
        "#F1FAED", // Very light pink for Female
        "#457B9D", // Subtle teal for Other
      ]);

    // Create pie chart
    const pie = d3
      .pie()
      .value((d) => d.value)
      .sort(null);
    const arc = d3.arc().outerRadius(radius).innerRadius(0);

    const arcs = svg
      .selectAll("arc")
      .data(pie(data))
      .enter()
      .append("g")
      .attr("class", "arc");

    arcs
      .append("path")
      .attr("d", arc)
      .attr("fill", (d) => color(d.data.name))
      .style("transition", "all 0.3s ease");

    // Add labels
    arcs
      .append("text")
      .attr("transform", (d) => "translate(" + arc.centroid(d) + ")")
      .attr("dy", ".35em")
      .style("text-anchor", "middle")
      .style("font-size", "14px")
      .style("fill", "#333") // Darker text for better contrast
      .text((d) => `${d.data.name}: ${d.data.value}`);

    // Add a title to the pie chart
    svg
      .append("text")
      .attr("x", 0)
      .attr("y", -radius - 20)
      .style("text-anchor", "middle")
      .style("font-size", "16px")
      .text(
        selectedChart === "gender" ? "Gender Distribution" : "Age Distribution"
      );
  };

  const handleChangeChart = (chartType) => {
    setSelectedChart(chartType);
  };

  return (
    <div style={{ textAlign: "center" }}>
      <div style={{ fontFamily: "Lato", fontSize: "16px", fontWeight: 700 }}>
        Diversity Pie Chart
      </div>
      <div style={{ fontWeight: 200, fontSize: "14px", marginBottom: "20px" }}>
        Display the distribution of Board members by Gender or Age.
      </div>

      <div ref={chartRef}></div>

      {/* Buttons to toggle between Gender and Age charts */}
      <div style={{ marginTop: "20px" }}>
        <button
          onClick={() => handleChangeChart("gender")}
          style={{
            padding: "10px 20px",
            marginRight: "10px",
            fontSize: "14px",
            backgroundColor: "#A8DADC", // Light blue for gender chart button
            color: "#fff",
            border: "none",
            cursor: "pointer",
          }}
        >
          Gender Distribution
        </button>
        <button
          onClick={() => handleChangeChart("age")}
          style={{
            padding: "10px 20px",
            fontSize: "14px",
            backgroundColor: "#F1FAEE", // Light pink for age chart button
            color: "#333",
            border: "none",
            cursor: "pointer",
          }}
        >
          Age Distribution
        </button>
      </div>
    </div>
  );
};

export default DiversityPieChart;
