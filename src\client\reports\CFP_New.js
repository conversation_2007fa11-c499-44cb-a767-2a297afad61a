import React, { useState, useEffect } from "react";
import "../reports/toc_style.css";
import { useHeadsObserver } from "./hooks";
import * as XLSX from "xlsx";
import useForceUpdate from "use-force-update";
import $ from "jquery";
import Axios from "axios";
import { API } from "../../constants/api_url";
import { useSelector } from "react-redux";
import { Button } from "primereact/button";
import * as XlsxPopulate from "xlsx-populate/browser/xlsx-populate";
import { saveAs } from 'file-saver';
import moment from "moment";
import pdfMake from 'pdfmake/build/pdfmake';
import pdfFonts from 'pdfmake/build/vfs_fonts';
import { Dropdown } from "primereact/dropdown";
import APIServices from "../../service/APIService";
import { hardcoded } from "../constants/hardcodedid";
import { Line } from "recharts";
import { layouts } from "chart.js";
import { getRPTextFormat, groupArrayByKeys } from "../../components/BGHF/helper";
// import { fontWeight } from "html2canvas/dist/types/css/property-descriptors/font-weight";



// pdfMake.vfs = pdfFonts.pdfMake.vfs;
// pdfMake.fonts = {
//     Roboto: {
//         normal: 'https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.66/fonts/Roboto/Roboto-Regular.ttf',
//         bold: 'https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.66/fonts/Roboto/Roboto-Medium.ttf',
//         italics: 'https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.66/fonts/Roboto/Roboto-Italic.ttf',
//         bolditalics: 'https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.66/fonts/Roboto/Roboto-MediumItalic.ttf'
//       }
// }
const { DateTime } = require('luxon');

window.jQuery = $;
window.$ = $;
const dcf_id = [11, 10, 15, 257, 16, 36, 292, 293, 310, 311]
const CarbonFootPrintingNew = () => {
    const [headings, setHeadings] = useState([]);
    const { activeId } = useHeadsObserver();
    const [tableData, setTableData] = useState([]);
    const [workbook, setWorkbook] = useState(null);
    const [firstSheet, setFirstSheet] = useState(null);
    const [year, setYear] = useState(2022);
    const [rfData, setRFData] = useState({});
    const selector = useSelector((state) => state.user.admindetail);
    const emissionFactor = useSelector((state) => state.emissionfactor.emissionFactor)
    const locationList = useSelector(state => state.sitelist.locationList)
    const siteList = useSelector((state) => state.sitelist.siteList)
    const rflibrary = useSelector((state) => state.library.rf)
    const [apief, setApiEF] = useState([])
    const [subcat, setSubCat] = useState({ one: [], two: [], three: [], four: [] })
    const { fymonth } = useSelector((state) => state.user.fyStartMonth);
    const [dcfass, setDCFAss] = useState([])
    const [dcflist, setDcfList] = useState([])
    const [response, setResponse] = useState([])
    const [report, setReport] = useState([])
    const [reportEF, setReportEF] = useState([])
    const [dpreport, setDpReport] = useState([])
    let months_ = ["JAN", "FEB", "MAR", "APR", "MAY", "JUN", "JUL", "AUG", "SEP", "OCT", "NOV", "DEC"];
    const forceUpdate = useForceUpdate();
    function formatSubscript__(inputString, findArray, replaceArray) {
        let result = [];
        let currentIndex = 0;

        for (let i = 0; i < findArray.length; i++) {
            const findText = findArray[i];
            const replaceValue = replaceArray[i];
            const index = inputString.toLowerCase().indexOf(findText, currentIndex);

            if (index === -1) {
                // If the findText is not found, add the remaining text and break
                result.push(inputString.substring(currentIndex));
                break;
            }

            // Add the text before the found substring
            result.push(inputString.substring(currentIndex, index));

            // Add the subscripted replaceValue as an object
            result.push(...getResult(findText, replaceValue));

            // Update the currentIndex to continue searching
            currentIndex = index + findText.length;
        }

        // Add any remaining text after the last replacement
        if (currentIndex < inputString.length) {
            result.push(inputString.substring(currentIndex));
        }

        // Filter out empty strings
        result = result.filter((item) => item !== '');

        return result;
    }

    function formatSubscript(inputString, findArray, replaceArray) {


        return inputString;
    }
    function getResult(str, str2) {
        let arr = str.split(str2.toString())
        arr.splice(1, 0, { text: str2, fontSize: 7, baseline: -5 })
        return arr
    }
    function ulOrOlToPdfMake(element) {
        const result = [];
        const listItems = Array.from(element.querySelectorAll('li'));

        listItems.forEach((li) => {
            const text = li.textContent.trim();
            if (text !== '') {
                result.push({ text });
            }
        });

        return result;
    }
    // pdf export
    async function exportTable2Excel(type) {
        let initialData = [

            {

                alignment: 'left',
                image: 'page1', width: 350,
                margin: [0, 0, 0, 20]




            },
            {
                alignment: 'justify', margin: [90, 0, 0, 0],
                text: [
                    { characterSpacing: -2, text: 'CARBON FOOTPRINT' + '\n', fontSize: 40, color: '#FFFFFFF', bold: true, },

                    { characterSpacing: -2, text: 'REPORT' + '\n', fontSize: 40, color: '#FFFFFFF', bold: true, lineHeight: 1.5 },


                    { characterSpacing: -2, text: year + ' - ' + (year + 1).toString().substr(2, 3) + '\n', fontSize: 25, color: '#FFFFFFF', bold: true, lineHeight: 2.0 },

                    { characterSpacing: 0, text: DateTime.local().toFormat('MMMM dd, yyyy'), fontSize: 15, color: '#FFFFFFF' }
                ],
                pageBreak: 'after'
            },




            {
                toc: {
                    id: 'sectionHeader',
                    title: {
                        text: 'Index', style: 'tdHead', color: 'black', fontSize: 24,
                        margin: [0, 0, 0, 50] // Adds space below the title

                    },

                },
                pageBreak: 'after'
            }], data = []
        const div = document.getElementById('main')

        for (let i = 0; i < div.children.length; i++) {

            if (div.childNodes[i].tagName.toLowerCase() === 'sectionheader') {


                data.push({
                    table: {

                        widths: ['*'],
                        body: [
                            [{ tocItem: 'sectionHeader', text: formatSubscript(div.childNodes[i].textContent, ['tco2e', 'n2o', 'ch4', 'co2'], ['2', '2', '4', '2']), style: 'secHead', border: [false, false, false, false] }],
                        ],

                    }
                })
                data.push(
                    {
                        text: '', // Empty text

                        margin: [10, 10], // Adjust the margin for horizontal space

                    })
            }
            else if (div.childNodes[i].tagName.toLowerCase() === 'sectionheader1') {

                for (let child = 0; child < div.childNodes[i].children.length; child++) {

                    if (div.childNodes[i].childNodes[child].className.includes('container')) {
                        let content = generatePdfMakeContentFromTable(div.childNodes[i].childNodes[child].childNodes[0])

                        const alternateColors = ['#D9F8C7',]; // Light gray and white for example
                        const styledContent = content.map((row, index) => {
                            if (index === 0) {
                                // Header row, no color
                                return row;
                            }
                            return row.map((cell, cellIndex) => {
                                return {
                                    text: cell.text,
                                    fillColor: alternateColors[index % 2] // Alternate colors
                                };
                            });
                        });

                        data.push({
                            table: {
                                headerRows: 1,
                                widths: JSON.parse(JSON.stringify(content))[0].map((a, b) => { return b == 0 ? "*" : 'auto' }),
                                body: styledContent,
                                style: 'tableStyle'
                            }
                        })
                        data.push(
                            {
                                text: '', // Empty text

                                margin: [0, 10], // Adjust the margin for horizontal space

                            })
                    }
                    else if (div.childNodes[i].childNodes[child].className.includes('text-under')) {
                        data.push({ text: formatSubscript(div.childNodes[i].childNodes[child].textContent, ['tco2e', 'n2o', 'ch4', 'co2'], ['2', '2', '4', '2']), style: 'text-under' })
                        data.push(
                            {
                                text: '', // Empty text

                                margin: [5, 5], // Adjust the margin for horizontal space

                            })
                    }

                    // METHODOLOGY
                    else {
                        if (div.childNodes[i].childNodes[child].tagName) {
                            if (div.childNodes[i].childNodes[child].tagName === 'SPAN') {
                                data.push({ text: formatSubscript(div.childNodes[i].childNodes[child].textContent, ['tco2e', 'n2o', 'ch4', 'co2'], ['2', '2', '4', '2']), style: 'boldBlue' })
                                data.push(
                                    {
                                        text: '', // Empty text

                                        margin: [5, 5], // Adjust the margin for horizontal space

                                    })
                            }

                            else {
                                data.push({ text: formatSubscript(div.childNodes[i].childNodes[child].textContent, ['tco2e', 'n2o', 'ch4', 'co2'], ['2', '2', '4', '2']) })
                                data.push(
                                    {
                                        text: '', // Empty text

                                        margin: [5, 5], // Adjust the margin for horizontal space

                                    })
                            }
                        }
                        else {
                            data.push({ text: div.childNodes[i].childNodes[child].textContent })
                            data.push(
                                {
                                    text: '', // Empty text

                                    margin: [5, 5], // Adjust the margin for horizontal space

                                })
                        }

                    }
                }
            }
            else {
                if (div.childNodes[i].children.length !== 0) {
                    for (let child = 0; child < div.childNodes[i].children.length; child++) {
                        let tag = div.childNodes[i].childNodes[child].tagName

                        if (tag) {

                            if (tag === 'OL') {
                                data.push({ ol: ulOrOlToPdfMake(div.childNodes[i].childNodes[child]) })
                                data.push(
                                    {
                                        text: '', // Empty text

                                        margin: [0, 5], // Adjust the margin for horizontal space

                                    })
                            }
                            else if (tag === 'UL') {
                                data.push({ ul: ulOrOlToPdfMake(div.childNodes[i].childNodes[child]) })
                                data.push(
                                    {
                                        text: '', // Empty text

                                        margin: [0, 5], // Adjust the margin for horizontal space

                                    })
                            }
                            else if (tag === 'TABLE') {
                                let content = generatePdfMakeContentFromTable(div.childNodes[i].childNodes[child])

                                const alternateColors = ['#D9F8C7',]; // Light gray and white for example

                                // Apply colors to rows
                                const styledContent = content.map((row, index) => {
                                    if (index === 0) {
                                        // Header row, no color
                                        return row;
                                    }
                                    return row.map((cell, cellIndex) => {
                                        return {
                                            text: cell.text,
                                            fillColor: alternateColors[index % 2] // Alternate colors
                                        };
                                    });
                                });

                                data.push({
                                    table: {
                                        headerRows: 1,
                                        widths: JSON.parse(JSON.stringify(content))[0].map((a, b) => {
                                            return b == 0 ? "*" : 'auto'
                                        }),
                                        body: styledContent,
                                        style: 'tableStyle',

                                    },
                                    layout: 'noBorders' // Remove all borders
                                })
                                data.push(
                                    {
                                        text: '', // Empty text

                                        margin: [0, 10], // Adjust the margin for horizontal space

                                    })


                            }

                            else if (tag === 'IMG') {
                                data.push({ image: div.childNodes[i].childNodes[child].src })

                                data.push(
                                    {
                                        text: '', // Empty text

                                        margin: [0, 5], // Adjust the margin for horizontal space

                                    })
                            }

                            // Operational Boundaries and Inventory Inclusions

                            else if (tag === 'BR') {
                                let txt = `Definition: Operational Boundaries requires choosing the scope of emissions that will be reported. There are three scopes of emissions that can be reported:
                       \n Scope 1: Direct GHG Emissions from company owned or controlled sources
                       \n Scope 2: Indirect GHG Emissions from purchased electricity or steam.
                       \n According the GHG Protocol Corporate Reporting Standard, Scope 1 and Scope 2 emissions must be reported. Scope 3 emissions are voluntary`
                                data.push({ text: txt })
                                data.push(
                                    {
                                        text: '', // Empty text

                                        margin: [0, 5], // Adjust the margin for horizontal space

                                    })
                            }
                            // .....................

                            // Image Source: USEPA
                            else {

                                data.push({ text: formatSubscript(div.childNodes[i].childNodes[child].textContent, ['tco2e', 'n2o', 'ch4', 'co2'], ['2', '2', '4', '2']) })
                                data.push(
                                    {
                                        text: '', // Empty text

                                        margin: [0, 5], // Adjust the margin for horizontal space

                                    })
                            }
                            // .....................
                        }


                    }
                }
                else {

                    data.push({
                        text: formatSubscript(div.childNodes[i].textContent,
                            ['tco2e', 'n2o', 'ch4', 'co2'],
                            ['2', '2', '4', '2']),
                        style: 'customText'
                    })
                    data.push(
                        {
                            text: '', // Empty text

                            margin: [5, 5], // Adjust the margin for horizontal space

                        })
                }




            }
        }

        data.forEach((k) => {
            if (k.table) {
                if (!haveSameSubarrayLengths(k.table.body)) {
                    console.log(k.table.body)
                }
            }
        })

        let images = {
            clientlogo: document.getElementById('clientlogo').src,
            page1: document.getElementById('page1').src
        }

        // const header = (currentPage, pageCount, pageSize) => {
        //     if (currentPage !== 1) {
        //         return {
        //             columns: [
        //                 {
        //                     text: `Carbon Footprint Report FY` + year.toString().substr(2, 3),
        //                     style: 'headerText',
        //                     margin: [30, 20],
        //                     fit: [40, 40],
        //                     alignment: 'left'
        //                 },
        //                 {
        //                     image: 'clientlogo',
        //                     fit: [40, 40],
        //                     margin: [0, 5, 15, 0],
        //                     alignment: 'right'
        //                 },
        //             ],
        //             // Add margins to the header
        //         }

        //     }
        // }
        const documentDefinition = {
            info: {
                title: 'Carbon Footprint Report - ' + DateTime.local().toFormat('MMMM dd, yyyy'),
                author: 'Navigos',
                subject: 'CFP Report',
                keywords: 'Dont share unless people within same organization',
                producer: 'EiSqr',


            },
            // userPassword: 'Report@',
            // ownerPassword: '123456',
            permissions: {
                printing: 'highResolution', //'lowResolution'
                modifying: false,
                copying: false,
                annotating: true,
                fillingForms: true,
                contentAccessibility: true,
                documentAssembly: true
            },
            pageSize: 'A4',
            pageMargins: [30, 50, 30, 30], // [left, top, right, bottom] margins
            // header,
            footer: function (currentPage, pageCount) {
                if (currentPage === 1) {
                    return {
                        text: 'Page ' + currentPage + ' of ' + pageCount,
                        alignment: 'center', fontSize: 12, color: '#FFFFFFF'
                    };
                } else {
                    return {
                        text: 'Page ' + currentPage + ' of ' + pageCount,
                        alignment: 'center', fontSize: 12
                    }

                }
            },
            content: [
                ...initialData, ...data

            ],
            // defaultStyle: {
            //     font: 'Roboto'
            //   },

            background: function (currentPage) {

                if (currentPage === 1) {
                    return [
                        {
                            canvas: [
                                {
                                    type: 'rect',
                                    x: 0,
                                    y: 0,
                                    w: 595.28,  // A4 width in points
                                    h: 841.89,  // A4 height in points
                                    color: '#315975'
                                }
                            ]
                        }
                    ]
                }
            },



            images,
            styles: {


                tdHead: {
                    bold: true, alignment: 'center', valign: 'middle', fillColor: '#315874', color: 'white', fontFamily: 'Roboto'
                },

                customText: {
                    fontSize: 12, // Desired font size

                    bold: false,// Set to true if you want the text to be bold


                },

                secHead: {
                    bold: true, alignment: 'left', padding: [10, 10], color: '#315975', fontSize: 24,
                },
                'text-under': {
                    bold: true, color: '#A3E97A '
                },
                headerText: {
                    fontSize: 14,
                    bold: true,
                    color: 'para', // Text color
                }, boldBlue: {
                    color: '#315874',
                    bold: true
                }
            },
        };

        console.log([...initialData, ...data])

        if (type === 0) {
            const pdf = pdfMake.createPdf(documentDefinition);

            pdf.download('CarbonFootPrint.pdf');

        } else {
            pdfMake.createPdf(documentDefinition).open({}, window.open('', '_blank'));

        }



    }

    function haveSameSubarrayLengths(data) {
        if (data.length < 2) {
            // If there are fewer than 2 subarrays, they are considered to have the same length.
            return true;
        }

        const firstSubarrayLength = data[0].length;

        for (let i = 1; i < data.length; i++) {
            if (data[i].length !== firstSubarrayLength) {
                return false;
            }
        }

        return true;
    }

    function generatePdfMakeContentFromTable(table) {

        if (!table) {
            console.error(`Table  not found.`);
            return [];
        }

        let contentArray = [], maxCol = 0
        for (let i = 0; i < table.rows.length; i++) {
            if (maxCol <= table.rows[i].cells.length) {
                maxCol = table.rows[i].cells.length
            }
        }

        for (let i = 0; i < table.rows.length; i++) {
            const rowArray = [];

            for (let j = 0; j < table.rows[i].cells.length; j++) {
                const cell = table.rows[i].cells[j];
                const colSpan = cell.getAttribute('colspan');
                const rowSpan = cell.getAttribute('rowspan');
                const cellText = cell.textContent.trim();

                const cellObject = { text: cellText };
                cellObject.colSpan = parseInt(colSpan)
                cellObject.rowSpan = parseInt(rowSpan)
                cellObject.style = cell.getAttribute('class');
                rowArray.push(cellObject);
                if (parseInt(colSpan) > 1) {

                    for (let j = 0; j < parseInt(colSpan) - 1; j++) {
                        rowArray.push({});
                    }

                }

            }

            contentArray.push(rowArray);


        }
        contentArray.forEach((i, index) => {
            if (i.length !== maxCol) {

                if (contentArray[index - 1]) {
                    contentArray[index - 1].forEach((k, ind) => {
                        if (k.rowSpan) {
                            if (k.rowSpan > 1) {
                                if (k.colSpan === 1) {
                                    i.splice(ind, 0, { text: '', colSpan: k.colSpan, rowSpan: k.rowSpan - 1 })
                                } else {
                                    let newind = ind
                                    for (let j = 0; j < parseInt(k.colSpan); j++) {
                                        i.splice(newind, 0, {})
                                        newind++
                                    }
                                }
                            }
                        } else {
                            for (let j = 0; j < (maxCol - Object.keys(i).length); j++) {

                                // i.push({id:1});
                            }
                        }
                    })
                }
            }
        })
        contentArray.forEach((i, index) => {
            if (i.length !== maxCol) {
                let len = Object.keys(i).length
                for (let j = 0; j < (maxCol - len); j++) {

                    i.push({});
                }

            }
        })
        return contentArray;
    }








    const isMergedCell = (merge, rowIndex, colIndex) => {
        return merge.some((range) => rowIndex >= range.s.r && rowIndex <= range.e.r && colIndex >= range.s.c && colIndex <= range.e.c);
    };
    const getSum = (subset) => {
        let i = 0
        subset.forEach((item) => {
            i = i + item[1]
        })
        return i
    }
    const checkReportingPeriod = (rp, filter) => {

        let count = 0, rps = []
        filter.forEach((item) => {

            if (rp.includes(item)) {
                count = count + 1
                rps.push(item)
            }
        })
        return { result: count === rp.length, count: count, rps }
    }
    function getObjectsWithEmptyString(arr) {
        return arr
            .map((obj) => {
                const updatedObj = { ...obj };
                Object.keys(updatedObj).forEach((key) => {
                    if (typeof updatedObj[key] === "string" && updatedObj[key].trim() === "") {
                        delete updatedObj[key];
                    }
                });
                return updatedObj;
            })
            .filter((obj) => Object.values(obj).some((value) => value !== ""));
    }
    const getCellColSpan = (rowIndex, colIndex) => {
        const merge = workbook.Sheets[firstSheet]["!merges"] || [];
        for (const range of merge) {
            if (rowIndex >= range.s.r && rowIndex <= range.e.r && colIndex >= range.s.c && colIndex <= range.e.c) {
                return range.e.c - range.s.c + 1;
            }
        }
        return 1;
    };

    const getCellRowSpan = (rowIndex, colIndex) => {
        const merge = workbook.Sheets[firstSheet]["!merges"] || [];
        for (const range of merge) {
            if (rowIndex >= range.s.r && rowIndex <= range.e.r && colIndex >= range.s.c && colIndex <= range.e.c) {
                return range.e.r - range.s.r + 1;
            }
        }
        return 1;
    };
    function findValueByKey(object, key) {
        if (key in object) {
            return object[key];
        } else {
            return null; // or any other default value you want to return if the key is not found
        }
    }
    const getLastResponseByRFID = (key, uid) => {
        let locData = JSON.parse(JSON.stringify(rfData));
        let result = findValueByKey(locData, key);
        console.log(key, result)
        if (result) {
            if (result[0].type === 0) {
                if (typeof uid === "string") {
                    let index = result[0].response.findIndex((k) => {
                        return k.name === uid;
                    });
                    if (index !== -1) {
                        if (result[0].response[index].type === 2) {

                            let value_2 = result[0].response[index].value.replace(/(<([^>]+)>)/gi, "").replace(/\n/g, ' ').replace(/&nbsp;/g, ' ')
                            return value_2 === null ? '' : result[0].response[index].value.replace(/(<([^>]+)>)/gi, "").replace(/\n/g, ' ').replace(/&nbsp;/g, ' ')

                        } else if (result[0].response[index].type === 6 || result[0].response[index].type === 4) {
                            let rflib = rflibrary.findIndex((m) => { return m.id === key })
                            console.log(rflib, key, uid)
                            if (rflib !== -1) {
                                let field = rflibrary[rflib].data1.findIndex((s) => { return s.name === uid })
                                if (field !== -1) {
                                    if (typeof result[0].response[index].value[0] === 'number') {
                                        return rflibrary[rflib].data1[field].values[result[0].response[index].value[0]].label
                                    } else if (typeof result[0].response[index].value[0] === 'string') {

                                        let rgloc = rflibrary[rflib].data1[field].values.findIndex((l) => { return l.value === result[0].response[index].value[0] })
                                        if (rgloc !== -1) {
                                            return rflibrary[rflib].data1[field].values[rgloc].label
                                        }
                                    } else {
                                        return null
                                    }


                                } else {
                                    return 'Field Not Found'
                                }

                            } else {
                                return 'RF Not Found'
                            }
                        } else if (result[0].response[index].type === 3) {
                            let rflib = rflibrary.findIndex((m) => { return m.id === key })
                            console.log(rflib, key, uid)
                            if (rflib !== -1) {
                                let field = rflibrary[rflib].data1.findIndex((s) => { return s.name === uid })
                                if (field !== -1) {
                                    if (typeof result[0].response[index].value[0] === 'number') {
                                        return null
                                    } else if (typeof result[0].response[index].value[0] === 'string') {

                                        let rgloc = rflibrary[rflib].data1[field].values.findIndex((l) => { return l.value === result[0].response[index].value })
                                        if (rgloc !== -1) {
                                            return rflibrary[rflib].data1[field].values[rgloc].label
                                        }
                                    } else {
                                        return null
                                    }


                                } else {
                                    return 'Field Not Found'
                                }

                            } else {
                                return 'RF Not Found'
                            }
                        }
                        else if (result[0].response[index].type === 9) {
                            return DateTime.fromISO(result[0].response[index].value, { zone: 'utc' }).toFormat('dd-MM-yyyy')
                        } else {
                            return result[0].response[index].value;
                        }

                    } else {
                        return 'NA';
                    }
                } else {
                    let str = "";
                    uid.forEach((id) => {
                        let index = result[0].response.findIndex((k) => {
                            return k.name === id;
                        });
                        if (index !== -1) {

                            str = str + " " + result[0].response[index].value;
                        }
                    });
                    if (str.trim().length !== 0) {
                        return str;
                    } else {
                        return 'NA';
                    }
                }
            } else {
                return result[0].data2.replace(/(<([^>]+)>)/gi, "").replace(/\n/g, ' ').replace(/&nbsp;/g, ' ')
            }
        }
        return "";
    };
    const updateDataByYear = (val) => {
        setYear(val);
        forceUpdate();
    };

    useEffect(() => {

        const elements = Array.from(document.querySelectorAll("sectionheader,sectionheader1,sectionheader2")).map((elem) => ({
            id: elem.id,
            text: elem.childNodes[0].textContent.trim(),
            level: Number(elem.nodeName.charAt(13)),
        }));

        let as = [],
            indx = 0;
        elements.forEach((item, ind) => {
            if (item.level === 0) {
                as[indx] = item;
                indx = indx + 1;
            } else if (elements[ind - 1].level === 0) {
                as[indx] = { item: [item], level: 1 };
                if (elements[ind + 1] !== undefined && elements[ind + 1].level === 0) {
                    indx = indx + 1;
                }
            } else {
                as[indx].item.push(item);
                if (elements[ind + 1] !== undefined && elements[ind + 1].level === 0) {
                    indx = indx + 1;
                }
            }
        });

        setHeadings(as);
    }, []);
    const groupArrayObject = (array, obj) => {
        return array.reduce(
            (group, arr) => {
                let key = arr[obj];

                group[key] = group[key] ?? [];

                group[key].push(arr);

                return group;
            },

            {}
        );
    };
    useEffect(() => {
        let gtaString = {
            "include": ["newTargetsTwos", "newIndicatorTwos", "newInitiatives"]
        }
        let dcf_list = [], dcf_submitted = [], locloc = []
        let category_string = {
            "include": [{ "relation": "newTopics", "scope": { "include": [{ "relation": "newMetrics", "scope": { "include": [{ "relation": "newDataPoints" }] } }] } }]

        }
        let ef_complete = API.EF_Std + `?filter=%7B%20%22include%22%3A%20%5B%7B%20%22relation%22%3A%20%22newEfDates%22,%20%22scope%22%3A%20%7B%20%22include%22%3A%20%5B%7B%20%22relation%22%3A%20%22newEfs%22,%20%22scope%22%3A%20%7B%20%22include%22%3A%20%5B%7B%20%22relation%22%3A%20%22newEfItems%22%20%7D%5D%20%7D%20%7D%5D%20%7D%20%7D%5D%7D`
        const promise1 = APIServices.get(API.DCF)
        const promise2 = APIServices.get(API.QN_Submit_UP(selector.id) + `?filter=${encodeURIComponent(JSON.stringify({ where: { dcfId: { inq: dcf_id } } }))}`)
        const promise3 = APIServices.get(API.DCF_Entity_User_UP(selector.id))
        const promise4 = APIServices.get(ef_complete)
        const promise5 = APIServices.get(API.EF_SC1)
        const promise6 = APIServices.get(API.EF_SC2)
        const promise7 = APIServices.get(API.EF_SC3)
        const promise8 = APIServices.get(API.EF_SC4)
        Promise.all([promise1, promise2, promise3, promise4, promise5, promise6, promise7, promise8]).then(function (values) {
            setApiEF(values[3].data)
            setDCFAss(values[2].data.filter((k) => { return dcf_id.includes(k.dcfId) }).map((k) => { return { dcfId: k.dcfId } }))
            dcf_list = values[0].data; setDcfList(values[0].data)
            dcf_submitted = values[1].data.filter(i => { return hardcoded.dcf.includes(i.dcfId.toString()) && (i.type === 2 || i.type === 3) })
            console.log(dcf_submitted)


            let loc_subcat = subcat
            loc_subcat.one = values[4].data
            loc_subcat.two = values[5].data
            loc_subcat.three = values[6].data
            loc_subcat.four = values[7].data
            setSubCat(loc_subcat)

            setResponse(dcf_submitted)
            // let val = [], filterarr = groupArrayObject(values[2].data, 'submitId')
            // Object.keys(filterarr).forEach((item) => {

            //     val.push({ id: filterarr[item][0].submitId, rp: filterarr[item][0].rp, rp_: getRP_(filterarr[item][0].rp), year: filterarr[item][0].year, dcf: filterarr[item][0].dcfId, site: filterarr[item][0].site, response: Object.values(groupArrayObject(filterarr[item], 'form_id')) })

            // })



            forceUpdate()

        })

    }, []);
    useEffect(() => {
        if (response.length !== 0 && subcat.one.length) {
            console.log(response)

            let rf = renderEF_New(subcat, response, year)



            let report_ = rf
            let reportEF_ = rf
            console.log(rf)
            // report_[`${year - 1}`] = renderEF_New(subcat,response, year)
            setReport(report_)
            setReportEF(reportEF_)
            forceUpdate()
        }
    }, [year])
    function isDateInFiscalYear(year, dateString) {
        const { start, end } = parseDateString(dateString);
        let curYear = year
        if (fymonth !== 1) {
            curYear = year - 1

        }
        const startDate = DateTime.fromFormat(start.trim(), 'MMM-yyyy');
        const endDate = DateTime.fromFormat(end.trim(), 'MMM-yyyy');
        const fiscalYearStart = DateTime.fromObject({ year: curYear, month: fymonth, day: 1 }); // April 1 of the previous year
        const fiscalYearEnd = DateTime.fromObject({ year: year, month: fymonth - 1 || 12, day: DateTime.fromObject({ year: year, month: fymonth - 1 || 12 }).daysInMonth }); // March 31 of the given year

        return (
            (startDate >= fiscalYearStart && startDate <= fiscalYearEnd) ||
            (endDate >= fiscalYearStart && endDate <= fiscalYearEnd) ||
            (startDate <= fiscalYearStart && endDate >= fiscalYearEnd)
        );
    }
    function parseDateString(dateString) {
        if (dateString.includes('to')) {
            const [start, end] = dateString.split('to');
            return { start, end };
        } else {
            return { start: dateString, end: dateString };
        }
    }

    const renderEF_New = (locsubcat, response_, year) => {
        let report = []

        let filterResponse = response_.filter((i) => { return isDateInFiscalYear(year, getRPTextFormat(i.reporting_period)) })
        console.log(filterResponse)
        filterResponse.forEach((item) => {
            report.push(...getEmissionFactorCalculation(item, year, locsubcat))
        })
        // for (let i = 0; i < filterRepsonse.length; i++) {     
        //    let result =await new Promise((resolve,reject) => {
        //     try{
        // resolve(getEmissionFactorCalculation(filterRepsonse[i],year,locsubcat))
        //     }catch(e){
        //         reject([])
        //     }
        //    })   
        //    console.log(result)
        //     }

        console.log(response)
        return report
    }



    const getScopeData = (year, area) => {
        if (report[year]) {
            return report[year].filter((k) => { return k.scope === area }).map((j) => { return j.ghg }).reduce((a, b) => { return a + b }, 0).toFixed(2)

        }
        return 0
    }
    const checkYear = (rp, yr) => {
        let betweenMonths = []



        let endDate = moment.utc(getDateObjectByMonth_Year(2, yr + 1)).local()
        let startDate = moment.utc(getDateObjectByMonth_Year(2, yr)).local()
        while (startDate.startOf('month') <= endDate.startOf('month')) {

            betweenMonths.push(startDate.format('MM-YYYY'));
            startDate.add(1, 'month');

        }
        return betweenMonths.filter((i) => { return rp.includes(i) }).length === rp.length ? 1 : betweenMonths.filter((i) => { return rp.includes(i) }).length

    }
    const isInIndianFY = (months, year) => {
        const startMonth = 3; // April (zero-based index)
        const endMonth = 2;   // March (zero-based index)
        const startYear = year - 1;
        const endYear = year;

        const startDate = new Date(startYear, startMonth, 1);
        const endDate = new Date(endYear, endMonth, 1);

        const isInFY = months.every(month => {
            const [m, y] = month.split('-');
            const date = new Date(parseInt(y), parseInt(m) - 1, 1); // Subtract 1 from month since it's zero-based
            return startDate <= date && date <= endDate;
        });

        return isInFY;
    }
    const getDataByDP = (dpid, yr) => {
        let dpreport_ = JSON.parse(JSON.stringify(dpreport))
        let d = 0

        dpreport_.filter((i) => { return i.dp === dpid }).forEach((k) => {


            if (checkYear(k.rp, yr) !== 0) {
                d = d + k.value
            }

        })

        return d
    }
    const checkScope = (arr) => {
        console.log(dcfass)
        let index = dcfass.findIndex((l) => { return arr.includes(l.dcfId) })
        return index !== -1 ? true : arr.includes(292) ? true : false
    }
    const getScopeDataFromResponse = (dcfId) => {
        console.log(report)
        return report.filter((l) => { return dcfId.includes(l.dcfId) })
    }
    function concatenateArrayWithAnd(array) {
        if (array.length === 1) {
            return array[0];
        } else if (array.length > 1) {
            const lastElement = array.pop(); // Remove the last element
            return `${array.join(', ')} and ${lastElement}`;
        } else {
            return 'NA'; // Return an empty string if the array is empty
        }
    }
    const getMCFuelUsed = () => {
        let result = []

        report.filter((l) => { return (l.dcfId === 15 || l.dcfId === 311) }).forEach((i, j) => {
            !result.includes(i.fuel_type) && result.push(i.fuel_type)
        })

        return concatenateArrayWithAnd(result)
    }
    const getSCFuelUsed = () => {
        let result = []

        report.filter((l) => { return (l.dcfId === 11 || l.dcfId === 310) }).forEach((i, j) => {
            !result.includes(i.fuel_type) && result.push(i.fuel_type)
        })
        return concatenateArrayWithAnd(result)
    }
    const getFEGasUsed = () => {
        let result = []

        report.filter((l) => { return l.dcfId === 10 }).forEach((i, j) => {
            result.push(i.gas_type)
        })
        return concatenateArrayWithAnd(result)
    }
    function removeDuplicatesByProperties(arr, keys) {
        const seen = new Set();
        return arr.filter(item => {
            const key = JSON.stringify(keys.map(key => item[key]));
            if (!seen.has(key)) {
                seen.add(key);
                return true;
            }
            return false;
        });
    }
    const renderFEGas = () => {
        let result = []

        reportEF.filter((l) => { return l.dcfId === 10 }).forEach((i, j) => {
            result.push({ type: i.gastype, ghg: (i.co2e_).toFixed(2) + ' kg CO2e/kg' })
        })

        return result.length === 0 ? [{ type: 'Not Found', ghg: 0 }] : result
    }
    const renderSCFuel = () => {
        let result = []

        reportEF.filter((l) => { return l.dcfId === 11 }).forEach((i, j) => {

            result.push({ type: i.fuel_type + '-' + i.unit, co2_: (i.co2_).toFixed(2) + ' kg CO2e/kg', n2o_: (i.n2o_).toFixed(2) + ' kg CO2e/kg', ch4_: (i.ch4_).toFixed(2) + ' kg CO2e/kg' })
        })
        return result.length === 0 ? [{ type: 'Not Found', co2_: 0, ch4_: 0, n2o_: 0 }] : removeDuplicatesByProperties(result, [
            'co2_',
            'n2o_',
            'ch4',
            'unit',
            'fuel_type',
        ])
    }

    const renderMCFuel = () => {
        let result = []

        reportEF.filter((l) => { return l.dcfId === 15 }).forEach((i, j) => {
            console.log('i', i)
            result.push({ type: i.mode + ' - ' + i.fuel_cat, ghg: (i.co2e_).toFixed(2) + ' kg CO2e /litre' })
        })

        return result.length === 0 ? [{ type: 'Not Found', ghg: 0 }] : result
    }
    const getScopeDataByDCF = (id) => {
        console.log(report)
        let report_ = JSON.parse(JSON.stringify(report)).filter((i) => { return id.includes(i.dcfId) }).map((i) => { return i.ghg }).reduce((a, b) => { return a + b }, 0)
        return report_.toFixed(3)
    }
    const renderEmissionProfileTable = (text) => {

        return (
            <div class="gridlines-container">
                <table class="gridlines">
                    <tbody>

                        <tr>
                            <td colspan="1" rowspan="1" className="tdHead">{text}
                            </td>
                            <td colspan="1" rowspan="1" className="tdHead">tCO2e
                            </td>
                        </tr>
                        {checkScope([11, 10, 15, 310, 311]) &&
                            <>

                                <tr>
                                    <td colspan="1" rowspan="1" >Scope 1- Direct Emissions
                                    </td>
                                    <td colspan="1" rowspan="1" >{getScopeDataByDCF([11, 10, 15, 310, 311])}
                                    </td>
                                </tr>

                                {checkScope([11, 310]) &&
                                    <tr>
                                        <td colspan="1" rowspan="1" >Stationary Combustion (Fuel Used)
                                        </td>
                                        <td colspan="1" rowspan="1" > {getScopeDataByDCF([11, 310])}
                                        </td>
                                    </tr>
                                }
                                {checkScope([15, 311]) &&
                                    <tr>
                                        <td colspan="1" rowspan="1" >Mobile Combustion (Owned Vehicles)
                                        </td>
                                        <td colspan="1" rowspan="1" > {getScopeDataByDCF([15, 311])}
                                        </td>
                                    </tr>
                                }
                                {checkScope([10]) &&
                                    <tr>
                                        <td colspan="1" rowspan="1" >Fugitive Emissions (Refrigerants)
                                        </td>
                                        <td colspan="1" rowspan="1" > {getScopeDataByDCF([10])}
                                        </td>
                                    </tr>
                                }
                            </>}
                        {checkScope([257]) &&
                            <>
                                <tr>
                                    <td colspan="1" rowspan="1" >Scope 2- Indirect Emission
                                    </td>
                                    <td colspan="1" rowspan="1" > {getScopeDataByDCF([257])}
                                    </td>
                                </tr>

                                <tr>
                                    <td colspan="1" rowspan="1" >Emissions from purchased energy (Grid Electricity)
                                    </td>
                                    <td colspan="1" rowspan="1" > {getScopeDataByDCF([257])}
                                    </td>
                                </tr>
                            </>
                        }
                        {checkScope([16, 36]) && <>
                            <tr>
                                <td colspan="1" rowspan="1" >Scope 3- Indirect emissions
                                </td>
                                <td colspan="1" rowspan="1" >{getScopeDataByDCF([16, 36])}
                                </td>
                            </tr>
                            {checkScope([292]) &&
                                <tr>
                                    <td colspan="1" rowspan="1" >Upstream Transportation and Distribution
                                    </td>
                                    <td colspan="1" rowspan="1" >{getScopeDataByDCF([292])}
                                    </td>
                                </tr>
                            }
                            {checkScope([293]) &&
                                <tr>
                                    <td colspan="1" rowspan="1" >Downstream Transportation and Distribution
                                    </td>
                                    <td colspan="1" rowspan="1" >{getScopeDataByDCF([293])}
                                    </td>
                                </tr>
                            }
                            {checkScope([16]) &&
                                <tr>
                                    <td colspan="1" rowspan="1" >Purchased Goods and Services
                                    </td>
                                    <td colspan="1" rowspan="1" >{getScopeDataByDCF([16])}
                                    </td>
                                </tr>
                            }
                            {checkScope([36]) &&
                                <tr>
                                    <td colspan="1" rowspan="1" >Business Travel
                                    </td>
                                    <td colspan="1" rowspan="1" >{getScopeDataByDCF([36])}
                                    </td>
                                </tr>
                            }
                            {/* <tr>
                            <td colspan="1" rowspan="1" >Employee Commute
                            </td>
                            <td colspan="1" rowspan="1" >
                            </td>
                        </tr> */}
                        </>
                        }
                        <tr>
                            <td colspan="1" rowspan="1" >Total Emission
                            </td>
                            <td colspan="1" rowspan="1" >
                                {getScopeDataByDCF(dcf_id)}
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        )
    }
    const renderEmissionByScopeTable = (text) => {

        return (
            <div class="gridlines-container">
                <table class="gridlines">
                    <tbody>

                        <tr>
                            <td colspan="1" rowspan="1" className="tdHead" >Scope of Emission
                            </td>
                            <td colspan="1" rowspan="1" className="tdHead">tCO2e
                            </td>
                        </tr>
                        {checkScope([11, 10, 15, 310, 311]) &&
                            <>

                                <tr>
                                    <td colspan="1" rowspan="1" >Scope 1- Direct Emissions
                                    </td>
                                    <td colspan="1" rowspan="1" >{getScopeDataByDCF([11, 10, 15])}
                                    </td>
                                </tr>


                            </>}
                        {checkScope([72]) &&

                            <tr>
                                <td colspan="1" rowspan="1" >Scope 2- Indirect Emission
                                </td>
                                <td colspan="1" rowspan="1" > {getScopeDataByDCF([72])}
                                </td>
                            </tr>



                        }
                        {checkScope([16, 36]) &&
                            <tr>
                                <td colspan="1" rowspan="1" >Scope 3- Indirect emissions
                                </td>
                                <td colspan="1" rowspan="1" >{getScopeDataByDCF([16, 36])}
                                </td>
                            </tr>

                        }

                    </tbody>
                </table>
            </div>
        )
    }
    const renderEmissionByCategoryTable = () => {

        return (
            <div class="gridlines-container">
                <table class="gridlines">
                    <tbody>

                        <tr>
                            <td colspan="1" rowspan="1" className="tdHead" >Emission Profile
                            </td>
                            <td colspan="1" rowspan="1" className="tdHead">tCO2e
                            </td>
                        </tr>
                        {checkScope([11, 10, 15, 310, 311]) &&
                            <>



                                {checkScope([11, 310]) &&
                                    <tr>
                                        <td colspan="1" rowspan="1" >Stationary Combustion (Fuel Used)
                                        </td>
                                        <td colspan="1" rowspan="1" > {getScopeDataByDCF([11])}
                                        </td>
                                    </tr>
                                }
                                {checkScope([15, 311]) &&
                                    <tr>
                                        <td colspan="1" rowspan="1" >Mobile Combustion (Owned Vehicles)
                                        </td>
                                        <td colspan="1" rowspan="1" > {getScopeDataByDCF([15])}
                                        </td>
                                    </tr>
                                }
                                {checkScope([10]) &&
                                    <tr>
                                        <td colspan="1" rowspan="1" >Fugitive Emissions (Refrigerants)
                                        </td>
                                        <td colspan="1" rowspan="1" > {getScopeDataByDCF([10])}
                                        </td>
                                    </tr>
                                }
                            </>}
                        {checkScope([72]) &&
                            <>


                                <tr>
                                    <td colspan="1" rowspan="1" >Emissions from purchased energy (Grid Electricity)
                                    </td>
                                    <td colspan="1" rowspan="1" > {getScopeDataByDCF([72])}
                                    </td>
                                </tr>
                            </>
                        }
                        {checkScope([16, 36]) && <>

                            {checkScope([16]) &&
                                <tr>
                                    <td colspan="1" rowspan="1">Purchased Goods and Services
                                    </td>
                                    <td colspan="1" rowspan="1" >{getScopeDataByDCF([16])}
                                    </td>
                                </tr>
                            }
                            {checkScope([36]) &&
                                <tr>
                                    <td colspan="1" rowspan="1" >Business Travel
                                    </td>
                                    <td colspan="1" rowspan="1" >{getScopeDataByDCF([36])}
                                    </td>
                                </tr>
                            }
                            {/* <tr>
                            <td colspan="1" rowspan="1" >Employee Commute
                            </td>
                            <td colspan="1" rowspan="1" >
                            </td>
                        </tr> */}
                        </>
                        }

                    </tbody>
                </table>
            </div>
        )
    }
    const getDataByDCFDPID = (dcfid, dpid, yr) => {
        let response_ = JSON.parse(JSON.stringify(response))
        let d = 0

        response_.forEach((k) => {
            if (k.dcf === dcfid) {

                if (checkYear(k.rp, yr) !== 0) {
                    let result = k.response.filter((k) => { return k.name === dpid })
                    if (result.length !== 0) {
                        d = d + parseInt(result[0].value.match(/\d+/)[0])
                    }
                }
            }
        })

        return d
    }

    function getDateObjectByMonth_Year(month, year) {

        if (isNaN(month) || isNaN(year)) {
            throw new Error('Invalid month or year');
        }

        const normalizedMonth = Math.max(1, Math.min(12, month));

        const date = DateTime.fromObject({ year, month: normalizedMonth, day: 1 });

        return date.toJSDate();
    }
    const getRP_ = (rp) => {
        if (rp.length === 1) {

            return months_[parseInt(rp[0].split('-')[0]) - 1] + "-" + rp[0].split('-')[1].slice(-2)
        } else {
            return months_[parseInt(rp[0].split('-')[0]) - 1] + "-" + rp[0].split('-')[1].slice(-2) + "to" + months_[parseInt(rp[rp.length - 1].split('-')[0]) - 1] + "-" + rp[rp.length - 1].split('-')[1].slice(-2)
        }
    }
    const getClassName = (level) => {
        switch (level) {
            case 1:
                return "head1";
            case 2:
                return "head2";
            case 3:
                return "head3";
            default:
                return null;
        }
    };

    const checkSite = (id, filter) => {
        let idlist = []

        siteList.forEach((country) => {

            if (filter.a.id === 0 || filter.a.id === country.id) {

                country.locationTwos.forEach((city) => {
                    if (filter.b.id === 0 || filter.b.id === city.id) {
                        city.locationThrees.forEach((loc) => {
                            if (filter.c.id == 0 || filter.c.id === loc.id) {
                                idlist.push(loc.id)
                            }
                        })
                    }
                })
            }
        })

        return idlist.includes(id)
    }
    const groupArrayObject_3_Keys = (array, obj1, obj2, obj3) => {
        return array.reduce((result, arr) => {
            let key1 = arr[obj1];
            let key2 = arr[obj2];
            let key3 = arr[obj3]

            const key = `${key1}-${key2}-${key3}`;

            if (!result[key]) {
                result[key] = [];
            }

            result[key].push(arr);

            return result;
        }, {})
    }
    const renderData = (search) => {
        let betweenMonths = [], betweenYears = []
        let monthly_fg = []


        let endDate = moment.utc(search.to).local()
        let startDate = moment.utc(search.from).local()
        let year = moment(moment.utc()).format('YYYY')

        console.log(search)
        if (search.to !== null && search.from !== null) {
            while (startDate.startOf('month') <= endDate.startOf('month')) {

                betweenMonths.push(startDate.format('MM-YYYY'));
                !betweenYears.includes(startDate.format('YYYY')) && betweenYears.push(startDate.format('YYYY'))

                startDate.add(1, 'month');

            }


            let res = JSON.parse(JSON.stringify(response))

            res.forEach((report, rind) => {
                let sc_total = 0, fg_total = 0

                if (checkSite(report.site, search.location)) {
                    report.sitename = locationList.filter((loc) => { return loc.id === report.site })[0].name
                    if (report.dcf === 16 && (search.indicator.id === 0 || search.indicator.id === 122)) {


                        report.response.forEach((fg, ind) => {

                            let date = fg.filter((i) => { return i.dp === "DPA0285" })[0].value
                            let yearind = monthly_fg.findIndex((i) => { return i.year === moment(date).format('YYYY') })
                            let monthind = monthly_fg.findIndex((i) => { return i.year === moment(date).format('YYYY') && i.month === moment(date).format('MMM-YYYY') })
                            let dcfind = monthly_fg.findIndex((i) => { return i.year === moment(date).format('YYYY') && i.month === moment(date).format('MMM-YYYY') && i.dcf === report.dcf })
                            let siteind = monthly_fg.findIndex((i) => { return i.year === moment(date).format('YYYY') && i.month === moment(date).format('MMM-YYYY') && i.dcf === report.dcf && i.site === report.site })


                            if (checkReportingPeriod([moment(date).format('MM-YYYY')], betweenMonths).result) {


                                let item_class = fg.filter((i) => { return i.dp === "DPA0287" })[0].value.name
                                let item_qty = fg.filter((i) => { return i.dp === "DPA0288" })[0].value
                                let price_per_item = fg.filter((i) => { return i.dp === "DPA0289" })[0].value

                                let total = (emissionFactor[0].stdYears[0].stdNames[0].stdScopes[2].stdTopics[0].data1[0].importedData.filter((k) => { return k.item_classification === item_class })[0]['co2e_in_kg'] / 1000) * item_qty * price_per_item

                                fg_total = total + fg_total


                                let updateind = monthly_fg.findIndex((i) => { return i.year === moment(date).format('YYYY') && i.month === moment(date).format('MMM-YYYY') && i.dcf === report.dcf && i.site === report.site && i.item_class === item_class })

                                if (updateind === -1) {

                                    monthly_fg.push({ scope: 3, year: moment(date).format('YYYY'), month: moment(date).format('MMM-YYYY'), ghg: total, dcf: report.dcf, site: report.site, item_class: item_class })

                                } else {

                                    monthly_fg[updateind].ghg = monthly_fg[updateind].ghg + total
                                }

                            }
                        })

                    }
                    if (report.dcf === 36 && (search.indicator.id === 0 || search.indicator.id === 123)) {

                        // 22032
                        report.response.forEach((fg, ind) => {
                            console.log(fg)
                            let date = fg.filter((i) => { return i.dp === "DPA0290" })[0].value
                            let yearind = monthly_fg.findIndex((i) => { return i.year === moment(date).format('YYYY') })
                            let monthind = monthly_fg.findIndex((i) => { return i.year === moment(date).format('YYYY') && i.month === moment(date).format('MMM-YYYY') })
                            let dcfind = monthly_fg.findIndex((i) => { return i.year === moment(date).format('YYYY') && i.month === moment(date).format('MMM-YYYY') && i.dcf === report.dcf })
                            let siteind = monthly_fg.findIndex((i) => { return i.year === moment(date).format('YYYY') && i.month === moment(date).format('MMM-YYYY') && i.dcf === report.dcf && i.site === report.site })


                            if (checkReportingPeriod([moment(date).format('MM-YYYY')], betweenMonths).result) {

                                let total = 0


                                let travel_mode = fg.filter((i) => { return i.dp === "DPA0291" })[0].value.name
                                let passenger = fg.filter((i) => { return i.dp === "DPA0292" })[0].value

                                if (travel_mode.toLowerCase() === 'air') {

                                    total = fg.filter((i) => { return i.dp === "DP_co2e_mt" })[0].value * passenger

                                    let updateind = monthly_fg.findIndex((i) => { return i.year === moment(date).format('YYYY') && i.month === moment(date).format('MMM-YYYY') && i.dcf === report.dcf && i.site === report.site && i.travel_mode === travel_mode })

                                    if (updateind === -1) {

                                        monthly_fg.push({ scope: 3, year: moment(date).format('YYYY'), month: moment(date).format('MMM-YYYY'), ghg: total, dcf: report.dcf, site: report.site, travel_mode: travel_mode })



                                    } else {

                                        monthly_fg[updateind].ghg = monthly_fg[updateind].ghg + total
                                    }

                                    fg_total = total + fg_total
                                } else if (travel_mode.toLowerCase() === 'road') {

                                    let veh_cat = fg.filter((i) => { return i.dp === "DPA0295" })[0].value.name
                                    let veh_type = fg.filter((i) => { return i.dp === "DPA0337" })[0].value
                                    let fuel = fg.filter((i) => { return i.dp === "DPA0338" })[0].value
                                    let km = fg.filter((i) => { return i.dp === "DP_KM" })[0].value
                                    if (veh_cat.includes('Cars') || veh_cat.includes('Motor')) {
                                        if (veh_cat.includes('Cars')) {
                                            total = (emissionFactor[0].stdYears[0].stdNames[0].stdScopes[2].stdTopics[1].data1[0].importedData.filter((k) => { return k.vehicle_category === veh_cat && k.vehicle_type === veh_type.name && k.fuel_type === fuel.name && k.unit === 'km' })[0]['co2e_in_kg'] / 1000) * km * passenger

                                        } else {
                                            total = (emissionFactor[0].stdYears[0].stdNames[0].stdScopes[2].stdTopics[1].data1[0].importedData.filter((k) => { return k.vehicle_category === veh_cat && k.vehicle_type === veh_type.name && k.unit === 'km' })[0]['co2e_in_kg'] / 1000) * km * passenger

                                        }

                                    } else {


                                        total = (emissionFactor[0].stdYears[0].stdNames[0].stdScopes[2].stdTopics[1].data1[0].importedData.filter((k) => { return k.vehicle_category === veh_cat && k.unit === 'passenger.km' })[0]['co2e_in_kg'] / 1000) * km * passenger


                                    }


                                    let updateind = monthly_fg.findIndex((i) => { return i.year === moment(date).format('YYYY') && i.month === moment(date).format('MMM-YYYY') && i.dcf === report.dcf && i.site === report.site && i.travel_mode === travel_mode })

                                    if (updateind === -1) {

                                        monthly_fg.push({ scope: 3, year: moment(date).format('YYYY'), month: moment(date).format('MMM-YYYY'), ghg: total, dcf: report.dcf, site: report.site, travel_mode: travel_mode })



                                    } else {

                                        monthly_fg[updateind].ghg = monthly_fg[updateind].ghg + total
                                    }

                                    fg_total = total + fg_total


                                } else if (travel_mode.toLowerCase() === 'rail') {



                                    total = fg.filter((i) => { return i.dp === "DP_KM" })[0].value * passenger * 0.00116

                                    let updateind = monthly_fg.findIndex((i) => { return i.year === moment(date).format('YYYY') && i.month === moment(date).format('MMM-YYYY') && i.dcf === report.dcf && i.site === report.site && i.travel_mode === travel_mode })

                                    if (updateind === -1) {

                                        monthly_fg.push({ scope: 3, year: moment(date).format('YYYY'), month: moment(date).format('MMM-YYYY'), ghg: total, dcf: report.dcf, site: report.site, travel_mode: travel_mode })



                                    } else {

                                        monthly_fg[updateind].ghg = monthly_fg[updateind].ghg + total
                                    }

                                    fg_total = total + fg_total
                                }





                            }
                        })

                    }

                    if (report.dcf === 11 && (search.indicator.id === 0 || search.indicator.id === 93)) {

                        let addedMonth = checkReportingPeriod(report.rp, betweenMonths)

                        let total = 0, co2 = 0, ch4 = 0, n2o = 0, sc_data = []
                        report.response.forEach((fg, ind) => {

                            let fuel_cat = fg.filter((i) => { return i.dp === "DPA0130" })[0].value.name
                            let fuel_type = fg.filter((i) => { return i.dp === "DPA0131" })[0].value.name
                            let unit = fg.filter((i) => { return i.dp === "DPA0132" })[0].value.name
                            let consumed = fg.filter((i) => { return i.dp === "DPA0336" })[0].value
                            let fuel_cat_ind = sc_data.findIndex((k) => { return k.fuel_type === fuel_type })

                            if (fuel_cat === "Solid Fuels" || fuel_cat === "Biomass") {
                                let carbon = fg.filter((i) => { return i.dp === "DPA0134" })[0].value
                                let cv = fg.filter((i) => { return i.dp === "DPA0133" })[0].value
                                if (carbon > 0 && cv > 0) {
                                    let gj = (carbon * 3.664 * 1000) / cv
                                    total = (gj * 0.000004184 * consumed) / 1000
                                    fg['value'] = ['Emission Factor-', 3.664, ' ,EnergyProduced-', 0.000004184]
                                    co2 = 0
                                    ch4 = 0
                                    n2o = 0
                                    if (fuel_cat_ind === -1) {
                                        sc_data.push({ fuel_type: fuel_type, ghg: total, co2, n2o, ch4 })
                                    } else {
                                        sc_data[fuel_cat_ind].ghg = sc_data[fuel_cat_ind].ghg + total
                                        sc_data[fuel_cat_ind].co2 = sc_data[fuel_cat_ind].co2 + co2
                                        sc_data[fuel_cat_ind].ch4 = sc_data[fuel_cat_ind].ch4 + ch4
                                        sc_data[fuel_cat_ind].n2o = sc_data[fuel_cat_ind].n2o + n2o

                                    }

                                } else {
                                    total = (emissionFactor[0].stdYears[0].stdNames[0].stdScopes[0].stdTopics[0].data1[0].importedData.filter((k) => { return k.fuel_type === fuel_cat && k.fuel === fuel_type && k.unit === unit })[0]['co2e_in_kg'] / 1000) * consumed
                                    co2 = (emissionFactor[0].stdYears[0].stdNames[0].stdScopes[0].stdTopics[0].data1[0].importedData.filter((k) => { return k.fuel_type === fuel_cat && k.fuel === fuel_type && k.unit === unit })[0]['co2_in_kg'] / 1000) * consumed
                                    ch4 = (emissionFactor[0].stdYears[0].stdNames[0].stdScopes[0].stdTopics[0].data1[0].importedData.filter((k) => { return k.fuel_type === fuel_cat && k.fuel === fuel_type && k.unit === unit })[0]['ch4_in_kg'] / 1000) * consumed
                                    n2o = (emissionFactor[0].stdYears[0].stdNames[0].stdScopes[0].stdTopics[0].data1[0].importedData.filter((k) => { return k.fuel_type === fuel_cat && k.fuel === fuel_type && k.unit === unit })[0]['n2o_in_kg'] / 1000) * consumed
                                    fg['value'] = ['co2-', co2 / consumed, ' ,ch4-', ch4 / consumed, ' ,n2o-', n2o / consumed]
                                    if (fuel_cat_ind === -1) {
                                        sc_data.push({ fuel_type: fuel_type, ghg: total, co2, n2o, ch4 })
                                    } else {
                                        sc_data[fuel_cat_ind].ghg = sc_data[fuel_cat_ind].ghg + total
                                        sc_data[fuel_cat_ind].co2 = sc_data[fuel_cat_ind].co2 + co2
                                        sc_data[fuel_cat_ind].ch4 = sc_data[fuel_cat_ind].ch4 + ch4
                                        sc_data[fuel_cat_ind].n2o = sc_data[fuel_cat_ind].n2o + n2o
                                    }
                                }

                            } else {
                                total = (emissionFactor[0].stdYears[0].stdNames[0].stdScopes[0].stdTopics[0].data1[0].importedData.filter((k) => { return k.fuel_type === fuel_cat && k.fuel === fuel_type && k.unit === unit })[0]['co2e_in_kg'] / 1000) * consumed
                                co2 = (emissionFactor[0].stdYears[0].stdNames[0].stdScopes[0].stdTopics[0].data1[0].importedData.filter((k) => { return k.fuel_type === fuel_cat && k.fuel === fuel_type && k.unit === unit })[0]['co2_in_kg'] / 1000) * consumed
                                ch4 = (emissionFactor[0].stdYears[0].stdNames[0].stdScopes[0].stdTopics[0].data1[0].importedData.filter((k) => { return k.fuel_type === fuel_cat && k.fuel === fuel_type && k.unit === unit })[0]['ch4_in_kg'] / 1000) * consumed
                                n2o = (emissionFactor[0].stdYears[0].stdNames[0].stdScopes[0].stdTopics[0].data1[0].importedData.filter((k) => { return k.fuel_type === fuel_cat && k.fuel === fuel_type && k.unit === unit })[0]['n2o_in_kg'] / 1000) * consumed
                                fg['value'] = ['co2-', co2 / consumed, ' ,ch4-', ch4 / consumed, ' ,n2o-', n2o / consumed]
                                if (fuel_cat_ind === -1) {
                                    sc_data.push({ fuel_type: fuel_type, ghg: total, co2, n2o, ch4 })
                                } else {
                                    sc_data[fuel_cat_ind].ghg = sc_data[fuel_cat_ind].ghg + total
                                    sc_data[fuel_cat_ind].co2 = sc_data[fuel_cat_ind].co2 + co2
                                    sc_data[fuel_cat_ind].ch4 = sc_data[fuel_cat_ind].ch4 + ch4
                                    sc_data[fuel_cat_ind].n2o = sc_data[fuel_cat_ind].n2o + n2o

                                }
                            }


                        })
                        addedMonth.rps.forEach((fm) => {
                            let date = moment(fm, 'MM-YYYY').toDate()
                            let yearind = monthly_fg.findIndex((i) => { return i.year === moment(date).format('YYYY') })
                            let monthind = monthly_fg.findIndex((i) => { return i.year === moment(date).format('YYYY') && i.month === moment(date).format('MMM-YYYY') })
                            let dcfind = monthly_fg.findIndex((i) => { return i.year === moment(date).format('YYYY') && i.month === moment(date).format('MMM-YYYY') && i.dcf === report.dcf })
                            let siteind = monthly_fg.findIndex((i) => { return i.year === moment(date).format('YYYY') && i.month === moment(date).format('MMM-YYYY') && i.dcf === report.dcf && i.site === report.site })

                            sc_data.forEach((item) => {
                                let updateind = monthly_fg.findIndex((i) => { return i.year === moment(date).format('YYYY') && i.month === moment(date).format('MMM-YYYY') && i.dcf === report.dcf && i.site === report.site && i.fuel_type === item.fuel_type })

                                if (updateind === -1) {

                                    monthly_fg.push({ scope: 1, year: moment(date).format('YYYY'), month: moment(date).format('MMM-YYYY'), ghg: (item.ghg / addedMonth.count), dcf: report.dcf, site: report.site, fuel_type: item.fuel_type, co2: (item.co2 * 1000) / addedMonth.count, n2o: (item.n20 * 1000) / addedMonth.count, ch4: (item.ch4 * 1000) / addedMonth.count })


                                } else {
                                    monthly_fg[updateind].co2 = (item.co2 * 1000) / addedMonth.count * 1000 + monthly_fg[updateind].co2
                                    monthly_fg[updateind].n2o = (item.n2o * 1000) / addedMonth.count * 1000 + monthly_fg[updateind].n2o
                                    monthly_fg[updateind].ch4 = (item.ch4 * 1000) / addedMonth.count * 1000 + monthly_fg[updateind].ch4
                                    monthly_fg[updateind].ghg = monthly_fg[updateind].ghg + (item.ghg / addedMonth.count)
                                }
                            })


                        })




                    }
                    if (report.dcf === 10 && (search.indicator.id === 0 || search.indicator.id === 116)) {
                        let add = []

                        report.response.forEach((fg, ind) => {

                            let date = fg.filter((i) => { return i.dp === "DPA0137" })[0].value
                            let yearind = monthly_fg.findIndex((i) => { return i.year === moment(date).format('YYYY') })
                            let monthind = monthly_fg.findIndex((i) => { return i.year === moment(date).format('YYYY') && i.month === moment(date).format('MMM-YYYY') })
                            let dcfind = monthly_fg.findIndex((i) => { return i.year === moment(date).format('YYYY') && i.month === moment(date).format('MMM-YYYY') && i.dcf === report.dcf })
                            let siteind = monthly_fg.findIndex((i) => { return i.year === moment(date).format('YYYY') && i.month === moment(date).format('MMM-YYYY') && i.dcf === report.dcf && i.site === report.site })


                            if (checkReportingPeriod([moment(date).format('MM-YYYY')], betweenMonths).result) {

                                let gastype = fg.filter((i) => { return i.dp === "DPA0136" })[0].value.name
                                let gasrefilled = fg.filter((i) => { return i.dp === "DPA0138" })[0].value
                                let total = (emissionFactor[0].stdYears[0].stdNames[0].stdScopes[0].stdTopics[2].data1[0].importedData.filter((k) => { return k.gas_type === gastype })[0]['co2e_in_kg'] / 1000) * gasrefilled

                                fg_total = total + fg_total

                                fg['gas'] = gastype
                                fg['gasfilled'] = gasrefilled
                                fg['value'] = ['EmissionFactor-', total / gasrefilled]
                                let updateind = monthly_fg.findIndex((i) => { return i.year === moment(date).format('YYYY') && i.month === moment(date).format('MMM-YYYY') && i.dcf === report.dcf && i.site === report.site && i.gastype === gastype })

                                if (updateind === -1) {

                                    monthly_fg.push({ scope: 1, year: moment(date).format('YYYY'), month: moment(date).format('MMM-YYYY'), ghg: 0, dcf: report.dcf, site: report.site, gastype: gastype, ghg: total })



                                } else {

                                    monthly_fg[updateind].ghg = monthly_fg[updateind].ghg + total
                                }

                            }
                        })

                    }
                    if (report.dcf === 72 && (search.indicator.id === 0 || search.indicator.id === 121)) {

                        report.rp.forEach((i) => {
                            if (checkReportingPeriod([i], betweenMonths).result) {
                                let yearind = monthly_fg.findIndex((i) => { return i.year === moment(i, 'MM-YYYY').format('YYYY') })
                                let monthind = monthly_fg.findIndex((i) => { return i.year === moment(i, 'MM-YYYY').format('YYYY') && i.month === moment(i, 'MM-YYYY').format('MMM-YYYY') })
                                let dcfind = monthly_fg.findIndex((i) => { return i.year === moment(i, 'MM-YYYY').format('YYYY') && i.month === moment(i, 'MM-YYYY').format('MMM-YYYY') && i.dcf === report.dcf })
                                let siteind = monthly_fg.findIndex((i) => { return i.year === moment(i, 'MM-YYYY').format('YYYY') && i.month === moment(i, 'MM-YYYY').format('MMM-YYYY') && i.dcf === report.dcf && i.site === report.site })

                                let ef = emissionFactor[0].stdYears[0].stdNames[0].stdScopes[1].stdTopics[0].data1[0].importedData[0]['kwh_in_tco2e']

                                let renewable = report.response[0].filter((i) => { return i.dp === "DPA0156" })[0].value / report.rp.length
                                let nonrenewable = (report.response[0].filter((i) => { return i.dp === "DPA0157" })[0].value / report.rp.length)

                                if (yearind === -1) {

                                    monthly_fg.push({ scope: 2, year: moment(i, 'MM-YYYY').format('YYYY'), month: moment(i, 'MM-YYYY').format('MMM-YYYY'), ghg: nonrenewable * ef, dcf: report.dcf, site: report.site, renewable, nonrenewable })

                                } else if (monthind === -1) {

                                    monthly_fg.push({ scope: 2, year: moment(i, 'MM-YYYY').format('YYYY'), month: moment(i, 'MM-YYYY').format('MMM-YYYY'), ghg: nonrenewable * ef, dcf: report.dcf, site: report.site, renewable, nonrenewable })
                                } else if (dcfind === -1) {

                                    monthly_fg.push({ scope: 2, year: moment(i, 'MM-YYYY').format('YYYY'), month: moment(i, 'MM-YYYY').format('MMM-YYYY'), ghg: nonrenewable * ef, dcf: report.dcf, site: report.site, renewable, nonrenewable })

                                } else if (siteind === -1) {

                                    monthly_fg.push({ scope: 2, year: moment(i, 'MM-YYYY').format('YYYY'), month: moment(i, 'MM-YYYY').format('MMM-YYYY'), ghg: nonrenewable * ef, dcf: report.dcf, site: report.site, renewable, nonrenewable })

                                }
                            }
                        })

                    }
                    if (report.dcf === 15 && (search.indicator.id === 0 || search.indicator.id === 118)) {

                        let addedMonth = checkReportingPeriod(report.rp, betweenMonths)


                        let ef_by_fuel = emissionFactor[0].stdYears[0].stdNames[0].stdScopes[0].stdTopics[4].data1[0].importedData
                        let ef_by_distance = emissionFactor[0].stdYears[0].stdNames[0].stdScopes[0].stdTopics[1].data1[0].importedData

                        let total = 0, co2 = 0, ch4 = 0, n2o = 0, mc_data = []

                        report.response.forEach((fg, ind) => {
                            let mode = fg.filter((i) => { return i.dp === "DP_MODE" })[0].value ? 'by distance' : 'by fuel'
                            let fuel_cat = fg.filter((i) => { return i.dp === "DPA0140" })[0].value
                            let fuel_type = fg.filter((i) => { return i.dp === "DPA0139" })[0].value
                            let fuel = fg.filter((i) => { return i.dp === "DPA0141" })[0].value.name
                            let unit = fg.filter((i) => { return i.dp === "DPA0339" })[0].value
                            let km = fg.filter((i) => { return i.dp === "DPA0144" })[0].value
                            let fuel_filled = fg.filter((i) => { return i.dp === "DPA0143" })[0].value
                            let fuel_cat_ind = mc_data.findIndex((k) => { return k.fuel === fuel })

                            if (mode === 'by distance') {

                                total = ef_by_distance.filter((k) => { return k.vehicle_category === fuel_cat.name && k.vehicle_type === fuel_type.name && k.fuel_type === fuel && k.unit === unit.name })[0]['co2e_in_kg']

                                if (fuel_cat_ind === -1) {

                                    mc_data.push({ mode: fuel, ghg: total * km, fuel_cat: fuel_cat.name })
                                } else {

                                    mc_data[fuel_cat_ind].ghg = mc_data[fuel_cat_ind].ghg + total

                                }
                            } else {
                                total = ef_by_fuel.filter((k) => { return k.fuel.trim() === fuel.trim() })[0]['co2e_in_kg']

                                if (fuel_cat_ind === -1) {

                                    mc_data.push({ mode: fuel, ghg: total * fuel_filled })
                                } else {

                                    mc_data[fuel_cat_ind].ghg = mc_data[fuel_cat_ind].ghg + total

                                }
                            }

                        })
                        addedMonth.rps.forEach((fm) => {
                            let date = moment(fm, 'MM-YYYY').toDate()

                            mc_data.forEach((item) => {
                                let updateind = monthly_fg.findIndex((i) => { return i.year === moment(date).format('YYYY') && i.month === moment(date).format('MMM-YYYY') && i.dcf === report.dcf && i.site === report.site && i.mode === item.mode })

                                if (updateind === -1) {
                                    let updateind2 = monthly_fg.findIndex((i) => { return i.year === moment(date).format('YYYY') && i.month === moment(date).format('MMM-YYYY') && i.dcf === report.dcf && i.site === report.site && i.mode === item.mode })
                                    if (updateind2 !== -1) {
                                        monthly_fg[updateind2].ghg = (item.ghg / addedMonth.count) + monthly_fg[updateind2].ghg

                                    } else {

                                        monthly_fg.push({ scope: 1, year: moment(date).format('YYYY'), month: moment(date).format('MMM-YYYY'), ghg: 0, dcf: report.dcf, site: report.site, ghg: (item.ghg / addedMonth.count), mode: item.mode, fuel_cat: item.fuel_cat })
                                    }




                                } else {

                                    monthly_fg[updateind].ghg = monthly_fg[updateind].ghg + (item.ghg / addedMonth.count)
                                }
                            })


                        })




                    }

                }


            })





            let scope12_array = groupArrayObject(monthly_fg.filter((i) => { return (i.scope === 1 || i.scope === 2) }), 'month')
            let scope3_array = groupArrayObject(monthly_fg.filter((i) => { return (i.scope === 3) }), 'month')
            let scope1_array = groupArrayObject(monthly_fg.filter((i) => { return (i.scope === 1) }), 'month')
            let scope2_array = groupArrayObject(monthly_fg.filter((i) => { return (i.scope === 2) }), 'month')
            let pie = []
            monthly_fg.filter((i) => { return (i.scope === 1) }).forEach((item) => {
                let index = pie.findIndex((j) => { return j.name === 'Scope ' + item.scope })
                if (index === -1) {
                    pie.push({ name: 'Scope ' + item.scope, y: item.ghg })
                } else {
                    pie[index].y = pie[index].y + item.ghg
                }
            })
            monthly_fg.filter((i) => { return (i.scope === 2) }).forEach((item) => {
                let index = pie.findIndex((j) => { return j.name === 'Scope ' + item.scope })
                if (index === -1) {
                    pie.push({ name: 'Scope ' + item.scope, y: item.ghg })
                } else {
                    pie[index].y = pie[index].y + item.ghg
                }
            })
            monthly_fg.filter((i) => { return (i.scope === 3) }).forEach((item) => {
                let index = pie.findIndex((j) => { return j.name === 'Scope ' + item.scope })
                if (index === -1) {
                    pie.push({ name: 'Scope ' + item.scope, y: item.ghg })
                } else {
                    pie[index].y = pie[index].y + item.ghg
                }
            })

            let index1 = pie.findIndex((j) => { return j.name === 'Scope 1' })
            let index2 = pie.findIndex((j) => { return j.name === 'Scope 2' })
            let index3 = pie.findIndex((j) => { return j.name === 'Scope 3' })

            if (index1 === -1) {
                pie.push({ name: 'Scope 1', y: 0 })
            }
            if (index2 === -1) {
                pie.push({ name: 'Scope 2', y: 0 })
            }
            if (index3 === -1) {
                pie.push({ name: 'Scope 3', y: 0 })
            }


            if (scope1_array.length !== 0) {
                let scope1_tier2 = [], final = []
                Object.keys(scope1_array).forEach((key) => {
                    scope1_array[key].forEach((item) => {
                        item.dcfId = item.dcf
                        item.dcf = dcflist.filter((l) => { return l.id === item.dcf }).length === 0 ? item.dcf : dcflist.filter((l) => { return l.id === item.dcf })[0].title

                        if (
                            scope1_tier2.findIndex((i) => {
                                return i.name === item.dcf;
                            }) === -1
                        ) {

                            scope1_tier2.push({
                                name: item.dcf,
                                drilldown: true,
                                color: 'red',
                                y: item.ghg,

                                subset: [[key, item.ghg]],
                            });
                        } else {
                            let ind = scope1_tier2.findIndex((i) => {
                                return i.name === item.dcf;
                            });
                            let subind = scope1_tier2[ind].subset.findIndex((j) => {
                                return j[0] === key;
                            });
                            scope1_tier2[ind].y = scope1_tier2[ind].y + item.ghg;
                            if (subind === -1) {
                                scope1_tier2[ind].subset.push([key, item.ghg]);
                            } else {
                                scope1_tier2[ind].subset[subind][1] = scope1_tier2[ind].subset[subind][1] + item.ghg;
                            }
                        }
                    })
                })

                betweenYears.forEach((j) => {

                    let dataset = JSON.parse(JSON.stringify(scope1_tier2)).map((i) => { i.subset.sort((a, b) => { return moment(a[0], 'MMM-YYYY').toDate() - moment(b[0], 'MMM-YYYY').toDate() }); i.subset = i.subset.filter((k) => { return k[0].split('-')[1] === j }); i.y = i.subset.length === 0 ? 0 : getSum(i.subset); i.incomplete = false; return i })


                    final.push(dataset)
                })

            }
            if (scope2_array.length !== 0) {
                let scope2_tier2 = [], final = []
                Object.keys(scope2_array).forEach((key) => {
                    scope2_array[key].forEach((item) => {
                        item.dcfId = item.dcf
                        item.dcf = dcflist.filter((l) => { return l.id === item.dcf }).length === 0 ? item.dcf : dcflist.filter((l) => { return l.id === item.dcf })[0].title
                        if (
                            scope2_tier2.findIndex((i) => {
                                return i.name === item.dcf;
                            }) === -1
                        ) {
                            scope2_tier2.push({
                                name: item.dcf,
                                drilldown: true,
                                color: 'red',
                                y: item.ghg,
                                subset: [[key, item.ghg]],
                            });
                        } else {
                            let ind = scope2_tier2.findIndex((i) => {
                                return i.name === item.dcf;
                            });
                            let subind = scope2_tier2[ind].subset.findIndex((j) => {
                                return j[0] === key;
                            });
                            scope2_tier2[ind].y = scope2_tier2[ind].y + item.ghg;
                            if (subind === -1) {
                                scope2_tier2[ind].subset.push([key, item.ghg]);
                            } else {
                                scope2_tier2[ind].subset[subind][1] = scope2_tier2[ind].subset[subind][1] + item.ghg;
                            }
                        }
                    })
                })
                betweenYears.forEach((j) => {

                    let dataset = JSON.parse(JSON.stringify(scope2_tier2)).map((i) => { i.subset.sort((a, b) => { return moment(a[0], 'MMM-YYYY').toDate() - moment(b[0], 'MMM-YYYY').toDate() }); i.subset = i.subset.filter((k) => { return k[0].split('-')[1] === j }); i.y = i.subset.length === 0 ? 0 : getSum(i.subset); i.incomplete = false; return i })

                    final.push(dataset)
                })


            }
            if (scope3_array.length !== 0) {
                let scope3_tier2 = [], final = []
                Object.keys(scope3_array).forEach((key) => {
                    scope3_array[key].forEach((item) => {
                        item.dcfId = item.dcf
                        item.dcf = dcflist.filter((l) => { return l.id === item.dcf }).length === 0 ? item.dcf : dcflist.filter((l) => { return l.id === item.dcf })[0].title
                        if (
                            scope3_tier2.findIndex((i) => {
                                return i.name === item.dcf;
                            }) === -1
                        ) {
                            scope3_tier2.push({
                                name: item.dcf,
                                drilldown: true,
                                color: 'red',
                                y: item.ghg,
                                subset: [[key, item.ghg]],
                            });
                        } else {
                            let ind = scope3_tier2.findIndex((i) => {
                                return i.name === item.dcf;
                            });
                            let subind = scope3_tier2[ind].subset.findIndex((j) => {
                                return j[0] === key;
                            });
                            scope3_tier2[ind].y = scope3_tier2[ind].y + item.ghg;
                            if (subind === -1) {
                                scope3_tier2[ind].subset.push([key, item.ghg]);
                            } else {
                                scope3_tier2[ind].subset[subind][1] = scope3_tier2[ind].subset[subind][1] + item.ghg;
                            }
                        }
                    })
                })
                betweenYears.forEach((j) => {

                    let dataset = JSON.parse(JSON.stringify(scope3_tier2)).map((i) => { i.subset.sort((a, b) => { return moment(a[0], 'MMM-YYYY').toDate() - moment(b[0], 'MMM-YYYY').toDate() }); i.subset = i.subset.filter((k) => { return k[0].split('-')[1] === j }); i.y = i.subset.length === 0 ? 0 : getSum(i.subset); i.incomplete = false; return i })

                    final.push(dataset)
                })

            }



        }

        return monthly_fg


    }
    const renderDataEF = (search) => {
        let betweenMonths = [], betweenYears = []
        let monthly_fg = []

        console.log(search)

        let endDate = moment.utc(search.to).local()
        let startDate = moment.utc(search.from).local()
        let year = moment(moment.utc()).format('YYYY')


        if (search.to !== null && search.from !== null) {
            while (startDate.startOf('month') <= endDate.startOf('month')) {

                betweenMonths.push(startDate.format('MM-YYYY'));
                !betweenYears.includes(startDate.format('YYYY')) && betweenYears.push(startDate.format('YYYY'))

                startDate.add(1, 'month');

            }


            let res = JSON.parse(JSON.stringify(response))

            res.forEach((report, rind) => {
                let sc_total = 0, fg_total = 0

                if (checkSite(report.site, search.location)) {
                    report.sitename = locationList.filter((loc) => { return loc.id === report.site })[0].name
                    if (report.dcf === 16 && (search.indicator.id === 0 || search.indicator.id === 122)) {


                        report.response.forEach((fg, ind) => {

                            let date = fg.filter((i) => { return i.dp === "DPA0285" })[0].value
                            let yearind = monthly_fg.findIndex((i) => { return i.year === moment(date).format('YYYY') })
                            let monthind = monthly_fg.findIndex((i) => { return i.year === moment(date).format('YYYY') && i.month === moment(date).format('MMM-YYYY') })
                            let dcfind = monthly_fg.findIndex((i) => { return i.year === moment(date).format('YYYY') && i.month === moment(date).format('MMM-YYYY') && i.dcf === report.dcf })
                            let siteind = monthly_fg.findIndex((i) => { return i.year === moment(date).format('YYYY') && i.month === moment(date).format('MMM-YYYY') && i.dcf === report.dcf && i.site === report.site })


                            if (checkReportingPeriod([moment(date).format('MM-YYYY')], betweenMonths).result) {


                                let item_class = fg.filter((i) => { return i.dp === "DPA0287" })[0].value.name
                                let item_qty = fg.filter((i) => { return i.dp === "DPA0288" })[0].value
                                let price_per_item = fg.filter((i) => { return i.dp === "DPA0289" })[0].value

                                let total = (emissionFactor[0].stdYears[0].stdNames[0].stdScopes[2].stdTopics[0].data1[0].importedData.filter((k) => { return k.item_classification === item_class })[0]['co2e_in_kg'] / 1000) * item_qty * price_per_item

                                fg_total = total + fg_total


                                let updateind = monthly_fg.findIndex((i) => { return i.year === moment(date).format('YYYY') && i.month === moment(date).format('MMM-YYYY') && i.dcf === report.dcf && i.site === report.site && i.item_class === item_class })

                                if (updateind === -1) {

                                    monthly_fg.push({ scope: 3, year: moment(date).format('YYYY'), month: moment(date).format('MMM-YYYY'), ghg: total, dcf: report.dcf, site: report.site, item_class: item_class })

                                } else {

                                    monthly_fg[updateind].ghg = monthly_fg[updateind].ghg + total
                                }

                            }
                        })

                    }
                    if (report.dcf === 36 && (search.indicator.id === 0 || search.indicator.id === 123)) {


                        report.response.forEach((fg, ind) => {

                            let date = fg.filter((i) => { return i.dp === "DPA0290" })[0].value
                            let yearind = monthly_fg.findIndex((i) => { return i.year === moment(date).format('YYYY') })
                            let monthind = monthly_fg.findIndex((i) => { return i.year === moment(date).format('YYYY') && i.month === moment(date).format('MMM-YYYY') })
                            let dcfind = monthly_fg.findIndex((i) => { return i.year === moment(date).format('YYYY') && i.month === moment(date).format('MMM-YYYY') && i.dcf === report.dcf })
                            let siteind = monthly_fg.findIndex((i) => { return i.year === moment(date).format('YYYY') && i.month === moment(date).format('MMM-YYYY') && i.dcf === report.dcf && i.site === report.site })


                            if (checkReportingPeriod([moment(date).format('MM-YYYY')], betweenMonths).result) {




                                let total = 0


                                let travel_mode = fg.filter((i) => { return i.dp === "DPA0291" })[0].value.name
                                let passenger = fg.filter((i) => { return i.dp === "DPA0292" })[0].value

                                if (travel_mode.toLowerCase() === 'air') {

                                    total = fg.filter((i) => { return i.dp === "DP_co2e_mt" })[0].value * passenger

                                    let updateind = monthly_fg.findIndex((i) => { return i.year === moment(date).format('YYYY') && i.month === moment(date).format('MMM-YYYY') && i.dcf === report.dcf && i.site === report.site && i.travel_mode === travel_mode })

                                    if (updateind === -1) {

                                        monthly_fg.push({ scope: 3, year: moment(date).format('YYYY'), month: moment(date).format('MMM-YYYY'), ghg: total, dcf: report.dcf, site: report.site, travel_mode: travel_mode })



                                    } else {

                                        monthly_fg[updateind].ghg = monthly_fg[updateind].ghg + total
                                    }

                                    fg_total = total + fg_total
                                } else if (travel_mode.toLowerCase() === 'road') {

                                    let veh_cat = fg.filter((i) => { return i.dp === "DPA0295" })[0].value.name
                                    let veh_type = fg.filter((i) => { return i.dp === "DPA0337" })[0].value
                                    let fuel = fg.filter((i) => { return i.dp === "DPA0338" })[0].value
                                    let km = fg.filter((i) => { return i.dp === "DP_KM" })[0].value
                                    if (veh_cat.includes('Cars') || veh_cat.includes('Motor')) {
                                        if (veh_cat.includes('Cars')) {
                                            total = (emissionFactor[0].stdYears[0].stdNames[0].stdScopes[2].stdTopics[1].data1[0].importedData.filter((k) => { return k.vehicle_category === veh_cat && k.vehicle_type === veh_type.name && k.fuel_type === fuel.name && k.unit === 'km' })[0]['co2e_in_kg'] / 1000) * km * passenger

                                        } else {
                                            total = (emissionFactor[0].stdYears[0].stdNames[0].stdScopes[2].stdTopics[1].data1[0].importedData.filter((k) => { return k.vehicle_category === veh_cat && k.vehicle_type === veh_type.name && k.unit === 'km' })[0]['co2e_in_kg'] / 1000) * km * passenger

                                        }

                                    } else {


                                        total = (emissionFactor[0].stdYears[0].stdNames[0].stdScopes[2].stdTopics[1].data1[0].importedData.filter((k) => { return k.vehicle_category === veh_cat && k.unit === 'passenger.km' })[0]['co2e_in_kg'] / 1000) * km * passenger


                                    }


                                    let updateind = monthly_fg.findIndex((i) => { return i.year === moment(date).format('YYYY') && i.month === moment(date).format('MMM-YYYY') && i.dcf === report.dcf && i.site === report.site && i.travel_mode === travel_mode })

                                    if (updateind === -1) {

                                        monthly_fg.push({ scope: 3, year: moment(date).format('YYYY'), month: moment(date).format('MMM-YYYY'), ghg: total, dcf: report.dcf, site: report.site, travel_mode: travel_mode })



                                    } else {

                                        monthly_fg[updateind].ghg = monthly_fg[updateind].ghg + total
                                    }

                                    fg_total = total + fg_total


                                } else if (travel_mode.toLowerCase() === 'rail') {



                                    total = fg.filter((i) => { return i.dp === "DP_KM" })[0].value * passenger * 0.00116

                                    let updateind = monthly_fg.findIndex((i) => { return i.year === moment(date).format('YYYY') && i.month === moment(date).format('MMM-YYYY') && i.dcf === report.dcf && i.site === report.site && i.travel_mode === travel_mode })

                                    if (updateind === -1) {

                                        monthly_fg.push({ scope: 3, year: moment(date).format('YYYY'), month: moment(date).format('MMM-YYYY'), ghg: total, dcf: report.dcf, site: report.site, travel_mode: travel_mode })



                                    } else {

                                        monthly_fg[updateind].ghg = monthly_fg[updateind].ghg + total
                                    }

                                    fg_total = total + fg_total
                                }





                            }
                        })

                    }

                    if (report.dcf === 11 && (search.indicator.id === 0 || search.indicator.id === 93)) {

                        let addedMonth = checkReportingPeriod(report.rp, betweenMonths)

                        let total = 0, co2 = 0, ch4 = 0, n2o = 0, sc_data = []
                        report.response.forEach((fg, ind) => {

                            let fuel_cat = fg.filter((i) => { return i.dp === "DPA0130" })[0].value.name
                            let fuel_type = fg.filter((i) => { return i.dp === "DPA0131" })[0].value.name
                            let unit = fg.filter((i) => { return i.dp === "DPA0132" })[0].value.name
                            let consumed = fg.filter((i) => { return i.dp === "DPA0336" })[0].value
                            let fuel_cat_ind = sc_data.findIndex((k) => { return k.fuel_type === fuel_type && k.unit === unit })

                            if (fuel_cat === "Solid Fuels" || fuel_cat === "Biomass") {
                                let carbon = fg.filter((i) => { return i.dp === "DPA0134" })[0].value
                                let cv = fg.filter((i) => { return i.dp === "DPA0133" })[0].value
                                if (carbon > 0 && cv > 0) {
                                    let gj = (carbon * 3.664 * 1000) / cv
                                    total = (gj * 0.000004184 * consumed) / 1000
                                    fg['value'] = ['Emission Factor-', 3.664, ' ,EnergyProduced-', 0.000004184]
                                    co2 = 0
                                    ch4 = 0
                                    n2o = 0
                                    if (fuel_cat_ind === -1) {
                                        sc_data.push({ fuel_type: fuel_type, ghg: total, co2, n2o, ch4, unit, co2_: 0, n2o_: 0, ch4_: 0 })
                                    } else {
                                        sc_data[fuel_cat_ind].ghg = sc_data[fuel_cat_ind].ghg + total
                                        sc_data[fuel_cat_ind].co2 = sc_data[fuel_cat_ind].co2 + co2
                                        sc_data[fuel_cat_ind].ch4 = sc_data[fuel_cat_ind].ch4 + ch4
                                        sc_data[fuel_cat_ind].n2o = sc_data[fuel_cat_ind].n2o + n2o

                                    }

                                } else {
                                    total = (emissionFactor[0].stdYears[0].stdNames[0].stdScopes[0].stdTopics[0].data1[0].importedData.filter((k) => { return k.fuel_type === fuel_cat && k.fuel === fuel_type && k.unit === unit })[0]['co2e_in_kg'] / 1000) * consumed
                                    co2 = (emissionFactor[0].stdYears[0].stdNames[0].stdScopes[0].stdTopics[0].data1[0].importedData.filter((k) => { return k.fuel_type === fuel_cat && k.fuel === fuel_type && k.unit === unit })[0]['co2_in_kg'] / 1000) * consumed
                                    ch4 = (emissionFactor[0].stdYears[0].stdNames[0].stdScopes[0].stdTopics[0].data1[0].importedData.filter((k) => { return k.fuel_type === fuel_cat && k.fuel === fuel_type && k.unit === unit })[0]['ch4_in_kg'] / 1000) * consumed
                                    n2o = (emissionFactor[0].stdYears[0].stdNames[0].stdScopes[0].stdTopics[0].data1[0].importedData.filter((k) => { return k.fuel_type === fuel_cat && k.fuel === fuel_type && k.unit === unit })[0]['n2o_in_kg'] / 1000) * consumed
                                    let co2_ = (emissionFactor[0].stdYears[0].stdNames[0].stdScopes[0].stdTopics[0].data1[0].importedData.filter((k) => { return k.fuel_type === fuel_cat && k.fuel === fuel_type && k.unit === unit })[0]['co2_in_kg'])
                                    let ch4_ = (emissionFactor[0].stdYears[0].stdNames[0].stdScopes[0].stdTopics[0].data1[0].importedData.filter((k) => { return k.fuel_type === fuel_cat && k.fuel === fuel_type && k.unit === unit })[0]['ch4_in_kg'])
                                    let n2o_ = (emissionFactor[0].stdYears[0].stdNames[0].stdScopes[0].stdTopics[0].data1[0].importedData.filter((k) => { return k.fuel_type === fuel_cat && k.fuel === fuel_type && k.unit === unit })[0]['n2o_in_kg'])
                                    fg['value'] = ['co2-', co2 / consumed, ' ,ch4-', ch4 / consumed, ' ,n2o-', n2o / consumed]
                                    if (fuel_cat_ind === -1) {
                                        sc_data.push({ fuel_type: fuel_type, ghg: total, unit, co2, n2o, ch4, co2_, n2o_, ch4_ })
                                    } else {
                                        sc_data[fuel_cat_ind].ghg = sc_data[fuel_cat_ind].ghg + total
                                        sc_data[fuel_cat_ind].co2 = sc_data[fuel_cat_ind].co2 + co2
                                        sc_data[fuel_cat_ind].ch4 = sc_data[fuel_cat_ind].ch4 + ch4
                                        sc_data[fuel_cat_ind].n2o = sc_data[fuel_cat_ind].n2o + n2o
                                    }
                                }

                            } else {
                                total = (emissionFactor[0].stdYears[0].stdNames[0].stdScopes[0].stdTopics[0].data1[0].importedData.filter((k) => { return k.fuel_type === fuel_cat && k.fuel === fuel_type && k.unit === unit })[0]['co2e_in_kg'] / 1000) * consumed
                                co2 = (emissionFactor[0].stdYears[0].stdNames[0].stdScopes[0].stdTopics[0].data1[0].importedData.filter((k) => { return k.fuel_type === fuel_cat && k.fuel === fuel_type && k.unit === unit })[0]['co2_in_kg'] / 1000) * consumed
                                ch4 = (emissionFactor[0].stdYears[0].stdNames[0].stdScopes[0].stdTopics[0].data1[0].importedData.filter((k) => { return k.fuel_type === fuel_cat && k.fuel === fuel_type && k.unit === unit })[0]['ch4_in_kg'] / 1000) * consumed
                                n2o = (emissionFactor[0].stdYears[0].stdNames[0].stdScopes[0].stdTopics[0].data1[0].importedData.filter((k) => { return k.fuel_type === fuel_cat && k.fuel === fuel_type && k.unit === unit })[0]['n2o_in_kg'] / 1000) * consumed
                                let co2_ = (emissionFactor[0].stdYears[0].stdNames[0].stdScopes[0].stdTopics[0].data1[0].importedData.filter((k) => { return k.fuel_type === fuel_cat && k.fuel === fuel_type && k.unit === unit })[0]['co2_in_kg'])
                                let ch4_ = (emissionFactor[0].stdYears[0].stdNames[0].stdScopes[0].stdTopics[0].data1[0].importedData.filter((k) => { return k.fuel_type === fuel_cat && k.fuel === fuel_type && k.unit === unit })[0]['ch4_in_kg'])
                                let n2o_ = (emissionFactor[0].stdYears[0].stdNames[0].stdScopes[0].stdTopics[0].data1[0].importedData.filter((k) => { return k.fuel_type === fuel_cat && k.fuel === fuel_type && k.unit === unit })[0]['n2o_in_kg'])

                                fg['value'] = ['co2-', co2 / consumed, ' ,ch4-', ch4 / consumed, ' ,n2o-', n2o / consumed]
                                if (fuel_cat_ind === -1) {
                                    sc_data.push({ fuel_type: fuel_type, ghg: total, co2, n2o, ch4, unit, co2_, n2o_, ch4_ })
                                } else {
                                    sc_data[fuel_cat_ind].ghg = sc_data[fuel_cat_ind].ghg + total
                                    sc_data[fuel_cat_ind].co2 = sc_data[fuel_cat_ind].co2 + co2
                                    sc_data[fuel_cat_ind].ch4 = sc_data[fuel_cat_ind].ch4 + ch4
                                    sc_data[fuel_cat_ind].n2o = sc_data[fuel_cat_ind].n2o + n2o

                                }
                            }


                        })
                        addedMonth.rps.forEach((fm) => {
                            let date = moment(fm, 'MM-YYYY').toDate()
                            let yearind = monthly_fg.findIndex((i) => { return i.year === moment(date).format('YYYY') })
                            let monthind = monthly_fg.findIndex((i) => { return i.year === moment(date).format('YYYY') && i.month === moment(date).format('MMM-YYYY') })
                            let dcfind = monthly_fg.findIndex((i) => { return i.year === moment(date).format('YYYY') && i.month === moment(date).format('MMM-YYYY') && i.dcf === report.dcf })
                            let siteind = monthly_fg.findIndex((i) => { return i.year === moment(date).format('YYYY') && i.month === moment(date).format('MMM-YYYY') && i.dcf === report.dcf && i.site === report.site })

                            sc_data.forEach((item) => {
                                let updateind = monthly_fg.findIndex((i) => { return i.year === moment(date).format('YYYY') && i.month === moment(date).format('MMM-YYYY') && i.dcf === report.dcf && i.site === report.site && i.fuel_type === item.fuel_type && i.unit === item.unit })

                                if (updateind === -1) {

                                    monthly_fg.push({ scope: 1, year: moment(date).format('YYYY'), month: moment(date).format('MMM-YYYY'), ghg: (item.ghg / addedMonth.count), dcf: report.dcf, site: report.site, fuel_type: item.fuel_type, unit: item.unit, co2: (item.co2 * 1000) / addedMonth.count, n2o: (item.n20 * 1000) / addedMonth.count, ch4: (item.ch4 * 1000) / addedMonth.count, co2_: item.co2_, ch4_: item.ch4_, n2o_: item.n2o_ })


                                } else {
                                    monthly_fg[updateind].co2 = (item.co2 * 1000) / addedMonth.count * 1000 + monthly_fg[updateind].co2
                                    monthly_fg[updateind].n2o = (item.n2o * 1000) / addedMonth.count * 1000 + monthly_fg[updateind].n2o
                                    monthly_fg[updateind].ch4 = (item.ch4 * 1000) / addedMonth.count * 1000 + monthly_fg[updateind].ch4
                                    monthly_fg[updateind].ghg = monthly_fg[updateind].ghg + (item.ghg / addedMonth.count)
                                }
                            })


                        })




                    }
                    if (report.dcf === 10 && (search.indicator.id === 0 || search.indicator.id === 116)) {
                        let add = []

                        report.response.forEach((fg, ind) => {

                            let date = fg.filter((i) => { return i.dp === "DPA0137" })[0].value
                            let yearind = monthly_fg.findIndex((i) => { return i.year === moment(date).format('YYYY') })
                            let monthind = monthly_fg.findIndex((i) => { return i.year === moment(date).format('YYYY') && i.month === moment(date).format('MMM-YYYY') })
                            let dcfind = monthly_fg.findIndex((i) => { return i.year === moment(date).format('YYYY') && i.month === moment(date).format('MMM-YYYY') && i.dcf === report.dcf })
                            let siteind = monthly_fg.findIndex((i) => { return i.year === moment(date).format('YYYY') && i.month === moment(date).format('MMM-YYYY') && i.dcf === report.dcf && i.site === report.site })


                            if (checkReportingPeriod([moment(date).format('MM-YYYY')], betweenMonths).result) {

                                let gastype = fg.filter((i) => { return i.dp === "DPA0136" })[0].value.name
                                let gasrefilled = fg.filter((i) => { return i.dp === "DPA0138" })[0].value
                                let total = (emissionFactor[0].stdYears[0].stdNames[0].stdScopes[0].stdTopics[2].data1[0].importedData.filter((k) => { return k.gas_type === gastype })[0]['co2e_in_kg'] / 1000) * gasrefilled
                                let co2e_ = emissionFactor[0].stdYears[0].stdNames[0].stdScopes[0].stdTopics[2].data1[0].importedData.filter((k) => { return k.gas_type === gastype })[0]['co2e_in_kg']
                                fg_total = total + fg_total

                                fg['gas'] = gastype
                                fg['gasfilled'] = gasrefilled
                                fg['value'] = ['EmissionFactor-', total / gasrefilled]
                                let updateind = monthly_fg.findIndex((i) => { return i.year === moment(date).format('YYYY') && i.month === moment(date).format('MMM-YYYY') && i.dcf === report.dcf && i.site === report.site && i.gastype === gastype })

                                if (updateind === -1) {

                                    monthly_fg.push({ scope: 1, year: moment(date).format('YYYY'), month: moment(date).format('MMM-YYYY'), ghg: 0, dcf: report.dcf, site: report.site, gastype: gastype, ghg: total, co2e_ })



                                } else {

                                    monthly_fg[updateind].ghg = monthly_fg[updateind].ghg + total
                                }

                            }
                        })

                    }
                    if (report.dcf === 72 && (search.indicator.id === 0 || search.indicator.id === 121)) {

                        report.rp.forEach((i) => {
                            if (checkReportingPeriod([i], betweenMonths).result) {
                                let yearind = monthly_fg.findIndex((i) => { return i.year === moment(i, 'MM-YYYY').format('YYYY') })
                                let monthind = monthly_fg.findIndex((i) => { return i.year === moment(i, 'MM-YYYY').format('YYYY') && i.month === moment(i, 'MM-YYYY').format('MMM-YYYY') })
                                let dcfind = monthly_fg.findIndex((i) => { return i.year === moment(i, 'MM-YYYY').format('YYYY') && i.month === moment(i, 'MM-YYYY').format('MMM-YYYY') && i.dcf === report.dcf })
                                let siteind = monthly_fg.findIndex((i) => { return i.year === moment(i, 'MM-YYYY').format('YYYY') && i.month === moment(i, 'MM-YYYY').format('MMM-YYYY') && i.dcf === report.dcf && i.site === report.site })

                                let ef = emissionFactor[0].stdYears[0].stdNames[0].stdScopes[1].stdTopics[0].data1[0].importedData[0]['kwh_in_tco2e']

                                let renewable = report.response[0].filter((i) => { return i.dp === "DPA0156" })[0].value / report.rp.length
                                let nonrenewable = (report.response[0].filter((i) => { return i.dp === "DPA0157" })[0].value / report.rp.length)

                                if (yearind === -1) {

                                    monthly_fg.push({ scope: 2, year: moment(i, 'MM-YYYY').format('YYYY'), month: moment(i, 'MM-YYYY').format('MMM-YYYY'), ghg: nonrenewable * ef, co2e_: ef, dcf: report.dcf, site: report.site, renewable, nonrenewable })

                                } else if (monthind === -1) {

                                    monthly_fg.push({ scope: 2, year: moment(i, 'MM-YYYY').format('YYYY'), month: moment(i, 'MM-YYYY').format('MMM-YYYY'), ghg: nonrenewable * ef, co2e_: ef, dcf: report.dcf, site: report.site, renewable, nonrenewable })
                                } else if (dcfind === -1) {

                                    monthly_fg.push({ scope: 2, year: moment(i, 'MM-YYYY').format('YYYY'), month: moment(i, 'MM-YYYY').format('MMM-YYYY'), ghg: nonrenewable * ef, co2e_: ef, dcf: report.dcf, site: report.site, renewable, nonrenewable })

                                } else if (siteind === -1) {

                                    monthly_fg.push({ scope: 2, year: moment(i, 'MM-YYYY').format('YYYY'), month: moment(i, 'MM-YYYY').format('MMM-YYYY'), ghg: nonrenewable * ef, dcf: report.dcf, co2e_: ef, site: report.site, renewable, nonrenewable })

                                }
                            }
                        })

                    }
                    if (report.dcf === 15 && (search.indicator.id === 0 || search.indicator.id === 118)) {

                        let addedMonth = checkReportingPeriod(report.rp, betweenMonths)


                        let ef_by_fuel = emissionFactor[0].stdYears[0].stdNames[0].stdScopes[0].stdTopics[4].data1[0].importedData
                        let ef_by_distance = emissionFactor[0].stdYears[0].stdNames[0].stdScopes[0].stdTopics[1].data1[0].importedData

                        let total = 0, co2 = 0, ch4 = 0, n2o = 0, mc_data = []

                        report.response.forEach((fg, ind) => {
                            let mode = fg.filter((i) => { return i.dp === "DP_MODE" })[0].value ? 'by distance' : 'by fuel'
                            let fuel_cat = fg.filter((i) => { return i.dp === "DPA0140" })[0].value
                            let fuel_type = fg.filter((i) => { return i.dp === "DPA0139" })[0].value
                            let fuel = fg.filter((i) => { return i.dp === "DPA0141" })[0].value.name
                            let unit = fg.filter((i) => { return i.dp === "DPA0339" })[0].value
                            let km = fg.filter((i) => { return i.dp === "DPA0144" })[0].value
                            let fuel_filled = fg.filter((i) => { return i.dp === "DPA0143" })[0].value
                            let fuel_cat_ind = mc_data.findIndex((k) => { return k.fuel === fuel })

                            if (mode === 'by distance') {

                                total = ef_by_distance.filter((k) => { return k.vehicle_category === fuel_cat.name && k.vehicle_type === fuel_type.name && k.fuel_type === fuel && k.unit === unit.name })[0]['co2e_in_kg']

                                if (fuel_cat_ind === -1) {

                                    mc_data.push({ mode: fuel, ghg: total * km, co2e_: total, fuel_cat: fuel_cat.name })
                                } else {

                                    mc_data[fuel_cat_ind].ghg = mc_data[fuel_cat_ind].ghg + total

                                }
                            } else {

                                total = ef_by_fuel.filter((k) => { return k.fuel.trim() === fuel.trim() })[0]['co2e_in_kg']

                                if (fuel_cat_ind === -1) {

                                    mc_data.push({ mode: fuel, ghg: total * fuel_filled, co2e_: total, fuel_cat: '' })
                                } else {

                                    mc_data[fuel_cat_ind].ghg = mc_data[fuel_cat_ind].ghg + total

                                }
                            }

                        })
                        addedMonth.rps.forEach((fm) => {
                            let date = moment(fm, 'MM-YYYY').toDate()

                            mc_data.forEach((item) => {
                                let updateind = monthly_fg.findIndex((i) => { return i.year === moment(date).format('YYYY') && i.month === moment(date).format('MMM-YYYY') && i.dcf === report.dcf && i.site === report.site && i.mode === item.mode })

                                if (updateind === -1) {
                                    let updateind2 = monthly_fg.findIndex((i) => { return i.year === moment(date).format('YYYY') && i.month === moment(date).format('MMM-YYYY') && i.dcf === report.dcf && i.site === report.site && i.mode === item.mode })
                                    if (updateind2 !== -1) {
                                        monthly_fg[updateind2].ghg = (item.ghg / addedMonth.count) + monthly_fg[updateind2].ghg
                                        // monthly_fg[updateind2]['fuel_cat'] = item.fuel_cat
                                    } else {
                                        console.log('EF', item)
                                        monthly_fg.push({ scope: 1, year: moment(date).format('YYYY'), month: moment(date).format('MMM-YYYY'), ghg: 0, dcf: report.dcf, site: report.site, ghg: (item.ghg / addedMonth.count), mode: item.mode, fuel_cat: item.fuel_cat, co2e_: item.co2e_ })
                                    }




                                } else {
                                    // monthly_fg[updateind]['fuel_cat'] = item.fuel_cat
                                    monthly_fg[updateind].ghg = monthly_fg[updateind].ghg + (item.ghg / addedMonth.count)
                                }
                            })


                        })




                    }

                }


            })





            let scope12_array = groupArrayObject(monthly_fg.filter((i) => { return (i.scope === 1 || i.scope === 2) }), 'month')
            let scope3_array = groupArrayObject(monthly_fg.filter((i) => { return (i.scope === 3) }), 'month')
            let scope1_array = groupArrayObject(monthly_fg.filter((i) => { return (i.scope === 1) }), 'month')
            let scope2_array = groupArrayObject(monthly_fg.filter((i) => { return (i.scope === 2) }), 'month')
            let pie = []
            monthly_fg.filter((i) => { return (i.scope === 1) }).forEach((item) => {
                let index = pie.findIndex((j) => { return j.name === 'Scope ' + item.scope })
                if (index === -1) {
                    pie.push({ name: 'Scope ' + item.scope, y: item.ghg })
                } else {
                    pie[index].y = pie[index].y + item.ghg
                }
            })
            monthly_fg.filter((i) => { return (i.scope === 2) }).forEach((item) => {
                let index = pie.findIndex((j) => { return j.name === 'Scope ' + item.scope })
                if (index === -1) {
                    pie.push({ name: 'Scope ' + item.scope, y: item.ghg })
                } else {
                    pie[index].y = pie[index].y + item.ghg
                }
            })
            monthly_fg.filter((i) => { return (i.scope === 3) }).forEach((item) => {
                let index = pie.findIndex((j) => { return j.name === 'Scope ' + item.scope })
                if (index === -1) {
                    pie.push({ name: 'Scope ' + item.scope, y: item.ghg })
                } else {
                    pie[index].y = pie[index].y + item.ghg
                }
            })

            let index1 = pie.findIndex((j) => { return j.name === 'Scope 1' })
            let index2 = pie.findIndex((j) => { return j.name === 'Scope 2' })
            let index3 = pie.findIndex((j) => { return j.name === 'Scope 3' })

            if (index1 === -1) {
                pie.push({ name: 'Scope 1', y: 0 })
            }
            if (index2 === -1) {
                pie.push({ name: 'Scope 2', y: 0 })
            }
            if (index3 === -1) {
                pie.push({ name: 'Scope 3', y: 0 })
            }


            if (scope1_array.length !== 0) {
                let scope1_tier2 = [], final = []
                Object.keys(scope1_array).forEach((key) => {
                    scope1_array[key].forEach((item) => {
                        item.dcfId = item.dcf
                        item.dcf = dcflist.filter((l) => { return l.id === item.dcf }).length === 0 ? item.dcf : dcflist.filter((l) => { return l.id === item.dcf })[0].title

                        if (
                            scope1_tier2.findIndex((i) => {
                                return i.name === item.dcf;
                            }) === -1
                        ) {

                            scope1_tier2.push({
                                name: item.dcf,
                                drilldown: true,
                                color: 'red',
                                y: item.ghg,

                                subset: [[key, item.ghg]],
                            });
                        } else {
                            let ind = scope1_tier2.findIndex((i) => {
                                return i.name === item.dcf;
                            });
                            let subind = scope1_tier2[ind].subset.findIndex((j) => {
                                return j[0] === key;
                            });
                            scope1_tier2[ind].y = scope1_tier2[ind].y + item.ghg;
                            if (subind === -1) {
                                scope1_tier2[ind].subset.push([key, item.ghg]);
                            } else {
                                scope1_tier2[ind].subset[subind][1] = scope1_tier2[ind].subset[subind][1] + item.ghg;
                            }
                        }
                    })
                })

                betweenYears.forEach((j) => {

                    let dataset = JSON.parse(JSON.stringify(scope1_tier2)).map((i) => { i.subset.sort((a, b) => { return moment(a[0], 'MMM-YYYY').toDate() - moment(b[0], 'MMM-YYYY').toDate() }); i.subset = i.subset.filter((k) => { return k[0].split('-')[1] === j }); i.y = i.subset.length === 0 ? 0 : getSum(i.subset); i.incomplete = false; return i })


                    final.push(dataset)
                })

            }
            if (scope2_array.length !== 0) {
                let scope2_tier2 = [], final = []
                Object.keys(scope2_array).forEach((key) => {
                    scope2_array[key].forEach((item) => {
                        item.dcfId = item.dcf
                        item.dcf = dcflist.filter((l) => { return l.id === item.dcf }).length === 0 ? item.dcf : dcflist.filter((l) => { return l.id === item.dcf })[0].title
                        if (
                            scope2_tier2.findIndex((i) => {
                                return i.name === item.dcf;
                            }) === -1
                        ) {
                            scope2_tier2.push({
                                name: item.dcf,
                                drilldown: true,
                                color: 'red',
                                y: item.ghg,
                                subset: [[key, item.ghg]],
                            });
                        } else {
                            let ind = scope2_tier2.findIndex((i) => {
                                return i.name === item.dcf;
                            });
                            let subind = scope2_tier2[ind].subset.findIndex((j) => {
                                return j[0] === key;
                            });
                            scope2_tier2[ind].y = scope2_tier2[ind].y + item.ghg;
                            if (subind === -1) {
                                scope2_tier2[ind].subset.push([key, item.ghg]);
                            } else {
                                scope2_tier2[ind].subset[subind][1] = scope2_tier2[ind].subset[subind][1] + item.ghg;
                            }
                        }
                    })
                })
                betweenYears.forEach((j) => {

                    let dataset = JSON.parse(JSON.stringify(scope2_tier2)).map((i) => { i.subset.sort((a, b) => { return moment(a[0], 'MMM-YYYY').toDate() - moment(b[0], 'MMM-YYYY').toDate() }); i.subset = i.subset.filter((k) => { return k[0].split('-')[1] === j }); i.y = i.subset.length === 0 ? 0 : getSum(i.subset); i.incomplete = false; return i })

                    final.push(dataset)
                })


            }
            if (scope3_array.length !== 0) {
                let scope3_tier2 = [], final = []
                Object.keys(scope3_array).forEach((key) => {
                    scope3_array[key].forEach((item) => {
                        item.dcfId = item.dcf
                        item.dcf = dcflist.filter((l) => { return l.id === item.dcf }).length === 0 ? item.dcf : dcflist.filter((l) => { return l.id === item.dcf })[0].title
                        if (
                            scope3_tier2.findIndex((i) => {
                                return i.name === item.dcf;
                            }) === -1
                        ) {
                            scope3_tier2.push({
                                name: item.dcf,
                                drilldown: true,
                                color: 'red',
                                y: item.ghg,
                                subset: [[key, item.ghg]],
                            });
                        } else {
                            let ind = scope3_tier2.findIndex((i) => {
                                return i.name === item.dcf;
                            });
                            let subind = scope3_tier2[ind].subset.findIndex((j) => {
                                return j[0] === key;
                            });
                            scope3_tier2[ind].y = scope3_tier2[ind].y + item.ghg;
                            if (subind === -1) {
                                scope3_tier2[ind].subset.push([key, item.ghg]);
                            } else {
                                scope3_tier2[ind].subset[subind][1] = scope3_tier2[ind].subset[subind][1] + item.ghg;
                            }
                        }
                    })
                })
                betweenYears.forEach((j) => {

                    let dataset = JSON.parse(JSON.stringify(scope3_tier2)).map((i) => { i.subset.sort((a, b) => { return moment(a[0], 'MMM-YYYY').toDate() - moment(b[0], 'MMM-YYYY').toDate() }); i.subset = i.subset.filter((k) => { return k[0].split('-')[1] === j }); i.y = i.subset.length === 0 ? 0 : getSum(i.subset); i.incomplete = false; return i })

                    final.push(dataset)
                })

            }



        }

        return monthly_fg


    }
    const getBetweenMonths = (year) => {
        console.log(year)
        const months = [];
        // Start from April of the previous year
        let startMonth = DateTime.fromObject({ year: year - 1, month: 4 });
        // End in March of the given year
        const endMonth = DateTime.fromObject({ year, month: 3 });
        console.log(startMonth, endMonth)
        // Loop through each month
        while (startMonth <= endMonth) {
            const formattedMonth = startMonth.toFormat('MM-yyyy');
            console.log(formattedMonth)
            months.push(formattedMonth);
            // Move to the next month
            startMonth.plus({ months: 1 });
            startMonth = startMonth.plus({ months: 1 });
        }

        return months;
    }
    const isDateWithinPeriod = (rp, period) => {
        // Convert date strings to Luxon DateTime objects
        const rpDates = rp.map(dateString => DateTime.fromFormat(dateString, 'MM-yyyy'));
        const periodDates = period.map(dateString => DateTime.fromFormat(dateString, 'MM-yyyy'));

        // Find the matching dates in rp within the period
        const matchingDates = rpDates.filter(rpDate =>
            periodDates.some(periodDate => rpDate.toMillis() === periodDate.toMillis())
        );

        return matchingDates.length > 0 ? { match: true, dates: matchingDates.map(date => date.toFormat('MM-yyyy')) } : { match: false, dates: null };
    };
    const findIndexByDate = (array, dateToFind) => {
        const targetDate = DateTime.fromFormat(dateToFind, 'MM-yyyy');

        if (array.length === 1) {
            return 0; // If array length is 1, return index 0
        }

        let foundIndex = -1;

        // Check for an object with both start and end not null and matching the passed date
        const matchedObject = array.find(obj => {
            const startDate = DateTime.fromISO(obj.start);
            const endDate = DateTime.fromISO(obj.end);

            return startDate <= targetDate && targetDate <= endDate;
        });

        if (matchedObject) {
            return array.indexOf(matchedObject);
        }

        // Check for an object with end as null and start date lesser than passed date
        const endIsNullObject = array.find(obj => {
            const startDate = DateTime.fromISO(obj.start);
            return obj.end === null && startDate <= targetDate;
        });

        if (endIsNullObject) {
            return array.indexOf(endIsNullObject);
        }

        // If none of the above conditions match, find the object with the lesser start date
        let lesserStartDateObject = array.reduce((prev, curr) => {
            const prevStartDate = DateTime.fromISO(prev.start);
            const currStartDate = DateTime.fromISO(curr.start);
            return prevStartDate < currStartDate ? prev : curr;
        });

        return array.indexOf(lesserStartDateObject);
    };
    const getEmissionFactorCalculation = (item, year, locsubcat) => {
        let betweenMonths = getBetweenMonths(year)
        console.log(item.dcfId, item)
        let month_data = []
        if (item.dcfId === 11 || item.dcfId === 310) {
            const { match, dates } = isDateWithinPeriod(item.reporting_period, betweenMonths)
            console.log(match, dates)
            if (match) {

                const standard_index = apief.findIndex((i) => { return i.id === item.standard })
                console.log('Matched_SC', standard_index)
                if (standard_index !== -1) {
                    item.response.forEach((sc) => {
                        if (standard_index !== -1 && apief[standard_index].newEfDates !== undefined && apief[standard_index].newEfDates.length !== 0) {

                            dates.forEach((rpm) => {
                                let date_index = findIndexByDate(apief[standard_index].newEfDates, rpm)
                                console.log('Dates', date_index, rpm)
                                if (date_index !== -1 && apief[standard_index].newEfDates[date_index].newEfs !== undefined && apief[standard_index].newEfDates[date_index].newEfs.length !== 0) {
                                    let cat_index = apief[standard_index].newEfDates[date_index].newEfs.findIndex((i) => { return i.category === 1 })
                                    console.log(apief[standard_index].newEfDates[date_index].newEfs[cat_index])
                                    if (cat_index !== -1 && apief[standard_index].newEfDates[date_index].newEfs[cat_index].newEfItems !== undefined && apief[standard_index].newEfDates[date_index].newEfs[cat_index].newEfItems.length !== 0) {
                                        let sc_index = apief[standard_index].newEfDates[date_index].newEfs[cat_index].newEfItems.findIndex(i => { return i.subcategory1 === sc.DPA0130 && i.subcategory2 === sc.DPA0131 && i.subcategory3 === sc.DPA0132 })
                                        console.log(sc_index, sc, apief[standard_index].newEfDates[date_index].newEfs[cat_index].newEfItems)
                                        if (sc_index !== -1) {
                                            console.log(sc_index)
                                            let ef = apief[standard_index].newEfDates[date_index].newEfs[cat_index].newEfItems[sc_index]
                                            let ghg = 0
                                            console.log(sc.DPA0336, ef)
                                            let fuel_type_index = locsubcat.two.findIndex((i) => { return i.id === sc.DPA0131 })
                                            let unit_index = locsubcat.three.findIndex((i) => { return i.id === sc.DPA0132 })
                                            let fuel_cat_index = locsubcat.one.findIndex((i) => { return i.id === sc.DPA0130 })

                                            let fuel_type = 'Other', usage = 0, r = 0, nr = 0
                                            if (fuel_type_index !== -1) {
                                                fuel_type = locsubcat.two[fuel_type_index].title
                                            }

                                            if (locsubcat.three[fuel_type_index]?.title?.trim().toLowerCase().includes('kg')) {
                                                ghg = parseFloat((((parseFloat(sc.DPA0336) * (ef.co2e / 1000)) / dates.length) / 1000).toFixed(3))
                                            } else {
                                                ghg = parseFloat((((parseFloat(sc.DPA0336) * ef.co2e) / dates.length) / 1000).toFixed(3))
                                            }
                                            if (unit_index !== -1) {
                                                let unit_type = locsubcat.three[fuel_type_index].title

                                                if (unit_type.includes('ton')) {
                                                    if (fuel_cat_index !== -1) {
                                                        let cat_type = locsubcat.one[fuel_cat_index].title

                                                        if (cat_type.trim().toLowerCase().includes('bio')) {
                                                            r = parseFloat((((parseFloat(sc.DPA0336) * 0.82) * 10800 * 4.187) / 1000000000).toFixed(3))
                                                        } else {

                                                            nr = parseFloat((((parseFloat(sc.DPA0336) * 0.82) * 10800 * 4.187) / 1000000000).toFixed(3))

                                                        }
                                                    }
                                                    usage = parseFloat((sc.DPA0336 / 0.81).toFixed(3))

                                                } else {
                                                    if (fuel_cat_index !== -1) {

                                                        let cat_type = locsubcat.one[fuel_cat_index].title
                                                        if (cat_type.trim().toLowerCase().includes('bio')) {
                                                            r = parseFloat((((parseFloat(sc.DPA0336) * 0.82) * 10.8 * 4.187) / 1000000000).toFixed(3))
                                                        } else {

                                                            nr = parseFloat((((parseFloat(sc.DPA0336) * 0.82) * 10.8 * 4.187) / 1000000000).toFixed(3))
                                                        }
                                                    }
                                                    usage = parseFloat(sc.DPA0336)
                                                }
                                            }
                                            let month_index = month_data.findIndex((i) => { return i.scope === 'scope1' && i.fuel_type === fuel_type })
                                            if (month_index === -1) {
                                                month_data.push({ year: rpm.split('-')[1], ef, scope: 'scope1', ghg, fuel_type, usage, r, nr, dcfId: item.dcfId })
                                            } else {
                                                month_data[month_index].ghg += ghg
                                                month_data[month_index].usage += usage
                                                month_data[month_index].r += r
                                                month_data[month_index].nr += nr
                                            }

                                        }

                                    }
                                }

                            })
                        }
                    })
                }
            }

        } else if (item.dcfId === 10) {

            const { match, dates } = isDateWithinPeriod(item.reporting_period, betweenMonths)
            if (match) {
                const standard_index = apief.findIndex((i) => { return i.id === item.standard })
                if (standard_index !== -1) {
                    item.response.forEach((sc) => {
                        if (standard_index !== -1 && apief[standard_index].newEfDates !== undefined && apief[standard_index].newEfDates.length !== 0) {
                            dates.forEach((rpm) => {
                                let date_index = findIndexByDate(apief[standard_index].newEfDates, rpm)
                                console.log(date_index, apief[standard_index].newEfDates[date_index].newEfs)
                                if (date_index !== -1 && apief[standard_index].newEfDates[date_index].newEfs !== undefined && apief[standard_index].newEfDates[date_index].newEfs.length !== 0) {
                                    let cat_index = apief[standard_index].newEfDates[date_index].newEfs.findIndex((i) => { return i.category === 5 })
                                    console.log(apief[standard_index].newEfDates[date_index].newEfs[cat_index])
                                    if (cat_index !== -1 && apief[standard_index].newEfDates[date_index].newEfs[cat_index].newEfItems !== undefined && apief[standard_index].newEfDates[date_index].newEfs[cat_index].newEfItems.length !== 0) {
                                        let sc_index = apief[standard_index].newEfDates[date_index].newEfs[cat_index].newEfItems.findIndex(i => { return i.subcategory1 === sc.DPA0136 })
                                        console.log(sc_index, sc.DPA0136, apief[standard_index].newEfDates[date_index].newEfs[cat_index].newEfItems)
                                        if (sc_index !== -1) {
                                            console.log(apief[standard_index].newEfDates[date_index].newEfs[cat_index].newEfItems[sc_index], sc)
                                            let ef = apief[standard_index].newEfDates[date_index].newEfs[cat_index].newEfItems[sc_index]
                                            let ghg = parseFloat((((parseFloat(sc.DPA0138) * ef.co2e) / dates.length) / 1000).toFixed(3))
                                            console.log(sc.DPA0138, ef)
                                            let gas_type_index = locsubcat.one.findIndex((i) => { return i.id === sc.DPA0136 })
                                            let gas_type = 'Other'
                                            if (gas_type_index !== -1) {
                                                gas_type = locsubcat.one[gas_type_index].title
                                            }
                                            let month_index = month_data.findIndex((i) => { return i.scope === 'scope1' && i.gas_type === gas_type })
                                            if (month_index === -1) {
                                                month_data.push({ year: rpm.split('-')[1], month: rpm, ef, scope: 'scope1', ghg, gas_type, usage: parseFloat(sc.DPA0138), dcfId: item.dcfId })
                                            } else {
                                                month_data[month_index].ghg += ghg
                                                month_data[month_index].usage += parseFloat(sc.DPA0138)
                                            }

                                        }

                                    }
                                }

                            })
                        }
                    })
                }
            }

        } else if (item.dcfId === 16) {

            const { match, dates } = isDateWithinPeriod(item.reporting_period, betweenMonths)
            if (match) {
                const standard_index = apief.findIndex((i) => { return i.id === item.standard })
                if (standard_index !== -1) {
                    item.response.forEach((sc) => {
                        if (standard_index !== -1 && apief[standard_index].newEfDates !== undefined && apief[standard_index].newEfDates.length !== 0) {
                            dates.forEach((rpm) => {
                                let date_index = findIndexByDate(apief[standard_index].newEfDates, rpm)

                                if (date_index !== -1 && apief[standard_index].newEfDates[date_index].newEfs !== undefined && apief[standard_index].newEfDates[date_index].newEfs.length !== 0) {
                                    let cat_index = apief[standard_index].newEfDates[date_index].newEfs.findIndex((i) => { return i.category === 11 })
                                    console.log(apief[standard_index].newEfDates[date_index].newEfs[cat_index])
                                    if (cat_index !== -1 && apief[standard_index].newEfDates[date_index].newEfs[cat_index].newEfItems !== undefined && apief[standard_index].newEfDates[date_index].newEfs[cat_index].newEfItems.length !== 0) {
                                        let sc_index = apief[standard_index].newEfDates[date_index].newEfs[cat_index].newEfItems.findIndex(i => { return i.subcategory1 === sc.DPA0287 })

                                        if (sc_index !== -1) {
                                            let ef = apief[standard_index].newEfDates[date_index].newEfs[cat_index].newEfItems[sc_index]
                                            let ghg = parseFloat((((parseFloat(sc.DPA0288) * parseFloat(sc.DPA0289) * ef.co2e) / dates.length) / 1000).toFixed(3))

                                            let item_class_index = locsubcat.one.findIndex((i) => { return i.id === sc.DPA0287 })
                                            let item_class = 'Other'
                                            if (item_class_index !== -1) {
                                                item_class = locsubcat.one[item_class_index].title
                                            }
                                            let month_index = month_data.findIndex((i) => { return i.scope === 'scope1' && i.item_class === item_class })
                                            if (month_index === -1) {
                                                month_data.push({ year: rpm.split('-')[1], month: rpm, ef, scope: 'scope1', ghg, item_class, dcfId: item.dcfId })
                                            } else {
                                                month_data[month_index].ghg += ghg
                                            }

                                        }

                                    }
                                }

                            })
                        }
                    })
                }
            }

        } else if (item.dcfId === 15) {

            const { match, dates } = isDateWithinPeriod(item.reporting_period, betweenMonths)
            if (match) {
                console.log('MC', dates)
                const standard_index = apief.findIndex((i) => { return i.id === 1 })
                if (standard_index !== -1) {
                    item.response.forEach((sc) => {
                        if (standard_index !== -1 && apief[standard_index].newEfDates !== undefined && apief[standard_index].newEfDates.length !== 0) {
                            console.log('MC_STD', dates)
                            dates.forEach((rpm) => {
                                let date_index = findIndexByDate(apief[standard_index].newEfDates, rpm)
                                console.log('MC_Date', date_index)
                                if (date_index !== -1 && apief[standard_index].newEfDates[date_index].newEfs !== undefined && apief[standard_index].newEfDates[date_index].newEfs.length !== 0) {
                                    if (!sc.DP_MODE) {
                                        let cat_index = apief[standard_index].newEfDates[date_index].newEfs.findIndex((i) => { return i.category === 1 })
                                        console.log(apief[standard_index].newEfDates[date_index].newEfs[cat_index])
                                        if (cat_index !== -1 && apief[standard_index].newEfDates[date_index].newEfs[cat_index].newEfItems !== undefined && apief[standard_index].newEfDates[date_index].newEfs[cat_index].newEfItems.length !== 0) {
                                            let sc_index = -1
                                            if (sc.DPA0141 === 2) {
                                                sc_index = apief[standard_index].newEfDates[date_index].newEfs[cat_index].newEfItems.findIndex(i => { return i.subcategory1 === 1 && i.subcategory2 === sc.DPA0141 && i.subcategory3 === 7 })
                                            } else if (sc.DPA0141 === 13) {
                                                sc_index = apief[standard_index].newEfDates[date_index].newEfs[cat_index].newEfItems.findIndex(i => { return i.subcategory1 === 4 && i.subcategory2 === sc.DPA0141 && i.subcategory3 === 51 })
                                            } else if (sc.DPA0141 === 19) {
                                                sc_index = apief[standard_index].newEfDates[date_index].newEfs[cat_index].newEfItems.findIndex(i => { return i.subcategory1 === 4 && i.subcategory2 === sc.DPA0141 && i.subcategory3 === 75 })

                                            }
                                            if (sc_index !== -1) {
                                                let ef = apief[standard_index].newEfDates[date_index].newEfs[cat_index].newEfItems[sc_index]
                                                let ghg = parseFloat((((parseFloat(sc.DPA0143) * ef.co2e) / dates.length) / 1000).toFixed(3))

                                                let fuel_type_index = locsubcat.two.findIndex((i) => { return i.id === sc.DPA0141 })
                                                let fuel_type = 'Other'
                                                if (fuel_type_index !== -1) {
                                                    fuel_type = locsubcat.two[fuel_type_index].title
                                                }
                                                let month_index = month_data.findIndex((i) => { return i.scope === 'scope1' && i.type === 'byFuel' && i.fuel_type === fuel_type })
                                                if (month_index === -1) {
                                                    month_data.push({ year: rpm.split('-')[1], month: rpm, ef, scope: 'scope1', ghg, fuel_type, type: 'byFuel', dcfId: item.dcfId })
                                                } else {
                                                    month_data[month_index].ghg += ghg
                                                }

                                            }

                                        }
                                    } else {
                                        let cat_index = apief[standard_index].newEfDates[date_index].newEfs.findIndex((i) => { return i.category === sc.DPGMode })
                                        console.log(apief[standard_index].newEfDates[date_index].newEfs[cat_index])
                                        if (cat_index !== -1 && apief[standard_index].newEfDates[date_index].newEfs[cat_index].newEfItems !== undefined && apief[standard_index].newEfDates[date_index].newEfs[cat_index].newEfItems.length !== 0) {
                                            let sc_index = apief[standard_index].newEfDates[date_index].newEfs[cat_index].newEfItems.findIndex(i => { return i.subcategory1 === sc.DPA0140 && i.subcategory2 === sc.DPA0139 && i.subcategory3 === sc.DPA0141 && i.subcategory4 === sc.DPA0339 })

                                            if (sc_index !== -1) {
                                                let ef = apief[standard_index].newEfDates[date_index].newEfs[cat_index].newEfItems[sc_index]
                                                let ghg = parseFloat((((parseFloat(sc.DPA0144) * ef.co2e) / dates.length) / 1000).toFixed(3))

                                                let fuel_type_index = locsubcat.two.findIndex((i) => { return i.id === sc.DPA0139 })
                                                let fuel_type = 'Other'
                                                if (fuel_type_index !== -1) {
                                                    fuel_type = locsubcat.two[fuel_type_index].title
                                                }
                                                let month_index = month_data.findIndex((i) => { return i.scope === 'scope1' && i.fuel_type === fuel_type && i.type === 'byDistance' })
                                                if (month_index === -1) {
                                                    month_data.push({ year: rpm.split('-')[1], month: rpm, ef, scope: 'scope1', ghg, fuel_type, type: 'byDistance', dcfId: item.dcfId })
                                                } else {
                                                    month_data[month_index].ghg += ghg
                                                }

                                            }

                                        }
                                    }

                                }

                            })
                        }
                    })
                }
            }

        } else if (item.dcfId === 311) {

            const { match, dates } = isDateWithinPeriod(item.reporting_period, betweenMonths)
            if (match) {
                console.log('MC', dates, apief)
                const standard_index = apief.findIndex((i) => { return i.id === 29 })
                if (standard_index !== -1) {
                    item.response.forEach((sc) => {
                        if (standard_index !== -1 && apief[standard_index].newEfDates !== undefined && apief[standard_index].newEfDates.length !== 0) {
                            console.log('MC_STD', dates)
                            dates.forEach((rpm) => {
                                let date_index = findIndexByDate(apief[standard_index].newEfDates, rpm)
                                console.log('MC_Date', date_index)
                                if (date_index !== -1 && apief[standard_index].newEfDates[date_index].newEfs !== undefined && apief[standard_index].newEfDates[date_index].newEfs.length !== 0) {
                                    if (!sc.DP_MODE) {
                                        let cat_index = apief[standard_index].newEfDates[date_index].newEfs.findIndex((i) => { return i.category === 50 })
                                        console.log(apief[standard_index].newEfDates[date_index].newEfs[cat_index])
                                        if (cat_index !== -1 && apief[standard_index].newEfDates[date_index].newEfs[cat_index].newEfItems !== undefined && apief[standard_index].newEfDates[date_index].newEfs[cat_index].newEfItems.length !== 0) {
                                            let sc_index = apief[standard_index].newEfDates[date_index].newEfs[cat_index].newEfItems.findIndex(i => { return i.subcategory1 === 338 && i.subcategory2 === sc.DPAW0002 && i.subcategory3 === sc.DPA0141 && i.subcategory4 === sc.DPAW0007 })

                                            if (sc_index !== -1) {
                                                let ef = apief[standard_index].newEfDates[date_index].newEfs[cat_index].newEfItems[sc_index]
                                                let ghg = 0
                                                let unit_type = locsubcat.four.find((i) => { return i.id === sc.DPAW0007 })?.title || ''

                                                if (unit_type?.trim().toLowerCase() === 'kg') {
                                                    ghg = parseFloat((((parseFloat(sc.DPA0143) * (ef.co2e / 1000)) / dates.length) / 1000).toFixed(3))

                                                } else {
                                                    ghg = parseFloat((((parseFloat(sc.DPA0143) * (ef.co2e / 1000)) / dates.length)).toFixed(3))

                                                }

                                                let fuel_type_index = locsubcat.three.findIndex((i) => { return i.id === sc.DPA0141 })

                                                let fuel_type = 'Other'
                                                if (fuel_type_index !== -1) {
                                                    fuel_type = locsubcat.three[fuel_type_index].title + (unit_type ? '/' + unit_type : '')
                                                }
                                                if (fuel_type.trim().toLowerCase() === 'others') {
                                                    fuel_type = 'Others / ' + sc.DPAW0002A

                                                }
                                                let month_index = month_data.findIndex((i) => { return i.scope === 'scope1' && i.type === 'byFuel' && i.fuel_type === fuel_type })
                                                if (month_index === -1) {
                                                    month_data.push({ year: rpm.split('-')[1], month: rpm, ef, scope: 'scope1', ghg, fuel_type, type: 'byFuel', dcfId: item.dcfId })
                                                } else {
                                                    month_data[month_index].ghg += ghg
                                                }

                                            }

                                        }
                                    } else {
                                        let cat_index = apief[standard_index].newEfDates[date_index].newEfs.findIndex((i) => { return i.category === sc.DPGMode })
                                        console.log(apief[standard_index].newEfDates[date_index].newEfs[cat_index])
                                        if (cat_index !== -1 && apief[standard_index].newEfDates[date_index].newEfs[cat_index].newEfItems !== undefined && apief[standard_index].newEfDates[date_index].newEfs[cat_index].newEfItems.length !== 0) {
                                            let sc_index = apief[standard_index].newEfDates[date_index].newEfs[cat_index].newEfItems.findIndex(i => { return i.subcategory1 === sc.DPA0140 && i.subcategory2 === sc.DPA0139 && i.subcategory3 === sc.DPA0141 && i.subcategory4 === sc.DPA0339 })

                                            if (sc_index !== -1) {
                                                let ef = apief[standard_index].newEfDates[date_index].newEfs[cat_index].newEfItems[sc_index]
                                                let ghg = parseFloat((((parseFloat(sc.DPA0144) * ef.co2e) / dates.length) / 1000).toFixed(3))

                                                let fuel_type_index = locsubcat.two.findIndex((i) => { return i.id === sc.DPA0139 })
                                                let fuel_type = 'Other'
                                                if (fuel_type_index !== -1) {
                                                    fuel_type = locsubcat.two[fuel_type_index].title
                                                }
                                                let month_index = month_data.findIndex((i) => { return i.scope === 'scope1' && i.fuel_type === fuel_type && i.type === 'byDistance' })
                                                if (month_index === -1) {
                                                    month_data.push({ year: rpm.split('-')[1], month: rpm, ef, scope: 'scope1', ghg, fuel_type, type: 'byDistance', dcfId: item.dcfId })
                                                } else {
                                                    month_data[month_index].ghg += ghg
                                                }

                                            }

                                        }
                                    }

                                }

                            })
                        }
                    })
                }
            }

        }
        else if (item.dcfId === 257) {

            const { match, dates } = isDateWithinPeriod(item.reporting_period, betweenMonths)
            console.log('ELEC_Match', dates)

            if (match) {

                const standard_index = apief.findIndex((i) => { return i.id === item.standard })
                console.log('ELEC_std', standard_index)
                if (standard_index !== -1) {
                    item.response.forEach((sc) => {

                        if (standard_index !== -1 && apief[standard_index].newEfDates !== undefined && apief[standard_index].newEfDates.length !== 0) {
                            dates.forEach((rpm) => {
                                let date_index = findIndexByDate(apief[standard_index].newEfDates, rpm)
                                console.log(sc, item.id)
                                if (date_index !== -1 && apief[standard_index].newEfDates[date_index].newEfs !== undefined && apief[standard_index].newEfDates[date_index].newEfs.length !== 0) {
                                    let cat_index = apief[standard_index].newEfDates[date_index].newEfs.findIndex((i, j) => { return j === 0 })
                                    console.log(apief[standard_index].newEfDates[date_index].newEfs[cat_index])
                                    if (cat_index !== -1 && apief[standard_index].newEfDates[date_index].newEfs[cat_index].newEfItems !== undefined && apief[standard_index].newEfDates[date_index].newEfs[cat_index].newEfItems.length !== 0) {
                                        let sc_index = apief[standard_index].newEfDates[date_index].newEfs[cat_index].newEfItems.findIndex(i => { return i.subcategory1 === sc.DPAN095 && i.subcategory2 === sc.DPAN096 && i.subcategory3 === sc.DPAN099 })
                                        console.log(sc_index)
                                        if (sc_index !== -1) {
                                            let ef = apief[standard_index].newEfDates[date_index].newEfs[cat_index].newEfItems[sc_index]
                                            let ghg = 0

                                            console.log(sc.DPA0138, ef)
                                            let source_index = locsubcat.two.findIndex((i) => { return i.id === sc.DPAN096 })
                                            let type_index = locsubcat.one.findIndex((i) => { return i.id === sc.DPAN095 })
                                            let type = 'Other', source_type = 'Other', r = 0, nr = 0
                                            if (type_index !== -1) {
                                                type = locsubcat.one[type_index].title
                                            }
                                            if (source_index !== -1) {

                                                source_type = locsubcat.two[source_index].title
                                                console.log(source_type, sc.DPAN096)
                                                if (source_type.trim().toLowerCase().includes('non')) {
                                                    ghg = parseFloat((((parseFloat(sc.DPAN098) * ef.co2e) / dates.length) / 1000).toFixed(3))
                                                    nr = parseFloat(sc.DPAN098 / 1000)
                                                } else {
                                                    r = parseFloat(sc.DPAN098 / 1000)
                                                }
                                            }
                                            let month_index = month_data.findIndex((i) => { return i.scope === 2 && i.source_type === source_type && i.type === type })
                                            if (month_index === -1) {
                                                month_data.push({ year: rpm.split('-')[1], month: rpm, ef, scope: 2, ghg, type, source_type, r, nr, dcfId: item.dcfId })
                                            } else {
                                                month_data[month_index].ghg += ghg
                                                month_data[month_index].r += r
                                                month_data[month_index].nr += nr
                                            }

                                        } else {

                                            if (locsubcat.one.map(i => i.id).includes(sc.DPAN095) && locsubcat.two.map(i => i.id).includes(sc.DPAN096) && locsubcat.three.map(i => i.id).includes(sc.DPAN099)) {
                                                let source_type = '', r = 0, nr = 0, type = ''
                                                let source_index = locsubcat.two.findIndex((i) => { return i.id === sc.DPAN096 })
                                                let type_index = locsubcat.one.findIndex((i) => { return i.id === sc.DPAN095 })
                                                type = locsubcat.one[type_index].title
                                                source_type = locsubcat.two[source_index].title
                                                if (!source_type.trim().toLowerCase().includes('non')) {
                                                    r = parseFloat(sc.DPAN098 / 1000)
                                                }
                                                let month_index = month_data.findIndex((i) => { return i.scope === 2 && i.source_type === source_type && i.type === type })
                                                if (month_index === -1) {
                                                    month_data.push({ year: rpm.split('-')[1], month: rpm, ef: {}, scope: 2, ghg: 0, type, source_type, r, nr, dcfId: item.dcfId })
                                                } else {
                                                    month_data[month_index].ghg += 0
                                                    month_data[month_index].r += r
                                                    month_data[month_index].nr += nr
                                                }
                                            }
                                        }

                                    }
                                }

                            })
                        }
                    })
                }
            }

        } else if (item.dcfId === 36) {
            const { match, dates } = isDateWithinPeriod(item.reporting_period, betweenMonths)
            if (match) {
                const standard_index = apief.findIndex((i) => { return i.id === item.standard })
                if (standard_index !== -1) {
                    item.response.forEach((sc) => {
                        if (sc.DPA0291 === 'Road') {
                            if (standard_index !== -1 && apief[standard_index].newEfDates !== undefined && apief[standard_index].newEfDates.length !== 0) {

                                let date_index = findIndexByDate(apief[standard_index].newEfDates, item.reporting_period[0])
                                console.log(date_index, apief[standard_index].newEfDates[date_index].newEfs)
                                if (date_index !== -1 && apief[standard_index].newEfDates[date_index].newEfs !== undefined && apief[standard_index].newEfDates[date_index].newEfs.length !== 0) {
                                    let cat_index = apief[standard_index].newEfDates[date_index].newEfs.findIndex((i) => { return i.category === 6 })
                                    console.log(apief[standard_index].newEfDates[date_index].newEfs[cat_index])
                                    if (cat_index !== -1 && apief[standard_index].newEfDates[date_index].newEfs[cat_index].newEfItems !== undefined && apief[standard_index].newEfDates[date_index].newEfs[cat_index].newEfItems.length !== 0) {
                                        let sc_index = apief[standard_index].newEfDates[date_index].newEfs[cat_index].newEfItems.findIndex(i => { return i.subcategory1 === sc.DPA0295 && i.subcategory2 === sc.DPA0337 && i.subcategory3 === sc.DPA0338 && i.subcategory4 === sc.DPA0338G })
                                        console.log(sc_index, apief[standard_index].newEfDates[date_index].newEfs[cat_index].newEfItems)
                                        if (sc_index !== -1) {
                                            console.log(apief[standard_index].newEfDates[date_index].newEfs[cat_index].newEfItems[sc_index], sc)
                                            let ef = apief[standard_index].newEfDates[date_index].newEfs[cat_index].newEfItems[sc_index]
                                            let ghg = parseFloat((((parseFloat(sc.DPA0292) * ef.co2e)) / 1000).toFixed(3))




                                            let month_index = month_data.findIndex((i) => { return i.scope === 'scope3' && i.mode === 'Road' })
                                            if (month_index !== -1) {
                                                month_data[month_index].ghg += ghg
                                                month_data[month_index].passenger += parseFloat(sc.DPA0292)
                                            } else {
                                                month_data.push({ year: item.reporting_period[0].split('-')[1], ef, mode: 'Road', scope: 'scope3', ghg, passenger: parseFloat(sc.DPA0292) })
                                            }

                                        }

                                    }
                                }


                            }
                        } else if (sc.DPA0291 === 'Air') {

                            let ghg = sc.DP_co2e_mt
                            let month_index = month_data.findIndex((i) => { return i.scope === 'scope3' && i.mode === 'Air' })

                            if (month_index !== -1) {
                                month_data[month_index].ghg += ghg
                                month_data[month_index].passenger += parseFloat(sc.DPA0292)
                            } else {
                                month_data.push({ year: item.reporting_period[0].split('-')[1], mode: 'Air', scope: 'scope3', ghg, passenger: parseFloat(sc.DPA0292) })
                            }
                        } else if (sc.DPA0291 === 'Rail') {

                            let ghg = parseFloat(sc.DPA0292) * sc.DP_KM * 0.00116
                            let month_index = month_data.findIndex((i) => { return i.scope === 'scope3' && i.mode === 'Rail' })

                            if (month_index !== -1) {
                                month_data[month_index].ghg += ghg
                                month_data[month_index].passenger += parseFloat(sc.DPA0292)
                            } else {
                                month_data.push({ year: item.reporting_period[0].split('-')[1], mode: 'Rail', scope: 'scope3', ghg, passenger: parseFloat(sc.DPA0292) })
                            }
                        }

                    })
                }
            }
        } else if (item.dcfId === 292) {
            console.log(item)
            const { match, dates } = isDateWithinPeriod(item.reporting_period, betweenMonths)
            if (match) {
                const standard_index = apief.findIndex((i) => { return i.id === item.standard })
                if (standard_index !== -1) {
                    item.response.forEach((sc) => {
                        if (standard_index !== -1 && apief[standard_index].newEfDates !== undefined && apief[standard_index].newEfDates.length !== 0) {
                            dates.forEach((rpm) => {
                                let date_index = findIndexByDate(apief[standard_index].newEfDates, rpm)

                                if (date_index !== -1 && apief[standard_index].newEfDates[date_index].newEfs !== undefined && apief[standard_index].newEfDates[date_index].newEfs.length !== 0) {

                                    let cat_index = apief[standard_index].newEfDates[date_index].newEfs.findIndex((i) => { return i.category === 42 })
                                    console.log(apief[standard_index].newEfDates[date_index].newEfs[cat_index])
                                    if (cat_index !== -1 && apief[standard_index].newEfDates[date_index].newEfs[cat_index].newEfItems !== undefined && apief[standard_index].newEfDates[date_index].newEfs[cat_index].newEfItems.length !== 0) {
                                        let sc_index = -1
                                        if (sc.DPAN1150 === 3) {
                                            sc_index = apief[standard_index].newEfDates[date_index].newEfs[cat_index].newEfItems.findIndex(i => { return i.subcategory1 === sc.DPAN1151 && i.subcategory2 === sc.DPAN1152 && i.subcategory3 === sc.DPAN1155 })
                                        } else {
                                            if (sc.DPAN1151 === 316 || sc.DPAN1151 === 317) {
                                                sc_index = apief[standard_index].newEfDates[date_index].newEfs[cat_index].newEfItems.findIndex(i => { return i.subcategory1 === sc.DPAN1151 && i.subcategory2 === sc.DPAN1152 && i.subcategory3 === sc.DPAN1154 && i.subcategory4 === sc.DPAN1155 })
                                            } else {
                                                sc_index = apief[standard_index].newEfDates[date_index].newEfs[cat_index].newEfItems.findIndex(i => { return i.subcategory1 === sc.DPAN1151 && i.subcategory2 === sc.DPAN1152 && i.subcategory3 === sc.DPAN1153 && i.subcategory4 === sc.DPAN1155 })
                                            }
                                        }

                                        const mode_options = [{ name: 'Road', id: 1 }, { name: 'Air', id: 2 }, { name: 'Rail', id: 3 }, { name: 'Sea', id: 4 }]
                                        if (sc_index !== -1) {
                                            let ef = apief[standard_index].newEfDates[date_index].newEfs[cat_index].newEfItems[sc_index]
                                            let ghg = parseFloat((((parseFloat(sc.DPAN1156) * parseFloat(sc.DPAN1157) * ef.co2e) / dates.length) / 1000).toFixed(3))

                                            let mode_index = mode_options.findIndex((i) => { return i.id === sc.DPAN1150 })
                                            let mode = 'Other'
                                            if (mode_index !== -1) {
                                                mode = mode_options[mode_index].title
                                            }
                                            let month_index = month_data.findIndex((i) => { return i.scope === 'scope1' && i.mode === mode })
                                            if (month_index === -1) {
                                                month_data.push({ year: rpm.split('-')[1], month: rpm, ef, scope: 'scope1', ghg, mode, dcfId: item.dcfId })
                                            } else {
                                                month_data[month_index].ghg += ghg
                                            }

                                        }

                                    }


                                }

                            })
                        }
                    })
                }
            }

        } else if (item.dcfId === 293) {
            console.log(item)
            const { match, dates } = isDateWithinPeriod(item.reporting_period, betweenMonths)
            if (match) {
                const standard_index = apief.findIndex((i) => { return i.id === item.standard })
                if (standard_index !== -1) {
                    item.response.forEach((sc) => {
                        if (standard_index !== -1 && apief[standard_index].newEfDates !== undefined && apief[standard_index].newEfDates.length !== 0) {
                            dates.forEach((rpm) => {
                                let date_index = findIndexByDate(apief[standard_index].newEfDates, rpm)

                                if (date_index !== -1 && apief[standard_index].newEfDates[date_index].newEfs !== undefined && apief[standard_index].newEfDates[date_index].newEfs.length !== 0) {

                                    let cat_index = apief[standard_index].newEfDates[date_index].newEfs.findIndex((i) => { return i.category === 42 })
                                    console.log(apief[standard_index].newEfDates[date_index].newEfs[cat_index])
                                    if (cat_index !== -1 && apief[standard_index].newEfDates[date_index].newEfs[cat_index].newEfItems !== undefined && apief[standard_index].newEfDates[date_index].newEfs[cat_index].newEfItems.length !== 0) {
                                        let sc_index = -1
                                        if (sc.DPAN1208 === 3) {
                                            sc_index = apief[standard_index].newEfDates[date_index].newEfs[cat_index].newEfItems.findIndex(i => { return i.subcategory1 === sc.DPAN1209 && i.subcategory2 === sc.DPAN1210 && i.subcategory3 === sc.DPAN1213 })
                                        } else {
                                            if (sc.DPAN1209 === 316 || sc.DPAN1209 === 317) {
                                                sc_index = apief[standard_index].newEfDates[date_index].newEfs[cat_index].newEfItems.findIndex(i => { return i.subcategory1 === sc.DPAN1209 && i.subcategory2 === sc.DPAN1210 && i.subcategory3 === sc.DPAN1212 && i.subcategory4 === sc.DPAN1213 })
                                            } else {
                                                sc_index = apief[standard_index].newEfDates[date_index].newEfs[cat_index].newEfItems.findIndex(i => { return i.subcategory1 === sc.DPAN1209 && i.subcategory2 === sc.DPAN1210 && i.subcategory3 === sc.DPAN1211 && i.subcategory4 === sc.DPAN1213 })
                                            }
                                        }

                                        const mode_options = [{ name: 'Road', id: 1 }, { name: 'Air', id: 2 }, { name: 'Rail', id: 3 }, { name: 'Sea', id: 4 }]
                                        if (sc_index !== -1) {
                                            let ef = apief[standard_index].newEfDates[date_index].newEfs[cat_index].newEfItems[sc_index]
                                            let ghg = parseFloat((((parseFloat(sc.DPAN1214) * parseFloat(sc.DPAN1215) * ef.co2e) / dates.length) / 1000).toFixed(3))

                                            let mode_index = mode_options.findIndex((i) => { return i.id === sc.DPAN1208 })
                                            let mode = 'Other'
                                            if (mode_index !== -1) {
                                                mode = mode_options[mode_index].title
                                            }
                                            let month_index = month_data.findIndex((i) => { return i.scope === 'scope3' && i.mode === mode })
                                            if (month_index === -1) {
                                                month_data.push({ year: rpm.split('-')[1], month: rpm, ef, scope: 'scope3', ghg, mode, dcfId: item.dcfId })
                                            } else {
                                                month_data[month_index].ghg += ghg
                                            }

                                        }

                                    }


                                }

                            })
                        }
                    })
                }
            }

        }
        console.log(month_data, item.dcfId)
        return month_data
    }

    const showSite = () => {
        let site_ids = [], site_names = []
        // dcfass.forEach((i) => {
        //     !site_ids.includes(i.site) && site_ids.push(i.site)
        // })

        site_ids.forEach((i, j) => {
            site_names.push(locationList.find((k) => { return k.id === i }).title)

        })

        return site_names
    }
    return (
        <div>
            {/* <img id='page1' src={require('../reports/assets/cfp_image.png').default} width={'100%'} hidden /> */}
            <img id='page1' src={require('../reports/assets/cfp_co2.png').default} width={'100%'} hidden />
            <div className="col-12" style={{ display: "flex", flexDirection: "row", justifyContent: "space-between" }}>
                {/* nav bar     */}
                {/* <div className="col-3 p-card" style={{ margin: 5, height: "calc(100vh - 9rem)", overflow: "scroll" }}>
                    <nav>
                        {headings.map((heading, ind) => {
                            let indexes = [];
                            return (
                                <>
                                    {heading.level === 0 ? (
                                        <label key={heading.id} style={{ display: "flex", margin: 5, fontWeight: activeId === heading.id ? "bold" : "normal", textDecoration: heading.text.includes("SECTION") && "underline" }} className={getClassName(heading.level)}>
                                            <a
                                                href={`#${heading.id}`}
                                                onClick={(e) => {
                                                    e.preventDefault();
                                                    document.querySelector(`#${heading.id}`).scrollIntoView({
                                                        behavior: "smooth",
                                                        block: "start",
                                                        inline: "nearest",
                                                    });
                                                }}
                                                style={{
                                                    fontWeight: activeId === heading.id ? "bold" : "normal",
                                                }}
                                            >
                                                {heading.text}
                                            </a>
                                        </label>
                                    ) : (
                                        <ul>
                                            {heading.item.map((item, ind2) => {
                                                return (
                                                    <li key={item.id} className={getClassName(item.level)}>
                                                        <a
                                                            href={`#${item.id}`}
                                                            onClick={(e) => {
                                                                e.preventDefault();
                                                                document.querySelector(`#${item.id}`).scrollIntoView({
                                                                    behavior: "smooth",
                                                                });
                                                            }}
                                                            style={{
                                                                fontWeight: activeId === item.id ? "bold" : "normal",
                                                            }}
                                                        >
                                                            {item.text}
                                                        </a>
                                                    </li>
                                                );
                                            })}
                                        </ul>
                                    )}
                                </>
                            );
                        })}
                    </nav>
                </div> */}

                <div className="col-12 p-card" style={{ margin: 5, height: "calc(100vh - 9rem)", overflow: "scroll", color: "white" }}>
                    <div className="col-12" style={{ display: 'flex', justifyContent: 'flex-end' }} >

                        <Button icon='pi pi-eye' style={{ marginRight: 10 }} rounded text raised aria-label="Filter" onClick={() => { exportTable2Excel(1) }}> </Button>

                        <Button icon='pi pi-cloud-download' rounded text raised aria-label="Filter" onClick={() => { exportTable2Excel(0) }}> </Button>



                    </div>
                    <div style={{ display: "flex", flexDirection: "column" }}>
                        {/* <img id='ass' src='https://mpower-s3.s3-ap-southeast-1.amazonaws.com/uploads/hazards/1690426362525Pushing%20and%20Pulling.png' width={'100%'} /> */}
                        <div>
                            <div className="col-12 grid" style={{ margin: 5, color: "white" }}>
                                <div>
                                    <label
                                        style={{
                                            color: "black",
                                            padding: 15,
                                            justifyContent: "flex-start",
                                            display: "flex",
                                        }}
                                    >
                                        Select Year :
                                    </label>
                                </div>
                                <div className="col-4">
                                    <Dropdown options={[{ name: 2022 }, { name: 2023 }, { name: 2024 }, { name: 2025 }]} value={year} optionLabel="name" optionValue="name" onChange={(e) => { setYear(e.value) }} />
                                </div>
                            </div>
                            {/* <input type="file" onChange={handleFileChange} /> */}
                            {/* {tableData.length > 0 && (
                                <div className="gridlines-container">
                                    <table className="gridlines">
                                        <thead>
                                            <tr>
                                                {Object.keys(tableData[0]).map((field, index) => (
                                                    <th key={index} colSpan={getCellColSpan(0, index)}>
                                                        {field}
                                                    </th>
                                                ))}
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {tableData.map((row, rowIndex) => (
                                                <tr key={rowIndex}>
                                                    {Object.keys(row).map((cellRef, colIndex) => {
                                                        const cellValue = row[cellRef];
                                                        const colSpan = getCellColSpan(rowIndex + 1, colIndex); // Increment rowIndex to exclude header
                                                        const rowSpan = getCellRowSpan(rowIndex + 1, colIndex); // Increment rowIndex to exclude header
                                                        return (
                                                            <td key={colIndex} colSpan={colSpan} rowSpan={rowSpan} className={cellValue === "" && workbook.Sheets[firstSheet][cellRef]?.s?.b ? "empty-cell" : ""}>
                                                                {cellValue}
                                                            </td>
                                                        );
                                                    })}
                                                </tr>
                                            ))}
                                        </tbody>
                                    </table>
                                </div>
                            )} */}
                        </div>
                        <div id='main' style={{ flexDirection: 'column', display: 'flex' }}>
                            <sectionheader id={"es"} className="secHead">
                                Executive Summary
                            </sectionheader>

                            <div className='m-3 para'>

                                {selector.information.companyname}  is calculating and reporting on its carbon footprint as per The GHG protocol Corporate Accounting Reporting Standard within its operational boundaries covering CO2, CH4, N2O and refrigerant gases. The company’s Carbon footprint calculations cover {dcfass.findIndex((l) => { return [11, 10, 15].includes(l.dcfId) }) !== -1 && 'Scope 1 (Direct Emissions) '} {dcfass.findIndex((l) => { return [72].includes(l.dcfId) }) !== -1 && ', Scope 2 (Indirect Emissions- Purchased Energy)'}  {dcfass.findIndex((l) => { return [36, 16].includes(l.dcfId) }) !== -1 && (', Scope 3 (Indirect Emissions) ( ' + (checkScope([36]) && 'Business Travel, ') + (checkScope([16]) && 'Purchased Goods and Services') + ')')}   of the operation at the manufacturing units in


                            </div>
                            <div className='m-3 para'>
                                {
                                    showSite().length !== 0 &&
                                    <ol>

                                        {
                                            showSite().map((site) => {

                                                return (
                                                    <li>{site}</li>
                                                )
                                            })}
                                    </ol>
                                }
                            </div>
                            <div className='m-3 para'>

                                This carbon footprint report for FY {year}-{(year + 1).toString().substr(2, 3)} has been prepared in full accordance with The Greenhouse Gas Protocol, the most widely used international carbon calculation methodology, compatible with other GHG standards such as ISO 14064.

                            </div>
                            <div className='m-3 para'>


                                The emitting activities covered in the carbon footprint report for{year}-{(year + 1).toString().substr(2, 3)} include direct emissions resulting from {selector.information.companyname}  owned or controlled equipment and emissions from purchased electricity referred to as Scope 1 and 2 emissions respectively. {selector.information.companyname}  has gone to all reasonable lengths to ensure the accuracy of this report

                            </div>

                            <div class="gridlines-container">
                                <table class="gridlines">
                                    <tbody>
                                        <tr>
                                            <td colspan="1" rowspan="1" className="tdHead">
                                                Base Year
                                            </td>
                                            <td colspan="1" rowspan="1" className="tdHead">
                                                FY {year}-{(year + 1).toString().substr(2, 3)}
                                            </td>
                                        </tr>

                                        <tr>
                                            <td colspan="1" rowspan="1">
                                                Approach to boundary identification
                                            </td>
                                            <td colspan="1" rowspan="1">
                                                Operational Control Approach
                                            </td>
                                        </tr>

                                        <tr>
                                            <td colspan="1" rowspan="1">
                                                Boundary of the footprint
                                            </td>
                                            <td colspan="1" rowspan="1">
                                                {/* {getLastResponseByRFID(41, "text-1692161963729-0")} */}
                                            </td>
                                        </tr>

                                        <tr>
                                            <td colspan="1" rowspan="2">
                                                Gases Covered
                                            </td>
                                            <td colspan="1" rowspan="1">
                                                Primarily CO2 wherever possible CH4 and N2O
                                            </td>

                                        </tr>
                                        <tr>
                                            <td colspan="1" rowspan="1">
                                                Refrigerant as applicable
                                            </td>
                                        </tr>

                                        <tr>
                                            <td colspan="1" rowspan="1">
                                                Standard Used
                                            </td>
                                            <td colspan="1" rowspan="1">
                                                The GHG Protocol Corporate Standard
                                            </td>

                                        </tr>
                                    </tbody>
                                </table>
                            </div>

                            {renderEmissionProfileTable(`Emission Profile of ` + selector.information.companyname)}
                            <sectionheader id={"aaa"} >
                                Acronyms and Abbreviations
                            </sectionheader>
                            <div class="gridlines-container">
                                <table class="gridlines">
                                    <tbody>

                                        <tr colSpan="3">
                                            <td colSpan="3" className="tdHead">Acronyms and Abbreviations</td>
                                        </tr>
                                        <tr colSpan="3">
                                            <td colSpan="1" className="text-center">CO<sub>2</sub></td>
                                            <td colSpan="2" >Carbon dioxide</td>
                                        </tr>
                                        <tr colSpan="3">
                                            <td colSpan="1" className="text-center">CO<sub>2</sub>e</td>
                                            <td colSpan="2" >Carbon dioxide Equivalent</td>
                                        </tr>
                                        <tr colSpan="3">
                                            <td colSpan="1" className="text-center">DEFRA	</td>
                                            <td colSpan="2" >Department for Environment, Food and Rural Affairs (UK)</td>
                                        </tr>
                                        <tr colSpan="3">
                                            <td colSpan="1" className="text-center">GHG</td>
                                            <td colSpan="2" >Greenhouse gas</td>
                                        </tr>
                                        <tr colSpan="3">
                                            <td colSpan="1" className="text-center">GJ</td>
                                            <td colSpan="2" >Gigajoule</td>
                                        </tr>
                                        <tr colSpan="3">
                                            <td colSpan="1" className="text-center" >GRI</td>
                                            <td colSpan="2" >Global Reporting Initiative</td>
                                        </tr>
                                        <tr colSpan="3">
                                            <td colSpan="1" className="text-center">IPCC</td>
                                            <td colSpan="2" >Inter Governmental Panel on Climate Change</td>
                                        </tr>
                                        <tr colSpan="3">
                                            <td colSpan="1" className="text-center">KG</td>
                                            <td colSpan="2" >Kilogram</td>
                                        </tr>
                                        <tr colSpan="3">
                                            <td colSpan="1" className="text-center">Km	</td>
                                            <td colSpan="2" >Kilometre</td>
                                        </tr>
                                        <tr colSpan="3">
                                            <td colSpan="1" className="text-center">KWH</td>
                                            <td colSpan="2" >Kilowatt-hour</td>
                                        </tr>
                                        <tr colSpan="3">
                                            <td colSpan="1" className="text-center">L</td>
                                            <td colSpan="2" >Litre</td>
                                        </tr>
                                        <tr colSpan="3">
                                            <td colSpan="1" className="text-center">M</td>
                                            <td colSpan="2" >Meter</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            <sectionheader id={"op"} className="secHead">
                                Organizational Profile
                            </sectionheader>
                            <sectionheader id={"sidam"} className="secHead">
                                GHG Inventory Design And Methodology
                            </sectionheader>
                            <sectionheader1 id={'methodology'} >
                                <span className="text-under"> METHODOLOGY</span>
                                <div className='m-3 para'>

                                    The procedure used to quantify and report the GHG emissions for the FY {year}-{(year + 1).toString().substr(2, 3)} is in accordance to  “The GHG Protocol Corporate Accounting and Reporting Standard – Revised Edition’. This standard was developed by the World Resource Institute (WRI) and the World Business Council for Sustainable Development (WBCSD) as it provides a step-by-step guide for companies to use in quantifying and reporting their GHG emissions. The GHG Protocol is the most widely used standard among the governments and companies to understand, quantify, and manage their GHG emissions.
                                    The accounting and reporting of the GHG emissions of {selector.information.companyname} is based on the following principles of The GHG Protocol


                                </div>

                            </sectionheader1>
                            <sectionheader1 id={'relevance'}>
                                <span className="text-under"> RELEVANCE</span>

                                <div className='m-3 para'>

                                    {selector.information.companyname}  ensures the selected GHG inventory appropriately reflects the GHG emissions of the company which serves the decision-making needs of the stakeholders to the company.{selector.information.companyname} started the GHG inventorization by calculating its scope 1 and scope 2 emissions and scope 3 emissions  {('( ' + (checkScope([36]) && 'Business Travel, ') + (checkScope([16]) && 'Purchased Goods and Services') + ')')} .

                                </div>

                            </sectionheader1>
                            <sectionheader1 id={'completeness'}>
                                <span className="text-under"> COMPLETENESS</span>

                                <div className='m-3 para'>

                                    {selector.information.companyname}  has put all its effort to account and report on all the GHG emission sources and activities within the chosen inventory boundary for compiling a comprehensive and meaningful inventory. Company has disclosed the exclusions with a proper justification.

                                </div>

                            </sectionheader1>
                            <sectionheader1 id={'consistency'}>
                                <span className="text-under"> CONSISTENCY</span>


                                <div className='m-3 para'>

                                    {selector.information.companyname}  has applied the consistent accounting approach and calculating methodology within the defined inventory boundary in accordance to the GHG protocol corporate standard.

                                </div>

                            </sectionheader1>
                            <sectionheader1 id={'transparency'}>
                                <span className="text-under"> TRANSPARENCY</span>


                                <div className='m-3 para'>

                                    {selector.information.companyname}  addresses all relevant issues in a factual and coherent manner, based on a clear audit trail. Specific exclusions or inclusions are clearly identified and justified.  Also, the company has disclosed relevant assumptions and mentioned appropriate references to the methodologies and data sources used in GHG calculations.

                                </div>

                            </sectionheader1>
                            <sectionheader1 id={'accuracy'}>
                                <span className="text-under"> ACCURACY</span>


                                <div className='m-3 para'>

                                    {selector.information.companyname}  ensures that the quantification of GHG emissions is systematically carried out in accordance to the global standards accepted across the industry and avoids the uncertainties as far as practicable.
                                    The quantification methodology used to calculate emissions was based on either activity data collected from the organization or robust estimates using appropriate assumptions, multiplied by relevant and up-to-date emissions factors. Calculations and the use of emission factors were all based on the standards set by the GHG Protocol.


                                </div>

                            </sectionheader1>
                            <sectionheader id={'organizational'} className="secHead">
                                Organizational Boundaries
                            </sectionheader>
                            <div class="gridlines-container">
                                <table class="gridlines">
                                    <tbody>
                                        <tr>
                                            <td colspan="1" rowspan="1" className="tdHead">S.No
                                            </td>

                                            <td colspan="1" rowspan="1" className="tdHead"> Manufacturing Unit/ Office
                                            </td>

                                            <td colspan="1" rowspan="1" className="tdHead">Number of Employees Working
                                            </td>

                                        </tr>
                                        {locationList.map((site, sno) => {
                                            return (
                                                <tr>
                                                    <td colspan="1" rowspan="1">{sno + 1}</td>
                                                    <td colspan="1" rowspan="1">{site.title}</td>
                                                    <td colspan="1" rowspan="1"></td>

                                                </tr>
                                            )
                                        })
                                        }


                                    </tbody>
                                </table>
                            </div>
                            <sectionheader id={'organbouninvinc'} className="secHead">
                                Operational Boundaries and Inventory Inclusions
                            </sectionheader>
                            <div className='m-3 para'>

                                Definition: Operational Boundaries requires choosing the scope of emissions that will be reported. There are three scopes of emissions that can be reported:
                                <br />Scope 1: Direct GHG Emissions from company owned or controlled sources
                                <br />Scope 2: Indirect GHG Emissions from purchased electricity or steam.
                                <br /> According the GHG Protocol Corporate Reporting Standard, Scope 1 and Scope 2 emissions must be reported. Scope 3 emissions are voluntary.


                            </div>
                            <div style={{ display: 'flex' }} className='m-3  justify-content-center'>
                                <img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAgAAAAFJCAIAAABipQKUAAAAA3NCSVQICAjb4U/gAAAgAElEQVR4nOy9d5wkV30v+junqjpN2pmd3ZndmdnZ2ZwUNmhX0ioHlMAiXZtwscHhgQFjfLn24z6/C8LGGPtiwDYiWYhgBDIgJIQkJCEhaSXtalerzXlyzrGnQ4VzfveP011dnWY6VPf0zNZX81l1qDp9qs6pXw4EEcGBAwcOHFx+kBd6Ag4KCM45RwSTxxNCCKGEEEIWdF4OHDgoCTgMYKmBMcYY0w0DEVOqd4L8S5Iky7IsOxvAgYPLF8QxAS0ZaJqmGwbnPEMBHxEJIbIkuVwuSmmhp+fAgYNSg8MAlgI0TdN0XRD0HE5HRFmSPB6PYxpaXBDaHhewPMhE2PoolSiVZJk6y+ogDRwGsLjBGAuFwzmTfisQ0e12uxTFlok5KByEiU/XdQCYe90RABAJIS6XS5Flh8E7SIDDABYxVE3TNM3GpxoRJUnyOqpAqcIwDFXTMrfymUAA4fhxO+Y+BxY4DGCxIhQOG4ZRIErt8/kcu0FJgXMeVlXGWP4rrsiy2+22ZVYOFjscBrAoEQyFbKEF6YCIZWVlDg8oEWi6rqqqXcsttAGf1+uoAg4cBrD4UFDZ34oyn8+xBS04CrTciOjxeBQnDvjyhiMCLDJomlYc6g8AwVCoCL/iYA4EQyGjMKoeISQcDmu6bvvIDhYRHAZQ6kBEHgVjTLXV6zs3hN25OL/lIBkRQ1/BxieEqKqqOzzgMoajAJYiOOeGYRiGgQAJCb3FtMkQQnRdV2RZkqSi/agDgVA4XFA3jwAhJKyqlFJniS9POD6A0oLBmKZpRXjyMwchpMznW+hZXF7QdT1sn9d3XiBieVlZ6Ww5B0WDwwBKBTbG+dmLdN5CUzUhTnW5PMA5Z5xzxkTSFgAAIYZhFHkalFKf11vkHy0UokTNTJB09mc6LB0TkKBHhmFwjEF8RaKglMqSVIIEy4zzK7WJAQAhRNM0wQAQUdU0xpi4t+L+kmgGmSRJiiyXTnChqIRqMMY5T/6WEiJJktgVxZ8bImq6ruu6yUSLPwcrRHbxIg0KEkyUGQZHtK61CHg1IVFKCJFlWax78edZglj0GgBjzDAMgzFB8eddV3EMJUSS5RKhVqqqarpeyjsSEX1eryRJnPNAMDjHVAUncLlc8gLZlBFRbAnG+bxbAhEF6RU1c2RJKsJ+QERVVXXDWHiqn4TysrKFnkIWYJzrus6iDD6rGoiCE5QIBVhALFYGIAQoQ9d5HmVwEJFSqsiyoigL9TCGVVUvbeovIKrFAUAwGOTz7ZkFKSnBGNN0XWgnORfFE0qMUrCCSLphhMPh0lzuhGJQiBitM8eFbYpQSkWNuYX2GOu6rul6DiUxEhCRVxTlsq2LvvgYgBCgDMZsGxCAAMiy7Cl6fry9GZ4FBQEoKysDoa8YRiYzRkSv11sEVSDnCjkpIfiHoihulyv/0awIh8N6sXI4coO5yoZhhMJhSBKrzepygm4WnxPkWfg2JYQg6Ha5LkM2sMgYgO3lz6xARLfL5bL7sU+Hec0pJQUzUIQjhkKhDKktIno9nsI9V4zzcDhsF+lPgL3lUUMFS+myEYjo8/kkShFxNhCYp9QooiRJHre7OFYUGwvfpoRgA16P57IyCi0aBhBZ/nivju0Q26s4ZVICgcDiuPUAkCTOa5qWYUpa4fSAIgjUdhGFRUH9BUxVOBAMZkIcENHlctmuLSWgaMpTcS6ndLA4GEBBBf9kFEEVKHKgty2ghLg9HilKCg3GwuFwJifaHmbOOQ+FQrzA0oCJPPdDkXdvnjDTPjKPTRCqQIGiSDnnRS5JgogSpV6fb3EsWH5YBAyg0JUvUwIRZVn2ejwFGn82ECjQyAUFIiqy7IneFsZYMBzOZGFsDDNfEN6Z835YXIY+iOfWmet5UJhMAoOxUCi0UHfvciiYWtKXh4iBQKBAFt65QQhhjAWCwUIMbsbRLzoQQnTDmA0ExPwlSfK63ZlciwjNzH8CqqYtiOZECDEMI4f9ECrVmJ85YEZ5uVyuzJPAhZHWxmnour6A1B8AAsEgsy/YpDRRugwAEQPB4MKSSTEH24ct8aj/uSFmPhsIiOBrWZYVRZmXB4iaM3n+dFhVF9CWQggRrtHMmbduGClz0EoZhJBgMKhGF4tSmqH5TvBIu0rLlYKNlBAizA8LOIdCo0QZAAIECyN9Z4tC8IBFRxSSIZ4NQQo9bneGluJ8LlwtmYSJzE3Si8j0bwUhRDOM2UDApH0ZdoYghIQ1LX/t1jCMBaf+AmKfL4EHNh1KlAGEQqHSMZEgoo1uqDzpYOkAAUwnsCcDQ5CoLZrbb2m6XjpqU4b7IZJCtTghbnQwFDKXzOv1ZvRIIqqals9Pc85Lym5mlXWWHkqRAYTD4VJTu0QhfluGWrxEIQEEwGBMrJQsy5k8sbml7zHGSi1djmXQKUEvekE32yEMd8J5QwnxuFwZsvl8yGVwQe3+6bBUmyOVHAPQDaMEsyVFQTRb2NJSEiWsln1Flue9sByYHyKWlDwoQAB0XZ/brV2ChV1zACEkFA6LhVMURcosKiYrh7+1bmOoVOks59wuEbCksDBhoKK5FUfkjHHL8lNKS1lARsSK8vI8B9GFfdOWCZUAzKhBxnkwg3jHdDH1oqQXR0RRxA0AACghvJQtZohl6R2k/tnZJcAABMzkAMZYJhI6JcSXJnyIiWefMaEOYrQCNkQL85UszDTphZ6InShq7QtRvU80uoJUKd2l+6gDQFTgzbNkEAEQe92mSeU0h6S601YpLNuhdF13uVw0s1rWhBBV0wzGRMy4bhiGrovKncnPP0aLeOc/z4KAkLCqpk0OKDFqRlJVxs/wfnLOdV1XFEWSJEIpzHdKcrlAjqhrmm4YCcn8sflEX6Qqio6IC686E0LC4fASa45UJAZgMKaqqhnRL3ZicX7aXui67lKUlOkhokrd6OiYCB8mQBSXUl29bFlVlbUYzgJKOZRSQa9VVR2fmAiHwmbhs5qaap/P53K5cvBRs2gx3gwVOFNd4JZyzdbbIuapaXooFByfmNQ1TRzmdrtrltd4PR4ReLrg4gKlVDRICAQC4+MTwvYt8qFqV9Qi57KiCK1mASdJCKGUIIKmaTMzM9PTMwZjgEglqazMV1Nd7XK5RKHvOeZJCNF0XRRJdcnyvNlholivKKPEORfVG2PPftp5UsFpJicn/f4AYwYASJJcWVmxbFmVoijigAW8n5zzxds1ISUKfiXWcl2lreFlBCHAJgh9k5NTFy+1dnf3BIJBSiVCI9cp7BkAWF9Xt3btmk0bNyxUJwpBUoeHhzu7egYGBjVNp5RankfknBNCV62qa2psaGpqlCTKWKbk1STEmV8aiQqJCacIktrX39/T0zcwMMg5j/JaIipRMs4VWV61qr5lbXN9fR0skNYoSZJhGB0dnX19A4PDw4Ao7jAAYDTQy+vxNKxe1dLSXFtbi8g5LzbZEiRVVdXOzq6+voHxiQkgYm+a2xM5x/JyX2NDQ0vL2qqqyjnIqwhqopTKsjyvNZxE281LkiSCd+fYG4QQSaKzs4H2js6B/sGp6enozYybZ3V1VcPq1evWrfX5fAvFBsR1LSUGUFgfQOnXv80BiFjm8wnCNDE5efDg4eGRUZdLMUlAMoTPg3G+6+orr7xiR1gNF40cEEIkSRocHDp2/KTf75fn7IAh5inL8hU7tm/cuM5ijZ/nJ4RebEaM5DZPSmlnV/fJk6dVTZu7Nwvn3DAMn8939dVXNDU2FpMcCBZ1qbXt1KmzQECiNN08BdkyDH3ZsmV7dl+9YsWKYjZ6pJQahnHmzLmLl9okic4heQh2pevGqlV1u3ZeVVVVlXKeVueNje4NSZJCodDxE6e6unrmfojEPFVV27hx/ZVXbPd4PAsSK4iIHrc7oWMEY0w0nkswqVFCgBBZkqSi9BrKAYViAIgYDAaLVq6ryBAVE4++dezkqbNutyvzpdV1w+127b9+X3HIgShoceTI0b7+gcyb3iCiYRgVFeU37L+uoqJi3scsfwZAKQ2Hw6++dnByclpRMoooFfPUdWPlytrrr9vncrmKoApIkjQxMXnw0BvBYFhRMhUDEVHTtPXrWnbuvErwj4JOEgBkWe4fGDh06AhHzLwOq2CrO7Zv3b5tG8cUPFU0+QEAu3KjJElqa+84duwEpZIkZfoQiUoqe/bsalnbvCA8wNzwkL5xghWmy1OW5XQG5IVCQRjAoiuAlRUIIYbBXnv94OjouMuVdbF4RNR1ffeunRs3rLOxrU0yKKXBYOjF372saVoOjTs455zj/uv3rV69au7HzHweQjklcEiSNDY29sqB18Wcsz2dMS5J9Lbbbq6sqCgoD5Akqbu75/CRt2Q5FzueYRgV5eW33HKjK4No+nwgSdK58xdOnTrrcuXS507T9FWr6q6//lpKEomDGduT20IngBB69K23Oju7cyuzqmnahvXrdu/eWXweYLUBAMDM7CzN+D6Lyqlul2vBu6oJSA888IC9Iy556s8Yf+HFl2Zm/JnLgAkjSJLU09Prdrtra2sLRAuEsfLZ515gjOW21YTdtrOru3rZsqqqqjnmKdooAoCuadleDKV0bGz8pVdencOWMt8IBBHb2zsaGlZ7PZ4C3U9B/Q+9ccTlcuW2tymlmqZ1d/e0rG0u3MMvSdKp02fOnDnvduc4T0mS/H7/8PBIS0tz4neECL+uaL+ezzxlSXr99Td6+/pzLrItSdLY+Pj09PTatc1F9gMRQhDAjOxgIqwx83MRdV1HzkuhAZnNysjSpv4AIEnSK6+8GgwG83yGPR7P0bdO9PcPFoIWCMvPb194CTEXmdo6jsvlevW1Q9PT03OMIy4BEbMlCoSQ2dnAywdek/PzjQv/wUsvHShQwjCldHR09PWDh935RQBTSjVNf+HFl22aVyIkSero6Dx37qLbnVcrC1mWJycnXz/4RoEYlSzLbx0/MTA4mGfvZUVRevv6T58+U3xKalU7pAxSIBNACDEYs1ZbWijYzABKM43bLkiSdPLk6YnJSVseDI/H/frBNwLzNd7LAZTSw0eOappGqQ0ju1zK7146kE7I4pwL8T8HTywh5JUDr1GS1u+X1VCc89def8OWS04YWdO0A68e9HhsaBlNKQ0Gg28dO2E7bSWETE9PH3nzWJ7UX0CW5YGBwUutbdZ5mkJAPmoWpbSvr//CxdY8qb+Ay+U6dfrc0NBQkQ3r1kJPsizPmxiRDsFQSLOpfmpusPOuLdVyGQJCXD1z9rwtG1dAkuibb75l796llPb3D/T19dtFYgghhmGcOn0m5YBmFaBsS3VKknThwqVgMGQXyaaUjo9PdHR0Z+5OzHDYY8dPon2taGVZbm/vGBsbs3fdJUk6eOhwDk6pdHC5XMeOn7RW5DdfsFxNLmIvHTlyNM9sSis8Hvfhw0eLHQ1MiCm8S5QqspxzHqWqqmreldJzhm1bUNf1BVdnCgpK6bFjx20Rr6xjDo+MjoyM2qsEnDh5yt5+lrIsX7rUFggkGvdEaKB4nZVDW4jVp06fyc2Pkg6KIh8/eSrzDIZ5QQiZnJzq6em1V2BXFOXEydM2Dkgp7enpnZnx27uRZEk6eeqMYKiiGRxEs3JznuelS232tkcW+fnt7R3FdKuS+A3v8Xh8mVXMTjEUIZquL1ShIdsYQInU7y4QCCEzM/7+Afs1TVmWz549b9fepZQODg7NztpvVpIkqbW1La4qA4Aiy2Lm2RaApJS2t3fafjMJIcwwenp67RpZkqSLl1olyWYTs3B9j49P2LVMhJBz5y7YqJsKSJLU1dUdCoVFRIBYa8ZYzgYg3TBa29ptp9SyLJ87f9FGxp8JMF7nkCgt8/kkScpBFRDyUDHTREzY85xk3jh0kYJS2tnVZa+4ao48PDJiV+s7Smlbe3shfGKU0q7uHquSRwHM5sDZsn/OeUdnVyFENlmW29s7bbmZhICqqn19/fbalARkWbHrDhBCZqZnJiYnC/EMSpLU29snYtjFJzm35aGUjo6MFsJRLwwpIyMjxfQEcESzTqoJr8fjyqBBXjIIIaFwuPjpzfbcL20pFkpNwPDQXNuLM6bruh4Nj0PODd3Q9Yw6AlIqDQ0O2/JU6Lo+MDg8xzwZY5F6fIiIaBpzRSXOOUYWivbk5JQ5T7PcY7blmoU3xe9Pba+INUxGTBY2RZKaruvpxD1CyLBNVIYQOjIymvBDiDx20xjjiADITIivEKP3OW3zZ0rJ4OCQLUIfpWRgcFCW48V/RHNKYgoiG11AzEkU3tF1Y468dEqlnt4+ABC2PkTMOXmFUtLd3TOHdBJdXEPcRm6Z8byEUZKk4eGRIouhom14OL4TstvtdueU6kEIKb4b1QZRcclTf0JIIBCYmJxK5wDQdWNNS8vaptVMV7va2/uGx2tq67ZsWe+i0HaptX94fG7VQZLo0Mjw2uSw6+znOT09nT4fEQ0OmzZvXV23fHZmurW1lbjLayq83T19CKSpeW14ZmJ0yj9HSotE6fj4xPLlNQDg83pN36+RZbUPQsj4+DilqYRfKm1Y2zzQ1xvSmOJ2r29a3d3VrXMUo3POFHfZlVs3VZV7+3u7Wzt65VRGD0WRx8cnVq2qz1OeIoRMTExYxX9EXlm9oqbc1dXTxxGaW9YFp8bHZ9WNLavFTzFd7erpQ6ps27515fLqmamJS63tQVU3B7QOHg6roVDI5/PlLfeR4ZFRqy8dERW3d039ColSXVP7+wd0Axub13oVAkAIJf09Pf6g2rimed3aJmR6Z3v70NhUZHbxa0kpmZiYNON089H1EWF0bDxtpQfOveVV27du8rpob093e/fA6qa1ZW4JAAilg709QY3N8cuU0u7unl27ri6yJ9IM6PR5vabg5XK5OGIOqpJZdbUAM00NGxjA0qv2kwxVTcvkdE3bde2NV2xaMzY6IrtruBoM0/K333nD7MyUyukdb2t569BrZzv65sjIJ4RMT/vznyQhZGJiKp34r+rGPW//vfqa8sHBwbXrN0wM9ZY3brpqXX1Heycj0t7r941cODP01mmaXkAjhM7MzLgUxRTicuvdSgiZmZlJDv5BRKJ4rrt27+zUul/86pmy5XX79l4zMTQwHlBF+ozsqXjXO++TWHh43H/d/hsb6i+8dPB4MnMlhE5Pz6xeXZ/VrFLOc3wizq7CGGtcv3nnmpq2tg6Dw97r9w6dP3OodfjafXsROQANz0y0tnff/95315QrAwNDGzZvGR7omw6EysvLKWA4fhcRAn7/bFlZWZ4MgHOe4J9HzqtWNt5w/U7knFBqaMFH/vOnV++9tsYNoqDbs4O9zbv27tmxYXx8jEguhRi9gyMer8/jdmlq2GBoXVLDMILBkCi2kbP9hxASDodVVU2jAaBBXPfedzeE/VNBbWNL89mLHVfu2beyjIoJH5gcDozP5dkihKiajgtXIDYQDHo9HvPqPG53Dj1BCSGqpi0mBiAqNC1tBkAICQaDKaMVEdFdWXPFpjVnjh06dKJVliSO8M73vnd6fOBnjz+PhNx217279uw619oNc1p7wxbzHwLIkpRDPylCiKqGU5Zf4pzVNW+sryl/7ukn+0ZnKKWU4FUN3GAGIiIgM3De2D5CYDYQSKD+yfxmXnJGCPH7U1cTQ0QOUL6s9o791xw41wcQizdhhr5z304Fw4/89Bdh3di6c+/+XduqT531hxNvFCGiWXG+e5JSkhz4xDk3GBMlvxiPZT88/pOfjAR1SsiWq/bUlLt/+bNHp0KMEkII7L3+pm3rGwHg5d8+0zc6a24kQsjs7CxAXZ7zTNkyExEB8EcPP+xa3vj+d93d0lDPOXScPv70a2+5FVnyVt21Y0PrueMvv3FalCqrbWi57/b9lMBQT/uzrxxxWdkqISJOMc/WbJqmpdsaiOitqvBIcOrMqddOXPB4vKLid++Fs0+8dNDtcnm9Xmn+cGGcDQS8Xu9CFQoNhcMej8esFer1eGazz/IRecJF4wH5MoCFzWIoGgxDT0lQEHFZRSUAdPaMer0eCsCoXFXubr3Q6/J4KMHe7r4NjTvLfEpA5YSSdAYW65YlABKladuMzIO05Ujr62tBCw+PzXg9bnFFiKh4fH/04Q+JY4YxNpOUu1ZscfOtLMvlqaS5eUtFIsxlR6YAbx4+es2+PRunZiPHI3JExvjy2mVTwyMqEq/P29vdD7u2VS+rmB6clBIZANE0zRY3sK5pCbcUEWVf+Yc/8kfirWk8f9cHPkgIabt4MiRVYcg/NquX+zwAoGr6msZVQ31tv3zq5cqKChrvTzYMlv80hRCWxIkRgFx3ww2VtXUAMDI5uYXzdVfs/OSOncyY/cUzBwlAd1uP1+OhlDBDX7m6gRLjkf/8qQayO16pIkA4Z5qu5ynqzUGWCaHBsdH2nqEr9+3fvmvP0SOHT13o5Jw3bdn+yc3bAYM/+cmvkMiIyZcZNz4zFjISXWg5UrRMECHE4/GEs/eQaUVkAPk6gZe8+C9ACBVF/RI/BzCYAQA+jySaGqqqbjDwVfh0w2CG4S0vB+Cqqq9ubFheVZ5hFeic+4lTmm6eJBzWweWSJWCMC9cgIUQPB7/30A+++/B/+nUkBBBRdrlrllWmdAgjgmu+fZmJV5MAmXvPaLOTL7x+/Jo91wAARyyvXNbcuBo5V8O6u8zLdIMx5i33AkA4rJIknocIkiSlug1ZQ5LlhPtJCDGC/u/9x8Pf/u7DswxMqfT86dPHjp/s7B40DEa8HmB6pLa2RF9++bWaVes/9icfrCp3J9xYQklBhdXVDY2gB1/53QsTAUOidGJo4Oix4ydOnmMcAaC8yhspYkxo68mj/cMzH/zQh3ZvazHiySghoBlGzsafOKQfwOWSXnrhuUcfe6p/bGbf/pvKCCeETI8OHz12/PiJc0AJ47h69So6X6hCvjPMD4SQQDBovlXmrL6eDjnYjnJGvgxgaSd/CSCi15u6yhihZHRkZFZlN95ya9OqlY3NLVvX1l/q6G3efMVVW9at3bBp31UbR3q7uKtyRXXl/htvVkhqDpBQqznnHZAuxIJKUld7GwC9+67bl9cs27p16+raSgRCCFUUWZFlSgkBojG47Y47r962IQ0dx3n74WWS44OIPp9vjgdZlpWOC6cGxv0AgByaGlat2bhl9xXrzl9s81WtuP2G3ctX1N9+w7V6cHpk3J/KNIdutzt/woqce5L0MEIIJVRWFEWRKY1RnI5LF0+fOTc0MtnV1QWgvPPe25dVVe7YccXyKh9TZx9//MkQeK/ascVKWxEh84RYUe8oJWRZVpJCD8XEnnriid/89uWeoXFFligF/8T4ydNnz1/sUKfHJoP6NdfduLZp1eqGxh2b1spu16FXXznV2nvl7t08Xq1HRNGeIQHZUltZluY4gaG8//prKzzSlH8WAFwUCKWB6cmTp8+cPd+mc+muu9/WWFc7x3NBCHh9C2P/iZ9GnKLscbuznhIhRcsJyMsEtOD3umjwer1phHeiUPbb3750683X3nb77QDY3XruhddfdUs37tl3PSE41Nf90mtHmMHGZ43qiUGVYTKJRsSyskTCahhGDtm8y2uqU86TEMKCUy++euT6vVffe89dnLGjoalQMDAz4yeUUkL80zOzgcA1+66dGhup9LpTyqUcsbKycu4JZOKFQ8TKygrhNY2bJAAg9wdCmmF4Pe7XXz903503IMFzF9vva2o81j00HdQPV3h37ti6fsuOmcmx3xx4Damcgvwjlpfn61kFAI5YVVVpDVclhIRDwWm/QimliP7p2WAoDJypqopUkmUkhPjHBl89fHLfru1vv6+ZMX16dPCKPddXV3p0LdjW3mWNBUDky5Yty2SeCOBSlDnUL5/XO+33m7eCEGLoWtA/I7vcLgQCwCjx+2dDqqYosiRJlJIXf/viLTfvv+WWWwCgu+38Jt/ynVs3cGQdFy6Q+D2KALXLlycXwjMMI6SqGTIBRPR4PHM0z3F5PWua127dshk5v3T25CSXAv4ZHlZdskIJWV6/2qsA1NatrCkfmgqmNKVKkuR2u0shKNEwDLMKr2gFk9VuJLk+/jkgr34AS772pwlEfPqZ59I5ZpFzLZoKK8mKIktmYQwqSYosgW/5B++/9cSJ022X2tRoUKMJw2A7tm/ZunWzNeRclqRk8XNeBIPBR3/2WLoTOWMiU4EAURQFgDOGonqMpmlAyOrGhtq6hvV1VU88/byStP9UVbvtlhubm9fMNYEMWoVQSkZGxl5+5dUUWxxR1XRFkSmliFzTDAC4753vCo/3t3b2DA6PIee6wQCQUKooSkpCEA6H773nroqK8jx5AKW0o6PzrWMnrYFGjBnWm0YlSaJU0zRrmWjGmK4bCCj6QjPD4ByBgEtxWfUVxtjb77s7w/YAotVwum9fP/RGR0dcWhlyrhmGy+Uyf0/XNCJJJgdC5GaeiiTJhIDQTiRZsrY8RES32/Xed78z+UdVTdOzcQHKsvzi716enJxOF0+haRoiAhBJlhVZ0nWNEEmWJUSUvJV33bxXNcj5Ywf7JwLJ6845r11ec8stNxoL6gYwYV0vTdezTkxBLC8vL8jM4pGfBmDXLEoeLkVZvrwmXaYJoTRBPlIUxeLGQaIHnn/hgCxThonUHwAYZytWrEiQ3IVxNlsDos/nKysrSxeYTyXJHReMRM13ghaPDA6Njoz1trlSxtdTSlauXDn3BDLRADjH5ctraMqwKELMZAtCqNvtQsSjhw66XYooNEtlWZovz9nlclVVVuRcsMwEItbWLk/Y5hYSCiYDS1h9SZKstFhKJcpxxOrqZT6vN0N/j9mSN+W3a9esuXQprnInodQd/7sJHJ0QmsCAU9oPOeerVqUJqM2Sv4qhxscnKE3xQ4SQpIfI3AmEBadfePl1r0wmZgJyqsRsxnhjY0PxGy+nA7M8v4osZ1vuDQGEVFGY2cWQnw/gsjEBMc7Xr2vJ1TdL0FBHRkYGBoaSq2BD64QAACAASURBVKiIFqMrVqToDBMIBnPIDt+6ZVMeuZqUIJ8NpKhLwTmvq1vp9c6jlGQ4V1mW1zQ2ZOLnIIRMTk4MDg3N+DPSNRljG9avAzu0UkRctmxZ/ppEShiGsa5lbVZcao79sGp1fWqGmjd0Xd+0cYMtQyFiU640mlCqhUPTs8GU1B8AgMCqVatKxyhNCDHVI7OMUlanq7o+GwgU2hlQQt0pSxmc87qVK3y5N5yKePCSvzAMtm3r5pSkkBDCGAtk2TVi08aNeapmKemspuk7tm3Na1wLOOcbN67P0Fwr/J8ZatCGwdata7EriIJzvm3rFl23+SFERJeiNDSszs40LPZDMJi8HyihW7dssp1YIGJlZeWK2lq7RisvL1+9uj631REF6VJ+xThvXL2qrCz/nGo7YZUXpexjgcSlhsLhghaLzosBXA7WfxOE0h1XbM/K6DkvEFGWpXkIFiHBUChzHuDxuLds3mgvLeAca2trGhsb5j0ywy2BiMtralbV50gL0oEx1tzcVFlZYRch4Jw3NjbYTlkMw9iyeWPOzYGDoVCykrfzqittL4epatq+a3an/Tr7x59zvmP7Vk2zOXmIGWzHjm2lFpEoym2J11IeDQM0XQ/FlxuyEfkygMuHB3DO17WsrayssNHOqGn6NXt2zaseiipRmdPK3buulnPdcCkRDAVvuP66TI7MfD8wzvfs3mkjLRBXu/PqK+0lBJTSa/bsmqMWSLZARK/Hc8WO7TnPkxASStoPiqLYO0/G2Kr6uqamxjmmke2YiFhdXb1xw3obBRRdNzZtXF9ZWVlS4r+AuUaZd41PBiHEMIwCRTc5GkAW4JzffNMNdikBhmE0Nzc1NTVmaArPXAqQZfm2W24Kh+3RHFVV27tnd01NdSYHZ74hELG8onzvNbvtolmaqu7bu8djd1944fzYvGmDXeseCofvetvtyZH7WYEQEkzaDzu2b121qs4W/ifmdtstN81xTLbRjQKMsV27rnLnEB2fCpzz8vKyq+3m+nbBfLTzJJWiRlAhssMcH0AWQESf13vrrTfmb5VjjFVVVe7buyfzjcs5z7zwRn193Q37rw2F8tUcdV1vXtN09VVXZHh8Vq5IxtiGDS0bN67LXw/QNO2KK7avaWosBCFgjO3cedWKFbX5hxiGQuHbb72pqqoKU8WDZQXkPFkqvOO2WzweT562IETUdP2+e+5Kjv23QqI05zCQO26/JYcm0gkQNTBuv+3mEpT9AUBUMIy9zXu0QhiC8mUAheg9UspgnNetXHnjjfuz7YFlhWEYVVWVt84pXiVDlLjJ/PjNmzbu27s7ny4Tqqo1r2m67dYs5iln2RHJMNiunVdv3NCSj4arqur27Vu3b9uac/jTvEDEm27aX1tbk7NDGBFVTbth/7Uta9dGPspbKkwWCGRZ/r233+vzeXLmVYioG8a9d905r85HCCE5NWBBRK/Xe8fttxJCcpZqRRGau+++M2dXShFgTivne2WFKC6S5yAJkB544IE8h7gcykFbgYhVVZV19St7e/sYY1mF6otSf01NDTfesD8HDRoRpTTRRCmxcuWK6mXLOrt6ACCr3uuCWl195Y7rrt2X1QwppVqWJeMRsaFhtSzL/QODUpYFBjjnhmHs2b1r65bNhTYCEEJa1jar4fDo2Fi2hRAYY0DIjfuvW79+nXmirml58oCU+0GWpc2bNo6NjU1OTktzVl9IhmEYiqK84+33iK4P84LlKsWL8irNzU2Dg8OhUDjbnmu6ri9btuy2W2/y+XzFbgefDeRoRohQqvIcTagUiq0yd16ZwACAiDmUPF0CoJQahvHWsROdnV2K4pp3ByOiYTBJovv27mlqashZQJNlOfMCMgKhUOjV1w729vW73e55mQfnXDcMn9d726035Rb/l5W/2oQsSZNTU4cOHZnx++UMSmiJ2vS1y5fv27enoqKiaCZgWZYHBwcPvXFU1zVZlufd+ZxzVdWa1zTu3r3T7XYrsiwyBO3KopckKV3h2La2jtcPvSHmPO8PGYzpmrZj+7Z9e/dk/uuMsWAe3UxFFMn5CxdPnjytKMq8bBURRSnDq6++YvOmjdYwmxIEIrrdblHAw0Y6WV5Wlv8gJvJlAAAQCoXyz7pcjBD5HdPTMxcvXurs6gaRSBUfGSUMnYZhVFVVbdq4ft26lnzUXvGj81ZkS4mx8fFTp892dnZLsiTFz1M8SJyjYej19XXbt21dO2e9h7lhGEZuheNFvH//wEDrpbbBoWFZlgmh1tuJURiG0dCwevOmjfX1dbEWksWCYE6dnV2tbe2Tk1Oi2Iv1eqP3kyNi85qmzZs31dRUi9RQn9drtlbPh3SamHs/IOL5CxfPnD0/Ozub8n5yjowzkUawY/s2ny9ttYl08AcCeV6DJEmqql681NrW3qFruiRJCQ+RuJ+MMbfbvWH9uk2bN7oUpTS9vlYgos/jEYnrdvF7RCyLlpu2BTYwAIMxu3qaL0YIsqXr+ujo2Mjo6PTUTCgcRs4RwOVSllVVLa+paWxs8Ho9upFRi+C5gYgVeRQJ0XW9r39gZGR0ano6FAqJkFavx1NZWVFbu7ypscGXE3dJwGwgkPO5lFJCIBgKDwwMTk5O+v2zqqoBIiHE7XFXVlTU1FSvWlUvon0WUP0XD+Hs7Ozg4NDk1NSsP6DpGiAQSr0ed2VVZe3y5XV1K0UjLfMpK/P5SLSVZg7N1FIiE5FwcnKqv39gfHLS7/drmgZAJEkqL/NVV1fX19fV19VlZSG0IqyqumHk7+GklDLGJiYmhkdGp6amg8Egi9YmKvP5li1bVle3srp6mSRJ+XuPiwNELC8rE0tsF5EUcSjZ5hXPARsYAAAEgsFFsSQFhSkGmhtUUWS3K2KuCYXDdsXn5cMAigNV07L1BCSARAGWkuOmObV0dP/keQpaBqnmaZXWw+GwXS5rk6lkCES0UVyztyKk9e4J7i4KTyMg56Wy6JnD5M2qqubc5MMK4T+fo79strDHn+B2ufJsF7cEkCyNUhLT1OzSWBfFE+BSlDyj5lOS+BLU+pPnKYwVKQ+2PrcLaDW19zmllCqKkr8SIJB894rXG8VuWL21dm5dW7mgPbakTLx2lxsQ0SwImnOwRDLySSksGgghrvwSnZYerPvBXuPVggtebpfr8qkLmSESl9u++2Mz/7ZrIN8C9WIuWVgzJOxppwcAUdNz6cPlci2WqRYHwlcsXttYUWrBqT9Em986j78V1uU2DMOum0MIsbfsq33eZEJyaX62RIGI1mBNuyqfYLwZocThyAQmENFanV+zTyAokf2gyPLllhM6Bwq33LYXX7NTRlMURbG1BtkiBSK6LD1+88kZTh56ET1mhBCvIxgCAIAsy2bkho1xq4iYsnXPgsDr8ZSCOlIKkC0dgZh9Hd6tZiW7YLOS7vF4cqh8vcRA43sbqfnFw1hh1SsXBWRZdpdwpn6REK8O2hX9CQCU0pJ63OZoWnn5ABGtPVnDtkbH2JsGDIUoBufz+XIrE7g0IOK0zLeqqtp1I0RioU2DFQ8ul+ty5gFiP1jVQRvlwWxzwguNnLMUlwxEopa53IYdqT/myNa+03ahIOKDz+u1tx79YoFI0zCFdFG/067wOJeilJS4lzlcLtfl6R8SNFqK9WHHsH3qoNWsVDoQPCChEOZlArHcVh3dRvGfJjV5tmdY20cU8Ho8dpX8XiwQsp71mbQl1x8AEECSpMUo/ptQFMWWHONFBEEOrEbbUChkl/wm/Cs2DWYzBA+43NyBycsdDIVs6U0NUcnSlqESUFhGzTkPhcOicGvhfmXBIVIrrbI/2Jr6K0nSkrGuhsJh4/IoH+vxeKwhOqqq2hINgoiUUp/FrFSyMAwjrKqYdyn8RQGvx2MV/sKqalfwt7WkhO0ohqam67owhZf+ls0BwjWfYI21a/kRUZFlT6nKermBMRZW1aUqFiCiLEme+JAYTdNsiQVARFmWS1b2Twm7OF9pQqyIx+2OW25dV+1w9ReB2RfPVKfpuo0esBKBLEnJNZaFkJv/4JIkuV2uErTz2gLGmKZphWvhsiBIuWSqqqqaRvLonyVAKXW73SUS+J8VEFE8/kvMKJR6uQWzz3twQojb5bI97jPxV4q8JNy+qNgFh6hbm/ChXRcoak3nP06JY47KOYsONFWvHkQ0GLOFHCyB/VD82t2FQ0GXG4rVbPFydNY7cODAgQNwmsI7cODAwWULhwE4cODAwWWKBSssI+qhx3Woc+DAgQMHRcTC+ACs3TBItK1S8afhwIEDB5czFoABJPdCErTfYQMOHDhwUEwU2weQshOeYEGiTdJi6fjsYGlgMhzqDwbtadeQAOT9wUAhBnbgwC4UlQFwzoGnJu7WTwUncNiA7ejuOPWZT/wxIeTtf/CRw6db7RpWDcw8/M2vb1pJdt/+3iNn2uwattDgTPvU87+oeeTbjT/9tvLof04zO/fb2YGue379yK0vv2LjmA4c2I7iOYEj7bMJAGLKGkkJNUPE8Yur/H1Jg4Xevf6q933zkcHBL/V3XWpoagQApoa6+wcQSd2qhnKfe7CvJxDW61Y3VvjcADA5Pjo17ecIq5rW+FxiqxhdHT0MaMu6tebCcB6alapfOjn4xI+/+YHPfrPtqa8u0BVmh4Ntp/+9f+j8+z6+pczdPjlZJREAONrXcWxwcGVN/f3r1+t6+JlLZycMcvfm7as9boPpr/V0tI2PL6uqfceGTW4CAHBxuO/V3p6WlU23r2kyR/7uG7/5aP8EaKEHd25aqKtz4CATFIkBiITPmIk/Mx4AQmlw3AO2gHPihqmpSclTvvvamwBgdKD1T973ftp4pRyY/OpDP3jyR1/74sO/3bNx5blh47dPPVrt0j72/rtx1U5ppm/Q0/LsI9/C2bHPfOLPQmXNY5deefvHvvzR379LDOytqPvUR/8IWLD14pkP3//+Bb3ILLCivAIIfv/0sU9ecfX66moA+Nzvfvn3YzN/2dA0OjV9tx7w/uJHd9U2uzT/n7RdCr/nfed6Ltz62sFPr9/09WMv//Ho5Peu3/fDIy9+uH/0/6+v+7MXf3H+nX+6pbpCjPz+nbfdv3W6/omfX1O7YkEv0YGD+YCFB+dc140UYCzzP8YYcl6E2S5htJ068v53371m066nXjuGiP/jPTf/7699T9zTnrNHABo6B6f0QC8AHDnTPTPeAwAX+icn+toA4ELf2MNf/pvPf+MH4+Ojf/c3/89XHnrcOvJIX8cH33HLA1/7D20hritnHGw/u/ax78OPv3tuOtA51AEPff2UPyS++uHh5+GJXyJi23An/OCbg2HjR288u/U3zyDif735IvzysdHZMfjBN77T2vrChePwgwd7g6p15ENtJ+CR70/ozo51UNIouIEFERnjAKkKYWVj5UcA5vgG8sP6K675yWO/+ct33/y5f3sEIPTVx17Zv/8aoVi9deTwXR/5yNr6qpkJPwAsr63pOHNq+11/2LJ6WTA0DQBlbnz1+IW3nv3lp//689tv/v1Pfvh+c9gLR39369v+4KOff/Dzn/7TUmlQmwE0gOvWbTt377uAq0cGR4b80yApq8sjhTZPTk388cp6ALg4PACeihq39Hhv5580rgeAA6PD76utH5uaANmtTo8NGqTzvR9p9MY16zgx0H197apq2VFbHZQ0Cm4CYoyZdp0Uhh9BzTM276Aovo/oGIWyBP/c3/zF1n13Ly8jB469ecedfwzgff+eyp/8+Kd8rK9uw5Z165uf+7ufP/nMs7/50b/9xQPfWFdX/uhjhzbXlb/47NPf+If/+bkHH2msrV0uY/U1N73njusmQ8wtRW7++MClrdfc/nf/8h2cHX3mN8/deOvtFZ5F0Lb+fN/FbS+8+PlN245PDAAo+1bXreAeQP19z/6qRZE/d9M9N65c9a6zZ+u16S91XHryjv9G1ZnHA6x6tOPeJ4/8JqB33bKnhvvBCB6ZmdkSmKqtqVtbFtew4c87Wv9p970LdXUOHGSIwuYBGIaR3A0iiW4jAM2hZ4STQZYFkL916MDrR46rBt96xa6733arTGBqbOAXP39iZHL2znf+t2u2tTz7xM+On+/cfNWe+++9XQL2nuv2NN389jUra6/Yc+2dN+0DAP/k8M9/8fjw2NTmXfvffdeNYuAjv3vq1RNtEgFN11H2/dX/+IT9besKAeQn+rsvzM56JOX6NetXuhUAGJwafX1oSJZcd6zbVC6R1zsvdqv67vo1m5dVHus8s/vgGz/Yc32trNy0dkOFRABgeGbstYFBpNK1jS2NPgsDMNSHL5y7fe3m5vLLqwmag0WHAjIAw2AAXBDqxF9N5wDOnpo7bKAQmBpqr1614WzXyLZmx40JAPCt157++ATD3/u9hZ6IAwd2olDausjoojQ1XU4VBETSfTE3hFEIEJ2yQjZCZ+TXTz/bsnr5Qk+kVLB/w1XHXWULPQsHDmxGoTQATdMJIYKeR8lyCuqcgmIjguidlBM1d9iAAwcOHGSIgjAAxhiPz/idgwfAvD7gHOxChFCHDThw4MDBnLCfASBitB96jARbqHFOPCCnQx33gAktFJycDdWtWD4yPFxbV0cBZmemDJSXVZVNjI3pjJdXVpd5XQAQDPhDOixfVjE5Ma5pRlllVbnPAwCjIyPC2ubyllVXlgPAxNgoJ3Lt8urRkWEEQgjhnHnLKisXm+dzcGq8PRBQKN1UvbLa4wLkhwf7t69sYGrg9NSkROiWmpXVHtf47PS5mWlA4Ij7Vjd5KO2bHOsIBJrKqqq98omxcZckISLjsL+hSXI2nYPFAOmBBx6wd8T4lphxz0FeekDi0WnHsYKjIE2X++PYefJQ86ceeuC/3/WHezYMlq+77uqtv330Oy+dm7xyw7JtNVt6/ePf/MY35KpVV25Zd+TVJ7/9/MW7965rKVvWMzXzw+//h6t23bb1TbeXlx+dmD3w4nN6Vf2V6+q/+NlPfec/n3zmV4/Wbdzz84e//POfPf7PX//u6VPHq+rWb1y7aqEvNzs8dPD5z/cP8eD0/YdeuqeupdHnanripx/deNWJ9hO3nz23Ugu84/CBD6/d2jfYvv/NNzcq8sXpqRsamp86+dq1R99cTeGxro7rV654tKP9T84eGZgJhFT1xoY1TgKAg0UBm53AopinRfiPK+4QNewnV3yIfAuZswGEWAW59Oc4tiABKsnbVlQAwKod+57+4b/u3nlleeUyb8gFgM133fftb39Tneovq268dmjG7faUe3QAGIaNDz70nf5jr238zLfwbdd562q++JV/ba52AcCzj32/z7P5sV/8JQGuGXDz7m/N9J58z2cf+uEP/n2BrzMnSJT+r/U7/mjzuvtq3/zg0UNt996z0uWiBCihn29Y/8A1u88Pf+/81EQ9lfaXVd21eo1MFR8PvvvNw10f/ItmXyTq9Z+uXSk/33fzlp1va1q9sJfjwEHmsDkT2DAMQW4tdiW0vosanBAgRV1oyC472HIOR8AUf5JTSy4egfHBv/+Xb3zzS58/1zkoUQIAyJjGwLes4TPvbh6cmIhqS2T35vJ//T9f+8ev/Mu3PvYBAFi5/ep79mwghJy42D8wcvKOa68jAADUJVMA0HWdMVaQospFgahS3lxV066HeDQ/XqL0C+3HyX895CpffXNDg4H4hhY8NtBzZmR4RgsDeJp8cTkPBqLO2UJM34GDHGEnfTQMAwAgWvE/jvBbECXxJM33EeqdHVJJ+ZIkZTnK0ocRmq6ub/7y337s45/5qrg/RJLdEkyPtP/LL7vXrFzBI7cex2bI9fuvHRqcuuOmawGg/9CBl8/2IOLVmxtWVm595tVXxDKrqr5wV2MTEKcNbUoN/fupo59d1UIJGeIIAAZnf7dh1xvX7P1ZMOSlwDl/e3nt+3Zde+/WLZWeCvDoPzzfOqmpXZPjYhjuFClxsNhgGwNARMYYAAAhVvqNsf/H6QEWVSAtG0g43vo3NwghDvW3ghnGucEZABg8fCkcVtdeeeM//+19/kAYgHQ+9+NdO6+574N/9dRLR9Ys96nh0ExIBcTuwWNX7L7uC5/71Mb7PgGAoBp3Xr9vx8aNf/jAv7/9Ax9pUrs2bdq+ds2mX796FAA4M17qHVuk9K+uovoLXaeve/7J1TVrv7B3D3B+Z3klIeBSXC5J2rfuys+Uw2Od3WUe76+Co2ue/nn1r3/WFtSG3vGB5zuO1zz3xMffeH3GQACo8pa7nF3nYFHBtiggTdOSxk4Z+pPSLZz4eYZIMO+LS6GUpktAu2zBDEPVDZ/XEwoGXR6vRAkzdIOB262EQyGOqLjciiwBgKFrBgeP2xUIBHxlZQQgMDvrLStTQyEAgoCESl6PGwBCoSAA9Xg9BACZEVJ1n88730RKFAyAWrYgA5CisokQkQxEmRDTviPFHymQOuvdgYMShj0MgDGm60aKcJsMeEDao3KCQ/0dOHDgIEPYYwLS9YghOJGbxNt6zE/TeIbB8rkIOueWgzH5xPhBuCQ51N+B/Qjo+iK1bjlwMDdsYAC6rkeiMlH8m3REWns/N2OB4k38yLk4iURfJJj+MYFVACClkhPv78B29M36v3Px/FgouNATceDAftjAAIxIxf+MeADEsQESPTHxBJOSExJ3dnoPMJEkJ+LTNiDimbPn/H7/Qk9k4XFgaNBNpce6Oxd6Ig4c2I98iaam6xb6HeEBiJgiXCf+oySqnzYcKBnJQUGy7ERf2In2jk7GeFt7RygUWui5LBgQ4OnuzildlylBhJ+2ty70jBw4sBn5MgBmMBJHt6P03ST0c1L1JLt+Slt/gsEn8Ycc6m8venp6A4EAISBJcmt7h6qqCz2jhcGpsdHW2VkJCAGgQMZU9eDQwEJPyoEDO5EXAzAMI0LoMZmIR1QBmMMzjKlYByR8luzyNbOIUVB/x/RvI3r7+ianpimlBAgCcM7PXbjI2WWX4Dqlhl8aHvJKEhBAAKDgovTw+PibI0MLPTUHDmxDXgxA1/VobR/rPwJx9Jwn132w+nbj7ULWv4QwoCgiFJ9SQp1iD/ZhYGBwYmJSeFOQAACKyN4z5y6wy4kHTGvqr3q6vJKljhQCAfBS6fjExHj48jWLOVhiyJ16Msaidh6cgweIY0R28FzmIBHukxTxY5Xtk8+W5UXQf3yxYGBgcHRsXJKkqN5m3m+i69rFS5eLBRwBn+vrCRgMgEQKFIpgBAKUAgf4r852LYVE48DB4kPuDEDTdYjI8RYqHeUB8W5hbnUJpGYDVjMOSd0cmMRrAw71txFTU9OjY2OSRDnnAGC28xHufCpJoVDo4qVWzNhRv0iBAAcHB0ZUVaYx5da68ygBIOT53u5JNbywU3XgIH/kyAAQIWoTQIv9Jvp/THgvDkPRJmyeej4pv046mlLqmP7twtTUVHdPT6Q2HBFiLwIAcjRXjxDq9/vbWtsXcqKFx+nx0aOTEwqliAAk8nyguA1EZCeCTEhXMHBgcKBA7VQdOCgacmQAmq5ZhHRMsuEnm4MAAAkRnuFY8tdcSKj9Fh/4KTtVt2xCKBTq7esX1D8Svxu3gjF1jlI6NT3d1t6xYHMtMIaDgReGBj2SJLoNIUZ0W1MjNV8olHYHAyfGRp0KoA4WNXJkAIZuxFdzxnipnQOg+Md05IrDICJjxnIFrLm+mQARHeOPXdA0rbWtw6pLRak/xsRbjPnwKaXj4+O9ff1FnmcREGbs8Z6uMtnSyytK9AWs25MAeCTp5dHhbv9MEefowIHNyIUBMMY4ciEaJsRpRimFeHQiMmR8i5i4oH5EM2YUUv6bYsaUOpE/tsAwjLb2GPXnHCMyr9Cyov8gIiFo8nhJkvoHBgYGBhd07vbj9NiogWhS/0zqevok6TcDvSFj8TbCcXC5IxdKqus6EEjyByIARoV764epab8lwtNMHo75CQQjSc745RwVRclhzg6S0drazjiPFtuwWPESea+p7EXWS5ak7p7esbHxYs62oDgxNnJofNQVL1jMywAoAAHy2/6ewk3MgYOCIhcGYBiMYERSNPWAeKTiAZjACSwh1vEDCLXAyhJMOEm/dqGtvYNxRoTEH6P+iNyyFhFezkXZcGtFJkWR29rbJyYnizztQmAiHD4wMuxJ8irFwmAt/yYcIBHSEww+0bXEfeMOliqyZgDW7F8A4BCJFLGScBFBkbr+W0KaACQck8gMIC5A1LH+24Pu7p5QKCREeyv1BwRCoqw9YoVDS9OIyJFiRSiVLly8FAwu7jKZQUN/c2xEntOomDZgDQAAXJT2hUKdM1O2z82Bg0Ijawagabo11EeIkAn+AEIE8UjwDFsChJJepmcG5h93qL8t6O3tm/H7rX4URLOfLcZofcw9E3PSxI7nCAAylU+dPruoecCrgwOX/DNymrwT80WCS4DEYkQBAFyU/qqv59zE0rGJObhMkDUD0A0mfILx4Z4RdSDqOjRhGnPMeE4LTY8n/JiGH0RfE4cB5I/hkZGp6Wkq4tyj5N6yYiT+rdWWF1UUxBlRswgh5MzZc5q+KFvDt09PXfTPeKJdhEj8H6Sh+yk/8UnyC0MD/uTGqA4clDCyYwCGYURMBabx35TlEZFEiT8m0/EYJ4D4D82xYo7G2HeRdxzAafKeP8bGJ4aHRxJiqKymfWvoJ2KKkq6xAC3LOnKOp06d1vVFFgzT4/c/0dftje4ra6xa/E5MfJ0SBMBN6WNd7cHFyQsdXJ7IjgFomi4CAmMfkWi0fzRdnotSnSnsPcJ2HKcQxGcRmzlfyeYfVBzxPz/M+P2Dg0OyLJuqWDTtKw6WLLCURA8x5r9HACQECCG6bpw9d76Q07cfh0eHyyUZIMYDMd7fa41SMNlDnB5ktW8iUkLCnD/e3RFmi4wXOrhskR0D0A0NI+Yf5EyQjygHiD4cBIjpGbZqAZj4DqImZusn5oHWMqBclqWU1YEcZIhAINjV1U0p4Txm648l/UY+Oy4QfwAAIABJREFUiv4vGoZl+TyyUta1srJwSmkwGDh+4uRiqY7wxvDgiBoW9X6IqPomkgCilxR7bbmimP2HEIg+PGKPEkIAUQKY1vUTo6NFvhwHDnJDFgyAcW4l1oRGnpGY1cfM/IoGicaxgUROYPIAM8jTGvJvCf1Bp+5bXgiFwp1dXQk2tETpP+YFIPOU64jugViKLCIiUkqDweCFi5fsnXwhcHJs9NjEuEJp5PJIFOZLAIgUASWEEJrkHohwAiJ6xQC1HOyi9MjE2HAwsJBX6MBBZsiCAXDGLH5fsApHlrjQZDJvITTxhv6UfoKE4YU05qT+5gzDMDq7uiLBnRG7BU/2xcS8wcgBwHI8WMR/jD8lfggASunY2FiJ84DxcOjg6LBCKTVl/3jE7g2JxjJnotZY9AYPpc/194adDGEHJY8sCKthGNGKiPG0wII4HhD9yCrZx763UPv0nAABUHKSv3IF57ytvYPHitdHNK2UJcyihR8ix4Gl9lki9U/lH4jaguSRkZGe3l47L8M+zGjac309EiGRfZ+q8DixlkYyi8AlHJM8tFk9mhBKSIixRzuWfvVsB4sd2WgAHGO6sWnhibkUI4clhvUQIJxYaH3SI5GKE8TM/+i4f3NHR2e3YRimOJ/WQI/RpTTfWb8BAECSpjKCJZhXUD+UZaWzs6sEeQAivDTQO6PrNBLHDFY1BwAwanmMVrSaz+0U7x6wQiIkzNibw07/SAcljew0gESbfqx0MAIiRpp6kTiSjlHfQPREYYGIlBlLNhBZXATiC8f+kxu6untCoaAQdq1mfQCIUXME4BETjyVRI/olQsQ/auXc8cdE198UAggAKorc1dU9PDxSsIvLGoj4+lB/fygoR6P+AQAIoZQSiwU/4t0VbyJqUAzmW4w4B6j5IUYHFO8BQKb0yMTYS/0lxwgdODCRDQNgRtRIEG8LNnmARfqPdHi00POYFYhY6Ek0hChaT8IiUiEQBIk69p9cMDQ07PfPUkoj4n182GIsXQORA7fyhqQunIKok5Tfm1WbAEzmEZGtZVm+cPHS+MREAS4uF/TN+k9OTrii28kk6BFTlxD/CQHxr+Ug6yDm27gIUQtjMD3D4r2HSmdnpvtm/QW+OAcOckSmDIBxDlbCYbXmc5EFZpJvEcQT9RxGy/2bh1tZghk4JDQDQOAWVoGAjvifAyYmJ0fHxiQas3DEBNVk/02cBQ8Ro4eltBfF8ZGUiH2nKPK5c+f9/oUnf0HDeHVkyCXLIG5FlI7HPE/Wni9JwKQXJlJWiIt9S4hHkn7d1zMWWsTVMhwsYWRKXg1dJ5Ea0BYeYE2YsViErHYhwQEsLxNHjmkNZhMBbrIIJPPaYR3EYzYQ6B8YpJRGRPGYmGsmagNATPyPvTd5QfwiEQIWfm9KzBHxH8C6piRhR1FKT5w8FQgsZEykwfnj3R1+w6AWEV7AWtdhjn027zFWDpFcOlSh9Jc9nU7vMAcliIwZAGMmoY/keUViBi3Wg2QeAHEiVTIxtz45FhtRtIeYU/85S4RC4c6ubimqNpmRuiiqagNYXDPCwB85kZBoJncEcZ6AiAmIQDRlKkFjiPOkWl4IUzk9cfKUqqq2X2yGODc5Pq3rCeXe0CK3ZEL6E4DxxxDT8pV0jPnJC31O2wAHJYeMTUDRJAAAIEiiIUBAgFhoPQAk+H8xThsAq7MxztZjBUbUgAgFyev6LicEAsG29g5B/S3OyJhEn3ArEWLZXjEzXbzxJ8o0MP5EIBFPP4kSevMbE0TwF0IIRzz61jFjIeLiL01NvDIy5LaUexOg5hQtLxLzvKJI+DD5MGLmglk+tD5aEqFtgdmXHYewgxJDpgxAN3RrID/hkVxfSEUgIkjWBqKNvtDSZjZyrGkJirIEAsAxUWd3kA6hULi7p9dM9zXds1bmavX9AkA8lTNjs+KGTSDsaAb6YOytqAIYPTyOeAoxgRDCOD92vNiFIsKG8cboiE+STTKNqYg7WK1YGPcVIYl3IIGRJJxnviVJ/7opbZ31DwZm87ggBw5sRkYMADHSKCpCIixKb4TIWyJ5oucAAJCIMziKWBCF8BlH/swAUuCWD3na2HMHCQiHwx1dXRaiHzPNp8/kiilkVr+uleHGVjTePmTWChJx85aomUTtwSSvlNBwOHz0rWP2XfQ8CBr6y4N9Yc7jInbSHBy76vhwJ7RQ9ESdAOMOj9H6JA5q+SMvDPZPqOEcLseBg0Ig4xgbbhFyzEhQK+lHcRS3KgoRYJQsJdT7iTiGufUFWGxHVHJCgOYHM1h3Ty+BuCjEOURty9ohAhGVIcRX1sRYxLgVtP7PdAVHa6lZTkkS/8WRQETBuNCp02fyudjMcXRkuDMQsJr+E4X9ePKdwgSEaY0/gpkQTHUWsRwcz3AoAZWxn3a2Mcch7KA0kCmFNYuzReg7t5iWTTaAaIr8CIgkaZej5Y9HmApBIs4iGI0yile3HcyNi61thsEAQJByxhjnkYI/nHPxlluzN6Lmn2jkfgxWBwCJkr2IZQ7jwljMWhGmXCByoQgBRM45Ms4RGRdgnDPOOSeEjI9PnDl7rnB3Q+DcxPjp6Slrk3chsxMESqLEOsqvEllE8luScJ8AogosQIQZWJUlEJ8SQI6JrgJC3JT+wuEBDkoDBDPYiIg4Pj4RdSxGz4xGhhDLR9bP4w6b/zdiI5ifKC7Z5/NldPocA+NSriQ9OjoWVlVCousYc8tYhXZEhOmZGfM954JhQHwKWGKEP5r8OP4LcVZy5I8YUFGUZcuqonWnAVGY8mLzYYzV19fVLl+e/+WnRP+s/4nebq8kxS17zACEouJpbKMioChgm2avopWspzmGAEHOY04DjP+V+FMNxFUe7z1r1kpLeGs6WAzItMwON5WF6D6OaPdIEKK+Wi6s+1FHcTQGJeUjY/GuIUnJJux4NBjjhsEoJbIsLUl/8ooVtRkeeer0GYx6cSDmISBWh4FV1sWoNS5BQEj+wHI8AKDP59u0cWO2F2IXwoz9uq/bQ2lkZgiRODURrEqE5SZ+y0XfiM8jBNwypoWBxW/a6OiRWLhYjQnESGxs9FuMUyMkQnqCgbMTY1cuX1HAe+HAwXzIyASkR7vcpVUWkKNpJrK6Cnn0E0w82RokmuwYiLoE8gJjnDEuEqAMgzHGihyFUlrASBWmONE9XrJPtAgBJNixzfAhy3mmhcP8aiFv8lgwwAEiJfyFFV9QdQKEEiBC1I8mNQJE/BlACBBqsQ8Ry595gPkXPZISICTyL4BFrInoygQjv0gh6jGIyEMeib42MjxjX3oE5zwx6suBg/mQkQag64ZpShFSDZCIUhAzfSIHIEgsZmEzcdgUt5I3Z7K4ZUUem5lzzhi3vAVhyAYASqm1INjlANMDH39P57q/GHXsgMWMJvLFENEMLoLY/+dxPhcHDRWVH9u8PcW+ItaXJGToP2q/5KbUqnuaYno6X0DCh/EHx3hAmPH3NLfUer2pw6PNn0OgNmmlwr8SHdbJnnGQKTJiAKasZHpnUei7FrVY2H2ICCyJlKC0np9oCErJEswHBs3/cgIiGEacdTuarYqEEM45YyjYwOXzqGCMvkU+SCctounSj//QQvQj45h3FTD2ibGg3eEJWHJH0q8tjTm5o2dFTJjZmR7THRxVJeYczKatxzmP18nAXOXLZ3s7yA2Z+QAQE+SiONkn7iuCgg1En6/YkfFu3nS+ATFgxCabqzyZLulURLkIuo+IjCEASBK9HJ4TjNLpZM6a5ACIWEUARMZvSkc6CmsPWpKlIqMshvp9lJCEDZjWvZvmmDmOt7LZQoPzFBOxcGvBlZf+9l5A+MfHn3vx2PSMVrOy5m137y1z2VO9ZnZy8rnnj07NaPUtTffdcaUtYyYj42cVY6kAFjk9alOIvkIztBCjdoe476PR5Rh/fuz0zE0UaWEY84TYJfAV4She2sZTsQ5RuwhC/E2wFnKIHUhii5KG+otTTT+P+CEwzWvBQHBocGJyyuZKcIaujQxPjI3P5DPI+clxOY0ZkFj+neOrOQ4rGubYtAkxu457oFDQ/bdv+4dX3hxqWbviwHPHgxoTH4dDqhZ9DYihUJyzJxxSNS1OSFXDKot3eg71dHeOBBtXl33mzoeffP58gaafIQMgMakRATiYSVsmEjZX1MErNl5cFVCT4PN4+s+ThjIYgyzBGOfzOY+t0e7WE5ewl9g/O8u5NXcjDgkJAXHqGrHGwKSEGU8fh9dfeOXK8v99x01fqan+q+cOd+Q5fxNGOHT/jv91/b7/s6L28//28Ms5j6Nznqi4pPo35R/Ev075tijAubd68lov1e29gJjo6H9zhL37PTfedsferz348RXlrpB/8suf+57X9z/d7i8EGLSfP/ff3/Mln+///dK/PskAUPV//jMPen2fvXLbF546cA4AmBZ+6N/+a6X3r+9751fahmK10zdcdfWH/+DmDesam3aVNTQXKmY6C21dBHxGnLoYMdFEg3YATCIRK+UZ+1TUdgDkFmpvNoqM/IkUMm7RGER+U+ZgjCWY/tNeCwLnyDkX/zImUqjQMNi8CsQiRTTkP6V12NTZou9jSXwELAl9SYKk+dYcFiRJBjX0xY89+dePvOtM65cmRv7pll1rASA443/miRf//RtPnGsfAYDTR0//69d+/urRdnFid2vnj7//1Le/+3Rb36T4hBvaS7959cFv/bp7aNr8Pdnj+eFrn2vr+sdffX//tx49oWW02imQjqzH3ZGMhzKPTzlUgcD5/M7elDzAUQZsRM3mzV/4+Nrbrvv63/7Dz/whAwD+4vf/ecRXNTD0hd7eT0NgZMO2r/7hZ97VeubP//bTz3f2jfd3DfzdV7vaOj771S9d946bf4gA3/j7hwbosgH/PzZMjBw+cskcmU2Praj7xIZtD977l/ft3ryyQPPPtBYQxDeEif0bFfZjpp6oqzAiSMZbeJBHSw+k3IIRx2OUJZAstipj3DAwS2tn5GmNtv+LMAZdN5aYUQhjluIU1hyI/4IQoPEbI5bSAVZXsLAOmfmBkWMUWQK3e/t11Y/++I3fHTxfvaLardDxwb59Vf/7wIlhBThQ8pNvP/KhTz9d6XO965qvP3+kC0D/+N3fONk+M9M3vLHpnwcmwkydvWfb//fGhXF1fPym93zPIgiQEwfe/MDv/8sXH259+gd/7MrV3TDH0pqkfI5P5t5lmbgT8gTnCRHWaf/SgHPudCiwBeRzD/7VpbN/fuKXx77y6OHg0OD3np358w/dtaquprGx5vALxzbdfMXb9m9b07wSgEuUtl3qvPODG9a31LWsXQEgTU5PPfWLvoEz7Xfu/8eyG3e+4207zXGlqlrER157/l2f+7NfTYUKNfvMHiAC0UJfGLMFcfNL04SMCWZ8U7BPtBZFqwMlbleIe0uQcJ6RjMcYNwxuo6+Lc9R1tmTYwMzMDJnT5B17m4LOEWFqMEN8zejP+GDQCBSXAkC/+OBffOgdzZ/Y/9AXv/lbAHj8x89v+eTOLz/wgY998t2NPu2Df/7m93/wpx/56P133VrR0zE6PTj6TEf4Y59616f/+v5aCI9Ozr7w5AF+ZfMdN21FPXz/XS1Wt9qabS0f+cOdwe6p8anc22zFWcgt10pijwRarT1geZ1kBUJiORgAJfJ/2XvvODuu8n74OWdmbt9yt/e+KqvebEuW3FsooRNsIGAD+UFIg0BeTAkQCDUYCAmQgEMNAYNtcMEF44JlSZZlS9pd7Wq12t7r3dvLzDnP+8eZerdod1WQEj2fq9XcuWdmzsyc85TvUw4Jnc/1DxjjCyTOrPwjLOD/FQP8j0SoPfzIwdHJcG5+jttLFJlSWQKAthN9oenZiMaJRMKhxPh05Jc/eWLD3vraiuDBR17Zs7N6ZmLim9947OP3XJNDyfjJ1FU3bvvFo39199+8Otejj77B7t7u7rG5uXBn50D9NSV+7/m6g2UJAEmS0MhrBBBRPqgjwxwgm0Vn40KmjWzABAZ7RzByV3DBiE8kqC4jppBzrqrsfEQ6cK6DQsuUQxctOf1L85+UwemJ7c1ZhCJoShhuBLLNrCwxoCiudDrjyc276wNv+YdvbrrngU4AGBoOVVYFRYPRkRkAT2VZMDU389gz4ZbN1YO9fdBY2VDi7Ww7NQ3eitK8ofHxIi8ZGZ6+6lXXf/1TbzCvpTG+Zn3jza/ZIw2m+gdnVv1ACALRzU0jacyqd2X4PlAM1azY0OynQ3T9yBQMRCZk5LwtgmbPbjl70m0BfhkVWg0lQpGnf73/tiu/fOO131l/2/a/etMOT1HJAz+9+ev/cF9B8eeO9UWuvXXP23dJu9d98bcvhR594P0Uk79+JvLCQy/feMO36ndv//SHblNy8r56/60/+upjV2//xu+eazXPfPCFo6++8l+v2vblQyeTD/74L5TzdgvLyttMp9OzoTlirqohTP6sYv1ZmqPNDJ4fDW23kbOxCT0ChQgjwevx+P3+pbunZRggEEooJQQIQ87ONb8mBAghlJJLcY1iRGxrP8G47oo39XcwUB27ExjREgNWhSFEG9wPWYfYz8k4W99Q/5F3fQfrKhrL3P/1ufaP/eIt73/r7ifuf/S2Nz/38U+2BKor/u72K27c/Pmtr10zemyo+vrN3/rsW777pR/84qmxG68u+9Q/Hf/5Y3f92W1bXvjds9e96ckvfHrL6HDsn7747hyPBAATfb1btv/XX31k3YkDvSNSzq9//tcFvuXWMsmiI5PjbaFZ2Xyb5m3rnDwbxZkXCmVXbMwWICYUB2gK5O6tqFxd35Ygxtj5g5cEmnc5ZnSllEqmkUhejzUUM6kMJ9Tj1vfE42m/3w0AQ13dNeu+2T/5xYp8n6JYZq2azqhIfB4Hn08nMwzA53Wd184vSwBwzicmJyml5iywNEY7f19EBuhVgywOv+gImw+eyrKUn5eX1ezUzNRDXSfcsgQAGkPGuYp8Q7BoNBELZ9K31jVvrajijGvnWgwILiDL0qWVSKyqatuJDkdFNp35O7y7tm1jA4yUMMsxb7FK83WhWfkGkDFt86ZNlPETnYPRJK9tqFjTUCrO1tXePTA8V9VU3dJUFp6aevn4QE5Rwa6tDaCl3vf6f26+dfeVmysb1tRWl+fr7U90D46EgyXBbZsbRV1wLZM+2dk/PhHNKcjZvn2tchay+OXJ8da5kOzIBluURAY8GtvIz3AQQ2zKyd1bXrH6/i14WsY5x/PNo8X5L4uB80EHHn7svT8ea/vlXRfPOrfLrQY6Pj5BJQqAABRN5EgHhdHB3OdHRSziFDOXk8wuDGdsi0uXFGXXO3uwo/WNxw6BrAAgcH6tP3dnXsGVBcVv7TwKnAMgKK7fbdp1Y8NadeWBpMskWaaXijUQiUZPdZ/WFwuzqfxZrx4RjbdlRvsQUzSYjntjr3Wc2Cf2qKq6a+f2FdVwnRzsL6393Mnhr6+tzD3LO10+PTsy3B+PSYSIwYz64qNollEQVdwQgBi13ggQbjR0ij5xSlPfQQ2g+VwLAERUVWau9HkBGLQwec/3VS7TH5eWZUETYkIfBIAToKYmSSzfoKU3Okrgmq4AME5gtwxsJoLjJAYhoqqqimIZR5Px2BuPHQS3Fzi/xu27OViUAfjlzPiTMxPfbWp5YmTgwVQCOL/5xWcOKK4rqurOORwkSNM4AJekS6CeRCwWW068ICIx8n4FMycw73XAIqJcME4BKLtcKzNavYHgseOfaCjLWdFRZ0Ma50OJuKSHOhGi17UiCISa/NwoCyf+UPOvuQv0oU/0unJGHBQBeh7wdE2zV/sBOP/LZYvA6Mti4H83LVeHVRQFzZlh4+sCGwZE4JYPzVjg0YwNRbt40P/Oj//JQocEEyIkncnYezIQjQACIP9gQckN+UXtsfDnxgdPMtbGtfefaru5rBI4A0LA7b2/+8S5Kra1GDHGMxmmqhd1sFA8niCEzBeuxAaAEKJXfdC/AoDhzV+QTBFhHiFiRBVFluWV4fI5BXlbNjco0gXkMoRQolfxBL0EEMmKWZhP6CyO5PCl2PzERgHRczkeRJ5K1sXPmPB4TohzXE5y5WW6RGm5c1WW5XQmA2DYzAA2MECYpaK4ChpbtjrpVqRdlh/NyiwzNxweBQ4ESSajgs0NvLGoGBQFNLUpkPOh/lMgyUAl/QBJbpud+nhp1RcmRwFAuiAQjYiPzWQ0l0u+CE0BznksFqeE2gUAIcJ4E/E882FAM+bHtktPlTC0AOqw18RDQARFOb8+q3ND4uYRRdVCu3WqpzbordCmdCMlBGwQJdh+0pdVMMJk7UbCOSGm6Vk4WeOL8wuBBaFI4+QIAJJ0sdu7F5JGOk9/8pc9P/jHW7/8N/+++92vvWZ7zZHHf98dC77+loYvfuYXcQS3z3PnB17XXJX/4Pd/k7dl+84q5Qv3PJxReXFF8Qc++FpPOvaJz/+SACTmUnf84zv21hW88PsDDz7SpnF4x3uu+e49vysqCyiUTIzFPvL5d6ypDJ6PW1iuAHC7XfF4XBQ3JybLJhyMVTBMUMiODunVx3TU1M5RBLiqK/m6Y9EJBwEXcQnAnJXdvLISe+M7X+zvG45GAJzBGYR8JzT9/rwCyKSAaXdtuE07pzFzixEiKspFuuBMJBLlnOsOgDORYa8hGK9lHum6rU3FNf0EBID7Az4WT3z/p8/c+d5Xv/z0Aa2gbN+OhtDgwKMHBt/xtn2P/uKpsXDcnxfce+3W6rLcucnJXz3R/t533rD/qRdP9o67c3JvuGlnZZH/gf9+IpTSNFWr37j2ln0tyWj48UcPT4ZTV12zIz410NoxGfC7U4lUUWXV61+zcxXPRNiFpnZgQvqASCgxRqXB6YRe4ox+sJ4YIiUEwWLEi/u8Vkmaxoz5Mt+sEFLswg09AQpJlxfrBgCATDL1wyMzPwDoOzTysW/929DM59KR8Pi0wrXMz+4fffLgR2JDXWuqPzuTvGd2eAqaMlqKfflQZvqhu377s/vvefDAp1616Z6fhybb/kqR0JsXOHHwxQ9+7sBDP72T8ow34PvCV97dvn//g0djX/zqm/y5Z4iEXDUt90W6XC4rjtDk5Wiq7oaH0DYSHWElaBxm4EUWvmD+RSM9DBGRG2oHF1V67J3xUXlvRf0bmze+LhAETQXGjI+21eP79O4bJ1/7jqk33FkfLNIYO7dx0/MJEWVZumgdwpFIRBTn1mleopcRCaqzcIAFuJe5GIAdNTLI/p55Tk6Olkq///0HGUJ/e981O/+1aySmxcL//UAnAHzzbQ+WNTYE3erO6n8+PRJRU4kP/7AVAJ789QtyUVmFj1Xt/EYkoT76HweLaqr37tvUXF+SiUdef+XnQ+Ddua12di5WU199xdaau//88dqWpvVrylf3THrDcxrnNrZJxa0RSo271I0B+9OwM19TNBIzaEY/zPAEnCMICBE1jaGBoxrJk9ltLiQCeZn7m0QIAYUCAIurP3zgTXff/eOERmWJAhB3vlJSmrP5yp2fuy091DMsuxUxQFwk031qcHBotjLXjwCbi6Lvv/Pf/+SmbyUj7PCRYx/5u+trqgqrasoLC/JKSnKLCn2+gLewKNdzjiqMzqflWgCEELfbpWlMqPYmYOxY+J2gTfvRNaGFB6bBUcChLtkjS8zJiQiYUVWvTYdNJjMAIFH6P9e9NslUMQUliSKCRMAly8KbaRaF5pwTcr7cAYoiX8xestlQSHTPjLkSZAvlFF+JLdwzm6zAUIspomkFmPFCnKPf5wOAYI4iTvqFr+756Ed+8C8f2psfkAFAAr5h89r64k1f//LA71/qeuuuslq/BACSTGvrK5tcMRhMqgx9+a5wKDIhpTdVVXW0HlOu3HDX2/aanamqKByG9JYdLSW+VXIiBCTLcN0Lh7BpCZkRbZAlRu1kLAOw3KWwz0RqhomUNItQ77+9mRAOF8AUWKYp+X+N0qfS23ZuruTTn/z0/js/9FoAAEIkCQDwxcfTb/xOwNQmakE78PRLj76sPvelnerkZGsq/9AvP+SSQZIknyswNBm2n/YCJOitwF8ny7KqaoK3EhviCU5nIAAYBsGC83OeKzL7J8GtrMggQkBTVfB4xPdMWjNC0REAvJJCCEkk4kePH1Ncrh07dk5PT3eePOn1erZv38H1lcCEDIBzHrFz0SI/gsLhCOdoyifD1SlUfkdLE/bhVpC7uWCwaGBFH5rmgg0F0snn82EiJba5prXs2HHjlpHPffWJnFIzKwoBgFKHNHL73F/40L1PPRd69A9/WZjj0jROqUypTAlhTHNlBfwzDvq69mehitqgerTfy7wwZjTVlQXPMA/LPIfENG5f6stx8YWW/RJZAudPHZHly9zfQZxzCGkAENbiibR205tu+8UPXkylNUTsPR568L7nDz9xsPKjN7fUBZ+LpvyMI2fdyZwP3/2OcM8///LJ1jdtr4Tu0CMPvUCB169tuOV119aVf8OD6QCmqzauv2nvWs5YMn2+AtkFrUAAuF3uRDIFlm5vJU9a1oA+e4xFthelBWHSeZaAESiaSKZycnIAgHNMp1VKqZmZiYCyLD/y8G/+/QtfGT/d8XR3z6O/+c19//Xj1o5jx073VVZVCRkgGnOO0rmLNnG5VpmGesFoemZmHptAIMRYbhnBwe4MdB8sqN+UAVnYnpkcYHOFApUkr9ebiidCUQ0AmMYSifQVr76h8mcvDqcZADCg3Sf7eg6P3/6h9lPDb9bYzFiCAUB0NvbFe//mI0ee+9Vvj71qT1MqohYW51ZV5obDc83r1h743n0/e9XaLQ35k3Ps+mtaABAgczasFvWoJ/1GiaGtmH/BeAbg3AM2maOvCm+ZsoCm0QALje6Vk9C3FrsH0+3s2I3AGFJ6Lp3DiEDpZd1/ASqqrnjicz4A+NjvPlBX5AaQPvft901FJG9O/h/2vzOW5u/9yNs2rq8BgOveer2rpNgfIE/FLKLLAAAgAElEQVTecx0A/N3n33fkxLiSE3jmmbcAIufc45aDZRXDM59sb+9TZdf6lmoAqF63/l3F6nm9Bekzn/nM8lsnYgkDTyamiqSTLYqQZDEWYtsAO5ux0TyJwMGYnQSQc6/XQymNx9No+pWNiSdT+tjM8P+01IdeddMddU0vscSTm5rmbrvhXWtaSnPy5qc7IeJZakmEXALcX9PY0NBwFiNAUek5ax+AuYua71FHs+elhdrkgVlFVbxjr9tTUVHOVS1YnLryqvXxSCRYXlJTEdx8RU1G5du31vvrSU/PkCr7//Xbt9eX52ZSqaDMrtzRFI6Ey+uqd+/Z1H+sLVhRkVuIg0MTJzsHpxOZK3auv/P/bT957GRb11hVXVltVRFwnuNN7tyzybvaVOCZVHI0kZDEIEIAokcyUIPZ6XdvMlgjRiHbf5L1F3UDAgEK3O6qwFllNqTTml7DdcmhuiCft4UynQOilFzm/guS2+9rrCkAgOqGioBHAoBAXl5pSQ6VpMqasvr68pJivYpBcXlRQa5H9rgba4sAwBvwNzaUElmuqyurqy+vb6goLsoBALfXU11bXltd4vcoAODPza0oyz+vt7CsTGBBiDg8MiqLoWB3F+oFVMxSKmD7Tfxu/GcYBmZAtUP/XOiS5qX9fp/fH4jFUiZDMkF9l6L852/u++ELz0+lUw9/+O6H/vD0o+1tvYnYUx/9ZFNVDefcFsVhkZABwmq2lTcA+1fT32VLi0VCiNt9/qoznTOanJwaGR2jlFqRWQZmb/4BpxPY2G/zYVpvwPiPWGewDx7OWXlZWWNjw3m+rXNAp+Zmj0xPKTa/vXPw6ndoqPWLclI0mzqJA/ol+U9q6qXVcmHOeTKp6lrOEi4Ho7+LsfuzL+ogSZdk/avLtExagRpLCJEkiSMaq8WCQxu0h/TY3AO24wHmaf/z8QX7b2DOP0LiiaRE3eZawYQQrodmAwAEEpnSk33ecNhHqC8SL+4eIJOTLm5xMZyXOWnmttg7nvXVGUGkwyaXBPcHgImpKSoRzE7hseFrdujbCPPXWaHDY+xoKV6hXhnUevPIOc/NW00tB8b4BQ4sMVmqwWDFvYg+oA32h3nc33gUSJAYJaDnwTASkIimqpxJ0irNxFRKNSW0aWYtysmtyN1sc0E49o3X5AD1sqaDw7tjRARcQvVOLtPqaGVv16UoeiAaAreyX62FvnQeiWLlFxtPtX84d3wFY5zaPvbyY6ijq5JYwx2sI2xLyyCgLKEii9oUKFGuyE7oR/TorJx0iOhyKRez19ekcCSiqirYynKg4z8wDZr5xxrguN31mS1FCEEby9C5Rk4gsNJ+Tk9HDx04HY+dx+r580kv4Gxxf6Kns4j7AEIBhPcp6wNAjEeK1IKFTKRSbFAUFdtW6wfQNMY0bs4Cc92WMw5ePW6aZ08m+077hvPDRcavSP1lDAm5HPH5v59W9oIVo8wLR67PHS6mismoTfeiwR3m8xhC0PyJEPvKAWhbX0Dwed1sQO5yeZxKKIBNDIAt0GUpBo3mkmQrlgSIIMvSpTIlRkZGKaV2LqSjbTrsrSu/9kxXgyEa7U14DufhhAiIdvSfIILL5fIYkVrLpEQi3XVyHACOHxu8kMtTzWXS1NCWhTOYEiCiNARBkepAqW4oOD4A+kOlehwptX0sgXB2GkImpdrfCwCYYzZLd5r/EbNodcPbTpReAl6uS4s6O0YmxsNnbndhaWXszOvxOIxEQH0lX1P5Nwxq8QV1NBkNSWGzEvSpposKszmx1hATgoADoEQlSiilVDKJGP8TSgiZnJyUJIlSyhgLhebObLcicM6FMMgSJyaJZmbmjSSRS2VKhObmUuk0MTV5AwjWI1+I4+EYlTtMWE/fsDiyzQgwz2nsIWb9uMLCla1bnclox44OCmaKiIcP9ax0CehV02AsKlMiBqBNVydAQAIK+oPSE7sIEKovGiMqCImvNoXD2KCE6ILEzI9fOWkaE8vQIQJyS1VHc/WkxQs0WWTau6uSqoiXuf85pr7eyWgk3XN6cno6eubWF5BW9ppdLoUQPQTThOMBwIx+sMP7AEY8HAKQbL+vWTArC7S0HMMGxIqAiuKhVOruPvXxT3wsGCyY1y9MJBJut7u4uPhT//ipTCZjrx66GFnYyCLzxOE7RXB5Lg3oHwDGxyccYRsETE+AHdwntopA8+PKCZiubxMUFvYDCrZn1EYjAMAYLyyY/14WpXRaPXZ0UJxaXJqp7JUj/Vdc1bj62142UUK4FQiqD0Qj9geoNSSJKEiip0DYwqWMlcKo3WGAlogFwFVKgFTSGfZnTAg0irUiIiW2ibIE6es5mLPTkN8I9hFvQnnmxqWCc14qNDYamhiPUpnICj3VNS5REixYMVh6nmjFgIbb7bJxBgAdSEYLlLfh8mD/y829Oorv8DKiYQCAATsYPxKgkiRTSiYmxjOZTDqdmvdJC45PCGGMrbQa5RkJEd2eS2ZKzMzMptM6xGGt02DTSImlthp7HLEiaGAIBCwZqbdCu3/UcO4QArIs5eYtN+pRU1l72whnesVAMSoIJamUevTl/rP00yyPLNZv00sseWYDcqjYIFnNDW1HoD62bTDVnlWMF8a4qPyDdreaAe4gcrHfRIQQbVNniQ8HzgE5cIbC7kUO5ocz5AzFBmNcki4ZnPOSoPGxuf7+GSqJ1wYSIR0nRqLR87bK+wppxW864PeL+BkLIciONDeg+SxwCGz4kF3RtxAYHS0yf0cAjijLLvEroVTgPPPJuoItlO+cECIoyiUzJRBxZHRUqP+2QBZdixdfliYTG9c15Hnt7QxaACWcY15erkSXFSrOGD9+fCidUvXu2cKsKCXhSLLzxMhyzrNqSmqa0ImphdjrywIbd2zdt9igtm3jY64krDdwHksoof2RyEr7lkpmhJS1e2vtcJAVSGHW0+LLkAB2mr/HRpTQy+DPOaSZmdjp05OUCBxPrL7MCSGtx4bi8dQfu3cAqxAAIiELwDEcYZ4TCUyNxahngcRi7iAGtCkI0ID/7U4Cw6SQJRfnpnWwAjIBU9N1tgoiBFyXSNwnAAwNj2SFfluYDwFCsqNCsxy/YPB3or8sHfawxKrt5KafknNWUly8zB52dIykUyqhRKiy5jXF2JAkOj4RPdE+vKK7XhFNp5LMhl6Z6A0Yewz3jzVerEdmBKfZDyTOZgTEcvOYXuFqdIiYyWhZexz2kLNfpqvqXJlMiOjxXjJD/eKniYlwR/uITAkXjhymcySOoHF++MW+812ncjm0GsXW7/cicnusv1h0K0sYGH5FAgBCDOjCgOvWpzmCrdXKzbA1Y5grkstwNsBKA0VMv5nJD1cqBhDR67sUCtwDAEA8Hp+dDYl4dqKT7q1ZAAtyIDmCrdsxEP0X49vCI9V4w7SoaFke4NbjQ5FwilIdbNYvgQ5OJ8t0fCxy+vTEck64CjKL/Zj3SQnRYwkACIDYppTqGwD6hxCx/ptkKyhtmA6mHQDCuUydYnU5lExmFgyac5COCdkCFRAJCLlOiKjPAqspfHhp4ZwXP0UiyZ7TU4pMUSRecCv4ERApIZJEDh44rakXKPBhMVqNAMgJBHS+DaAD9sQK3TRlgKl9A4DpSwSboiQI7UJD/GTFwHFFdqFx2tWNTeOp6/IDzUjVxdrbIKmLc42XBQkRB4eGZVkWGrz9J8vzZ7juSXYDu7vG3GvfZ70u+/MQz7agILicdKGensloJCWJBGzDA2TKdcMWAECUZdrfO93XO3nGc66KRK6IXi5HMowgYlWFFvE8+ocQouOM5k+UEgD7TvMogVuaUmFFlE5rBv7jeBHZdgA4zOSEpp6cnVSRz6WS3aFpQshEPNoTmlmpDJAkernW27mieDzd3jZMiB7jIKJgAACR2N4l0TR+5EjfBXF6LUqrEQCyLLtcLoM1mPx8QcRRBxPAJhLs24b7WG+chXsSIlEim+cRQNDy+2lXlGwpYwj6agPAuf6ZD2cJeaNcOnjo8PAIY4xk+3ctMkJWTLhlkWYGKGSgP8bRxu92AUMAOGdlpaVn7N7Q4MzYaFhU4kPr+g7WZp8Isky7T01MTJyXuGlKCEEUSr2pv4OtsL+98YIagBkyZWf94szSYscsSZmMhrbU9PlMfwE2gUAJPT47vuOp+0KpxJPDPVt+dx+h8G9dRzc+95BLVlZi76L70glyu8hJVVlnxyjRtQF7lLn4a2nJlGAynnnljyoDVsng8nJzp6amrYXiLTJ1yflTwA606m1QBJ8toOEAIsiyiyETa3cjos/vGxwaqBDR4gScEshxMAE42n7M7/NnwblWD0SYixGDiMb57HaJ59IBf0KhublwWJIkgXcZPJoTx9NBM0AF9dU6AfQYUGJs2Frbz5RlGxh1IxDQ5XIVFJxhsbrRkdDg4Kws61WJrBmBQAjhtilh+YgAXC659djg5i3Vpee6HpZQ6q2vxt/sUbvgKAaAeSVCwWbdCiwIV1hBIZVS7RdDUbOVc3s/DfeM5Y+RJdklK+D2yrLsVhTweCVJ9isucHuoTEAlaKvuvdjdIILbc8lYuhc/vfRiLxAdatW1TgKWYWfAn4QAIKESmZtLth4f3LK19o/S2xUUg8ui4eER4z5ser5+VnHbziGFaP20cF+y2BV6PblGhRZCCVBKZ2amTUTDyOIh9hMLswsQFEXOzc3jBgJlH/tohDzrAgAsvde8viQRn8+94ofyx6BUKn26p9esb2p3Etr+WqqI0DTRFoBlsmWb3uk8yilHjW1kjNXV1VVWLLUy19xc/ET7iCRJdrRfvELd12N6BDgHJwbCEJnKrtrdlJvnPatnZKORWOyl6UlrER+zqLn4ZjjNiQ0m404hYW04eap9j4bYEMjZVFi0nC4h4vR0jFIzAAsoNevI4XyjBECfC4cPvxjR0k8N9b2uYf1EPPLy9NgbGze0TY2OxmO31jZX19Ssb9nANI2bTu8FuTwBv//SGOoXOSHi8WODiYQqxpYN5zQml97MmHLGUFc1VlQY2Laj7sL3efUCIBaLhWbnLElnRR2asm9ZpOuBxtzhpleNULc7YA1ZJEaV82w8Wjg7URdDlpa08K0tQ9HhHHNzvRfzOl8mMc5PnTpt4luCbGYmAqK+oDiAwcjFWFxYANi/QrYMsJ6ouCLneMWuHUskXkQjyePHhxRFEgfq8hhBHGteRkwJp41sSaKMyq6+uikn99zIgJFY7Mj0pFkK1EC89Gtlu0fm0Xxj0bghfZfoN0OsD+RsXJ4ASCYz8bieuS2Wnu/q6jJ+JGbZJTtRSqdnpp577jmPyy1TqnJGgUiUZhiTJUqAJDPp5ubmbdt21NfXE5Jd7NTsNiL6/e5LJcr5IqcT7cPhcJJSKgY3Iool1HVMz2imA9AAHIk5r1SN1dQWrltXcYH7vHqMOxAIzIbmwHDvWj+Yls48/mvHgMyJBJZnGFHPECYcuCJ5wKatI+EcKXW4GSwAiZhapPGDfqFsK8TZoYVNYnC7L+pVHu3U29tv3KzDgDKYOOpCdQGy2gsUSEiK7EYCMNLflv0QwhgvKSlegvsnk5mOjlFFloR4Mhh8tmR24k7zZDYBRaZHXurbs7f5nNRhpXopOPP0to0zvnPD3rRYqX1M600MIGjZIyiVypj8GRFkWX7uud///L6f60nvNpvMTori8vmWEopHj77yta995cknnw4Ecmwom33+gKLQy9z/nFDXybFwOCkWprW4n66SGlEWBveygyHCPSnL0mD/jCzRpuayC9nts3Jy5uflhcJzBt7lVIvmzWQH09eZk64zOVuB0FcpUdBY+pHo6Ul2wAYMDxzhnBuiJHvOmbJBkiTGWPYMX8hCQI6eS8Qh1j8wqKoZc813ztEwpQSZj8nJV4kuF5AQgboYzcxCwbpXwDrAXCTGKdaXAH8yGa2tddiQHIB2FIrYpwEAgGGQWBzKzqsIIZzhwRdO79nbfPZpSv3RiCItmOLmhHgcu3VYCMAohu48hJgTwHma5RDnqKpWQWwE5Jy/+93vPXb8WCadkQjR3Qn22eXYXmBdMLFTkuTS0rIlTRr0eC4ZR9fFTEODM9PTMZ37m+o/iAIqzuFAADggOCaSeEOyLPV0T/n97vKKMzjVziGdlfDPzc0xaqRYPjxwboBtj2gpmL6t6BuAcTza4n+MeHZqumoBAAgQqodfUyolo3M9vb2yJGIfqWAiIkrP7uiTXMr+/c8vc1UjxX1RL/Nr0uDQUDKZpNSK93Pkf5lmlfHVrpDaWjqQDOeNm4LBjovoG5xjQUHQ611YA+UcT7SPiMV2rNFhGCXZugHOS/Gbrz1Qomn88KGes4+XUBkzQqWMMUsM2NHUKcD6QFaKrxXlmQWpmLVA0TzbciiVygCQcCpFCVGoJFPaFwnl+nPv/sjH5yan4qWFUjxFk2lEpKkMTWVoMo1gbXNCaTKtf3XuhERqfV09dblcVFaI5KKyxo30SwTgoMjSpWLpXsxkxDjoY4oTq2Y4iF0GGVAnouGb1KMe9S+guKTW48NjY3MXrPNnq08F8/NmQyFKqcHNdV3IrJNl32kc5JjDNtzG2idLLgCglPT2dCN1NdXXhOZCBTn+uZTGYqFQQquvLj/V2xfw+js62hSXu662uqer05dXVFKUd6qru7C0ykO1kfGphuZmNR4a7p8dGp4AJzS0IEyEeGnoRP0Dg8lkklBKjJVtMFsEgxPkIZzbB6LAMQhS+3OntvaAVvyICdYJbZeIrL6qykXxyuNHB1LG0s32K1odMCcHAup5TKYqYIgLp1wgBBKJzIH93VfvW3PG57MUUSIcTQQoAlBrAdOFxf48xN+OFRF7R/US6HqDBYD7BSmZVGVKX54cfmvXS7cXVvxPaAIyKfDnviuv6Om/uxMkCvEkJFNQkA/x5Np4IkrIaHEQovF1yfSURGeKCiAUXpdRu10KU+S18WSY0vGiIERizcnURH6w9Mn/+YviqhZfztNz0++tXXNjTbPGda+Qx3sJDPWLnGZnY31907JsGXBZyrAZ56ab3Ub8IZhD3dRGCACAokitx4Z8Pldenu8C9H9lawLPJ7fbHYvFjG8L6pUWmdJuaTUOERTZRwDcHvLkwc7GMt/A0GD/xODaktwXuicm2g5t3b33kYce2b7rCqamNaJokXFNCeZ46CNPPu1n6nAkva6p5of/+b3y4ry+0alXjh+/6spdnR1dmzZusIKCFr4uKop08S/4NTA4mEqlKJUMFxJAtvqve0RMJ7keHupQVh1kWgxWFp6dAZtMT8epsaAgWFG+MP7T3jacSGQs7i8WbHC+cZs3GWCe74EA2C9ueIaAUpJKqeFwouIsbOShWERFpLou71DzTUZv/rVvLP4RqL/daAAEyFfcxd4zzGHOMRpNE0K8ktJMZQX57UWVH6hdt0t2S5zdVVLzF2X1m33+3QVFf1vZtDc3OOOWriou/XjN+m2BnBm3cktpxcdq1q73+8Me1+3lNa8vroi6lX2lZf9f7bot/kDY69oZLPqHho2hZPzXoYmd/tw/qWn2Ky7x6BVFcrkvmTSXi5Mi4WR7+4iiSGD3sDg1Wt0FbHJ8tMazzTHjmI+Ukv6+6dLS3AuQnHEO/D8FwSBjHI2SO4LQLFXofBxm1OhSfaISIdTUCN0eH2MYCWfSqiaQTY9HSTKUZIkCd0k0WBgc6XzlUOtJn+SuXrepOt/1zPMvyB5foLBiw5oGJCDJLq6paKPFLn2Rl/1Bjn39A8lkipD5kejWU7VFgjoaUBPMITqjd1YGdY5CJ05ObMciYk119YI97D41Ho2mKCWLcX99OpjbC/l9s3cYjRBQkujkZPSVI70LXv2MFFUzcVWTbKCP+I8KzFC/U2stADsg5ESGjJ1gHmOdVCwYsBwLIJ1SCSAFOBmaeGB6eELLPD4z+tq2/S+Gp8e1zP+M9b26bX97NDScTn6xr/14ZOZdZfUtgfxP9LZOpZLvq2gsc3s/cOoY5/gXlY0UoC8eeUdFXaMv5y9PHYuo6vsqGgsUz1+eOuqR5Y/VrUcgveEZAEBEzi9nfp0txWKpjo5RRZERzVo4iCwL3hBeAB0mt+IgAMBpRzqOApRlevDA6VRKhfNMZ2sBAICiKMlkkjG+QNz/Qg6qJUgEF1KiSJIMBBQXffyJ3+fm5GzaurPQpxxu7d7Ust7tcuXkFzc3VBx64UBeQbE/EPB6vcGSSp6K+IPFuV5X3/DI5i3bN66rP9XTV17V0FhefPjw4ZZN2/NyA3aTyyT7LPZdxAHR6XS6f3BQ0zQD918IObehFcKXOz8+awFyPhHbA8luiICcY2lxcUnJAtXf+vumJiejdlh5vu6f3WPjV0olSiVKKRBK54k3MxQVRdHQcIozXlS83ALUJqWYNhiLyQQIggDQTOZOAETtB0BCCeirxSzI9Q0133JSORuK1EKZkAr/Gcq+RyNJ8VICiptomelMende8d/XbWCaFtYyNxWU/23tumgmneH8HeX1aab99Uj3XDLx8boNQ8nYh0d7XEz7bMPGl0JTd4/3raGKT1Y+PNKbTiU/07ixNzr396O9eYzdXb/h0MzEx8f7a6n0+po1AZdbPEPvZfznLEhVWevxISPunIgcDgQ0ioMYChYhhFLTwNYjfalerB0BiAhup5a6wDk3lZPBwZmqquB5LdGx+jwAO2maNjg8Ii/PywpgBeeYX+0/ypJXlt2AqLhoZ/dwS1OtqmlEkighnHNKCeecECJJVGwIrIOId8CRUso5B0IkShjjhOgznc2rzpjFHD0e2XuxJn9FItGx8XH7QxPxOwgcwVi+S3+MHPTwA7Ot5SQw8XUw7VMbcGlmAMzL+TK3QdXUK3YuEPs/ORHu7p4UYKjO2HlW+T5HQDQl1MQDKaWdnUenpscJpYCgMW3D+u2FhaWMaaKAoAE9GX8R02lt/YbypqaVxcxFMukD42NuSUI9JIGaCJMDAnLEzdtvYAH4TH8R4owIIHKqARnCDZVVElnUyEaEyckwQSJL9Jnhnh+Onr4+t+hAdPb+6Mz/C5Y1ewO/i0w/EQv/fVFlmcv9o9mJ1+QEm4IlmqZ+d7TnzcHSstxgOp3669Ger5TUBP25c4loRM3U5BUkUsmPjfV9qazO5/PPxiP3TA59uqJRdrtHw7NXllTuKa/TOPP6XBc/1HnREmP86CsDmsYIIZwDpSSRiEfmQo5oCeBm5R/d96vPSyveFwHB5mkDBMXtzsnNR6M8Fkd0u+Xrrl9//u7l3AgAAJidDUVjsZXGz5hZPxb0jOh2BSjRZYkkSTpwnx3iAgBAKYlG5gI5+Vn49bxiZyQSnsvLz1/iZpFjbr734qyHNT4+EY5EzKAme7qWQBSdPJqDjWtn8fQsAWBuGVm6Jly5wBkAQNXYmqbGwsLsxb9mZmInO8cs7m/n9Ea/zJ+E4nOqq20mNEUJBQRC6fHWg6HQhIBhNKa2rNtVVFTe3LQhP79QB04doCoAQDqtbtteW1W9gqUoI5n0ixMTIssDwdDkFyTLgw5mHGjW7iVI9PGaioolBICm8cnJsESpTOnzY/1v6DoMVNKjjUzzmRAjMI4CIqBYyozq6wMAAWpsm3xF7BSx0aKx2GbaY1v2Xl1RpzEWDAaWH6d0mbLo5SN9qsrNMgKK4vrUJ9830lRjOdlMshxn2TR/J5No7vGjn/7EvUIuAIiXxn0+5drzJgPOmReooCAYi8eXaGDyepgXeOMUGwggoQB8CSWEShJFZII1cBHlSfQTyYr7xecfuelP76Jc45zpGQOUAiKVJILAOaNUktzKY7944o477tA0DRbxQBBKLkLun0qlxyYm1EyGUkqAcOBOlkUI6B4lbkWoEwAkjuB/a48VyE/scsAiSs1sAHtaAAAAIuYE/PO5fzSaPNU1LssSGEANZHNJk/sbbJSQY60v9vS0TU1Nx2Kx6prqZCKTSqVKSopTqVQkEm3VDnOeKgj+VTBYzFED07xBMHQqcLnkY68MShJdUdy05fZ1dlGs52vxd0RbeI+lU9jNKkLmPT4T3l2GoGCMEUIRQON8T1nteFmtcaTtJGAGX2XdxaLCawkdjABojEuSdJn7r5raWocyGSZCzQH0eibp/NzapOaZiwEgzWiwDC1h/htAxNHGZjeVOXLkwAAZcolCIpF5YX/X1XvXnoe7OXcCAACKigonJqckZxkswZQNXwgh2VlJkGU0EBHOD0AIkSTy2GMPBwOkZM3emZ6XVcbr1u+KjJ2cjWaCpVU5kBiYjk3Opob62ocnIhvXteTk+GdmxjraWjfs2Hv0yUdcxQXrt1490PkScfup4rV1CZwdAERQlIsuH3J6eiY0NwcAlIpn4khCIs5UkvnjKZvj2/YIzobESKQ2ufKSai4iNDU2ZO0U6zuanBO5ET5qXdge8iOQIaAEFEmempr98Y9/XFVV9bWv3fOGN7y+oaHhRz/68datW7Zs2XL//ff/8Ic/sMaGeQYjFVCcVFboKy/37/YoBStZZFU/KdUxICCEUhqemUnFE3oAFecFpSUuj0dXqEF3Zgtpa9XIs70R1MWs3Ww4A2UyzO60Me07AOt9EuOpETC1fAKAxDAR7I5EqytgzisE2zUEFHE5+GfV1NszGYulBaRsWbliDQbGAVD1e9tu3eHOrMp5ixCV6XfnOjhAmpJ9cv4mpZgDUkrmQskjL/Xu3JU9+86ezuVQ8Hm9fp8vmUyCxWvEk3LiM0vBRAhE0lVTIqnjJ/0VazfX4uG+aWC+1776mv/40RMuHn33O9/8vQd/52WJ29/y5ieiQx5PYGqil7dQSunk6FhSjfSOTBNPcMfmXSdPt8+mtNfffG1/7084cluIli3eEQERZOUiUv8TieTk1JSqavYJTpDMB1YMX69D90Sb5mro/uav80E/3YpY0l1LGGNVlRUej8e+l3N+/PggMTxYBoJEABZYdcHEoPTGhMRisbKyskAgkJeXV15eHggEysvLxFdddrwAACAASURBVJ6CggJhroGtAIUtogzN25Ek6cD+7utvWO8PeGA5RCzDXDh+o6G5VDL5yPf+a7itgyoKACRD4dd/9G/r1q/T8TTOC0pLFZfpNRUmKOgy0zAZzHPqVsqZJEAmo6EZfmpTkqxbRb3clc0IA5Oni++ECEe0CYwJvyJoajqW1vICfoNHWTqCGbR+mVZEw8OzY2NhRZHQAEsBnCJYY3PrG72Uzvi8S798x5x00kOhYY2QsmjsYE31fyslAguSJDI2Gj7VNbZm7VKFF1dB51gXKC4qHBwatqM9KyDkAIRSWbetOVPK1mZeefQPI9HKDbfOTHf8/onf7tixKTp8/Jmnn2kqLyyU/b//w/MzoThnWFvk6Roe39PSeLzj5LatzQmATDoNmsap389DBw8dprLHDADVjRIxY5EIxPUiwX8YY5OTU7F4nFLJRGN0ImZvF5ABujtT50IWfzQUSuJ0BhDHaU1XkKndGq5141ro8bgr52V+dXSMqioX/bQu5Hj1FqCCNg4u+lpbW3PvvT8oKAgeO3Y8k8kUFxcfOHCgo+NkZWXFsWPHA4GACfjrneBodsnklYSALNPnnz917XXrzhjZIqB0Kq4PhBJQXO4Hv/0f/a+0Er+He92+QEBVVUWmT/zwJ4m5cDwazwnmqaHInff884ZdOzljYCrU5sQnxlO08WajdsRSfIBpXHgJJVk+0fpi84ZdblkS6Kbw91goHKUij0/UQ2SMSxJFAM64JEmJuYnuyfSWNZUIhFIaGu2dwZyGklxKqagQDoCEUMG0GPKLZKhfWjQ1FR3sn5FlKpy5hmPGMUMF0lqUTLVXV8ASKUcEqMq4ImdjEVYDElbkmngKfVYLl0s62TkmSaRxhYEPS9M5FgCEkNLSktHRMUk6U0EFJ8cBEDOHA6GGJwsYpwXllbNjg+WF/o03v0ZjnBIC1TeDXied1CEn27cj8qLiCkogndHe9mdvZ4xTSsjaOkS+s6iIkI2MsS0bNmqaGQUkzHzBOFHo1n90tYgxFgqF5ubChFCJSqbKrvN10VeduxhcxuGtFTEIIsyAACwqgCmlBr8mBiqjm0SoW0Z2aIkgImNs08YNWefpPjUWjaScSJQ41QKB/zbubyVLer3en/zkv0dHJ3ft2vLzn7cNDU1s27ahtfVEb+/Qtm0tLrcEhBAKqOnnsfFcB5Yotp975uSNN29QlrTkKAE96N9YEl4i4Pb5iM9z13vurKioeOCBBzdu3LhmTfNjjz1eU1OzYUPLgQMHn3zkt5Ikm9GfBIA7lgSwVHIKIgmeIAENcSaZKPb5F+wJImqMmzMkMjve3tHhSofGU65t65uG+k6m5ZzIYFdJVUVGDoYH26vWbPV7sGdgMjw7c9MN+547cISo8auv2ffkcy9sW1t/vG2ovDA3PDHU2tW1d2vLKx3dvg1V46oU6Tul+AI5hdVDp44XFBfQnPI9W89jSMn/VorHUqe6xg3dXwj8bHDS+CrMcVyUuVN6Vc9AxYnuI9deOZgbWKyZnqRuzEUxo91u+UT7qCTRuvqSc3JfcE4SwbLI43bn5eXxM667aLNp9X2EcEKEDwAAEIAzbU3z2j3XvyrP41JVBoicc84Y55wxJry+4i+lhAsFhzEAFA0Q9Q2YFwNqXhwROBdrBP7R/GKqpk1MTPYPDoUjUUopoVZ6luEGNJVMwYCykoyIDTOeF4aQ7WA39xvslAAQfa3gBTVWxlhDfb3b7QiQ7Tk9MT0dlyRqCBtx+mw132HA2GFtBADo7e397W8fmpwcvPbafQ8++KupqcE///O3f//7352aGnzf+94bjURNwwINwbfwqQEIIZzj/j902cT8AjQSjSiUSMRI/tLj9mEuNPeqV71q3759u3btvO66a/ft29fS0nLttdfs27dv9+6rYuEoEbldRryxTIixByhQApTaGoiuUgBt8VmgiQBX/WYAqbJl01bQUlt37UmFx2dC4dHJMSD+PXuvG+8f2Lxtx+BAfzIa2bR5x7Y1lQePv7Tlqn1X79rc2dnVvL6lpry8sWVzSZ5yerA/NBtKpZItm7YVFhSw+Nwsy73xhhsHRk5SX8GePdf0jcxcdgCslDIZrb19xFzOyA75IC6Jmy5EflWt/sORI3u2x93LysMwclR0crmkttbhkaGZlV11cTovo6GwIJhIJKy6C4tjQXakwHCrUw6cEj2g0OX2gM4GHDg3GqcVz2d4dKSqvIIbdpm4qInzL8SGHPTH4v7xRCIaicYNr0mWY1cACSTbzDTAK6LnQ2Sd02yPC0SyU5ElsIAnwLI4CBLzkRHOWX5+fnGxo6796e7xycmYWN/RuCgILylaLE9/AYwxtBkvyDkiiLrenKOiKAAgSbJILFAUReyRZcmKQwWbxuD0KtljVSkliURm//Nd113fstgD5ybTF/eKKBKec3JzDh8+XFpa2tvb5/V6k8nUwMCAoiiM8c7Ok16/j9gOAzsAZbB7s6IdEU5BATQtPqw0lYk4IySAiGUl5QcOPO9BiozHotG8gkIXJ9pMWNWY36vEkhkX5YzhS0cOUo1dd92+Fw4cYBrbvWfn6ZEptz9vqufgKaUJqKuqojJYVnn0+UPedbXu3OK61PBjTz65rmlHb28XY8yrKNLl6m8roUxGaz0+ZHB7BLCgUZ376yoaWEjlkhRXlMFrd23b/3L/7m0zJcuKYM7CPGVZevnlAa/PXVC4gsCHxeic5QFkEWNscGiY2pmaHkVhTJhFDpRpDqK+Ep6hlgIhVFMzRJJkWc5k0m63GznLqJosy5JEkWMqnQr4A+l0ikqyRImqMpfbxTVV47hEwXqTFIUGgwub6ueDNKZFItFoNKZpGjESRy2ylWi2MTg73gBGXLxd2QZL0hlOKrSC+i00335CtEeCmuo2oPEDEkI2b9poL6Q60D81Ohq2yhcbTkbTxWIiIoigquo937w74MvVGCMEJEkSGrosy6qqMqYVFhZ4PN6xsdHc3LxAwD80NJyfn5eTkzM1NR2JhF73p+9pbGxhTHMAQDayCwDRC8Z4QYF/z9ULF4zrnJ0OpTN6BjABRHB73D/40leHT3b19A2MjEzu2LFpbm6up2dox45N0Wj01Kn+zZvXeSTp9o9+qGXHdqZp84S0/bU4vnKODbm5ZYskA8/NxaORtCRRcVuSsQQYR06ozqUJIZqmyZKEALIs93YclvPra8qLNI2JWGiGSAEY6vJSMAjdQ6BD/7q8FJKYUpqT4z77ktr/R0hwf03jRtiPqYogIqChfrlcro9+5UOekpLA2NTElVtowPtcZfliPoAiTVvbMxCYi7Jc/1Mb1i7cjJDCaKxSUu4puoqBHhqAthmOiKrKrrt+Xe5ZF4w7X0NBkqTSkuKJiUk9fcng/rgk9wfQl9QWuq2+yheVpvuOHeoJXb9vz9FnHsovrkRvQd+xYxuvvHLo9CvXXnvL/iNH00TaUCD3RUhTdWXr8cMFAW9p7Zonn33+1utuqK6uXLoGHACJx2Jz4elAIODzeX1e73kyCJKpVCKRiMcTqqqJIUXt61KZj4VSJ4ziVOQFa8AFjAO7R8WQs/pZbaqr41IObuaMGAVApvFNG1vs3H9ocGZ0JCzJNu7vBH3s3J8Q4FyLxeYkKldXV3POe3t7GxubCIGhoSEhmI08DyCEUCq0fkKpA8fX6zLYzTuDqRnqmGViSJTOTMdeOtyz64rG+a+AAJj1swVwJgzsubnwv/3bNysrK37xi/u2bNmyfv26++775Zo1a7Zs2fzss8/96D/vFZCOiBEihsEqYEa0nRz1aF3zFSxKmTQDmynDODObI9c4IZIkcc4AQGOMEKppau2aHYBcVVUAwpluajFCCBDknNkGuaaJDHkjK97oj5ABS3XrMhmkaqy9dVg4FO16htCtiK7LogCcNxTXtqdn0lVFYY8ruKRKPa0o9QE/jcSeaFmzlKPYgGbBMc0NNYiArND9z5+67ob1Z7ly7XnUBXw+X15eXjgcppSiHQBehBCREjDxDUQ0kuy1gtqNZePP9w1N9J7s2FpSX5jr6/MWrV/b7FZnnz1waMOWnR3dnR2dwze/5c/UeGhsZKJk+w5vTv4tu7e3d7ZX1VQvtiaW1SWCiBiNRqPRKABRFNnr9XrcbrfbJUnS6uYM55xzrqpaKpVMJlOpdFpH8QmZvwaTAK+ICW05o3Eg298ruu2QAeYROgMyfEdoeSjBaJY1QC2JTChBroeEappWX19nj/scG5sbGQnJir3Is/mjHQw1ZgcQAKJpbM2atV/+8hcB4E//9PXf+tY3AeCuu94bDof7+wfuvfd7wWDwG9/4xu23315aWnr//fdv2bKlqanpoYce/pd/+bJxalvvrU0CsIBoBARZlsZGw+1tgxs31WQ95DTnxByE1hkwHotv3bolJyenpqZmzZo19fX1lZWV69atbWhoGB4eTsYTkiy7vZ7nH3sinUqJS6uZzN7bbnF5PIh6fK7wM4mXKIkxt7gQELWzBIxAZYkQ4IgSpcLRpShK6+HflzdfGczxSJIUnervnkiWBXMqKisJgJpOgux2yRIhNBUPx1KZ4x2dt1x3nVgdiHMuURqJRvPy8ocG+0orahRJzCakVPqjBztcEsQ5bz02oGmOxcPtqhLn3Fj/AVRVffs7PogcKSHtyfHvtD8HhCyxINxLNZVQUwk4P2/YJMLM4iLcHkxPxB+ReYCAT/++45ZbN52NSXd+jcGCgmA6nU6l00vo1KaBY2au6MtCEgIAnHMqSeHJkZloorGM3PDat3Z2na5sWJ+X69PUTHXThhdeefiWm4M9/a7tt1z/0P0/27ztyh27doxOzDSu3djWP+DPCSJynr0qj85kiTFd9RruRic1TYtEIhERXUkJpdTlclFKFVmmBrmdPpxEMikGCGNMzWQ4YiaV1lFDQgBAonQJxYDq3QCALLU8yx4wSEeaF7ADKAAnxJCklgywNXOoFQsaZExj1dXVRYUWRjk7G+vtmZIlypiOPpmOXxDMDvXCJmaaDDFebiaTTiaTjDHGWCqVSqfTgle6XK65ublAIJBMpsLhcEFBQTQaC4fDqqrG4zFKJTCWa3eYGAig6wd6epbeEa7fjniep7omXS55zVordDXDWFLT7GugEACJAAESLAg++OCDlZWVL7/8cjweHxjob21tC4VCo6Ojhw4dKq6qaH/x8OzExOM//pn+ggAziVQ8NJdXXCRyQf25ubuu3SsCe8STWZrRoqHOSRTajh3xF5RVFuW0d54sKK5sqik//spL3T39VWt3U0J6Tp0YG+kNlK3jyCdHB8Zn4/muzJG+yeuv2NXb011SVuT35s6ND7740uHNm7cO9HQ3NjeMjI0dfuHxtVuuCXgkQrDt2MvevJLa8oJjbW0ltXVbW5qW7Nr/dUKOnR2jJvcHm6UrXpmtxJX+GrlZsgrhFa+rrn8Q7cpK1gXm8cL5zBHTmXBTI1ixzsS6PAAY4YsEYf8fuq69fv2q1/U8Xz4AkxDRzAxYrIHeFUIAqES8YOPFxk9U0isNEEoJZ4xSKpiIrCiapkmShMhF4SDB2EURCADMjv9Z6HFn1CRjKSEUFpRVdoekMcMdd0QIoYSgrWxR9nkQwUDA+IKv3O7zNA9xbGbj/ibw4YTCzWJw+r2ixSQtHBNs2KLTEwCMsby83MaGevM6nOPoaMgBHqHtn63TmYw2Nho2hVYynfzyl//O7y84daqbUtrc3HjyZLeiyE1NDQJZGhwcmpuLNDU1zMzMhELhurqaeDw+NTVTV1fjcpHXvuauxsYWxpj9uYvbYByLigMFhQHT7Ww1MSQGY6ymttjn00V1mrFj0xMSoYYZDQTA5XF//wtfGejsGhoenZ2dW7OmMRqNjo1NrlnTGI/HR0bGGxpqg8F8LaPGIpGkqno8Hp/PFwqF3G6Xx+UaGxqVFbm4uCgnWHD3d76haZo5xFTEukBu6SJhoKOjIabplQrHRof3739u5+aNUkFN77H9+cXBsvptYx3PN2+/WUpNHjk9ubkmZyiM0dmpyFTvtr2v5eGhTF5VOY23jsbXlfsGJ5OjY8O3Xnvl488dkVKJW159y8GDR1KZ2E03vOqZpx/OzS8oKW8e7TroK6+nLKCFT6y/5g1luZdTARal9rahaDRtlTQHscY7MSchcpGxpE8ItAU6M+QvJEZ1xm3wGvsU1a1zE7c0CG37AYAD1tOcaimHA+q6DqJVRl1X3QgAMM59ftcNN22giyvZS9B5dwcRQirKy4eGh+cDKVnebUROQAIAoIQb89NoyTWVA4Asy5rGEIAzHTPVMhmglGkMADkTGrd+FOea0QdbfxwdMLFyU1O3YNmsu7AfboIHon4xAKTVTCgSKSgosHNiE7gxN4zQEYtMlHYxlVw0IEa+rx3VBQIEKaEg9A9DTaAAKNbDNJFi+1kppYw5rmgnjuhyuRrq6+w7KSVVVdklgBakZFIdHgrpaUeEIAdE9Hjcn/jExzhnP/rRTz/1qY8xxn/zm4cQMZVKvetd7ywuLnr88SduvfXm8vLyp556qrm5uba25siRl48fP2p7AULDJ4QAcBQ2eHFxbl39AlWplyBzeBgzCQRgNjM985Of/KCsrPynP/3ptm3bNm7c8JOf/HTDhg3bt2976qmn7r33h5IkvfWOP3vLW97S09P7+OOPffCDHxwcHPz2t7/zgx/9MBaLvvmNby2srLSNWQThzjpTXAihdLK/rW04VZCfwxiTgAa8biIrqVRaliWO4JaVVDKpSPmMZdLp1C2vfttTv32gpqke5ZTm1ahECRCOKElSJp2SFDePTqQ1hgDJeExlmoB9VMbSGTWHABJamJebSWYgd6nV5P8v09RkxO1WvF4XIjjmso2HIyAlZGYmxo3q/+Zrpkiu81ZbkL0+YA17mZjL4BoqGKChhRP7pcSv3BAuVdVBzq3UsywWwTn29042NJau4n4vRDyAosjlZaWj4xOSs56lTqadBUCJBARAaPG2/WKLc97ZeaKlZSPXH4YO3Ng0ZQv+ts6MS4FthlfdUQFNpErBfC3e1h9KiEzpbwa6j8XCkiSFege2TUfv/Iv3qaqqqaplACICITKVkAByrssAva41lWT4zZMHX3fLvkwmYyRtAucMCCWEMM5dsvzI7w+++rpdGkdKKdP4H5595vqbbuKccc4JoQrG97cNX7V5DQLMjI94gqVeRUolouFYdHQyvnVDE9M0IlFNYwBEZOcdPvjirt1XcMYJoCgNxrmoSoaM8xyfu7GhYdVucMaYESSnvwFVVevq6t7+9jsA4Fe/euCOO+4AgGeffW5ubm54ePTtb78jLy9vdHT0jjtuLyoqkmV58+bNjY0NXq/3wIH9jlOLmDA0Q6SQ8aVC/hd8cXrsPxGzDijVAatUKl1dXR0IBAoLC8rLy/Lz84PBoNgoKSnRNBUAi4qK8vPzq6oq8/Pz8/PzOUePx1NcXBQM5s/MhohR6grA8LST7AXk7SSkOOe8uGZ9o9ZDldLSwnyQPTkbtucXFA/1dXuLmwJeWZbLdrZoQzPhdQ316crSseH+5s1XNdRWn+7uSvlLmqu8bhepcxcUBlzdg9M3XnN1Mrr+VOeplo2bKGs+dfr0pi07g4XFfac66jfvLcxxZ5hUEiyWvBdpzfOLgYpLcotLcpfTMnZ0IBFLg8OqBkTUgFs8yNBxQYcHiHBHcuCWfSDqRDvD8UBoTwaUu2lzDZwfukABYV6vt6iwYHp6xmRzJoe1A7LGH4NxO51+odDsDTfuuftjn3rPez7g9/u5yOAyjl0A27E7K01l3PazrhAKobE4z7PBcPpVZEpHo5EnJ4Y+OT4IkgSEbtBS7MChY+3tqWTqS1/5ksftNoEtYOyxRx9SfDnrN7eoJG+gpxtC4/Vbt51ue6W+ZRNPhp945Dd7brxl/2O/DlStCw33bNp6xcxw10QovmXb1s7O06G5BJXkwe7W7oHRyroWr9ffcfzQ8MTc+k1bOtpad21dc7rrVE2R8sqJkaoCqQDpya6epuqyuYw60N3r5XO5FY19p3uv2rUNgTz10M/XXXnz9MTY8NhEf2dbJE1uuG7Hww88umX3vo5jJzgmc7yeP739zrMMguKcEyoZgxsopeFweHR0TNPUWCw+Pj6eTqcTiQQhJDc3p7e3t7q6emZmtqenBxEHBwfz8vLy8nLHx8ddLsV8hDZ/g+nFnl+K8wxEAMSKLwKfF6ipeP+lZSWf/vRngsFgR0fn4cNHiooKu7u7Dx06VFZW1t8/kJubCwAPPPDr9vaOmZnpiYnJUCg8Ozs7ODj02c/+UyaT2bRpAwJS3fOnFxdZAvZkjDMNKSWAyAltaFprIMvodRch5zX1zSIWjjNWUFxZVFKJiF6PJ5gfBESO2Lxmvc5xAHMUkhNoqATgTPP68zZsFGXPfWvyChGRa6yucT0B5Ah+Nwnkr3gVncu0MBkRQVYyjhWv6eBGhBCOBgQMwnAnSPT3BwYuZGr/eoSycVbOVjjQV0IXLiI4LzdXU9W5cITYGapBxi06YSIRBmSUlyktLXv00Sf/8dN3t7Ufu/tjn21sbBJx9CYrWEAGWBeY/xCzK/yiziOcPlVKVVWdnZ21+yoGhofeooYACMgKAAAlnOMVe6564+1vU1XVbXJ//bSYV1BSGnSFU5xrkYymScTV1FTT2tZVV1na2Tu2pS73WGd/ceP66VBy5+aWo6d7lblhf1FdMjxZ3bQ2eboPAGZnp6jsdktEY2xucsTjq0zHw4VltYWFBUUl5TleV3Vjkz81HJqdbFzbkiclp8bThSWl67Zue/i+X2zYfVMmk1YUV8um7V1dXYGcvNKS4P6nJuuamro6etds3haLRoOlVT6XdvWW+unZeGmedznJE4s8Z3AOfZBleWxsbOfOPYoi79ixZevWqzwe1/btmyVJKi0tefe73zs8PHHlldsefvi3g4NjO3Zs/O39v4mHI8HyEl8Weu50vBAATVs6uncBooYEyN5P6djYeDQaSyaTU1NTiUQ8Hk+k05lMRg2Hw2KoplKpoaHBaDSWSqUHBwfj8TjnfHh4WNM0aV51HUJgiVUhHT1H5IzZYEphBRoNOADRQ0SJgf4SewPBOpg1BdDURc0GXENKAQU8eJnOFdliPLKBW8d7FwqvHplBjAwbI5oHAJDruqhN0THUUyeCfc7pgqaEFBYWqhqLx+NART2uLCIUCBrJmgDZt844DwbzP/nxz7e2Hn3XnW/4xMe/+Ce3vUbTNLEKmBn1op8LAJwiIWvbuqr+T7wSG1JPCKV0enr6a//ytRtvutH0YciS9Nizz8Itu8HmeeeAissVCARUVRXh4fYrTAyezi+5ZkNtZevRl2ury91Ykk5kdrXUtJ3sbWmo8viktcGcoa7WNXV1c6Hx9c1NhYG1J04NVDVu6u1srS4rUhnbuOWKtrYTxaVBgpmy4o3tJ3uqahrwdOfp0URTiSuUIKX5PlktL8sv6e04TgtLayoqosrQ2MRcUXlFRdDzyvG2q668IhaLNTU3BGimr3/sxhuvnoxoDZVF7R0n65qamIZ1NRUFuT6MJJ8+2HrLvu0rfbnGg822sRjnhYUFP//5jznnX/vaPb/61X+rauY///P7qqpGItF/+qdPFxcX33///e95z7vLy8ufe/rZX0BScnvcobD/aKt+FjteCBanWwXNn0uiJtDkxOSvv/cfpaWl3//+vbt3X7Vhw4Z77/2vbdu2bt++/fHHn/j6178hSdI73/n222+/vbe378EHH/z7v//w8PDwF77wxW9/+9+j0ejOHVfuvnqPSBZeTnEATWPZrWy6jtNNZXj8qAMUhcV0nQXNDiP4d7EKlJdpFaQvvASEEjIzM5H//7P35gFyXNW98Dm3qnpfpmd6evZdo31G62i1LFsyNrbZIUBICF9eWLJA3kvyXkKAl4QACflIWL58EOAlISwhCWAWYwzed8vWvo+20cxo9r33rere8/64VdXVMyNZkpEsGx3MqKe6uqaq+tZZf+d3KmK4IOyTYZzMdYBJ/YplixihNEcMbRNgonzAQmhcO7nePYG1NbHxicl0JsMcNsAsf6B5m8jCwVvJXusGCMG5qKio+NCHPtza1v67v//uD//en3/oQx/x+fycG/JG2qk0squtdnqoxHtWlt93Zt/mGVxE/OIXvvQXf/kXgUDA6XPt2rXr9SMD/zI2+FQhL2kNfEw58OK+gdGRXDb3x3/yxx6PpxQxKMrr3vwOv9+vG3zVmo3yorlhxBpbYyQAUAjhI6rcsImIoLZaehabe6oE550ruwmAc840z/qNG4UQgdZmQbRxw1oAaF+2ioigLgoCBAgAPxGt7F6nKArnvDqyOp2YWbZ6vaK6ulat5obRuWqNPHg4SkRQVQNC0PoNG4ho2dJO6fX7Q4WauqunnLXDVSFksoa4YVRXx269dQcAfP7zX7zllu0A8J3vfDcej09OTt12285wOLxv396dO3dGo9HUXPxjiRHw+7vGJwN7D5aOS/PJF4lA065s9Tq9Xyy3BYIL2fHgcrkk65GmafKF2+0SghgDeX88HrfEL3k8HmuLp1jUySoBl00IuIjvJmmAHFG+Y1cr8znvoySIgMy5pzLV4Khmw8XDX7rI65vysoVsTeXzhxAom027vQFeLGhut8F1TXWl08lAMMwNPZvLs0J8OJVrrWsVQidgIHSmelRVyWVSLrcfgHKZlMsb0mfOjBT9bTW1ejGLzKWqyjW1AK9AU3htTWxyajqZSjHn+PDyp4aE45fSPgwAuBCGEHfdde9Tjx/7+Mf/+Ld/591/8+nPL126vFgsSFVu9xNI3S6QyqpxZNYB7UAMAORgpnnnIuFXDQ110q8HxzMmON9V27SzpvEDR194uJADxEOG/s6u1e9633u5YbhcrjJHgBsnjh7best2CeaR5+hyaedPn65t7UjPTUaitQpxkhSnCJwTMnbq2EEWrO1oipEgcy4mooQAhXU0tgAAIABJREFUKYwxpux/7pG2VZs0TfN7NAJgJFHzginsyYd+Ud/Z1dlaH6yoklgCD6I58FwIQJLtQtIIqorS1t5q53yQuT0vI08wPZ0qzVoHBABFVScnJ/fvP6DrxUwmfeDAAV3XU6kUIlZXR59/fk9DQ/2FC0PPPfdca2vrwQMHobMWhGAOGjUHWMuBlUJgykX060VkPJuR9G1lc3UAEaC+sf6uXXcl5xKtS9u//OV/6uu7sGFD11e/+vXTp/vXrFkhh6D98z//6+/93h+1tNTX1MQ+9anPNTXVtrQ0NTS0B4O+9RvWynOzC0VApCBOZrJRz6Wa9YWAMnCcVeQAMGkc5sPhRAnQUELvounOlHI+pTVc9gRJh/KaphR+dYTIbLJhiGf6j7dUVvQOJ1MTQ8uXtQRrV/cffYpUf0VlVe/Z80biTKx9a7WPTc9M1lVX79m3JwRFb7RlOp5Y3xIbTBRT0yOtrUsn4olkKr6hNTY3O1WBuaNn+9es3Vyh+q7p1/XKsILEqqOqoszMzZXGh6FJJ2YtaTsIKr0vyy4AAEIUOW9oaPzq1779r//61fe+702f+Njf3X3PG0voE1ucEZa1BRwJUkGSc8JUMPaTY8cH2Vx2PgYUAIgMIRDxn7u3CiBETCxNTo6Pa6qqWAgiWwhwemLkZw8+svOWdQcOnghW10dY/oUzo6vqwydPnYPcxOzMxPhsuqahLTc5MDGd7u7ZVF8TudDfX9nqU5Q6BsWHf/HI8q6u4f7zOR02rl1+4GivP1zt0VyJ+GQ+Q0OjF7yBSENEmRyf9NUuXbu8ZjKZX+Flzzz9DGqeyoAnz6Gra9W544fj2Zzb7Q1Xxfr6ztVXBnM6GcX0um13aqr66COPNtT6OPf0T2d29qx4+tm9zBu4Z9etV/rNUgksLR8QlkyODw9r7//A+xiy6ljN77z/fYwpseoYIlNV+OQnP5HL5aqrY88//3Q2m6n2+OGPPgDATwu9Mj5ews8ROCMAmRi5igZICwVUajyUXSNAVL15bZQxiCdvX9P1jndET506FQ5XvPWtbx4YGBgbG0fElpbm3bt3zc3Fh4eHPvrR/5FIJE6dOv2Rj3yoWNT37t0bAEAn2mzRXhD7TTPulLhde3frFjpeObW/ubEcPF26OeXZJyv8BJl1ls+AvJ8vs8h/UwAgn9czmQIyBMsbIYCW1uVFZTba0nXoxWeitR19Z45X19TX14VCHS3Hjx0INDY0tS2v8Puj1c3LIyzpb8zmz0yODnsqmytalxUM3tK8bPjCOV7MNbasqAl523P54cG+ylVrDLgytNsVyStGC1VZGWGKMjUzI9l/SngpYQ6FRHQwBy1csUScc5fL9fu//z9Wrlz9t5/9372njr///b+vaZqpMwh8Ph9ZEHvH5+yMqKPtC8s4ecB6zJiqIrDxsbHaujrrqI7RfYyBbPoXIhwKVYTDC0inzZmLNfUtmpGeHh0Et9fFaHhi3CMMErR0acfpAxdy2czmbT3PHToJRW31sjbd4Omp4WDDkvTs2NBETZ1Pr2hor6vwTlbUV7L08RMn12zacXr/E7pL45wXcsWWzlWzZ48O5TzbNqw+0J8EgIpYQ1DlsebO1GhfPI3d67sVBhfmcq/fsmpP72DfycPkcs/o6C3yu++9Z++x/pXt9elM2jDUytqO2dmDJw4cjBvqsshlgeHmiaGX7gABuVzuv/j4N8CKx+SP8nEBZSltJPhvHkUAqY2dyp9uCQbDRKKUFrREfmuXpv5fRBAIkVm9GLLuI5mqAPGB3dvApcHBo//4m+9v6+j41re+vWHD+lWrVj3xxJN/8zefVVXlPe9599vf/vbR0bH77vvBRz7ykenp6b/+60999KN/ViwWly1dtfP2mAyyhJCMJpeCJNipSBvAYNcAHcVg0zI4nXbn02B3AymMGSRyRsGtai6mGE6GGYslSWJOSJAQYh7V0k25KiFBxGS4hsLn9auqR2UgPGFVC7ix2NjQUulTz/afW7J84+TIadTcoVjL/v1PKsvWB3weUFEB9Hs97e2bDx09ANHmiEcDJK/H7Yo29x96BjpWTs9OV1Q1W6HgtZJXkhewIhzSNHV8YhKsFS6IM1DlGjepcp0iMfTgQFcIIYh277qrs2PpX3/q43/4hx/IZNKnTp0UQsTjyW9/+/u7d9/JDWO+S072CyodufRuCdDNOf/ABz/wxS9+qSZWLTv/HE+1+a9h6Bs39mzavGlx7Q8AjMVqY8wIx1ra544crqqq8iq64tOXLG8+ceJktK5VUzXg1FgVcwXiz+7Zs+P1b/GE/F4cq6xv1FCoweowGxmaLVR59LTh37Z99YF9LzYtW++lbDyrV8cCzOPSmpr9Ic8jjz9Vv3yrENhcH/VW1YnRo5HGVhcRI+JcbF7d8dzhs91r1kz6XOGKimB1vSaKbrevo7keANatXn5+bLZOBc1TsXrdeu/Jw/6qqxk6MTOdsWyq5KCgiooqx20nKOukl2+U3bSQbHRxAflKJdB5aQvpwl9F+zszHX5AaccdoxWYEHLgVzqdJqBcLpdKpYgonU4pCmNMSSZTRJRIxFOpNAAkEolCoVgsFpPJpN/vA2tBMDSrfOUp+jKx8kSlJCQ4lqXzdpS9tmCE9laGqCB7YrTvsyOnj+aSq73BP61f+rqGTk7C+pA5/A5Ly/XmRLBfgphQTwICFIJ3NC8HWYILbaJiev3mu4iEK9q0qbqZSFQu72lHEIa4Zfs9ggRGkQjcJCqXLxdCbNiwkySPCYmKtk4i2r7tLkGirqaJSBgGt1vZr4VccyqIlxQhxNDwiKEbgKAqPsFZ+WwSRInF5BwZDl44VygYy5atEOVNQIqiEBdf/acvTc9MfOpTn1IUFo/H77333od+8YyMCeTDwy7ql2GRJ2w1g+XOpqIoc3NzYLn/84JoAvD5fHYOnUqNzVap2dqbiBxkFSCEpO0F67U6NT6cM7CxvpaEQFkklzG7xPABoCT7lSMYTdAACSGQsVR8ejaebWxqAjJ5MhiTB6dSnzCi4IIImpsaAoFrQn/9/LNnDSEc2Wgq/SCw/yEAZIxKEyPMf4VZOy4d0ExbQ9kWACgUjF13rAxc5hxgAEF0em52nqNNBF6f+/Mf/8vTh4498sF3gaYuOdMf+cp/DCWSy7qXZtLZ4b7hjlXt4YoKAMikM2eOno41xiqrq3oPnqxuqK5tqDt1pNcb8LV3toUjkU//y1d1c46xeU0K4rKKRTqoM5nCxHhCYTYpRZlTsYjDJ88Yy26NpqijmcTzkxc+MtwLqst818h/o3Xd3U3L9MW65ISgqmjA77/ZCPZyZXIyceL4iEmUKxmpzNxwiWVLWCveUeG3/rXAPWZ0Zu4o0FH2JJPEW1RV+TdtuVb0Ta88MzhjrKW5aWJyKplMCuByGhqAXbklhqx3duzOk0+BqoFsFX6+d/5REIHEA1t37Nn3dG1tDQBUV1d/+MMf3n9g7/ZtOzjnIJt9pQ+1GMoCQQUr1+ZU8URkGIZsBbL2dDyQ1j6OE0HnEdAK4aUql1Ai27GVlAxSODeqYvWIknjE5B8xj29/CgAQLOJfIgt2JjgPhCqDoSqJOZM2xvpb5kkRANcNj8fT2tqsXBtO4NTc1ODwRFNjDQnrZFHyWJQI6UDaUWTDg6fqWlagMEqjLAGQOWcRm9qxZLYt4DURCS6utAYgl5RM3cnzYQwN3bjjzW9at2XLrsYYMOYP14S+2EWL6OBLHlmIYEVY4ncQQH5zlziEPHOyFrmz7gUIZUhoKba9skRBtn9q6I29z4LqBq2k0Ld6w+eyyblCNuz2igVj+RBBLxpw0wC8bBGcwAr1AFmpO9FRpgIAu4xfklIKDwgJhDn+j2ixznEiAPB4tWt3Ia+8AZBSE6sOBvyTE3EbHmdCeghBgQI3QHWBYt2IRWNYo2gYhnPNv+ENb/jMZz57646dZnLG1vtScc7PKjBBHB02vLxyQHZFzlFoWyR4cn7QuRocPAHm64Wxl0luuZhcPFBDsKg9bT2yEDdGAILzaLSyJvZLmya6UGanZ+YymWYQ8cRcKFypF7KZXD5SUZVLJ4ocwqFQfG7a4wu5NTWRivf3naxtW8UI08k4Kq6g3zc3N+PyBBgZmUKhoqIyGZ9x+0KJif64obY2NOUyCVXzetxuAEBEzaVeaQpIWhqJPGNoZoGIi9Xr1iJjaFOsXJ72tysXZlqGiHMuXXrF6ky82JFs+BuVHJ3ykkB5DWFe1IIIDPEn4/2geezyFZAAwX+juvPDw6c+MzWwb83uiDfgVTRBJScDEQuFUoxyU65aJiaSynwFUq4PEBmAmFdyd6oPBBRIFmUckWlHAFE2hVjqQjgHcvzS5UYxAADg8/liMWVsLOFQoPM07kuLUymEQqG+vr5cLq+q6gJ00HxTy0AVULT4QBfRzqUigTMIWCDy5K3B64sUcBaah0VE2ioq6YGS8ZAFi/K1Ji0UlrLP5YAQIsZYU3ODTFVfO0mndbfLvX/Pw9G61tMDF9avWDY3fnZ6ZnZwsG/Txu2j/YcThm9i7GBLQy24ItzQZeeUELz3+DMNTR0T8dzqziXPPvfo2g07p0dOjadEYvpAa1OrqrlS04NnxuJrlq+0l4Pryg0AmCZ8fqJPCCFHc5T53Jfy4E1xdO9bq8IB5wcAIShr6D51vgdnM2YDgKQFMD+EpgmfvzpFmTFAxJl85p/Ts6b2J7Fc0f5bVWtKGPVuPygqAPUceQwU9WdLNq6rajDIIqklKurXEFLyqyPZbIEQzdjUImqUb817ruUza1nv0kYAcJY4TRivVT9zYN7wmo7wuYEMAAC43BoROdsDCIhMfCgt6nGbgiBp8M+d6xsYGFBVhTHl6NGjHo/bMXv7UoLI7FLwpeoiVjL90kcrFQBe+u+WCgaLvHvxj1nJHfOu2AR2tudopkoED4VC9XW11wH8NzuTYooyOz0SrGpsjEWPH9/n97oyutjYtfrY8f1B1RDeaENjWyY53NnVPTNUAUDFXPrEqWMIPBSp58W+U+f7N/dsP3J8f8CtGK6KWG0zCd0fCkcr6lOZ473nTq9fvYaEEEIEg5eb/bcFzb7f+d8Lll7gwo3zpNzwWra83Bg4Hnjii019QkRVtScrAKFgYMKRyDkelEp/gqyOAERQgP1o6BSQAGS1yH69oq5Kdf3pxHkAgumhr9cv/eDIKVBdQHTv6RdO9dzr19xkPT6C82LRuDkV8uWIrvNcTpcgNDSJnUuiIiNmevEkc7nSqWdMEtkCAiMUCCiIO2ZVITPbJwEsFxARSPj9V7zUL19urHWgqowESSJd+5ESQHW+0LZQ1I6cy0qCAAhQBFinuZUkzszM/OZvvk+mv/1+X2Wk0qyxlAfUpY9bd5uIMVSEMC6hKB2ByUXF0al0Zdp/kXBBuhaObBVaZLDOxOL8eICZWkom31uam32+68H9y7kQ6BeFkU3b7h6fng2EIkY+agiK+YOziblotKa9te3U6ZN+X6Clrud07wHFF0EhFM0dCUcQI7yYLgqMRiIzs1OVlbH2jmVnzpzw+YKxyoqTp0+KXMwgVl1lFlQJ0Hvl0AgH5Ef+am4EM8Zi8qtjDCVBozNEkBgwIQRDRHNkKQMwO7PmocxKf+Lia0BVFcMwTIcPUDo4CFhKBMt6gJXWK4UWAoBBmnMA0AA/UdP+4dEzgAjW7NV/mRz4y2jzJ6eH5JaB5GxXVT13+KeFvH7TALwcSSSy83SIlV0mJLwvNxA90Z9qqfUUDE/RmKoKNg5Ozy1pDJ0fyTZUq6msXijEayqaTw71drW8Wa11oUpWVM9sG2BFFlxQVfSXMPz9YnLDrQOv313IG04nmwC8hnj7sUFV1biFslA11aLxNDl8VEX5xlOPeL0+TdMAQFEUzeUyDyDz45bWtB/XBQg8ZiMYF57Y5fvPV+prYzkH9bz3YLE0mOljlvafV5sgIqqMRKqro1d0Ji9HkoksMPfKzk5OUFFVT0ThwCoAJBIIdQAgBHWt3kBCCBKrVq4HAM4NZMqKFWuJgEhURKoBAKgWALigVSvXy6pId1eP9SWakw9IiKsYheqs6chagJULAsbY9NhIgas11aELw6N1TS08n56amvX6ffl8sbGlaWp0OFcwmtvaE9PjqUyhsblpemw4W+B1DTW6Ti4FdIEej4vM/BuAFRZcbDG43Goup1segHmCBAAo7NSAg1XCzh0AABjE31Hf+XezQ78dqP73qQvAmNO7edEobOH67W7PE8U8CF7tDVhTg8zVlMkUgjfnAbwMSSdzzMHaZI8/UpAdyI59YbbvrY/c/6MPvft1Rw6lQoFed/Mdj//ivua3vu7YE/FMS29d9a4HHrr/t99x14X9g9pEonP173qX6UQOpQSlbx5RLs5rdy03nAFwu5RCXgdAO8fOGOs9dfx7P/xuS3PbW9/6biE4AN5333dzuey89JgQfPu225YvXymI4nNz3//Bd4KBkHBGaPaj6XyorG0Iykvnii7yZSya7r98uWKbYWeNJBwUzdSUIBJC+H2+mli1NITXTcZG44jAhaCSRitBPy20K5ffgZPK35ymBwAOViY0d7Y/5VCTiEQQCF4dlIUQGACWVCYCMhYfHTh5fm7zplWP//hH217/+id+8WSDz2jo2fnEj36y7ZbVJ46eHx8b2LS288kn9m/ftPTckYNFNdC398V169ce338sXaRaTyayfKvXUqpoVXEvUUjwel3xuez8GqH1PcrzIiKzj13eHjQpJAVBjT90duMbONGf9T4Len5eVPql5OQXajpCc+M7Yu013qA9FkjWAm7WgV+mTM+ky9LUFm7FIPFHhaHdZ4YKXctB1UKD4zO3b64TpOkiKAAbm2JTiX2rlnlU17qx6XPbN/V8/Xv/+eGGHjax3l1jmHPBAaxshXwoFIVd9bjHy5EbzgAEgp5EImfVVQAAiKixsbWzc8W73vmb3/zWP6dSidqa+vf9Px+8//773G63bTmFEI0NTe3tS772f/4RiHbvuvvd7/qtC0MX3JpbkDV+wfopb7WF2zMDcCIgZt76i2rkBbVfJ+LzZV774mVhR0HY/itkgSWdJyOHiceqoz7ftS32LhQiSiZzslXOdoGhPEqxGgDMAMURfjlyJhdh0yQq6wkUXASDV+bD6pwzB3R33ndVyGUD4Qq/11Mo8lA4TIUCBjR/uLIi7K2MRcdPz2qaq6KqysicfvTBX7Q21mVzutvtrayOjU0XgsXR05P4tlv8xaJhFwMk7hYv7lC4zSRM2VpzfvmlokIpQHCQOgC5mMKQfXLJporBYzmZJiQAhLwQ769p66luelfTKgHCcDRbmA0onAyD32wHuzopFPREPOd2SyAvgFmWB0bsqcygEEb1s4dO/frdzdmCeyqeCfo1w8BcXhEiVRlu3XMYbts0s2FV86ETP3nTHSt6Vt154MT/3Kz8QAmGFa9kijYx28KE9rnd6jXN191wBsDt1sxyuBVJcy7q6hr//nNf+c53/jWXzwSDofGJ0VQy+elPfV5mUWXdWD4bH/nD94eCYcbYT376/S9+/mv33P3mQrEAUMr4WloIoFSZtzC8wEyXa56Kd+Z/FhiA+cjfl2cGFrcBF60Ro4U2EYqixKpjweArM+4jlcwVCoaiMETmrLnbdgoAZIuDk4hBig26taDTZmbPeePNnjdrNy7ElXYzZQ0dAFj5l2OehqDajpVzB/bt35+98813P/PoE9vv3p0a6VdAb+lcomq+2hoYOLVvz/Ns9z13jJzrHRqZWlUb9uttTFWjscpwgzd9PsMtGlRHVxddYimomqJpCuccLJLmRcyAvBPWjRBO6DIBAXESlR7/3y7fZh5TVaWrUDR0HQARhJX+NAEmputDevGmAbhKmZ5KyvIvlasCF8MD+kzNXBIN43A4tGo2zl3qpMfTkM2yvK4hFjQV4ingNBUKVE7MgsETLY0NP30CNnXtLY7f5WsnMitK5hEJhCB/4Bq2AcMNaAAYQ1VTHENwCACFEIVC0e3x6MWix+0tFvOMKcViURoA2xVWVIVIyKp7KjnndrsLxaIjUzL/b81TqxJgLSmz5lULS78slgK60gjgIgjS0tEWglYXRYzKIEZhLFZdHQq9kpOeRkbmmNW6PK9iYTXEOPLhliqyZT5yDshu2nIeyTID4Pe7r1R/oQRml9se51mu6tkMACTEztftFkJEVqwiri/rWk0Ejc3BNVt3rFixrFgsti1f3bYchBCVK1cSQDSUefHgyK27biXBGZplJsufeIkF4fFo6TS3HB2w4YKl5UEmbAQct9QBNyIE4ESS+EFVtYcfeTA+N1tX37B9+60PPPCjXC7b0bF0zZqNnBumGbZuczZbuIoq+k0BgMnJFGMWSN+xvSjE6wLNcOy0LnjPkZO+VFrP5VceOemeS+YYNB/tDc0muFvbfqzXMzLBE+ktB475B4Y58V+b1dc11sjWSQs7BiBXgqArjXSvVG44AwAAbpeazRbR6e0AcK7f+bp7+/vPnTx5ZMeOO3p6tgrBnfdLCGCc/uSPP/Hlr/xDKpX8+Mc+W1UVMwyDypp2Fxc7Zw2gMlacV/G9xEcX9otdzAzMa+RZLIVTJmU6cYHyl1kURVUrqypDr5DX7zyZmek0s9pY7NM2/7XUIYAZbdnBnVPmwWFxftBFtj9MROHw1eS4ECQbnH3M0nnJBzqTSrq9ATDHtgqQZL8MDV0sW9bJDUNRFLIQQfJUvRXVu+6o4ZxLGIcNDLf+4qXE69NSqZxNFAJWe0n5bSndBBPmU8pllu3PBf/Kd74e9PqrK6vWrt34ya/8v8ubO9ZPDPf0bMnlDNsNkgmqfF6/iht4U4pFIz6X1TRzrq0ToWcQrfXWXCi4Tv/h+25VPQIId90G6aRfc0XvuK1FCIbswKZ1bO+h1XftKu7mQUMPb1y/98kn35f112oB3eASLGq7m0QkiCoi14SyxZYb0QB4PFoqVWAMnOP7iEjTlA//wf8qFgtut5vIxNeWnmQELkRjY8unP/V5LrjH7TEMAxZke+fJfNVKl/QrFxxrnu9/KQhpufa/yPEu/mErCS4ZfV2aFolErnVj12XKzHQGgTFmUzaUYLd2EdhqcCW5HWWkNp+qwBIyk3XmzuUxhRBUWXXFwDidBFIJNCAJO+31xZgycurIudHUxi09oYBH9o6oqlLMpWfThcPPPnfLnbdlC5RJxFuXtA2c6WuQdBqSiEmSgDrAgCDTNQBwsYADAAA8bldZ+cOh2xeukLJqiuOnLRnD2PtbbwfVtYkps8DH/udHxlStzRdBx/hJ27XM5fSXCVv41ZSJ8biisFLS0tpOBJLOfpbEZ+64Z3lltdz+Qv/Zza1L7Pv8w9PH3n748NNveVeeG8l8LuYP1jz9OCcSVqbPjNPA/L45F1fn61y+3JAGwCsHqqA59d3CbxMRAHe5NHs41/wcDgHnHBFVRZO+/2WLI/8gFGRXMGz20k/RPMf/JWMR+5gLs0DEOSD6vN5wRVgyIrwiYhjG008/bUOMGGJ/32QqnQuGKhAgHp+V69dpACx3mSRLDiEqjHV0rva6PGQW6E0xL9zU+BZRjl3htI51FQYgZ+il2RPWmdtFBsbY7MzYXFzXi/mHfvLwip5tlT544rFn1vf0cGCCBBWL3OA//ua/7XjzO/xMKAo+89BD/ljTspbYs0/vWbNtR3VVyKZvQqtocemvWtUUsCjDLFPk1O2mg3+ZWjqganf+5888bk9VZVXV+rs2fvnf6moba1avos13gV5eXiYAwmy2eJMV7kplYiJpzTsqOTpg1vxlflI4RwcKIZxfoYQ27h8bzhby8Xyurarar7oAyJ7rY5LgWOJyqR7PtcXy3YgGQMKeSjG/nfdEB/zRFPMhcZbRbLSJ80GaV8o1P4yIAE6yFAAQQlVYYf7OV+srOT04Rzm0tMPFrJSztkxEiqKEI5FgMHBNiUEuRwzD2L17965drwMAFCJfFS421YX6B7atvgUZe3Hfo5qDm6zsrnGRbG0kxPDAcDGX3vW+j+zxifcE2+vUgEFOBlGAcv1vxnn2MRFDVwFjtzSy02Az+y0AIrzzTW/yiEzHkpb9z7zopcJbf+s92Ymhw6fHFFXVU8m5rFiybsu2LRt+9M3vKpguuiJj+573GF2uUDQSCZZm0VlFD/nrJdYNY6iqimEIRBBEtqPjvAeLRgPWH5m/KlUAxaQ5QhVBMZ+XRTKIyCA+l7lpAK5IkslcOlXQrOJTWWrzYmMbF3iowPna2oZUPhfPZtuqogm9CGCXt8o+IARVVvquKQYUbkwD4HKpiqZIBgdmwelMj3J+RF3q2DT50Kz3FcYs+PP8iBkt0fXi7OxMTU0dyIybNN3EnMGx+e/le2IOKeVtHXmqy/6smV7web2hUMjjuYGe1e7u9dFgUK76vq0bD7S33BIMqMRQUb2a1wWMNBUQsaiTqhJDVtRJURTgP925DYjeMfFzJFIIf2zEfzxz4B2uyDsCbQ1asGQGrHjYzgBZ2SMQggIB11UgWJi1VsCplK1fEUBwnYie//kjTatXMZUqfe7Tp86FFQMAjWw6lysAeDPTo6Nj40AUjkS9mcyut7zN59GUsycee2jPPW+4xTAszsGX8v1tcblVwyjYTP9EAuYDCsqiAVuoRBpW2mLdLZRTkBa4PM5HALLZ4k1OiCuS/vOTmmoyeDu7cMyqj0nzgKrDRVMVxdmrpCoKMDaby2YKhZReSBh6kDEwl4yVJEWLO5codI3zP3BjGgAAcGlKPmcglvElEUiCPWAM5WhcWdGLz027PCGmoMdlhkvECwdOnt+wcoWDicV8HlRVi8fnstl0Lpf70v/32URibk33xl27X+/3+ds7OiVxtDA0RSsvBV9Ec186/+NM/lyO2B4/Y8zn9foDfq/HcwMmatVktn/r2rTf1/XQs4rgwDlyAQoAUaap9sDa1V2HT6q5wpEt6zrP9oeGxw/csb315DczAAAgAElEQVRpbLJh7zHgXEKFmMuV6O3979vXfyk79gMj+YO5g2/UKt4VaGvUggZxZ39sKfdtRt0iVhO+utNGayaE4/sgi5BOLF2z3aPQjjfce6F/4LZdOysrAv3nznmqaldUVLtWtepcBDVP09velMwWtr7utkg0imyoUNQDHtUVrLlrTQsZgllNDCXqDniJQrDX60pnCgzAmvCOZOXOyr/3+e6LY4tZNmCOfmCFlTMKLeaeMobT06n6+shl3r1fcclli4l4TlFszG4pSWsmgxCBKMCU7+5/YWmwghMpiMdmJl8Y7K9yewSAiuyR8eFNwXDNgz8AzgEBGNsSDIH1cSSgErE0cC5isauZyndFcoMagEDAncvqiAgCgMl6Hcskpoankq0NNecHBzo6V2aTMxdGxzo6Vyiq5hKJf3/ohbtv3cEzc0V0t9ZXTSWz81QnAqiq+uRTj37nO//HMHSXy+33B6PRmsEL/V/5yt9PTY1/7Wv/URmJCkn9IhDZReH/5KB0fknYzyWkVCMFQkRVUz1uj9/vc7tcN6DeNwUBc8XxqshQwL+6HBSLQLlQcChauTQUIILB6qqGyanAhNpfXRnO5xscuVFVcR3p3duRTn6ua23O6zYYCk7HcoNFraImEPH5/AY3zFKwFRebxVVB1Vf1VJglWadqBgBA26MLVkSICFFt7+yUEIO2JZ32Q27ScXldgTAQgTCMaG2D/ALbl4SERSaKdvEDzMx+Si8uZAO1xevViJOZrIFS7xgh2IBaZ9oKEfP5XDKZmL+2kc3NzZqzhghOnTpREZ6v2aV34dRc6XRB1w1Nu0GVwA0lAwNTjDGnDgAoeRLSTSFkXk77/uYLe9EMAlyadpxz2w31AXPfvgkUxfwgY2U6BqxUp8xmCLjWECC4YQ2Az+fmIolMAbMBHoWRe2LvkVs2bVJULej3nzh6cHY6vnnbpmce/3ljS0ssUlldWxfw+nQweg+8GAxsV5iJoyo9RYwNXRj8yhc/3dhs1eUFgeAqYNAb8NQ1/9d3v/nBD/13xhRA4lxTmQMqN/95ky7q1eEoyqoYqqp63B6v1+NyuV7x/P5lCYERC298Ys96RWGCC2TALGYdxqpO9b1xYtadzADQG+9/xJXNM8N4008fU/NFcmmAaBsBl8szONp3frAXHWXgY0LX3L57Xv+epUu7dWHff6soTOByqZGreyqslIhpCUpQe2ujicO3ZjLg/OKQefVWSoYkWpSAFiR8CGzwz8Vyw6Z4PBqYw0CsioSjhZisX6ViQEBNc/3VJ/+sv/+susCoaJoWClUAwFx89v//8j8Eg2Fd1wlAEHHTMBAgmlPYrNzX1GSqvuFmEPASks/rkxMpi/7TTDWbuBRnjReAEFwVIVUt9Vg4vyfUDXM/+wP2vw64hDxeZZX/WhcA4IY1AB6P5tIULmw6CBBGQfOHa2pizz32vVDtShWKTNGqo7UaFmUFxu0OB1T9vmefi/k9xaLOycRX2COxVWSHxwYiu3cJzb0wKGaAL2ZS2y6cW9ex0uAGCUYCkS0czgRQHgAuKhbmp5TZs3d3u1wut1tTVbfbrWnajevpX1zGJ2dbUmmGWCThnZxq9brZ1LSorgSBhl7UZmYly6UyO8cROQKbiwvEIhkrRsaAoaEXrZZcRLVsBbrARSS++19feMubPtjdtZmLEjgaLPzPVT8VDGTzFAPHM2jVgcv2nFdpwoXLxRLCkr8PYFYu7P1fMghERLdLLRYNZIwsCIhV/JWlCdsGAAEJQZzz6uraSxCEMcYCgaB1OgSlAAJLoZSVx0il8jcrAS8pfecmFIVZOBK0qy92GkBmgJABN4xsNlXCQaDj/wBY1JNCBxJgeQ9JrpuD29DO/Jm1rrr6q0x1XpHcuF98KOydmckoDAURA1A9kZW1gQd/8cjark3Hjh5dsXrt1MyhRx7+ybINu1WecYdqKwefPjXs7mium07mwxWRmDIoRMmJUhh7eqzvwzgL2zde4o8+M3vquZq6mDdESIahaVrBrMuVt+vYc31tMd00U1BRkDGmKIoqRVFUVXW73a9GdT9PFEX5m7/683TKyKTz5qMwxkV0WayuGRG3rruNWYVv+3ZYzg2sn+KIILq225B3q7pvFX4BAEEIkU7MITI5pNP0Vok4F42NV+muSpYhBZgzDiiB78uV/sIvaaGP79xeoq+wVgku9qlFJRT2Tk2mBBGzEIBm6UiGAjj/OIhXYv/kUUTZlTqFMTYxnmhqrrqCY/6KSSaTn57OaBpzEFhhafqG7dwhcs7XrNm8ZIkcW2SHrTLwIgBAooLPNacFZKmHEEIdwVpPhEhYhQRzbXFD1NZVXIeru3ENQCTim55KEVMAgIA4NxrbVjR1IAlx2667EDHXsax9SQfnBkEIibo33Q6CkDUjABd8zYaNgluUkzLhhgwIgV9yIhIyRIYISCAE+P2RSMRX1HUHY6XTF5T+AMjKnKKYGv81oOUvIQXG/rba31CrJYUQjKlCqAA5onvdFQrA/bEGLyBHKCIikbu8FmmLDmAwVAU51x8BCCABcFIYn3DXkeDgfCoIGMOq6NW0PWcNncgE/pdbAKm85xUGHKd0ER9+3jZhH8k2A/KEL2MhhMPeiYmkAmjaACjdKdt1vGQa6TKk3Bw59AwAQiZbzGYLV0Gv/Ssip0+NaxpDREAAviBYtIZ+AwCRaGpqA0I529VuXir9DxBJ5g5l1ECioU5YmWqztQARBLlcyjWdA2PLjWsANE31erVCkdu3mAtOnBAROEfGWlpb9KLJGCEIQHBE4AaXmXSJyUNr5KYAWhqIALxUhxeyWm/QEAIIGOL0dCYSCfi8N8nTnULn5qbO+Xxv556mgYmz7TU/wwKQ2IZeBfFoPgWIXejedX4yWVP1Db+xiPZCuMdwd5wb7e+oe0CzE/0EoFSh0orq//bUbfHWyhS8lbsGDhCtDl51skJOWkGwkz6L5HkW6vrLLOMzh442zQBebhSgqorPq+ULRhmwxO41dQA9AeGSecdFZGHg4kgsmcIYGx9LtHdcw2HRr14ZH4tns7qqoBmcymkNViIIgOT43tKNFZZ2F2aWR36PQjaN2SOArei3DLou/wQBF6K1/TpN8rhxDQAARCoDY6NxxBJ/gsoUYMgNQ87jlLUYhsg5B2QujX7x1J67br2dC27yDJSCcnIpbmCXvF6irW6/ikwn0/dEgOHhmdbW6mt/ra8a0ZOFH7ff5na5X3j0Z4///D/e8Gu/+webbxGCa8iA4K6KNsbYxIX+b/z4H1at3PKz3/lDXdfBkeAhIsaUk/v3PPzID3bie96/4w6rZ5s0VH2K5gImgDgJ202VGpBz0dxceXXnjGBRcy7gg7NqEYt/qsxZdkQDZH8Wy3a2qwEWabO4nNnClZX+kZE4Kg5f0mQiMtGhJtzAvIlXYARowQdkkILlW4pFPj2dil5VdPUaFl03zp+fUhQGCChn88nUj+OGIgKiQiQHFdkbzUHPVhwgo0+zyx2REVl2Qu5k5ZbklywEXTd47g1tACoqfKOjc2AnPYV44cWn8sK1fev2E4eeSxvqju3bzxzdPzwd37b1lqnhc/3Dgwb6hwdPnRkcv/WWW4Whk5U+RsSgy7OJaXvJuLhXRq/zhDigHAEIRAiUTRfn5jJXiTx5zQnn4uyZ8cZgBJHtvPUuj9u7eeMtAXdAkFXKIgSEyvaVb37n77e3La9SfYIJ03sCADPaxQ1rtuj5bM/GnUFrB0nnDSSxN8hQEY6hMUSgKqw6dvVlMbJmLdqIy3mteYuuCeZsQsb5253bTL1fXi9WgHEiQwj1kkOdQmHf2FjCLCzaWsRqMpIFJ6stHHRdNwydsZcGjBmGwSWa1sIUodN0WX9F3u6pyVQ47JNAl5si5VTvmKMj1FoFiM7BRUyBmfHB/pGxld09HoVJwnMSAhkCoQAhiBhjjIxzA2c72lYKLoaHzje2dAAR5wbJSaRC9jiZZCJen+taUwDZckMbAACojgampzOMIWPK4MnHOtbcMdK3v//o/kDt0lZMPP3kHm/QvWPzxqdfPMITI3e/+Y0PP7d38PxgXWM7kBwjZf2f6Oi+F9ZHwnvTMxf3yajRFdi/7/m163u4hZZTVBwbjfv97ptICQDoPTlChCdOHgYARGxp6Rgc7CMSiCgJ1JCZ9a7a2sZkKh4/OYOMSePAEE28PDIS1NzUMTZ24YKhIzL5cQRAZIKEVIIdS1bZz5oQoq0tyi4np76YGEKgXa+xPPfF6qv2S1tbXrSg42zBdZYK7DS+Wdaz0GiXlkjENzOTYXYWWIrML0BpoxBiw4YtwyMXlEUsSpnxQUCDGx3tK3Td4ELIeceSC1VYVwg2npEIAYaGZtrbbyaCTBkdnUul8iYtjaMqY2ML0RzXxi4MnI1UN0MueXZstK6ueXRkoK5pCfLCyOhQLNYUDgbOnz9VVR2bHu8D0FpalgSCoWIufWF4sLF5eTY5mi5QQ33j2PBZzROuqozqOu9Ycv1SDje6UqusCk5OpRkgEYVrlx09tCc+N7Vl3Zq+8aEMTzS1LD9/7sTQkBKJVs2mBsbHx/P5/IY161985kF/ZUNtSLHdNMMwHnnqYfVtrwd2cT42UoaTs+f3vbC+Z4ujhRgYYxcGZ5Z01lyXK75xZWBgKpUqqKq6f/+zra1Lg8HQmbP7m5s63G5vb++hlpYlHrfv+MkDSzu7XC7XiRP7W5o7FVU9dfpoR/sKRBy8cK69bYUQ/Pz5Uy0tS4SgvvMnulZvTqVmR8cutLetyGbTI6MD7W3LC4X88HBfZ2eXIDMIMHTe2nb1T0XOMOTQdAueu8ARnp8FYgvenycOVYtmd5XNzyzrhcIKCC7HakUqAzMzmfnn4XxNBAC6ob/1Le+WCYT5FWrb5jg2ImKxWEQHHllYLSzyV5uDBhkW8sbkZPI6dJ/e+JLJFAb6p1VVKUN7W80TZI/qNGG/SlND88CpQ6GG5W6XK1wRObT3mbr6hmhV46ETRxtC7khDp9+juf2VUS/1j08kzp8IV7lYoI2KiePnzlW4hIKFEwNjOzY0EgFj2NR4/UBZN7oBUBQWqfAlkjkGwh+qXbnM/cL+2Yratq7gnE6ssiJcXeHP5ItNlVW8tSaeSO7s6dHz+fXb74mES6Sh8ok0QKwSrt+YSGoXCZ+LglfWRkaITP4sBwCjUOTDw7ONjVeZg34NyNRUcmw0oSiMSLz3vX8AAADY3d0jEVDd3RsRkQR1r+mRmNnuro0ASATd3ZvkrVy7dqtMZHd3bZZkHhvWbwcw+/UYMiLqoVuRoRBi/brtZPVhkaBYTdDtvnpaRLT4VexJ8OU4oHmq/nI09rx9cEH0YFaGLzNo0TQlEHCnMwV2Me/EPDZxbjh/LX93sY3ztizQaLYwhtNTqUDA/SuOCBJCnDg+IrEktu00DaXdB2gBmBFACO71eAmY3x8cOf0UxLrcaDCGHq9fQ61Y1F0uN2OIihqtjAwMZ0mIJUu3nOl9cSDu8fn8jY1t/mBoi9f3/OG9OzfvjEZ9Lvf1U8s3ugEAgGh1cC6eRQUJIJ/L33rL7S4mtGAYADjnLq/f7fWT4ExxVVVFCcDtchMAt7rzZairaNpb3vyu+MzMzPd+6HZ7AYAxxhQFAATnQnBAzOcyTX/w0c477jbkfD/pyhEQgMIwEc95vemqKycifg1IMpntOzulqszBXQFmIQuIhPxBVt2dbB5TuTcgStfT8qfMXKeTTUU41D0AMBtoDWBw0d7xssIvtEA+0kW20ic2/uIi6r8UL5Tl5ctelFz8Uo4YABwTAS5XqmPB5LkcqspV5rnss7ZeoAP2Y/8KVs6HnPvZL5ENDs4sW3apRrPXvJw4PuLE5pQxL0lPAkmYLAAohKhr7OBCVMbqFUb1nT29p47Xdaz2edwKw6aG+uaGumPH9kfr22qrGw30xMKKUJfMzgznyLdyxZrJ4XPDYyNLPZ7+oQurOldyXe9Y0nw9L/YlOlpvELkwOJPJFJCh1AuLPltONFXptWOrprmeeebxf/iHj73rnR8EgKnpiX37nkbENWu2NDW16rp+//3//rGPfa67a11RN0xMrvP4iNzgbW3RQOB64HNvHMlmC8ePjTA08e3l3qTVBGNNqkGr58W+9UQwPTUSjNSoTEE5NaNkHmzsjHk0ZwVVWEgJj0fbcevyl3MJ07msHKhbjvmZjwUqk4vmCRe0BjjuyEKHXBBVuLza5ZF8DAxM5XIGXg5yqPzPoRltsHn0FRa3TGlXxlgmnXR5/OWEcQ7IE4HLrXT8qqJCz5wem53N2qRvUkqd/NK7QSAhLFQBITIhBACa+AXGhCAAIiEAmBCCMQn7QW7VooAAEUlwuZa4EApjhsH9Ade27Uuv5/W+Oux8bV1YCAICwUW5Biq9LMvnOuFv1mu9WNy65dYf/OCZs+dOPfPso0s7V3772z//5r/9bOfOO5944sF9+5/9+td/uGpFt67raNUhAazHmwiFUBEHzk/lcsVrfLk3kGSzhRPHR+UUJBs2M08BMqbkcikhAIGSiVkCSGfSjDGuFxLJhKKqo6ODnJB4MZcvEBmCiIRBgEYxa3DBmFLMZwwusFzzMWQAYBhixYqGl3kViGa0Xk7cAqVXiPZ/tPAK5x8LoLwI4Dyo05++0vOMxUKcCwRkisoUhQCZosqecqYogIBMvkBFUSQbrqIosgtRUdTJiWGDSFVUhSlAwJiiKqpsMEZkTFEURR3pP77v2CndpKEncOg107gj5HP60NDslZ78a0AG+qfm5rKqwhy5n3KRa0Q6OpaPSMTlHQQAAOKcy3iYiGTgKwS3XwPJSFkIzknmP4UAICGEbhhLl9Vd50t+FaSAAMDlUsMV3lQyL5PIC737UlFvgQ+GjveBiHN+9uwxr9c/PTNJQhBBIj6Xz+emZ8aLhbzP6zNRHU6RoGwARGAM+/smO5fWar8CoKBsttB7YrSk+IWQIWM56IVNDZ8eSRQy8emWhuqJJJ8cfry5sWUyUDt54URNfWvfQL/X5ebFzIvHDvrdWjAcyZPPJ2ZVb+XI+LhRyCzvXNY/MpqNT2/e8Xqk8j5tgmDAE63+JeDT5fkyBw+QHeEv3M1eM5dQ4WXryj4Okc3nIlPEV9QV7vO5/X733OT4Cwf2ByI12zZ0//znP13e1ZOZGZ6Kp7dsv3184Nj5kemtPRsPHtznr6pfUhvZd+Rod/fG/tNHfNVNQYVTPvv4C8+i5tt52+4TB54dn5ldsfaW+mh4evzC8ZMnOpavzybjqVRekApkEUSIctwRAENIxrPjKpNsBBldL3Ij4nmNt0NeGJyenEypqgJm866EggNjTAjhSASinOFoGlZL9ZSiVwSTPkb2ghA4i/CwQEtJnSaIwiHf9W/FUP7qr/7qOv/JqxOfzz09lZoPBLxYmL7IJvkFCEVRejbe0tjQuuv2uxhThOBNTS01NQ1vuPfXGhqanFyJpQxFOTCDAGdnM+GKaz6s55WVVCp/unesrLwpsz3lLi5jyuneg75wtc/rJkOvbVmZy8x1r1o7PDZi6KJn086BE88r3qCmCOavXdFUMzRXNBIjmSL41GKePJFgIBWfFKo3EvAFwjGVlX2nhsFXrW58+Tm3nKEjSgxkufZfkM+ZXxwuF1owkQjLLQEuEgqgR1EXQ20uLl6vq+/UyUjj6gqWimcLGe5fWheYSlNPd+e+g8dGRqbfeO8bTh58hryV548drG+qjadFc13Nyd7ezqWrpkb6gsGAAd76StfI2FRCCWxc2jgwnW+sjjz29BP33vv2fc/+3BMMr1p7a8hjPRALLlNuUZmSzRTHiunvnDt22wtP/t3RAxt8gWWV16k99frL8NDs6GhCVeW0CMnjKCMr9dFHf/roYz85cmTv4UMvvLj3yWi0trIqCmYO05rjAGA/Gc7QspSCM/fAMo/Acfc5F0s6a64b/N+WV40bq6qsKhqYnckgs0Yd2WA7a5/5ThmVj2AHAEQhRENDU2NjixCchAAiBXHblh1EJLmDnMRZZn7a+YjIQwo4e2ZiSWfs5UBTbmRJJLJnTo9LtSVz9UQkM/jgKIIKIuB8xar1B48fraxuDroNBPL7/ILA7wskJwafffbnVW3r/JA1lGBh4swLI/rGntvjI8XhjNbU2jK671m9orZz6ZojR/cVAlGvSzEkUxOaSYlQ2FtT+0vgREQ7kQflVGoLtP88Fb9A3dvBp12+lh1shGCl1O1QwNz1ympsHo9aURnc33sG85Nr1/Xo+pzbGxwfO6LqszUN7cnJ/jNnzwQi0Vya37L7Hp9brfROvnD0xLr1639y/083dbcJAMMwGGPeQMXQqcPHZpm3rhuAKjzq2TPHhRZC4s5x2c5YhwAQ0YUsZRQPxYf+c27okewcMA0YA0379sDZNy55WZWYG1YGB6cnxpOawghAVdXHHn/w9JmjClOJSGHKxOSIrhcZU2R658Gf/5equu65+50NDc3CECYYFMDqpnBChgAZEDf7ge2MknW3nc4Hut3qK0LJ9+ooAtty4sTIQnyCnf4p32pp/8WQGw6FUJqct1jcYL3A+VuIgAuxZEnM95obrDo0NDs2GpfekLxeK/UD0gDKqleJBxHQZOYgIBIIyIXQFOXw0YNr1mzghmFuZwwRk3PTp86d6erq0RREZETEhWBmG6Qo9UwRFIvG1u1LXr5PRACz+Qw4KPep3FW3t5nXsthB5mn1xf7KYqVgIiIKuTyXWQSWcqHvwBN7E6uXdwS8WiqTDwb8ej6TyOqxqgg3imOTU3V1Dam5SQM1v0ebiSframonxocrKmuEUdA0l25wlQGgOjM1Njp0rrpjfXM0zABGx0Zq6hrzmaTi9qtskdWuIBvJJb4/2ffV1BigYg7LEQIE/2hj259u3J4rFnwud4X7NQWCOHdufHYmoyqKvCGqpn3ve984cuQFWVcxuCE4eb3efD4veX0Nw8gXMr/x6x9ZunQlFxwtpW9ljq3MWgnYYC4IMt+xWvBKiFIwDN7V3Vj3Skxne5UZgEQiOzQ0d9Hcy2IIaGQMkQlumFlsZ0u3E4lS+oh8GxyAlgVHtV5wLpqaK19LRBF9fVPJRJ4hFIQhBxcxQmFhoBljicTciROH16zp8Xi8VgmrzKyShedhjAnBy5CjACZMgnOTJKscDypfEwDnIlodWLeu9eVfkS54opg3C7UOECg4Q3NY8KokJf/4YnWBclxCCdcEAEQU0txXZAC4XhgeS2fSRURzlUrwGxHJAbNkDXix53uZvNzW+SFjRGJuZopp3opgQJhIdkbl2skpSFAUfG3vQ6BogAxIgOA73YF3VzR3hWJFP331Qu/3psaA83P3vqsj8lpoiNF1fub0WDanKyiddiAAVdXuu++bP33gvgceuL+hoeFzn/v797zn1xsaGr71rW+tXbu2u7v7hz/80ee/8Pcf+sCfL1u6UjcMq/uPLG/T1vwy/2MXH+VP+1frlQAi8gfcW7Ytud7XDwCvFhSQLeGwz+dzydRNyYu3/7PF2oKMFfOpQ4cPIlNkaYYsB3B2brbIy57VEmoaAQSfnpq2/UQq/88WRWFDF2bHxxLX4dqvtRQK4t/+7fsPPvjgubMnxhPTm/uemOUFTdUOZSfzxBFQVZXJidHPf+ET3/73LwwPD5gsVxKPwpgiZyDIV4rCmEJCyNdSZAKUMdQLmXQ2zxgMjw4riio/D4CMKYwxNEkbsLv7lwOIlk6X+UVa2h8BmIXpsD3/ixRssbS7+dP+z9pjwQcv9d5LiaK5m5uqrJiDEMTA8CgiMsZGL/Qb3ETHEhFIlnIikpAS+QcZG+w/pzIWqawOW9ofALjggEhAzkVvZyUIQUW22uUDYQDR/wrVPdK27dNNG3Jcf8f553cdeup78RlweQDg5OzUlV7RDSjZbOHo0aF83lAUZtN3lPwAhoZhAACRkGyGnAtJMMw5t2lE7GyE1WgIAOYrczmVUg7zC78mxweCwcWKVfXX9GIvIa+aGoAtjY2Rs2fG0RmTk1WdnOfZECmMPfP4Yxu23S59HwJgTDGMour2nO87vWx1j0tVBeeATEHkJi8WImI2lzx37mx1LCYfrUuIorCpqVQmU2hrr75qsppXXEZH5qZncqlU+r77/sUwdJ8n8KbqmqdahqAm+hlj8snNv+YLhAcG+772tc8i4tbNd3V0LJP89b3H96IvWlsR6D17esXqntzc2IWR4UAwlM7murt6Th95gaPKSHhCsfrKoM682dSMXxOP79m3bfNOTdMmxvoujIy0dXSFPMqRk4c9geiyJcu4rncurfkl1tjtoI8cLg8iyhoeYik0n6fZFx7H2UHlzB060o3yWFR69K9cuKHX11cMDU4RoEejE2cHljQ3FHT97MnD0foWFcjgnIhcLhc3dC7IpWmFYtHlciGAMPRjRw8uXdJZNAiI5IJHRTuw74nuDbe5VSwWi6rqkqnP0nUTKMi+3bFjKJdo8IaHsvHvTp79RnYaEAEZKCoQB4LvdG+5t2PZVVzRDSVjo/Gx8QRDBgyoxBcAdvtXU2PDH/3Rn2ials8XXnxxn6pqhUL+/vt/6na7i8ViMBg0/X7HMdHiLSfrppoKyXzDRBs7mx/BbHKMXv/ary2vPgPgcqmxWGhiIqkorNSracs8XAeyTCZ3fngE+o/3rNv01LGTHSH3+YlZvyfk0tySSnpsdOD4iaObt92x55mHFLd/y447jz73c/T4QQ3C5T2+jGEuV+w9OdraFvW/2koC+Vyxv386mysyhd1++11bt9x66tTxXzz0w8nx4SPplHqS/Rqwzzz8rOb2yITYurXb3/623xLEAXD0wml3Zevy1vr7H/jxG+554yNPPhkAY+vtr3/0scc2LG/qH5vICbUjFsi7GybO7ZlVl6heZXpy2F1bs7p7a2208rn9+z1KcdPGbc/v26MUEhtvfeuLT/yAty+rrPS9HOafeaJb4/vAQfHDARK6YQ2mdHUMOeQAACAASURBVO7u7PtcIAQaw5Cqoaoy8zknIpKw+nJ8lPkuABYEv6IU0I/+/RtN3dtWNkcefvKpmqDS0L7S6/EMnjk8kmbTM3MIIIz80088XF0VCdQv633hkY233Nl/5nB1XevI5GxNAHU1lM4Xhs4e1MMrRs7uaW5beex0b/fKrnw2OTM7e+bQU60rNtbXt2isNI/N/IlgkDibjb9p+DAIHVABGTdz/d2+qndFOxq8YUISec48r1bSUF3n/f1TyVReYZJ5z7b6Tr2Mum5s2rS5pqbmqaeeXrJkSUND3XPPPV9XV9fe3n7o0OG+vjMWDAVIpuKsvIHEDpW3SwI4vVMEEqVZ4i5NaXtFCfhefQYAAKpjoUQiWywYZXGAlHLIJhBp/sptG9Y99tQvFKYomuvokaPLuzfOzE0CahK7Oz07G60KDY3HG1uXNceqslMnE67Ge7d27dm77/JPSYJ5z52djEb9Da8SyiAhaHhoZno6zRClu20Yhqa51qzZuH79lr6+0089/fDRYy+GQ5VhdxSAisXi5s2333vPO/SiLjEMXo93aHSsJhJwKzAxMeZye3kuLlABMlSGElilapph6ApTPG7P0PhwLp1W1MaZqdFsNAQAJAQpCiCridX0ne9N5HQEWLbilxkRF61+SwCQEQBj7I2/+PGzyfjlMvXYQgDcOHrbPf/0mc+dHxzQi7pe0N/6zrf9zu9+iIAAaP56JGSLaIOXkLvf8tYHHvhZItGw6pbtDTx1rH+EMXViYmTN9rfR3AXZiVpT27S+e9WDe46FKqJN9XUvHjywu2v9wP3fHy+wnbtvnRjuQ8aQuMvlmpocW7VqbVNt7PRxd1N9U6Vv9zPPPllZUeUKBpxNlQTABe858wQAAGOAChAHVD9X2b6jsilt6A/N9H9u8EUg8cXxFe9du76y+tXHiTI7kx4cnCEERWFIZh6GCAQJidqXzT6IMDw88sEPfqC6ujqdTr3nPe+JxWI+n3/dunUrViy///6f/t3fHQAAQIbEy5x6tAb5AKKFTzSDQgsh5BhAB4Yh1qxtUtVX0pq+Kg0AALS0Rk/3jplJAisFNA8ORAACyOfzGpxHA8E9e19QApVr13YPTs00NC/X5wan4wm/Nzo5NhqMBMJuTScVEQ1XXbVyZO+BF93ei9AiyjHziCQIWZnXpzCcnckkEvmm5spg8MYFSxDR7Ex6ZDguiGzKQxu2wDnnnDc3t//We39vZOSef/zyp/2+IAAW9UJH21IkMrkvSURizUtAiSdTO2+7c+DC0OaNmxIzk6JY6FrVpfq99W6MBT0ejxYFNbykK1gRE0YxH14ejDS05PS8zpcv6QTixMXS9qUhvy+eTDLF3d7xf9l77zi7rupefO19yj239zK9N0kjjaRRlyxZ7jauONgQTEIJvJ8D75fwkh8vhARDyCMJ8F7yAiEJCSVguo0LtoUt25JlW10jaSSNRtN7v72ec/bevz/O7TOSLDe1+7U/mnvPPfXevfdq37XWu1xsQ6vmg3KUTUQpUMZAJ70NBz0QdX5+fvczu77x3f9dV1efTCb/4gt/sXrtms5NG4iqZbGxvCy5S2WBAgD09/TwOvOqlevPHn29eza+fNn67q79Lau2HtrzdEzmeYyAoIGzJ2b9C1s3bz91/LBKYeOK5qee+mXbyvV2Pdr7ym8pb/RUtb6y92UVG27YvPzwmy/PztTVVNa8/uYep8Ug6s28KGbaWGa/pUygHwEQ9SGD4yFXfYVk7YnMfm740GElDggDrwOqjqnRuZmIPxirrnFeLTXSE3F5dHQhHpdzCe0YBwP+vEYL6V+N43hZlo1G48zMrMlkCocjs7NzJpMpFArNz88lEjXBYJDnM0+NEAKN7JD1+KH0PzQXYioyM7SLUsrcHrPH+350fr8ArjIWUD4WFiJTE8F0we7Cj1BBQBcxYBhjoqoYcwhhQlRAKFs4BWNMCMEYZyP0HMdpZOpM/fp0oL8gsTj3c2r0cpQtZcMAKKEWm76qynF5xfuSCAXjk5NBOalo4Q5WQNEByLxiABzGL+3+7Z49zwEgSdLLifiO+z/SuGxlpWAkOR4bzmc3AEC6JwZjKF0gSMsigCybhVKqtb8ALVpPKUI4EQtOTM80t7RsebdroYTlFGGUAcOAsw7Ce3731D459fYEwKstq27s6JRlWRAEAOjq6vrGN771/Z/+JCWn8vbL0T84hMzi23cMDg3MyinCgGnRKUIIUVNDw0OtbStVVUGZ0YsQooxq5CsAIIRymANgmVo0WU1UY8PRxRMfIzSbig3E/HUGO2Fst3/k78NTwAho1XMpBYQ+Z/L8vq/NxImUUkCorMz6ruRpv3dQVTozHZyfi0Ka6pMulsRh9KGH1tdUrwgGQwiBzWb3B/wYIavVZjSaOY4fG5vw+wNNTQ3z8/N+f6ihoSYWi09PzzU01HAcfOTDf9zcvJyoRON9Qo4umMv5ZXljII1MRF6bBdtvbLvsyaRXhwBfEk6nORRMxBPy4gq6aYmbXsgYAFBCNdY5ZWmHQLZsVn7HJS0JUAv0a9sBMtlPmVgZxoiyXFA/k2Kcm04IgONwJJw8c2rS7TX7fNZLKwjwnmFhITI7E0mlVIwRzvRFWMLljTQSLHrmmV/s2fuc2Wy7847fe+bZn+o54c+nT/5HU2OVYNJ6m2rNWooobunXuWrOLCNQIFegO1sxjmmRMarTW+vrLOs669+D52bZ9C6MNb8tKn7kS4H2W2afZdWqVV2HuyZGR73lZXltJFA2OPwOUVXjPHd2CgEiKC2nES81Ni+TFRkBpLMXabqrGmhsHwAAIKqq8RC1fS4EBgwYBXCKhtF48FsTJ55LhgFzgDAABkq2iqZHbFWd1nKzqFMoSdfCRDAxHlhYiFZVOw0G8Z0+57sNxtjcbGRqMghaB7diDwFzOWutVucf//F/p5T+r//191/+8mOKov70pz+jlMbj8c985pPl5RVPPfXUgw8+UF5etnv3y9XVVc3NLa+//vqBA2+mz4EAAGHGKEaMFrT/1EpBkEy39yIjQFVpx+qqy776w1UtAACgts59tmcy+xYBZkARQ2ltKMt9RghpPacQRpk8jCyFWqtNr6UyhRamUkzvddkpUKCAMaZEKyuNNK+3iJXu3tG25kaaR7sucj0BgKavAYK5mcj8bMTlNnu8lsv1e6dSSjAYn5uJEMqQ5u7Py4MDgGwWI8IcpUQUdYos/+d//OPpM0d9vqrPfPrPvd6y3734ZCKZ+Ka9db21MiEnIUNE1xb4LGM9d1Wtii5juZUQscw10/ZBnsxERFXbV1W9R5XQNd0/65KFJYrJvoOTY/zRP/z97hMnyior0o0xs97ed0MG8DxXUeUYGV5Ip+YBADBK8knmAMXDD7RNrHD7YuSRUhCP8HdHj/1TZBowD5wAAECVR42++90NY4nQJ2bP3hua/Lu6TZTlYscch+WU2nd22mrTV1ZfKfauopD5+cjcbIRRlonzp3+E/DpWhBCLxXLPPXcDwP3333fXXXcBwNNPPx0OR+bmFj7wgQ+43e6enjN33XWnx+Px+/2rV69ubW2V5dQrr+zOvxxLi0OUGedp+Ugpw4udEwgUhVZV213uK6LxztUtADBGtXWugb5ZjsMI2OT0REVFZSy44I/EyiurMaOT0xNeXwVT4hPTczXVNdOTY5LJhhBYzZZIyG+y2BVFnpmeqKxpmJ8aN9nd0WiUCbrAwjTBks1ifPa5Xdtu2G4QsD8S87rdSjI+G56Y8geWI5TV9PJlQBZZXQBjpGkis9Nhi1Vyus1mi/79MQcUhUQjyUAgFo2mgDGMca4CcOGapLUJ5Hn+qad+/uRT329uWoUxNzM7sXbN1g899AlBEFKp1KaNO1966TfJZOJIZGql3kU0sZrOQso8dd4sS29Je8Q1gyn9TpMB6ZreCBgDVaWVVQ73ezYlNMZqfvLvu+v3pJQGgyGUsaggz0EIuX/fPqxWg9OZDATiaZ5xVsgswmJf6JIoENUMQDOIEVLS1gMDQEDJT8s6Njqqd8/1fXrmDHDC0/G5v6KKDnE5LRoAIcTxKBJJnu6esDuMXp/lXayPMhUOqYR4rTbxrRVTisdTC/PRgD/GAGGMEJch52aBMco4PDHGiUSit7eXUlZeXjs4OCjLciolI4QsFtOZM2dqa2unp2fOnDmTSCRGRkYNBqNerx8ZGRV1uvxBpBWNY2nuL4Kcqy2n6GSSUBGj1GAQ3v+qn+fD1S0AAMBg0PnKrHOz0Vd3P1XX3Hbo5MnQ+NS6De37j3Xrk5PWstrXDh7yKDNlHTd2H9vjrml/482DLiPduPOONw4f+8Btt/WePWhwNIz0HgspwvTRA+0dqwkgjheOH9rbsfFGk8Vm1HHP/+6FukpPNJnsP3Vs0/oOyAQYIC/YoCHfFMgqX+m4E0bRaCoSSXEcNpl1FqvebJL4d7sHN2MsmVCisVQ4FE8kFG0UchjnohP5volCLzAhZPv2W/V6w4GDe6LR8Pbtd95374cpIZRSjHBNTSMi9JeTp+9MNHXoXQQA0tmP5ymcm/1CLhZkopTZbPr3bkqgjPGRfgeAEMQYAy05/FKhKhhzW9dv/vzn/8zr9TLGksnE/v0H/+gznzrvEYxqEujt3b+GikpHNJpSFYqyVNbMknNRNT8fuZ8q64PLZN6plP5RRUd7cHxKSXw1NAYYf3Ou91f2yk/P9AAnAKDHKzsNWFAZLbpcWgxwKBiIBwNxg1F0uU0Wi+Gtp8VEFLnXv5C/P0Z4MOj/4P5XAaP/p7zmv63sVGnOkcUYeCR9lSUdQZVlNRJOzM9FUikCCHLxPNDYGlkrR/N1pZM1MIcTicTNN9+JMbd58/qNG3fo9bq1a1dxHGe32//0T/9scHB8y5bOF1/cPTg4vmHD6qeffranZ2DdulUGvT7D+8y3ojMeuoyXUaOEFhm7jMHy9sorxCcM14AAAAC3x5JM0tD8RLyqyet2+Sf9bl+l3DWsKFOC1Vfh8yYGpx1O19DphM1iYYx2dnbueu6Z5uWdhFLGqN3m6u0/ySRTRV2TrBKJi712qFuSZZWoOskoYhpMxDjRpNdxWLCVlfnY2al8DSy73LNF8gAyH+VLAspoJJIMBRMII0HgDAZRbxD1kmAw6tCllg8GAABKaSSSlFNqKJiQVUJUmrlW7mS5KmaU5XwTedB2tVptt99+34oVq7/8lc963N7c8GW0rLyScWgbmB/2tsmynFHtNRbBYvcPAGQnWoEjKGcEaPfDGMeh5SveacX/C4DlZADS/OQcQj/euH04GMAZl2Duzs+zlrJMkWeTTicPjUlm08TExMjIKMYIY2zQ6zUBmw4P5I0KuLgEfKuob/D09kwihnPXyFBOLoqcz5MWHoByqiwD4DHe7qwVMScC+1Jg5BhReqPzgBAw+l1nU6etMkXVfP2maCBhjIBBIq6MDC/wfNBo1FmsktV68bq5f/Hmnu+MD0JRr1aMwWAEgO/Oz3z3xaeKjyHq1F0f4RW84I/KMtEeBXMozfXKS3EAjLM1YBAghjWzACilOp3umWd+TSn78Ic/+vLLz8uy/I1vfFNV1XA4/PWv/211ddUPfvDDz3720Zqa2ieeeKKlpaW9fcWuXb979rdPa0p9brJmnKj530hWxWKZt6pKOjqqLJYrqLD2tSAAAKCq2rZ1570z/pDZaKutKVdVWlnp9noaRian7FaHoaqeKnJ7xw1957oDsZTJURkOTFX5PJQQm82LGVm1dv2ps+f0BpvPYZ5aCFZ7vZQyk2QwiamJQGLr6jUJhTls7pb64OFjp+uqKtPFVQCy9LECDbPQRkc52cA0YpgWSQYEqkLCoUQolNCsUo7DoshxHNbrRS2PXBA4SRKyfALGaDSa0i6RTCpyihBKVZloETmEEWJQ1OmpACjNvlm8Q3Y5VlWlsrKaURKLRfNnt8Vidbh8qUgks4xnDkTFJyySBtnM2PydtPnCGGWUrd/U8N4VVWWMCTgv+RcAEFBK/+8XvzzWPxiPxupbm3meG+ob0EkSt1S6VjwatTkdLo97bmY25A9IRqOKkagTjUajw2Gfn1+IRKIMGIewiDiEi9bXrJr+LkAQuKpq58jIAs/hiyj+ma9bS0+BtDxmRcdktZb8LQSYgmGDo/pboh4jpCIAjgNgboOFQpbMnt45/0BIm1eAADiEKWWRSDIcSoyjgE7HG406vUE06EVJLyzWcnQYAccXCIC0eCEACDCAWDhCGACHhgfnTKLW3rXghAjSigtknw6n414ZhwwGoJRQo9G4evVqAOjr62lvXwEAer0UDkeCwfCaNavdbnd5ednatWs9Hs+JEw0rV7a3trb29/enkjm6V4EhXRDtzW5IBwdUQqqrHQ7nlUWaukYEAABs3r7+7JkphMHe2qSqpKmlkTFot3sZo2BfAYQEQiFgaMeWdQN9XWs33M4jRij1eusYo4AMHSvXMGCUsRqjFVAtAqCUrli1gVGKXC4AxiitqmsDAMgUjs5HdgAuolcjyJDQs3tqI1FbtaGwR1UqRQDUWFwuYhlll1yMAXKVbRBiwPF56hXK7YygWNvL3FFaP8op6nlHa3kAf/nF/2O22ggh2bmOALWv6Hzjjd3Fkzeb8L7IEZQ/N/Kvqp0RIVAVumpNjSS9hyW1EUJ6vvj8MpHPDQyohPzt//6HeDyuqorP5/v2t78zMTFZJAOi0djH/vCRZcuWLSzMOxyO3t5zP/zhfwHAJz/58ebm5rm5Oa/Xe+zYsX/+5+9SonIA3KJrvbuw2gzueGpuLspxF3SvaOlGafWTLTZLF2sA6dGFUCKVfOm530qcwGMMgM4Qecf8IAM4bp51bLvV4/SwDIOrSP1HxeKEIdC88KAoJOCPBQJxTQghjMwWiecwL3ACj3me+8KKTbeb7AKX49cnknJKJsAYZSwSTSEEkEe1p5TWOLx2vVHN0a6WQH7IV3sP6bq1FGHEcVwkEnnxxZcIIWvXbnj55ZcVRYnF4gghp9Oxe/fu8vLygYGhF198qbKy8sSJk5FIdGZm5vjxE3qDHnIRr+wtpx3/6bSyAgoAUlXi9Vgam3wXuNvLgqs4D2AxotHkQP8czy+yN1l6KHCY04YvynSNz8wVRBnFhY17lkDReMpHntcjc03AgMJqCiNk5sWAnJA4Xo+FeSVu5XW8Vms37w7eClD+K1Y455aa0+fTPLOk1cyfgoOzy3l+cWaE0Pz8bF9fz6ZNOwhRtY3ZZMcs6zl3BFvqbR5dWpZJY7OnsvIy1EAHgK6u46lU8sknf3P0aBfP8waD/i//8ovj4+NFuiTP8xzH/9VfPeZ0Ovx+/5e+9EWO4xCCVEr++tf/weGw+/3+r371sVQqtWPHDovlfVLuhgbnotHUeT3sLP2f9iOydCEalPf5eYERHhjp/+Y3/sJqS/d+wYBEzANAOBa+/QMfuu3mDxBFyR/kebrO0tBSZDI5spkDC4eHKAh/8tlHMMflIvW5kDpa/KRB/9z/98W/r29o1sh4Syk6Ofoxy9uUe1IM/+3Rh9zu8sOHuzDm1q9fvXfvfknSdXau0pSA/v5BLQYwPT09MDC+ceNqSSGpeELvsMqIfOQjn21pXq71esydmDHQaNGs4C4oZQaDuHpt7RVYK+zasQAAwGSSKipsk5OZ7DDGEMIYI6ISjDmM0cLslN7qwiSZVMFiNlHKgCYCEaImg15fjX9+0mTziBgBhyihGCNGSZrXl6VLFmHJKhQZ57eI8Z+OHmoTjV+qWbdtYO8/OBtvctbu6Hv58crO1dZywqg2Nxb3mcqdXsuz0k6fu9AilxPkiYG8F9kPiz3+uLDS9SJTQFs4ctdBwBhzuTxuj09VlPwbKUJRaHnR27SIVRXa3OIrr7gMBdA1rF7dEQ5H7r//oa1bNwLA1NQ0Y+y+++5dvOdf//WXfT6vTicKgnDkyJGvfOUxAPjCF/6nz+cRRVEUhX379n3ta3/zft58bZ2r9+yUqrKi8ZhNagVI6zQZS7MgRqXJ7NxvUzBuGUJI0pt0Yi4ZW/sBBZ2O4zgEBbOgyH0EeWMzN/YyC3G+ypxe3rM6EMZlNbXBoL/IAstl5xRCkE04q+cxwLCErqO5v9iiwEwWhBCj0Xjs2AHGmNfrDQaDqVTq85//s0QiMT+/8MMf/mdDQ/03v/mt++67t7Gx8Wf/9eNP64hDJ7ZOz9l3vbr0dM0IuTyHJ2KMCAK3vL3yClz94aorB31RuNxmp9Oo9Y7HCM9NDLy0+4WUSkf6uva+8dr08MDvXt41Nx9YmBjsHR5NBKfGZ8YDkcR4964Dhw8EQvOA4dDhvfuPHE3GAq+++sLkXABjvLTWn/NuoIIFVDMsGNMqI4uYEzDmMAcc5jDGGINWPRln2q3khwsWoVi9QgAIOMAcxhzCHGAOYQ5hjDIvMMbadrx4n0KLFaPsaYuN2dyrgg8YY0RV8/dbpK0Vu/6L3yIAAFWlZZW2y7j6azAY9B0d7fF4PJVKRaOx6uqqJXdbubJ9ZmZGUdTp6ZlVq1ZqGzs6OmZn5xRFmZyc7uxc+z7eNQAAQqihwZNdWkET2DQXcEQ5b3x6W1YxKFj9s+M2+39676WHI6OL6q5nP8psXOoz0AKz5+uuoT2RzWot8kbIshyLxQKBYDAYXHRK4Hk+90go/2/+eSG9HGujs1DRoYzpdKLb7fZ4PABgtVo9Ho9W5iGZTJaVlZlMJqvVUl5ebjKZXC4XWE1+k0ExGYAU86Dy2BaA8gY/YyAI/Np19e+pn/Od4JqyADRUVDpSKTUaTiIeY17nMXLDwZmx8cg9d956/I3Xdt54O4tMB5BtZuAc9trLK8p6xuMmV+2K9duOH35pbHwwnAQaPj1tM4qS0WIyLo6YplUXjDP6eaEjFAEAYA5PTo6n4vHG2YCRi/YqZ2+ZCacSk6MhtX0mFCJjZ8VAQ1MLy7JhzudY0qZunqVNGTsVmeYulSzEAAFaZnGTfI+/Vq9NVUKRuMNuK7DJATDG4eACrzfrRAGyNJ5self+/UFWt1/kB14ERSU1Nc53sdjn2wbP89///vf+/d+/l0gkvva1r3q93iV3e+CBB1RVfe21fQ899OD999+vbXz44YcIUd9888BHPvLhe+9dwm54ryGIfF29u693RhC4TAsahvLU4KKfIe+DRX3x8rYzxmxW24J/2mS04DxlnDEa8M9VVlWzjH5TcDdLcddyN8DS6yJjDJ3H1mWMmc0OxvqzW2RZ7uhYdc899zQ3N50+feYzn3m0sbE+1w2QMUGUIGs7s8JrFdxbOhySVVbSwo8Bx+FQKPztb3+HMbpz583/8i/fJYREo1GEkNfr/d73/sPlch4/fjKRSDqdztOHjsJdW7LyjwFotM9MhinKxsJYNmpHQVHUjjX1wrvN9n4XcU3FAPLRe3aKMvyLn/5ww+q2gFQ1c2pf84pOPhGYiEOF1SAjCZPgwXP+j92xat/pIB/pcVW2++cGqxpWnuk+WVvfYjfqp6ZGx6f9O3fsIHkhX4RQMpmMRsMX5mtyHPf6G68MDPYaRYkxSFFVzwkqoyolEicolISiwY898qjL6RZE8aLUz+zHGKPByPxdw/vTiZqXBDV1YsXdXNH8QCgZCx8/O7RpTQdjlDKmEfsRQiohqiIjTuA4RBlwGAMDQijmMKNUVQnP86qqII2XnpkV6b4L6UhKYYiBMVlWG5o8FRVXR7XUKx+hYHxocE7Lv813vJx3ShdN9iJhgBAA8Dx/8mz3nleen5ka12QAY9RotOy89e7O9s4llot8YyK7reh+MoNq6dUfQOCFZ5755b43XtJcT4yxubn5X//6FzabLRwOO53Offte/9GP/isQCGgywO+f+9rX/tlmtWsVqYriG2yxIGBA87oBAgCH8UceucntrkjEE4DAaDDGYjGEkF6v1+ZjKpVUVVWvNyiKrBLVlFR/9xePAgNxaqbzX7/3+cf+rba2QVUpQovGOYBW640Suqaz9gqvD3/NCgAA6O+biYTjKVk2GI0IaCyWMJlM8VhU1ElIq+XKgMdIJQwjSKRSOlHkBUFJJWSVSjoxkUwYDOYinjjGeGJy7Itf+rTLWXbhNCKD3pSrGrgUZucmH3zgD2+++U6t99CSYiDn+dG0CoxURfnR6y/ohEspvcKAIWZE/O9tuU2lRf1tkJyKPbNrV5nN1LZuY/fxnhs6m070Byb7Dy7r2EYjc6ay2sP7XjaZ9S0d28517zdIorty2ZE3d6/duHN2+ARnLm9pbBX4XB29og6P+SEyVaUej7m55YojQlzVmJsNT4wHeT4v9WlJE+xSprlO1D2/64mXX92l00kAoKpKW2v7H33yT2Kx6NIH5DEgisLCBW/PIwAAgOP5V15+/oVdT0qSAQD8/sDmzRv/5m++Ojg4eOTI0Q996PcSicSdd97tdDq0abLgn/2Hv/s3o9G0pAAABnTRpTQvWTYnMOcKSysuhbG4LHOKZitaZc7P0mFn7ZDsyTUxp72llGIOr1lTc+VXS73S7++doLHJOzQwG44KmlfGZDIxxgwGY2ZN0ihqoEW2jAYDY4xRyguSIAJjzGQ0L5aOjNLqqrpPfvx/PPPsz0xGC0DB6C/e+YKzzmyyIoQ1o3Qptmb+iXIvVULe/P6/+spr3uKXkL0TRU09tOMuSJF8JREhYJQ2tKzsqHKcGB+nlHIcx4CtWbPh3MhwmVUPAA5f7arGip6R/hl/pL21heP4NR0dY+Ojy1rXdHcfj6fqbYI+Nxs0N9GivDBFoRUV1svb++KahNtjAYCJ8SDH44yvJQOWrvy5pJJ+ARBKQBDzf0JCaEH4pwiFTOLc5vxQsDbiKKBFsVAGgBgzm62EUABIJBKf/OTHm5ubf/Sj/zp8+DDHcSaTUZaV++6754UXdpnNZgBgjGW1qyXcjgi0EsH5kWGkbc0FSorvHDLpY9k9Fin2OV5nUTxDSzfOhtUpheXLy6781R+ubQEAAHUNIGV6awAAIABJREFUntGRBb8/xuFM4W+Ubp9dNB/yhnuWC5r7jSljOo4HgBRRmSLftP22cye7huYnBApYUQCACTxSVYYRooxxGGk1kbUKAJoBTChglN7IYUQpJGWz3sAhpHX6pozRi8WCGWMGgx4bDJx0aRXzGWOICkuQmAA4QRjvPx6csWzacmtfZN/uV/fUtG6cnO5VCWCO11oBMwYGm7elTp1ZCFTUNHX3TTHevDA/m1JkCumieHlJLwWhYEqZqpL6evdlj/peq3B7LByHRkf8XLYWW55q+jZOSCmtr2mMRIKSpAeAZDJeVdekXkAAaCjUhFhBhCgTl16KCaPtZrVYCVUBYHR0/K677rRarYlE4j//8wcul/Ouu+6KRqNGo7Gn5+zMzAzP8wIvaHyh4tUf5V040/9BA+YwJZQhhpjmn88s23nHpW873cWH4Qx3KO3fLHRtLVkEhTFGCetYXfXu9rR473CNCwAAqK5xcjyenQ6n8wMuRRXSoEP4YwOvbjW6MaBvRKZrEHrYUrZwy+anSQQU0uYPJQVuyGJeOzlrjsXHXY5+q2nDxIyUkge9rjFJ3Dwxy1E66na4w1FjIjnmdgyaDJvGZ7Asv+jinux//U5LOUbod6HJv6xeK2IuNxABFuk2wHE8/zYCAAAchxHOJB+wdGCZMiYI0v33PcwYo4Ss6twBAJRRVFWWZvcz6l67lhDS4kCAKgGAUrpxyy0aT7S+aTmllNEMYTTNvMtTpBhgBKtWVZmvpPT3aw8Op5nj8ODgPJ8tulDE8ryUMU8pbahtvPHWe196/leioKtqbNuyYRuh568pvSSptNAzk2XjLB7SCAAoMxiMlNJUKvUHf/BRq9Xq9/sPHjyoKMrc3Pxf//WXn3zyN4888tHPfvbRRx/9nMfj1huM6eDTorBHQZg5W/WE0ZGR4Zqaes0RxLTSTJnEBO0vBqCMcRiH5icV3uq0GIBBtnqwdv8IMvUPEQOEMMaUkkzZeFBVghDqXFeru1I5P4tx7QsAAKiosOt0/MR4IJ+h9RbBAFRgj9hqfx6ZUoH9H0ctAPwwPFkmCt81t0XU1M8t82s43Z/o7f+vwQBEuV+y/pFk/4LRAJT8ocFZxxu+bDYCY58wuL6fCgFRHpbsn5IsXzQagNGVoq6GEz8zfQoY/TNrlQ7zFBgr0msWKTmSpM/PqXlLT8GY2WTJo66mT6uNfk25Y4whIJptSyGnPLK8ivPZw7XAeI7wUKxoIoQYIYznUcfq2iukSvBiBGdGX9jXtX5tR3/3USYYapraR4cO8Mg0MhszscTtd9z045/tqqk0tW+6BcVnX9+7//YP3PfLJ56p8Og9biez1KL53rq1N3iuDNlmtRmbmrn+vtl03dN3FtujlD583+/feMNtKSVV5a0kqnIhf2ZW98c4q2Dlr8LpBOA8KnAW6aGNQBB1jFKMuZmZGQCYmpp64IH7V65ciRDccsstU1NTU1NTw8MjkiQxxoxGMy7MMVnCFEg/iaZIkZmp0YaGlpmpMavDC1SZmZ0tq6yhqficP+Dx+BLREMWCyWCIx0JjA2etdWsQRrFIMKGA22GPhPwyRTaTcWZ2xukuU1JRhWI9Vl5589j69Z0Y0YRMzEazw2Fsbim7Mvn+58N1IQAAwOUym836/nPTqkov8Atlte/8/Age0LPhiZ0Gpw7j74UmbAg/aHQrjH3VP9jB6z9ocIaJ8kx46p+ttRInjKfCB+L+f7E3CJjvSwTOpaL/5mxBCJ9LBP7FVsdzwlgidCQR/DdnM8b4bNw/pSb/vWwlAjgSnZOpKmQsgOLVnwFoZV0QXDi2vPRzMaaT9AXL9CKqBEIoEgmaLQ6gFGfIbRn3Fc1UWEQIyGv79m3bto2oanGiVx4IYTa7vrn5ip4PR/e8euf9f2BRg12nnA/esf1nv3rS5tTduGX7r18+uqPNOT89W93Q9oGdy5978aDdCIlkYG5m1lPXsqPdc3wezfcf5wlZf2Ws/hpMJqml1TcyPJ9Kqu/8a1cUxWmxIUCULO38QTkGTNb9t9Rg0LrvLhXoyu3CQOB5xqgg8F1dJ77//R984hMfB4BEIpFIJAHAarVGo9HHH/+p2WwCYEaDSWtzVnA/ef9CVifBAACIIkEQh3oOR5DpxImubTfsJGriwJHjEOzxtWyNh+e7evogFWxpWX5qaEqfjNsQQggpqjpy7gRuaX9x977bb7npRPfrkrnGmgwdPHbCLKpVdcuBKgDw9G+fuXHHbWWN1ppa11v+dq8UXC8CAAB0Or5tecXoyHwwEL9wecJ8o5IBUICbzL5nIlMyYx+zlCeo+nx0VkDoz20100riych0FafbZnB+zt8PlNwqmtZKtkfnzwGjD+nt5bz0mbkeYOx+yfbNyDQwcrfOskxn/sxsDwD7qN7h5HWfnj4FAH9kdPOYW0xf097nG7Z2uzMej1+qBWDUm4qSezUnafZJOZ7vPviCo2a9z2Udm5ioa2gaHxpweSuSiVhtVfnA2LQOpRbCiZaWFkVVEUBB+Z/sqbSwMmE1tc6yMttbv8PLgvU33fL8b3+7bnW7W4q/vPul9Ru2Do90UUb1OgEwJ+rEM2f2S2h+xer1Xftfr62vlyRREjkAbJCkZp9xKGm70oSbXi+2tpUPD84FgxcZ5JDnzl7yo7RvBKPR4XNWZ7nFZMxfcBFCiVgIRIteQJSqoUgsncmFilUXgAut/dnwliCIoigBgM1m/eUvf3X06FHGWCAQHB+f/NznHv3Od77L87zVauE4jhBVr5cwQiTf9aRZzue7DmOA0OTIqLWutbK2vrfnmGhyx/2zmzfdcujwAcVXDlTx+KqCC9P1y1eJ0z1JyjAix7qOO81cArgbNnZ0dZ9ct7rzzMkDoymXShWLoxwx4nRVWCy2HVs2j03033ln54W/7SsT15EAgHQDGXfQFhsZXkBL1V5e7MzQ5tCJ+MI95jIR4Rcj0wbMfchaqTD6VHiynpcesVVFVaUvFf4XZ3MKGCNKbzL0HVeLAiyuJOfU1LfdrZSxsJK82VKmMrYgx5KU/Kt3mczoZDJs4vjveperQMcTYUIphwu5HJlXWe2GUVZT3XDq9HH81lpkaGCMSUZjNhez2L+U3glEg33ZsuWnj/yupuUGg0D0EnfweJddYCaRBQKBc319DR5934iR57i0dxUhhBDLK8hFKeM4btlyn+HKpj9rMLvKH3qgHADq69Ksqobq2wDg7pvWAwCQYGPDsltu2wYANQ+mU8Bq6qsBoNE/98rJ8J333HQ57vriqK13z86EJieCuDgBPI10wEarWYIQpYQB4jCmlHIYawYfpZTj+YWJc7OBhLdchExYQXN8c5wwPnTSVLPFJHHJWPjs2YHNmzYQQoqCwJnLLZ0AljO1GUiS3mQyaxan2WyemprW7tHrdf/whz9yu1356bU8r0MYAyG5PPzs/S15FV4ASldv23H46HFfRZ3LgUamZytqamfHh3m9ubK2JRo7GoqRzlVrjhzaF0spK1Y3U4btVmMilTQJqG9yyul0RoKTBIneqiZFTgYjyfr65oH+s7OzI5yYamitWRTauDpwfQkADTa70WLVDw3MRcJJzC9tKmdpvVq3t8eTocfjcwDp0fbr+DwAAEKvyWGIzWivf6S90Nbs6FTmNUB4NPM6T+UKjeReZ8zoj5QtW3qWZEc4AGPU6XRTSgAuKdDEdKKQzYfMLfuFPnyipuKxKKUMY3zo9ZcqWldw3OyqZbXff/qVRx95qG9goKZ1ncFk6T99KseuyJtyqkqcLlND49IptVcfONsH79225CdWh/v+B+5/n2/nkuDxWq02w2D/bCqlLjYFOMwN93VZK1vHeg75KupOnO1ft6ZzeuR0IBL1VLc3VPpmJwfPDQ7wkr2p3DI2NtLY2MCB8vLefcuWt2MlOjI2arBXWUWdwOP9e55TEcfrMwTfPIFzYRlQ9KkoigaDUUvEBYD8okBGo7Hw9hnP8whjwLkkxGI2NmMo07YPAVAGa9dtJYRu3bJdIyz7qloAGKWsBiFK6Zq1W4AxQtm6TTdqhAhC6JrOzQBAVdq5YZsWPfaVN1NKV6zcSAFkWbnljrvq692XmpV/ReF6FAAAgDFuaPIGArHxET9lTHOYLg5kan9TjBxovBEVJrlA4Qg+v4lbMB8K9mQZVlzmH62cfEEfUVR8IcbA5XSQi7b5LgRlTJJ0RQ+IIK/CLgNCyZoNt4yMDVfWtesw3bBlR99A3+oVK81ux0MfuJMTjXfs3DE2Odtgc63v7FApQRkvAaQVf9zSWma1GS7pxq5AnDy819Oy3mfRh/yznMFhkngAOPHGq44V26usV1PtLJ1OaFteMT0VnJ4KYYyzDhJtGFBFRpijRNEZLBIHqVS8+9zY+jUdC3OTXE1l77kzW3Y+eHzfb0KqZV3nDVaLmSopm1lSVBgb6rv59of37HrcUlmpxgJxU/3OldUHjp0uUMbfGvL5DhhjSWeMRCJv5SiMcJ5alF92HOW/yJ9KGospO3HSprDWOjxXGxgoISxT1D23MyEsL+9LpSoANDS6Xa4rq7j/28B1KgA02O1Gq1U/OrIQ9McxhxHKrdEa9ys7ngx5rSrynZsX8KIWI2MX55YQnD5ZzrbND2Tl7VrEm1iq1/RFwCjVS6bMTbMlnF8AiDJBMjY3NTPGKKUAYnPLCsaYqlK3x0OJajTZW5rtlFKnu5wxqn0RDGGiErfbVF3jupLjvefD3OSIYCtTw3O83nLu9PH6FetUVd3/+r71azslpGCE5idH+sYXICF7eDw12j8+n1y3ZsWZE8ewydnaUHO5b//i8JXZbHbj8OBcMqlkTQHKqLus6vChN5LRQJVKTAZhfDa0rL4iGA7XNy8nRG2oazxyYLfCWao5iDEKjFGiGPTGwbHhpqraA2/8TrTWGHW6QFI1xIcPd80IOteFeEcszQUqKAia+UhjASGMbHb71PTYkm158iGnknV1TYzS3AwtGs5LEYQWv86/DZqXE7DEbmnmJ6MMKKVms9TY6H3X+7leFlzXAgAAMMa1de6oKzk8PK+olMMo56nJxTgRxphQkr9Sa4v1koXIi4AK6QoIY0apLMuiTqddpoibAxliXHZL4XqPGLvAVFsajDGdTlqSqZ2LCQOggh4AGZ0n4+XPXpdpahMDQqgkCXWtXoPhKvD4LwmRg0PHe/nQ0Lw/3LZuyxM//VFHe1PnunV7nn9+5ZpaXZwcPXz6oQ/ecXTP7ujCzN7XTizzSa8fSp3s6vnYIw9e7nt/q5AkoXVZ+cx0aHoqiBDSCpYZ7WXbNjoZwhihlmWdekkCaE4mk6IoEkLKq1ucvlpR0FFK7IAYpVjQN7W0L9fpMMJlqaSo0zHGnITwVbcnUyntKIDi0ZX/asmQW1blUVX1zjvu7+/vWViYvUB8ixBit7vWrN1AKFnC4Fh8iYy6Q7UPMxkwmYmdrouef5hGcMglgmY+owwQgrr6a0Hxz+J6FwAaTGZpRXvl1GRgejqMENK6KmZ+foRIcnhqvrayWqv8B1rvRUYxwhgBy9ElcQG5Uls8MTc53OOuaeOAAkIc5np7TrYuXxWcmXKVVXAoLWa0EU8IQeeXKBn1nToczkBgzqg3xhMxesGOSNrtGwymcNhvtdm1FJbsvV3kQIzSGV5FmhFCiDFCGUKousbpdl/dk8HqrZl9+luO1u0eNZlIpO6+7/7R3qM6vUEngcyYBEwhBGNEiKKqajwZr2haZ3d6VjZXf+cf/+MLX/zs5b79S4DXZ7U7jCPD85Foiucwo5TjeO2n1YkioRQYE0URABgAoVTgBcYo5rjsGBNEHTBGGeEFQduoxYpFUVysW+QDIaQ1o14CmelCKfV5y77y5W8FQ8GCaAEuUOcZIJvVJqRvIOvVKhzPRXVItL9pPX7J+wO0uOpvnnrEAIhKHU5DTY3rotyqqwvXcjG4twFFIaMj88FQIptRiRBCqdCTu55fv2Hn9Ei3s7zZwOLnxmbXrV+3f/+bIubWrl3bdaKrrq51ZqpfVVRPbbtBWegbn/H4qgUlNBMlxsRAVKpd3tTYc+ZEecOq7gMvVTS184Q0NdUfOHQQi/oNHcv37j8g8OLmzdu5/JK+RXZt5gXmuHPnev7p/37tEx//nCTpL/b7sR/84NsPPvgHGzdszS9eVbxT5k+BOcLyOltk6n1SygilPp+lvNx+qVl1VyaCC7Oc0WnWQXf3KYvTZzPykskenJ+VDKLOYI/PT47M+Bvqa2JJwqvRiblQS0Nd77leV3lVpffyF7V+GwgGYiMjC1rdJ5RhBGUdg4QQjuM0sc9zfG9vd1vLcplQlOb6IwDAmGOU0GwxhPzs+rdhAWRkgKZ650pJLeWnTMedsgRQLfsM8ob04hKnlBbZ2fnjPD0p0v2Oc8+i2bwUgBIqSUJ9vVuvv5QKjFcJShZAAQSBa2j0xqLJsTF/PKFwOG0v17esaqiqmBjpaWlsPNtzQpRD88EFV1ljlS02FYgzxgSBSyD+lhtvffrJnyug++AD9z/x3LNek3Ht+m1DR2fbNmwLTQ/abNbe0TGj2bl+TecrL/y2Vw6u3XRjZLi7b2iwrLLZDbPz4WiZzVQkktOs0LxhzSitqamfm59sX7FakqRcFX5UeFTmkEjEX1vbUFC4JM8OeOv0Na3OicNlrKh0XEt6kM2ZZrC0r1yV3ej1pauWSr4Kh68CACxmALC7y6oAoGP1mvf7Lt892OxGm904MeGfnYlgjCOhBYVxHpczFg2FY8kKn2d8YtJbVoGBLSxM9/QPNDc0KpSjSkKnt6TiwRTBZ0/sqW7ZWO5xTk5OerxlS1NNL4acOzWDdOCN0lznlmwZuzx6D0BmAOdfN+v3z2YjFx2YuUQ65JCRAZpY0nop5E88xEAllBe4mmqXw2F6Gw94VYB77LHHLvc9XHEQRd7lNks6IRaTFZkIAj5+4qhObwqFFupqq1957XWrXjR7fNGobDFBkhqMPOkfG2epUCQUwGavz8zGpmd4QYdV2V1WGY3Mj03Nj42PmEyGuMpZUHQ+KscjkabmppOnT88FA421NYGobOVTVHKZ9WJRRiPKOiUhMyUQ4jju7rsf1kkS1UqPZzz2RdAc+rfeeq/ValvCUChK2Yf0xMijV6R9pZQxRpnNbqhr8Didpqsx2FtCESwWvdNpklOpvbt/pxOoP07OHNtXUV1/qutNg9nWffqUEp2NqXh6ctxnF4OyYXKoCwvGrtOnvW73/NyI01N78tBuvdVjNlvTw2EpFlD6E5zuvL34o8VvljArs2debGcUyYDFyGOFFlxnMaUv70KUMo5DZeW2+gbPNan4Z1GyAM4Lu8NodxgX5qOTk8HtW29iCDsdm5IpdNetd8Rl1Ww0eK0EY7AyFDfpmlus+w6/XlHVZDabGWPhcNBisadSSR5obfP6cCRiWbY8FArUmiw8botEo/VVlZKk7zRaMa8TOGS0UISqEOLP7ypluWnAGCAkiRLL63GRz6jLQjOXRS38u9jXl5fGD9mzFFIgtApAdruhrNx2JTc2KuFtQBT5unpXRVVVx6pVT73WLQmGcq/3wNHUlobWkYFTU35x89ZNU6MDAIhSwvOCf362tqHV5/b0AvZ6fNb1Ow7s32My2122JQqnQ57bfXHpqsV+Ira4P21mtGOOU2SZ4zL1nzEHCIiiIK2SO8vrQ5B/lfwOMJnVv4hgnQ8tfKfFvXxlVo/Xcm14OC+MUgzgLWFhITo5ESAqwxgQxgggExAGAMAcB5SmZFkUxXTdK636NMp1y81WhgZNIdJ8NginGTUAcAGdCJYQAAX3VzxS02Va8qUCy54nHwXhtdwfxkCzHtwek8tl4flrx+FTQgGo+o/f/KfNN2wsq19xcv+RmrrW2cmh6QW/zVnhtunO9g0ohG3sWHns5AkKaEX72lPH99vcVTokh+Oy2WwKLsxU1S+3GvWscEwWaCFLloBmAAgwxwEwqpEgGKOUioLQe+qgo3qV3cADIMxhAJge6ekZm9u0YTOmkZODwXqvkSSj3QMj6zs3mYwGRgjNH9VFzqJ8t6eWs5KeCLl5AVpCAKUIIV+Z1eU2Xw9Lv4aSALgEhELxqclgPKEIGKZmZyp8FZRRjLmp8UGT3SXwOp0oMMY4jtNqZ2KEgoE5AMxLRqNeyo5FhDHGiCipUDRht1khHYlKlyEHSNPU0GK1KM+niTkuLVRAs3Bxuvg5Qkoy5o8ky1wOBggQLJE1VjRh8jQjxhhVmU7i3R6z/dp1fZaggVG169ipNZ0d2ttQKD49FU6ko1+AEUYICKFayIdSgjFXHKPSHI2LuMWa4sOyzSDzoL3BGJ05eYgze+vKHEeOHfZWNjXVVp3oOnju3Jmb7/m4w8jHwv6jx4+1r9480vVKgLNu27iFKv4jfaFGjzgx2OeX+Q1rO0+f3F/ZsKrM4yA0zZ7IGcH5FsB5FB3Q+nBQJgqcx2txOK+7AV+KAVwEREkFwzG9XlJSyZSqVFW67FZpanzqdP+56ooajsOxWLT7yGvVzSsFnk8mIpgX973yO5e3XOBxKBrV603TY6MJyswmM2KEMsQoiQemzwyOOh1OnhdEgQuGwwajkchyJBqV9HpNOUEZXkTexMrZsxzHnTl9yuO0nB2ZRsmAQqH/3JmETGOh2dGxSb1efHnvHm9Z5eRwz/DIiNNTxmPE8i2JIjDGIN3T2mrVV1Y7fGW2a9v1WYIGhHBZea5JpyQJLpfJbNElEnIiqQBjmdBsOglkUZSJAsrmK+YIl5m1mC1ZBxQBYMyN9R1boKa54W6FUHf5iomhU9HwvKuq1SKiuEzGxkdPdR256Y57X9n7slsvLV+/+dTxg4TXJRUuOt8HyLBm3aYzB5931Kw6eOT1tpYVuMiSXtJKzmdSIGCUMQoGk1hZ6SivtOsN1+OAL8UALoJkNHTqWJexrLn7wBsrVpZHUWV85KinstZpNdfWuXc/91yESpGUOjYxrpNj3SOTN2zenIyH5/0LY/1n3bXNKf+Q0VIxOz3Rd+bE2rXLIoqdBPsE0TU3Nx1xW8Znw6nYvMVmGxydJNFRg8lxbljYsGZ1VmfPjNZCqxYAAMnRuYFhvrtvotEltiz36AR8oud0a7kV8TYOoeZlqytM5JmjPR0NrsGxmWX1FelkrrRPKk2hY1oDdwaSxDvdJovFUArwlmA0Sk3NvlRSmZ0NBwNxQuj5isotmYibTWa8QBNgBkwQdSRBV63dJocmKUJGURB1UjAUjERDtfWrG43VwcnBcDCAOV5VFYT5Fe2dlIQHJubswCghCCHJYJRVdeeWHZiRxTFdtDjlJWMuE8p4nrO7jU6nSbgaGje+dyj5di+CrlMnbWZTeG6WlyydnTsX+s6NRsXO9Zt0mJnMYigZ+dDDdzT7bBwPkq2s1msdGpvS6e1tLW1xmbW3tSFGCKGtbauafOa5uMwo4zhMCWlsWeGy2+REVOF0a9Zsik31Il5atXpDLB5CeVMom4yWm2kIAUKU0cbm1ldeO7CpxRdMiWNDZ2TgeQ5X1TRH53qnFiJz44NzMeazmzwVzY1VPi1ckZ0hjDHKgAGSJL683NbcWtbQ5LXZjKXVv4QsdJJQVe1c3l5ZU+uSJJ4SRulSDuOl+AUoy68/j4OZUeqrXV5tE8OxqKOs1mGVahtb6xvbTZxi9dTaDToEePOO28bGhm/YtLW8ZSXHGCcIos7RVG6rbVhZ19SMgS5fu5OXQ4qqLsUbQrm5k7FdKGVAmcGoq61ztS0r95XZrvPVH0oxgIti/4FX5yYXqptXTY1P3HH7jlde3l/uwKcGxpye6htvWD8/Prj3yEk1CWtWt8ZitPvUSaOtkkVn5xNIYLBxy5azJ94w2cp7erv1ZtfWztV79ryYomjb1h2v7X1xWduKpMLxNDo2PdXQ0hmZG1i+at2R413rO9aqWQuAZbosQZ4Nmw4ss1giKQmCTJlREqcmxw0WB0eSMRnKvO7AwgzwBpOOm5pfKPOWAYA2JSljAs8ZTaLVajAYxGuJzl/Cewo5pfr90WAgnkwoCGsVJQoGZBbZN0taACjvj8anYIxBptsixhwgoJmu3RhjjeqWXaayloi2RWsLk8kbw8AozdOcWHbQM2YySXaHwWSSSmM+HyUB8O6DUkgm5FA4EY0kEgmCgGGOQ8AIpTzHIYwopRhzGf6PVqmEAUaUEMxxlBJYzFJD6Vrs+ayGdG4XAmBafUSWrbqVr4RRSg0GUW8QjUZRr9eVKD0lvBPIshoJJwKBeCSUQBzGmigoIq1l/iwO/6b7RkMB3TlzCCouyrAoh0tb3LXWBflnxhhPTYxanT5J5CljjAGjTCfxBoNosxmMV0N3isuCkgB4b0EpjUZTibicSiqRaIoSmk65z+rxml8HcoyfrDczq/5n7dnMiq9Vtco4OjPVrLIMIkHgRZETBE5vEPV6URQ5hNCZUz3LVrTJichsRK302C/Hl1HCNQVKaCyWCoUT0UhKTqlaZQicJwwWE0AxQuHAjKC3GAxGhBGjVNPoCSEcxyMEE+MDrvJmAZHssZRS7awaBwkBANC+vqGWlgZFURHCGCOE8OCZI1FkaqiuslgMOkkwGnVGY0nXuTiudxfYew2MscWit1j0kPFCEkJTKSUek1WVplKKohBGWVZn0XbLKUSEAiOReMJoNPEYhcIRq82myMlYLGFz2AUMgUDQV+bFoMQTSZvdzBjxeJy/+vVPbrvrQYteikTCos6AABLx2Ct7jyxb0cYJOptJkFPJeFK2WS2X86sp4SoH5rDZojdb9ABACE3EU9FoKhxOqgohhDIARDLtgPNkQiw4d+jN/ds3rdt/+OjK9rWTY/0q1m3bsG7f6y/ZXLUQH+juH2+rLesdGdYLOjkRWdG5MzI7MDoxtbJj46nuA6mkvGXrLQde/W0osn3dmo6FmdEz5057a5aLOhpcmKlo3OQyldIVLwElAfD+ASHEcYjjsCjyZnNBM3FKaFol18LBAAAgAElEQVQGMMYYEELTxURB/fFPfnnT5o6+mXB8tr9xRUvfZBjI2RW1a3vGh0zJWXtN3cFTJzyWeV95p6hDM8MDx/onFCWZSCaP7H3eVtbQ0z/d5FFFd3MsGgaA0MLk0SH/+OmT229Y89pR5Z6day/Pd1HCtQWOwyaz3mTW+8oAAFSVpFJqMiErKlVVEo0kCWHAgDGmELrjhpt1ykLdsg0oPjGfBHWhZ6hhldliH+w73tRQdmPnLf2Hn9968wcOPfPL7bfefuh437mjL61at+Hs2ZNGr+++lS37Tg01Ld/44YdvAcaefu23/+NTH3/hqSeTeuN9D9zr0JdW/0tDyUS6IoA5zPOcIHCiyOt0vMEgGow6g1En6SApWRpqqwROHZiYDoUTjTVeEPi6pqaIvNAzPBn0hxqrfTKDmrqa0wcPTC74JwPzSJC8dlv3qd5IONLaXDsxPNLR2uRzuwEAAcIYizpjfdPKwNjo5X7uEq5N8DxnNOqcLrPPZ62sdLS2lS9fUbGsvaK1rWzZsvKTvd0Mx0wWccXa5XqRW7vlNi8/FAe9RS94XabTpw4Bp3o8esGklJebDQ7rxg1tja1N995/i4CTbp9Zp9Mheezk6bOA0OZa9779B0JMNIhMlpXL/dxXH0oxgCsd0+PDxw8fQO62lV48uJBsbGodmehav3LTa0dPrvDaTo3O1dY1Bf29Lcs2j3QfDSlU0VndLDIWgfZ6++n+mZrqOoeBHT7Zm0jSu+/cGQnMDM5Gg9PT27dv2bP34I7tGy7385VQQgmXDSUBcKXj6P6Xx+cS2268yVHoNSqhhBJKeIcoCYASSijhsuH4gTdWbNjCI5ge6aN6b7nnIsSEN7oOblldZLYyStH5m0iWcCGUvrZrDWoqkZAJU2OnByYu972UUEIxZiZGZwNhANLbNwgAY0ODCEFwYebM6VOxpAoAM1MTQyMTABCcnxwamwGm9PUNAgAwZfDcuXOTY+FwGADm5xYAYLC/b7K/55e/3hWKxGemxuYWQpfx0a5GlATA1QFG5KOHD03OB+Vk5Mihg33DkwAw3H/m7MAoUPVk15HxidmzJ4/3jU7554d+/OvnFkIJt8OspuIHDx4MRpOR4Pyhgwcn5kNKPHT40OFwQr7cD1TC9Qj/9MDPXzpolIRf/PhxvQi/+M0uyWyeGe1/5dAZzBjGCJi669ldJDH9/GuHfvyrN0w6/OOfPK4X1Cd27fnlT3/GSRJCwkuvvAhAn3/m1Zef+7UiGIgim8zm6Nzwj3cdlXTC5X7EqwwlAXB14MVnf1W3bPWRV588eeaErqo12n9wz2u7952e6H7tV2d6BiYjfGWFx1Pme+mJnwZkWL9xk93M9p0498QvfrF2/foXfv3zE10H6pZ1vPLCs3v3vUQNdkup0mcJlwMOX8N9W9p+8vTzDAvVNfUkPC1TFgkG6hpaa2uqCKEA4PSVNba2j41NWNzlbo9DBamytjk4PRwhQk11tcQRjlEALPBofCHWUlNhNesdbm9F/bIPbWv68TO7L/cjXmUo5QFcHeA4LMsphjDH8YlYTMAcBmzScRvu+KiBJMeCqhoe/M1rvR6LTSE4HA6mPEaEgMecIqcwhxFCGHEGXtq24+7j+3e93EVvWt18uZ+phOsOiaj/5Nmh9R2rqyz4yaee2nHXB9XAMLJWBs4e6VqI3njzSgA0fvbEsxw8dP9dZ3uGALibt6x86jdP3/vBh1Fs5qmnnq5qbOuocD3z1G8qm5rWLPM98eRvOjdvjs4ePtEdnRyd2NKx/HI/4lWGUhD46gCjyqH9B6vaVilzZ/b2RtYvb25tqO7rPTUXZeva6iYDyZoKZ3fX0YiCN6xbfezAG966OpUIdR7T3jePrOnckAhOWjw1UyPDkkiHJgJrOtfoS/0dS7gCQcnZ3r7WttbLfR/XC0oC4CpDcG5CNbhdxpIPp4QSSninKAmAEkoooYTrFKUgcAkllFDCdYqSACihhBJKuE5REgAllFBCCdcpSgKghBJKKOE6xbuQBxAMRt98o/t41xBRKVrcnrmE6wCMMVHgOI/+VDmvWgRMSsyCJaAyts3hNJ4NDR+fwjoMpcly3YMxppMEr89yy63rystd7/8NvFMW0OFDZz710cedHkFv5Eqr/3UNBqAyXmYT2y1HVtkAp3tVllAARgGhlWfC1a+GgEeML02Z6x2MMUWmC7PyH39+y0cfuVUU39fk3HckAIaHp+/a8U/N7Wa8qPlnCdctcJLJDu7cjfazHj3QghbfJaTBIW9UWfPagnFYphKG0uy57sEYhEOy2aL7q8fuX7O25X27LvfYY4+97YN/9INdcwshUVfKKS0hB8YjLsW8J2M+VR0qk4DDJRlQDAYxEfc3mXVuznEugQCgpEJd30AIJImjlD3+oyMLgZn29lpJr3s/rvtOLIAt677kLTOWHD8lLAkkMypA3y2OU9UmILQkBpYAhwwpsvX1BeuZJDGWTIESAAASCbX/VPw7P3zgllvXvdfXevsCgFJ2y46vWG26kgC4JDAGcoqIOi7/e2OMpVJUl7eRUSYrVHe1W1cMuBgNt+j2b3OF9Ry8z8FhbWxf4QMUAfB4+Xis6SU/n2RUvLLv9ioBJTQUUmwOXf63SVQajqg2m5gdEamEolBkMl5xNTEZg7mZRF2j7Wtf//3qau97d6G3TwO9kktIMMb88ylCAQAiITmZohfYOZVQZZm817ekyCQeJ4wBw/im29skMSd5GWNUEG6/s0XgWSyqqCpjlOmsxhtvbiLqe35j7y0QEBM2jcg3/2Ry3bkwYHQRJZcSUBVQ1bc/vBgDQgAAGL3PbP+U3XZFj1QAYAAKPV1meOoj5XOrjThK0YVG6/sFxhZmE+PDsamJRCL5LgzC4EIymXrPB3NwPplKEUqoxev4+j88wCeU7G9PVOqp9X3tb++GlDI7k2QMknHltg9uePRTnZHoFddNHiHw+PShQOKDd/3zv//rs6nUe3WH12AeAGMsrkp/94+/V+eA2Xny3//nnffsLI8nSDKuppJpFZRSloirikKTSXLz/9/emYdHWWQL/7xbr+klnXSSTrqzEhISskHISlhCWGQJO4IIXJUoKI4gOKgjMg7OuOsn13EUvXpHnTEOLiziMCA7BggJCSRkD1k73enu9L69a31/wHh1Pr7RKwyLk99f3fW+daqefrrq1Dl16lR5dvH4BI+XBQQMzfv9HEKAEKKDHMteGYssw/v9HMcjAEACCga4YJAXeOT3c/z3V7Ucy/v9HMddKWRoPhjkGZpLz09auTzN3OdLzEnZsGFhgpr0+jkAYFne7aDzp4x9+JHFGjm5eFVR6gjlkDUwfX7hxkcXhdAcywkBPycICAB4XvD7/kf47QIiMSCwuIPOmftMYQH+/+vvFvhNuoSDueMP5OT/hyIEBAEAQBAACQAACIHAXym88r7wP1+vPOWBlHySlgkcCyz3zLjSX43OBo79ZxW/W/2yqkACCPz3xQpXPvxD4fcEXu3ptzJ5/gc8YAICAjuWr6lfGsGJMezm6n2EnD7shTdW7D342J8/qZg22eD2sAAQ8LMuJ8OygiAgj5tBCJggFwjyCCGvm+F5xAQ5t5PxuNnLf1efl3U5mUCQd3vYp15YNqM03uVmOVbwuBiXi0EI6CDncjI0LQCAICCvm7n8PgAEfKzLyfh8nN/Lup0M951RxnOC28W4nQzNCADAMrzbyXg9rNvN/OrVu8smGKwWOjkrMSd7hN8apGkBEHjdjM/DZBSmZ6Tp9am6N3es8A943Q46v2RsUqLW5+O9HtbtYgQBIYS8Hsb1d+E3F5LCYxPlOyvPLSh/sb6u/V/RxE/fBEYIPvzjMYmEvNUsbMbPFS0cd1d5/uu/3R+WFf2rDeXVR2u7LfSM8mx9tORCnYWiMAGoBUvGSIBrvDi0fcfaUDkveL31rc6CickTCuO7W00mGzdzQY6CZLv6vC5bMKtoxKSSxOCQ0+5meQwvm5mZoJe1dvuWLc0x91p9QYHAMQCwmfyp4xJKJyQFXW67i7EMBEpnp2emRdZU9W998Z4RkQok4QeNDmt3fz9H5o0Oa2y0ZOYmjMvS9vYODVzqPm9kn//1nWzAy7FBu5NuqW48WjsYHhdWPjOt4+JAgOaVGtXcBVmsy2tzBW+zyCsMEIVRbiGuwWsbJfdTV1t5BAOVZQsNEhxI6f1Z486117XR/MMxCRkkXufzAUk9rk/Il4lPuZyA48CwD8Uk5Euoao8HMAxwcoshMYPCl0QkVWSlE77AN16XFgnvdlxs5+EJQ1KhTFLldgEGwHEPx8SXKRUXve7AZXuE59SU9Om4JCUbbPH7ylQRqyIjjzqGLot9ypCUQqAGDn4bF1/lHOIAACee1CfmyyRVLidgOHDc2uj4O9SaAZ/bzguRlPgxQ+IoEqvxeB7WJyVhfGMguC0xFYKebk74IQMI7HLKnCKPb/ZiAtysLQGOFeIy49f8x6Sdf9xNS9V3L5/aU19/ps617J7C8jnp7kGrFxdXVBQ3HmvJnJFdmB56+qLz4Q1T3MaBpDHJixaPyR2ja27ssw4yKx4omXlHWqQaO33B99tfzyNxFhivIFMsWz6utDT5+BcXC+dmL12cQ3Le9ksuRIlXrymZNDEpOpT85tTAglWFc+eMjtPJRo5NmjkjxdRudAYEHMfoACuP1Nz/QHFhUbyUEuprBrNKRty9Ij8rU3v8hG3b1nKSFEjwCjh1vvpC3qycMClbd9G17tFSMedXyLGqY+eSi8ZOK0kOM8hqD3SlZsr//ME3Njda+8ikiePjG852+1nsnjUTp01NCSHp9i43Sdz8USaWEDgG2587lZ2niY+Pvr7Cbznn17Xj8zBFWQn2QVOPkbtrVSIAqnyqptLxhopwSxWaKXknlj9b23RovbHXLJs9rtWzR0aAJkKn0yu3PD9palHCkM1xZH/LngO/EHyu8MjwXz/07Jx1D45NiXB42ZUL8ynNlrb+l2PUgkSmWP8o6/CyK5cULp3/GkNIBpqdr366rjhL73AF7rl7Ai7b2m3+jT6UHHL7m0406WOUfpO/KEs7dnJRvNKnNOHTUqXrN9d9vG950+HD4+dlSe0dRl4OiM/MS288Vbvs0Qc6qw4vjijdeO9kq91XPjNj84vH/uv1FT1dpmkT05fftSNUK73Zv/T/GkRiGItyTtkPTIn4x/0AhEAaZpDgDxzYu4OVoIVLk8SyrqlzlYjWhKi0BytXFcyPIWiZTHlXV8PYEyeby1fES3GJWDat/mR5QzO/YpXd47po7hbkUX6aW5aQ+IXJsjW3oGJPy6U7locivzpENbu1duKFNseiO4H2KuTKme1148/VAQZPxaZvK8g3uRwFSmlJgNyQleGk2TUjUyI/qxxcuZ5g3GEhqu0szQh4xciU8M8rOxauDic4uUyx9FJD7tlzveV3h+OcSCxfamxLP151aeEyt9sepdaMOvHVzMwCc1/DEOl5Krdw/+etP8rgFpBLQvQVq+K/dgrimzP7MEFuXN4IFPRuf6a2znLGh15Nz9Hlzp8+LT+utd/92xfTVz/2l9mzx78/58OHviz3trd2BGTzZuf/ak5lHdrU3dQem1wQLoFmWrNydurp2q7oaPXsVToQeI6Q+12+RzbdEyml21p673p8zpJZoxuaB5/YtvJJbJuHe9rZ0yvWRmcla+/faT25ekb9ufap0wr6u/ol2ojRI7TlSz7S60T6kfq33ri3+UJr/OgUnRIbEoW9sHlWw/l2N8aUrcwFnuMJWcDhvXfdcnNDdeyY4iYF24spF8wbv++trx7YurZ+7059Wpxt0BUeqnSO1M+bN6lq75mPv9jADg2qDYZQOThlCRNGhTS2W7URCv6WOc+IE1h2kfqh+z47XZ8WorieA/9n5wJCECBEaSkxHeeafUGhpGCEs6/vBOij1eRvFj3/l13Vo3JTvSISAHo7uu7Iem7mlFTObY2Le+7Rx85nZMT97U97wyOeuffJJbzDvGvfOY7nk0uKx6Zqi4lfdJpcl3r7AMKTY+RvbX79ZKO582L96i07MblcqyWCPnbqmknFWYbp5CONXfaOlvZXP1xkCIXCkVumFbzkTx4lwyFP/7vCaYfSRkY1Hj9vNzp4wLe9VhYmZTeX/3VksrbqeOOE/JTO5naD4bnnDyBDKFlT1fXgqtKDew919A7RDCNXSwGg5XzTzMLtqvDbb/a/AoFJ3FfzhyB+oy4WAN6YswotXHqsvT49bkwY8r/R2MAiZObIVIX4qcNfPHGmLjXasCYuM1VBSD9+u7KlKz1Grwg34MBr9308qfrsGK32v2uPJP9tz8RIPQjMu24hQU4t3//n4wPWUKl8X/6kIVt36F8+YhCcNQ8AjgOP/TIv/0+1h6P3VJbWNmzIyth4oDLp4IGI0EjQ6CJE2Ph9fz7vCh5prd1UUyuXykGmjgmRbju2+6XqmhRdzM6xE0I4l+yPf7Dz8E1Px5TwaBkOui/e7fRysTK5xe8Vi7AnU0fX9zR9Q3M/dkWPwKMgb/SG+Xfwupic7KTO1k5aRWaM18sArG6ibELqC5t+n/7L/aQ8BAIBOsiU7ZirxnmGYVYuKT7518OylXkigPvSX+kyeQBDfT1mjJJQrOfxNYdKRht6W1sL8t/ZfYFL0Ks+/sOuggkf37k4v7mhVRWh7rrY8dG+JTBkNiQ9CzxXX9PyekU65/eUzfswwMPO9/fWtJkBwwCQy0bft25W1/kLaVkfEQCH9jc+cu/EMwePjcl+555VX07OSejtaC/I37Gvi4gKlx//6rzb68E5ckPF9PrjJ9+7qNApYc8n3Ulxmj3vfTqt6MOKBelBt1OZl68PF51vs0pJrLGu02QclKlCfYP9r7xeL5PeQiEYGAZBH2ezOa+v2J+bAkAIhYWSGqW42+rrdYqyMxOaGluffDkL+MCLB4KZ2UkD3QOxduuCO19Pzc9787NlSRmJnW19AKIJ86OilFRTt3l0gjQmUgWkJC5a+fT6/6TUGrfDUwXy1PiI04cbNvxuDAC/+xSTFhd2sW5gztRMl81xtp4WeGF0chjttB/g8YyU6NrT7SNHRLjs3rM9bKhONL04nnZYuwCW3qPXKqi6VrNx0CIPCdu0dupn7+/FFo+SY7Dr5aaRI3UDTc0qtXTt3aOAYzssnJSEsLCwoa7ONas/6j7buuYX70+eN+XFd2a63bfrre64TxgcJb/KVCgIE6OiLfaBp89UA6BJZ45nazU8EBkq1ROHdnWIwgCx262u+fGxRrttbES4ze0GAR8TE9lrtTxniHV5HSDgmepwFYXvMRuBJMuiYvqsxvKYZBDor7xCSnhonbEnOkRh8XoXxaZIMOHQkA0wDMQyBQ6dLicQhEodBgCv2oaeiU8M+JybomJZxtvCi0cpJA1Wy4wond1pKQ+NlGDcK1b7xNg4k30oVqm8ZBmA0FgtCTvMpkXRBo/XBkiRFEKesBqNbnecbsT4GO1j584A9aPvKyew2GYvunnhQA63aFRSeFtz77iJMbs/We2xmo+22igAREkOPlbkGhw8Uu1gEHbvyilvv7knPidDH4pv23jw8cUpPttgY6o+JVZRV9871Nm1dtOfckvyNj6WqE/V2S3O+XMjZhTHiHHUUG9KjFeLACQi0f6dB1cs/TA6WhkMsgvvLwhTS6pr2jJGJ5l6+qeUxUkJqN7XlGmI7GjuUqkojgWlWhLw0VueyxHhqLmtjyRIWYhs0ZIoQ7TYMDLKabbPnRs5K08nxqHm5EBrl71oenGMEnvmqa+e3JgLXMA7OkJGYSYXNr5MkZpqsPT0hMUoADCSZ57ZvKPykKX+69NbX9k7eVbpsjIxzd4qFgAAeFzMHQvi4uN111fsz00BYDjmc7Hnm41LVs5h2d8IrsFtzxwZl5MMhDQYfCVegz/99N73Pt9Q+cEatQxvb+6yWxwpuWPcls0FxemA2IMfdEcZZKfPtmp12pJJOYUFyUePtyg1oX7fMyFiTC7CS/JSfVaLyBAarqQu1l4ozIof7O4jFJRESh79pl2s1vh9z6mluDJEsvuLmtCoKI59ZeuWsn7jkDhUSwdfKp+aBQJ94nPj4ZpBSiYxX2p/cv3piqXZgs+z18o57O6SRfObTq7Kzoz3OaxffmbsMrkz80ZPnZknCZG9+/HG//PiXSKEWhu7bsfwUEwA3C8MTFKeSVFeZXnL82NjDK1Dluc7G/0IO5iTX9neoQpRzUhKKYvR3xMZBRgVWPFgplq8/uypPf094aGR9Kp1YVhw8oW6Un1cq3kAcLyDY/wI9i9Y/UlqemZU9AVTX0VCwqDDAqHRkSLysM3yaXvTuMTRH06a7Pc7v/TTgGFAe85a7E+XLvAtWuGy9Vlpjl26Zk1a8taqQzPjR/YNWSZookU4vDpoyovWnxvoLYsyAJD+u9Zmh8pWV5+sNZvGjsh2zy0HAB2Glejj2geNZfHpAOiVIeduY59WrjrWdOZrWvhR0ag4BhhMrrKpWoI3K0uEwPLZ85JxJJTOnbbjzQeClv4H73un86SpuqHv8RfXFo6KfG5bpZplh7xM05lzL50YFJP4p/+910jjMSMM7c2XFs+OZ4PBlku+V19f+9qzS6xG0779puaG7qwJhW9vv3tkbrJjYOBUIxMbxXx9si0pPXX12rnTyiI/qzyljol57/lFQX8wQiVPSoisP9c2fX6OxzJYeU6t04qrj7XIpIQqjNz7+dlRRePWV5QF/XR0TPhHn1al5+f+8YOHpxepGi90ZUwofmf73fGZic6B/uPVyNbVI5aI3n6pst+BjR9j6O82HT48YLb7H//d/esq8gxxUfUXenftOG5x0GXT8ysqyjLHxL325kNPrJvR19H59SlGRN38DQAAEATUe8lbPCH+pVcrrvu20E8/B8DzaNrkW/IcAAKz0T9rYZqUYD79pCPIwJ4jmx09nV9+dW7X55fCo8QihaxsckJbXc/5VhewaPqCNL/d5Qyg2EjZ4aPdUgnhsATGTEpM0IqPneh2O+nEDH1ECG8eEgSfJzJJh/v9Tb2BCUUxf/3LxeK5oy09/d09fpLAaB8TnRxl0BL9Zhpng909Xk2spmiMrvpIW7+Vzps8QiPi++2sPkxy9EQPT6MZ80ed+KqJwfFRGdGhCuKbk32MgM+cldLVZgzRqCk6UNNgdXnR/EVp9n5b3flBjSGscKyu9nh73xCjuPUil/85eBAFtUTDlPAejQiuGsUkCFt0sQ0O064AU6GJVOHCyxbTDJW2OES+ZaCncdqdvG/wo7aWlywmIEUg8Hly1fQQ+TZTPxDkLyNiWj323cEgIBQvlj2g0bxmGXgoIuZrmzldoWEYXw3NLdBoXrNaHo3SbbXbjVPmtvVcmHzhApAkAADLbogyMIz/9y4HAPFrXcwHlv5LLL8pIsYUcDfzMFet3DpgfDwm4a2BzpNT7yT95nfb2l829wMlBp67Pzza6HeKKdk3Ptd94bomt40jxGkSySVBuG9EZlFEiGrXThD/kMsOAyCwJAs9+msb5RJulvf/MgInmAdogsI5hiPFRLhWTBDY0GCA5TGOFSKiJSIKt1tojMJUStJiYhQaSi4j/C6WBQiRE3Ybq9KQ1gGaEuMsJ0TFSL0O2hcAUgTqENLtE8K0IgzAYQsGaMABNBEiTBDMZpYkQBAgLFJMezlSglMYuLxCeITIagyqIkRiCgeAgI+x23kMCQjDI3QSLsjahngRham1oqCb8QWAFGGqEMLtE8K1IjrA2W1sqFYklRBBH+enhVAVaTYGcYqQhuCIFkgZKRVjpv4gJSYIEhQKwmZhCQLjAemipbfCxOb1sAyNnn1hfvH4zH+F/J+jAgAAAJrmAUBE4X6BePu/Htz1+/d2n3CrlSSGARIQTfMkRZAkBgB0kMdxDMeB49G3K2uW4XkBLp/M4lhBQIDjgOOYwCMEQJIYTQtiCcHSPE7i34YK8JzAC1fexHGM5wWWQSIJgWPAMryAgCAw/u+t0EGeEhM4BhwnCAKIRDhCiKZ5kiQAIQRAUThCiKF5nMApChd4gWEESkQQt0Bkwo8H4xAwyFiqOj1KBcI/PT8iCIBhgGGABIC/f0AAArp4x+I9tX97wuoG4u+mD0KAEOD49yp+t/xyISAADDAAnp+qivxoYikG0DfYO/bMSSCoqzQNCAT0vW5clonjIPCAiMbp8z45d2SbZeiK8vi2LiDAvtMoLjHdMTfgdy48eqAO/dBhNBwDTiitcYSd8fIK4mdnlg/zU+A4oeW855EnCu5bPUss/tH+w/8l15QKYmLR02Hh0ltTAXwLQshhpeVqkVg0PLBuLAgIv+AZIa4uCbPLr21Xk2MBJ681YQ4SgOMAADAMyJ86on5sTxCwLAAASf2z2R8DIPCsHm/i1w6cRTfR7z/MrQNCMGQNjhgZ+tTWJQmJ1znu8x+4Jk9Cbl50e+sQdWtPrBiGaSIkN7sX/3ZgLBIoaJ+pqU8MAQFda0zLT56vv9cnHCjRtQr5sT3BfrgtAlP5+fwqq7IlKMjw4dl/GAAIBji7jdm8Zdq8+SU3oLlrsgCqqho2PPiJPk5+HTs0zG0PAjwgONOlpwo1PvENz/9zW4ABYNjYS564w07s8knpYf7tQQgZe/0z5iSv+8VcrVZ9Yxq9JgWAEDy6/q0LdWZ16I3IXDrMrQ4CnEG8FGudGtYUIwV++DKAq0FiUR5uzBGbrJcRpMMZQIcBhJDXzTqGuD+8vzw3N/VGNn2tN4IxDPfG9i/+83c14Xpy+Eawf2d4FvkoaJ2qhLEawGH4OrCrgBAIBJy1Fu5w8wk4ujWiDIe5ubCMQJHkQxuL7lo+laJudHj3tSqAy/C8YOy3YbdXdpphriMIicUieXiI0e9Dws3PonVrEi0LUVCU0+51e/3Dq6VhAK4MnIjIG+Tw+X+5PgpgmGGGGWaY245bOoBnmGGGGWaYfx3DCmCYYYYZ5t+U/wtR1F8mQ4wAAAACSURBVPYi6326ZQAAAABJRU5ErkJggg==" alt="" />

                            </div>
                            <div className='m-3 para'>
                                <label className="text-start">Image Source: USEPA</label>
                            </div>
                            <div className='m-3 para'>

                                {selector.information.companyname}  is reporting the GHG emissions for the
                            </div>
                            <div className='m-3 para'>

                            </div>
                            <div className='m-3 para'>
                                With the established operational boundary, {selector.information.companyname} has identified the GHG inventory.
                            </div>
                            <div>
                                {checkScope(dcf_id) &&
                                    <ul>
                                        {checkScope([11, 310]) &&
                                            <li >  Scope 1: Stationary Combustion Emission </li>
                                        }
                                        {checkScope([10]) &&
                                            <li >  Scope 1: Fugitive Emissions </li>
                                        }
                                        {checkScope([15, 311]) &&
                                            <li >  Scope 1: Mobile Combustion Emissions</li>
                                        }
                                        {checkScope([257]) &&
                                            <li >  Scope 2: Emissions due to Purchased Electricity </li>
                                        }
                                        {checkScope([36]) &&
                                            <li >  Scope 3: Business Travel </li>
                                        }
                                        {checkScope([292]) &&
                                            <li >  Upstream Transportation and Distribution</li>
                                        }
                                        {checkScope([293]) &&
                                            <li > Downstream Transportation and Distribution </li>
                                        }
                                        {checkScope([16]) &&
                                            <li >  Scope 3: Purchased Goods & Services </li>
                                        }
                                    </ul>}




                            </div>
                            <div class="gridlines-container">
                                <table class="gridlines">
                                    <tbody>
                                        <tr>
                                            <td colspan="1" rowspan="1" className="tdHead">Scope
                                            </td>

                                            <td colspan="1" rowspan="1" className="tdHead"> Category
                                            </td>

                                            <td colspan="1" rowspan="1" className="tdHead">Description
                                            </td>

                                            <td colspan="1" rowspan="1" className="tdHead">Emissions Sources
                                            </td>

                                            <td colspan="1" rowspan="1" className="tdHead">Generated GHGs
                                            </td>
                                        </tr>
                                        {checkScope([10, 11, 15, 310, 311]) && <>
                                            {checkScope([15, 311]) &&
                                                <tr>
                                                    <td colspan="1" rowspan="1"> Scope 1</td>
                                                    <td colspan="1" rowspan="1"> Mobile Combustion</td>
                                                    <td colspan="1" rowspan="1"> Company-owned or leased vehicles</td>
                                                    <td colspan="1" rowspan="1"> {getMCFuelUsed()}</td>
                                                    <td colspan="1" rowspan="1"> CO2, CH4, N2O</td>
                                                </tr>
                                            }
                                            {checkScope([11, 310]) &&
                                                <tr>
                                                    <td colspan="1" rowspan="1"> Scope 1</td>
                                                    <td colspan="1" rowspan="1"> Stationary Combustion</td>
                                                    <td colspan="1" rowspan="1">Use of fuel and gas in the facility: fuel used for Diesel Generator (DG)s</td>
                                                    <td colspan="1" rowspan="1">{getSCFuelUsed()}</td>
                                                    <td colspan="1" rowspan="1"> CO2, CH4, N2O</td>
                                                </tr>
                                            }
                                            {checkScope([10]) &&
                                                <tr>
                                                    <td colspan="1" rowspan="1"> Scope 1</td>
                                                    <td colspan="1" rowspan="1"> Fugitive Emissions </td>
                                                    <td colspan="1" rowspan="1"> Refrigerants used in Air Conditioning Systems</td>
                                                    <td colspan="1" rowspan="1">Refrigerants </td>
                                                    <td colspan="1" rowspan="1">{getFEGasUsed()} </td>
                                                </tr>
                                            }
                                        </>}
                                        {checkScope([257]) &&
                                            <tr>
                                                <td colspan="1" rowspan="1"> Scope 2</td>
                                                <td colspan="1" rowspan="1"> Purchased Energy</td>
                                                <td colspan="1" rowspan="1"> Purchased electricity for operations</td>
                                                <td colspan="1" rowspan="1">Electricity </td>
                                                <td colspan="1" rowspan="1">CO2, CH4, N2O </td>
                                            </tr>
                                        }
                                    </tbody>
                                </table>
                            </div>
                            <sectionheader id={"calc"} className="secHead">
                                Calculating GHG Emissions
                            </sectionheader>
                            <div className='m-3 para'>

                                As the Operational boundaries have been defined and emission sources have been identified in the Scope 1 and Scope 2 categories, {selector.information.companyname} have calculated the GHG emissions of the defined locations by following the below mentioned steps.


                            </div>
                            <div className='m-3 para'>
                                <ol>
                                    <li> Select a GHG calculation approach </li>
                                    <li> Collect activity data and choose emission factors </li>
                                    <li>Apply Calculation tools </li>
                                    <li> Roll-up the GHG emissions data  </li>
                                </ol>
                            </div>
                            <sectionheader1 id={'cala'}>
                                <span className="text-under">Calculation Approach</span>

                                <div className='m-3 para'>

                                    Definition: Two calculation methods are accepted for the purpose of calculating GHG emissions. Direct Monitoring and Measurement requires the use of scientific equipment at the point of discharge. Emissions Factors applies a multiplier to the amount of resources consumed.

                                </div>

                            </sectionheader1>
                            <sectionheader1 id={'activitydata'}>
                                <span className="text-under">Activity Data, Emission factor and Methodology per emission type</span>

                                <div className='m-3 para'>

                                    {selector.information.companyname}  has made all efforts possible to collect the primary activity data for each source by/through spend reports, complies the data to obtain accurate activity data to calculate emission.

                                </div>
                                <div class="gridlines-container">
                                    <table class="gridlines">
                                        <tbody>
                                            <tr>
                                                <td colSpan="1" rowSpan="1" className="tdHead">Emission Scope </td>
                                                <td colSpan="1" rowSpan="1" className="tdHead">Activity Data </td>
                                                <td colSpan="1" rowSpan="1" className="tdHead">Source of Data </td>

                                            </tr>
                                            {checkScope([11, 310]) &&
                                                <tr>
                                                    <td colSpan="1" rowSpan="1">Scope 1: Stationary Combustion Emission </td>
                                                    <td colSpan="1" rowSpan="1"> </td>
                                                    <td colSpan="1" rowSpan="1"> </td>

                                                </tr>

                                            }
                                            {checkScope([15, 311]) &&
                                                <tr>
                                                    <td colSpan="1" rowSpan="1">Scope 1: Mobile Combustion Emission </td>
                                                    <td colSpan="1" rowSpan="1"> </td>
                                                    <td colSpan="1" rowSpan="1"> </td>

                                                </tr>
                                            }
                                            {checkScope([10]) &&

                                                <tr>
                                                    <td colSpan="1" rowSpan="1">Scope 1: Fugitive Emission </td>
                                                    <td colSpan="1" rowSpan="1"> </td>
                                                    <td colSpan="1" rowSpan="1"> </td>

                                                </tr>
                                            }
                                            {checkScope([257]) &&
                                                <tr>
                                                    <td colSpan="1" rowSpan="1">Scope 2: Energy Purchased </td>
                                                    <td colSpan="1" rowSpan="1"> </td>
                                                    <td colSpan="1" rowSpan="1"> </td>

                                                </tr>
                                            }
                                            {checkScope([16]) &&
                                                <tr>
                                                    <td colSpan="1" rowSpan="1">Scope 3: Purchase Goods and Services </td>
                                                    <td colSpan="1" rowSpan="1"> </td>
                                                    <td colSpan="1" rowSpan="1"> </td>

                                                </tr>
                                            }
                                            {checkScope([292]) &&
                                                <tr>
                                                    <td colSpan="1" rowSpan="1">Scope 3: Upstream Transportation and Distribution </td>
                                                    <td colSpan="1" rowSpan="1"> </td>
                                                    <td colSpan="1" rowSpan="1"> </td>

                                                </tr>
                                            }
                                            {checkScope([293]) &&
                                                <tr>
                                                    <td colSpan="1" rowSpan="1">Scope 3: Downstream Transportation and Distribution </td>
                                                    <td colSpan="1" rowSpan="1"> </td>
                                                    <td colSpan="1" rowSpan="1"> </td>

                                                </tr>
                                            }
                                            {checkScope([36]) &&
                                                <tr>
                                                    <td colSpan="1" rowSpan="1">Scope 3: Business Travel </td>
                                                    <td colSpan="1" rowSpan="1"> </td>
                                                    <td colSpan="1" rowSpan="1"> </td>

                                                </tr>
                                            }
                                        </tbody>
                                    </table>
                                </div>
                                <div class="gridlines-container">
                                    <table class="gridlines">
                                        <tbody>
                                            <tr>
                                                <td colSpan="1" rowSpan="2" className="tdHead">Scope</td>
                                                <td colSpan="1" rowSpan="2" className="tdHead">Source of Emission</td>
                                                <td colSpan="4" rowSpan="1" className="tdHead">Emission Factor</td>

                                            </tr>

                                            <tr>
                                                <td className="tdHead">CO2e</td>
                                                <td className="tdHead">CO2</td>
                                                <td className="tdHead">CH4</td>
                                                <td className="tdHead">N20</td>

                                            </tr>
                                            {checkScope([11, 310]) &&
                                                <>

                                                    {removeDuplicatesByProperties(getScopeDataFromResponse([11, 310]), ['fuel_type']).map((i, j, arr) => {
                                                        return (
                                                            <tr>
                                                                {j === 0 && <td colspan="1" rowspan={arr.length}>Scope 1 Stationary Combustion Emission</td>}
                                                                <td colspan="1" rowspan="1"> {i.fuel_type}</td>
                                                                <td colspan="1" rowspan="1"> {i.ef.co2e}</td>
                                                                <td colspan="1" rowspan="1"> {i.ef.co2 || '-'}</td>
                                                                <td colspan="1" rowspan="1"> {i.ef.ch4 || '-'}</td>
                                                                <td colspan="1" rowspan="1"> {i.ef.n2o || '-'}</td>
                                                            </tr>
                                                        )
                                                    })

                                                    }
                                                </>
                                            }
                                            {checkScope([15, 311]) &&
                                                <>

                                                    {removeDuplicatesByProperties(getScopeDataFromResponse([15, 311]), ['fuel_type']).map((i, j, arr) => {
                                                        return (
                                                            <tr>
                                                                {j === 0 && <td colspan="1" rowspan={arr.length}>Scope 1 Mobile Combustion Emission</td>}
                                                                <td colspan="1" rowspan="1"> {i.fuel_type}</td>
                                                                <td colspan="1" rowspan="1"> {i.ef.co2e}</td>
                                                                <td colspan="1" rowspan="1"> {i.ef?.co2 || '-'}</td>
                                                                <td colspan="1" rowspan="1"> {i.ef?.ch4 || '-'}</td>
                                                                <td colspan="1" rowspan="1"> {i.ef?.n2o || '-'}</td>
                                                            </tr>
                                                        )
                                                    })

                                                    }
                                                </>
                                            }
                                            {checkScope([10]) &&
                                                <>

                                                    {getScopeDataFromResponse([10]).map((i, j) => {
                                                        return (
                                                            <tr>
                                                                {j === 0 && <td colspan="1" rowspan={getScopeDataFromResponse([10]).length}> Scope 1 Fugitive Emission</td>}
                                                                <td colspan="1" rowspan="1"> {i.gas_type}</td>
                                                                <td colspan="3" rowspan="1"> {i.ghg}</td>
                                                            </tr>
                                                        )
                                                    })

                                                    }
                                                </>
                                            }
                                        </tbody>
                                    </table>
                                </div>
                                <div class="gridlines-container">
                                    <table class="gridlines">
                                        <tbody>
                                            <tr>
                                                <td colSpan="1" rowSpan="1" className="tdHead">Activity </td>
                                                <td colSpan="2" rowSpan="1" className="tdHead">Emission Factor References </td>

                                            </tr>

                                            <tr>
                                                <td colSpan="1" rowSpan="1">Fuel </td>
                                                <td colSpan="1" rowSpan="1">Diesel </td>
                                                <td colSpan="1" rowSpan="1">DEFRA {year - 1} Emission Factor</td>


                                            </tr>



                                            <tr>
                                                <td colSpan="1" rowSpan="1">Mobile Combustion </td>
                                                <td colSpan="2" rowSpan="1">DEFRA {year - 1} Emission Factor </td>
                                            </tr>


                                            <tr>
                                                <td colSpan="1" rowSpan="1">Electricity </td>
                                                <td colSpan="1" rowSpan="1">Singapore </td>
                                                <td colSpan="1" rowSpan="1"> EMA Singapore emission factors ({year - 1}) </td>
                                            </tr>
                                            <tr>
                                                <td colSpan="1" rowSpan="1">Electricity </td>
                                                <td colSpan="1" rowSpan="1">Thailand </td>
                                                <td colSpan="1" rowSpan="1">  Energy Policy and Planning Office (EPPO) Thai Government Ministry of Energy ({year - 1}) </td>
                                            </tr>

                                            <tr>
                                                <td colSpan="1" rowSpan="1">Electricity </td>
                                                <td colSpan="1" rowSpan="1">Malaysia  </td>
                                                <td colSpan="1" rowSpan="1"> Malaysia Grid emission factors ({year - 1}) </td>
                                            </tr>


                                        </tbody>
                                    </table>
                                </div>
                                <div class="gridlines-container">
                                    <table class="gridlines">
                                        <tbody>
                                            <tr>
                                                <td colSpan="1" rowSpan="1" className="tdHead">Scope</td>
                                                <td colSpan="1" rowSpan="1" className="tdHead">Category</td>
                                                <td colSpan="1" rowSpan="1" className="tdHead">Activity Data</td>
                                                <td colSpan="1" rowSpan="1" className="tdHead">Emission Factors</td>
                                                <td colSpan="1" rowSpan="1" className="tdHead">Methodology</td>
                                            </tr>


                                            <tr>
                                                <td colSpan="1" rowSpan="2">Scope 1</td>
                                                <td colSpan="1" rowSpan="1">Stationary Combustion- Diesel used</td>
                                                <td colSpan="1" rowSpan="1">Fuel Volume</td>
                                                <td colSpan="1" rowSpan="1">DEFRA [{year - 1}]</td>
                                                <td colSpan="1" rowSpan="1">	Fuel volume in KG x Emission Factors in kg CO2e/liter</td>
                                            </tr>



                                            <tr>
                                                <td colSpan="1" rowSpan="1">Fugitive Emissions </td>
                                                <td colSpan="1" rowSpan="1">Refilled Refrigerant Volume</td>
                                                <td colSpan="1" rowSpan="1">DEFRA [{year - 1}]</td>
                                                <td colSpan="1" rowSpan="1">Refrigerant quantity refilled in Kg  x Emission Factors in kg CO2e/kg</td>
                                            </tr>
                                            <tr>
                                                <td colSpan="1" rowSpan="1">Mobile Combustion </td>
                                                <td colSpan="1" rowSpan="1">Refilled Refrigerant Volume</td>
                                                <td colSpan="1" rowSpan="1">DEFRA [{year - 1}]</td>
                                                <td colSpan="1" rowSpan="1">Refrigerant quantity refilled in Kg  x Emission Factors in kg CO2e/kg</td>
                                            </tr>

                                            <tr>
                                                <td colSpan="1" rowSpan="1">Scope 2 </td>
                                                <td colSpan="1" rowSpan="1">Electricity</td>
                                                <td colSpan="1" rowSpan="1">Electricity Consumption</td>
                                                <td colSpan="1" rowSpan="1">EMA Singapore emission factors ({year - 1}) [{year - 1}]</td>

                                                <td colSpan="1" rowSpan="1">   Purchased electricity in kWh x Emission Factor  in tCO2/MWh</td>
                                            </tr>
                                            <tr>
                                                <td colSpan="1" rowSpan="1">Scope 2 </td>
                                                <td colSpan="1" rowSpan="1">Electricity</td>
                                                <td colSpan="1" rowSpan="1">Electricity Consumption</td>
                                                <td colSpan="1" rowSpan="1">Energy Policy and Planning Office (EPPO) Thai Government Ministry of Energy [{year - 1}]</td>

                                                <td colSpan="1" rowSpan="1">   Purchased electricity in kWh x Emission Factor  in tCO2/MWh</td>
                                            </tr>
                                            <tr>
                                                <td colSpan="1" rowSpan="1">Scope 2 </td>
                                                <td colSpan="1" rowSpan="1">Electricity</td>
                                                <td colSpan="1" rowSpan="1">Electricity Consumption</td>
                                                <td colSpan="1" rowSpan="1">Malaysia Grid emission factors  [{year - 1}]</td>

                                                <td colSpan="1" rowSpan="1">   Purchased electricity in kWh x Emission Factor  in tCO2/MWh</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </sectionheader1>
                            <sectionheader>
                                Result
                            </sectionheader>
                            <sectionheader1 id={'gross'}>
                                <span className="para">Gross Emissions</span>

                                <div className='m-3 para'>

                                    The total GHG emissions of {selector.information.companyname} accounted for {showSite()} plants of the reporting period of FY {year}-{(year + 1).toString().substr(2, 3)} against each category/scope are as listed in the following table.

                                </div>

                                <span className="para">Emissions by Scope</span>
                                <div className='m-3 para'>

                                    The total GHG emissions per scope covering the sites included in the report for the reporting period of FY {year}-{(year + 1).toString().substr(2, 3)} are included in the following table


                                </div>


                                <span className="para">Emissions by Category</span>
                                <div className='m-3 para'>

                                    The total GHG emissions categorized as per the Emission sources identified for {locationList.length} sites included as part of the inventory for FY {year}-{(year + 1).toString().substr(2, 3)} are reported as follows.

                                </div>

                            </sectionheader1>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

const comparisonFn = function (prevProps, nextProps) {
    return prevProps.location.pathname === nextProps.location.pathname;
};

export default React.memo(CarbonFootPrintingNew, comparisonFn);
