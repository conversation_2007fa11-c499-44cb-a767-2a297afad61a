/**
 * Utility functions for safely handling Chart.js operations
 */

/**
 * Safely destroys a Chart.js instance
 * @param {Object} chartInstance - The Chart.js instance to destroy
 * @returns {boolean} - Whether the operation was successful
 */
export const safelyDestroyChart = (chartInstance) => {
  if (!chartInstance) return false;
  
  try {
    // Check if destroy is a function before calling it
    if (typeof chartInstance.destroy === 'function') {
      chartInstance.destroy();
      return true;
    } else {
      console.warn('Chart instance does not have a destroy method');
      return false;
    }
  } catch (error) {
    console.warn('Error destroying chart:', error);
    return false;
  }
};

/**
 * Safely gets a Chart.js instance from a canvas element
 * @param {HTMLCanvasElement} canvas - The canvas element
 * @returns {Object|null} - The Chart.js instance or null
 */
export const safelyGetChartInstance = (canvas) => {
  if (!canvas) return null;
  
  try {
    // Check if Chart is available globally
    if (typeof Chart !== 'undefined' && typeof Chart.getChart === 'function') {
      return Chart.getChart(canvas);
    }
    return null;
  } catch (error) {
    console.warn('Error getting chart instance:', error);
    return null;
  }
};

/**
 * Safely cleans up a Chart.js instance from a canvas ref
 * @param {React.RefObject} canvasRef - React ref to the canvas element
 */
export const safelyCleanupChart = (canvasRef) => {
  if (!canvasRef || !canvasRef.current) return;
  
  try {
    const chartInstance = safelyGetChartInstance(canvasRef.current);
    if (chartInstance) {
      safelyDestroyChart(chartInstance);
    }
  } catch (error) {
    console.warn('Error cleaning up chart:', error);
  }
};
