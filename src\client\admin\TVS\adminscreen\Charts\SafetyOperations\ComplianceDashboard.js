import React from "react";
import { CheckCircle, Cancel } from "@mui/icons-material";
import { Grid, Typography, Box } from "@mui/material";

const ComplianceDashboard = () => {
  // Static data for compliance status
  const sites = [
    { name: "Site A", compliant: true },
    { name: "Site B", compliant: false },
    { name: "Site C", compliant: true },
    { name: "Site D", compliant: false },
    { name: "Site E", compliant: true },
  ];

  return (
    <div style={{ fontFamily: "Lato" }}>
      <h3 style={{ fontSize: "18px" }}>Compliance Dashboard</h3>

      <div>
        A dashboard showing the compliance status for each site. Green
        checkmarks indicate compliance, and red cross marks indicate
        non-compliance.
      </div>

      {/* Grid Layout for displaying sites */}
      <Grid container spacing={3}>
        {sites.map((site, index) => (
          <Grid item xs={12} md={4} key={index}>
            <Box
              sx={{
                border: "1px solid #ddd",
                borderRadius: "8px",
                padding: "16px",
                display: "flex",
                alignItems: "center",
                justifyContent: "space-between",
              }}
            >
              <Typography variant="h6">{site.name}</Typography>
              {/* Compliance Checkmark */}
              {site.compliant ? (
                <CheckCircle sx={{ color: "green", fontSize: 30 }} />
              ) : (
                <Cancel sx={{ color: "red", fontSize: 30 }} />
              )}
            </Box>
          </Grid>
        ))}
      </Grid>
    </div>
  );
};

export default ComplianceDashboard;
