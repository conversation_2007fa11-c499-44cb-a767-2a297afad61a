import { useState, useRef, useEffect, useMemo, useCallback } from "react";
import {
  <PERSON><PERSON><PERSON>,
  Line,
  XAxis,
  <PERSON>A<PERSON>s,
  CartesianGrid,
  <PERSON><PERSON><PERSON>,
  Legend,
  ResponsiveContainer,
} from "recharts";
import { Button } from "primereact/button";
import { Card } from "primereact/card";
import { Menu } from "primereact/menu";
import { DataTable } from "primereact/datatable";
import { Column } from "primereact/column";
import { Calendar } from "primereact/calendar";
import { ChartSortDropdown, createSortOptions, sortData } from "./components/ChartSortDropdown";
import APIServices from "../../../../../service/APIService";
import { API } from "../../../../../constants/api_url";
// Default data if no supplyData is provided
const defaultData = [
  { month: "Apr", selfAssessment: null, calibrationScore: null, calibrationSupplierCount: 0, selfAssessmentSupplierCount: 0, totalSupplierCount: 0 },
  { month: "May", selfAssessment: null, calibrationScore: null, calibrationSupplierCount: 0, selfAssessmentSupplierCount: 0, totalSupplierCount: 0 },
  { month: "Jun", selfAssessment: null, calibrationScore: null, calibrationSupplierCount: 0, selfAssessmentSupplierCount: 0, totalSupplierCount: 0 },
  { month: "Jul", selfAssessment: null, calibrationScore: null, calibrationSupplierCount: 0, selfAssessmentSupplierCount: 0, totalSupplierCount: 0 },
  { month: "Aug", selfAssessment: null, calibrationScore: null, calibrationSupplierCount: 0, selfAssessmentSupplierCount: 0, totalSupplierCount: 0 },
  { month: "Sep", selfAssessment: null, calibrationScore: null, calibrationSupplierCount: 0, selfAssessmentSupplierCount: 0, totalSupplierCount: 0 },
  { month: "Oct", selfAssessment: null, calibrationScore: null, calibrationSupplierCount: 0, selfAssessmentSupplierCount: 0, totalSupplierCount: 0 },
  { month: "Nov", selfAssessment: null, calibrationScore: null, calibrationSupplierCount: 0, selfAssessmentSupplierCount: 0, totalSupplierCount: 0 },
  { month: "Dec", selfAssessment: null, calibrationScore: null, calibrationSupplierCount: 0, selfAssessmentSupplierCount: 0, totalSupplierCount: 0 },
  { month: "Jan", selfAssessment: null, calibrationScore: null, calibrationSupplierCount: 0, selfAssessmentSupplierCount: 0, totalSupplierCount: 0 },
  { month: "Feb", selfAssessment: null, calibrationScore: null, calibrationSupplierCount: 0, selfAssessmentSupplierCount: 0, totalSupplierCount: 0 },
  { month: "Mar", selfAssessment: null, calibrationScore: null, calibrationSupplierCount: 0, selfAssessmentSupplierCount: 0, totalSupplierCount: 0 },
];

const CustomLegend = (props) => {
  const { payload } = props;
  return (
    <ul
      style={{
        display: "flex",
        listStyleType: "none",
        justifyContent: "center",
        padding: "10px",
        margin: "10px 0",
        backgroundColor: "#f8f9fa",
        borderRadius: "5px",
        boxShadow: "0 1px 3px rgba(0,0,0,0.1)",
      }}
    >
      {payload.map((entry, index) => (
        <li
          key={`item-${index}`}
          style={{
            display: "flex",
            alignItems: "center",
            marginRight: "20px",
            padding: "5px 10px",
          }}
        >
          <span
            style={{
              backgroundColor: entry.color,
              marginRight: "8px",
              width: "15px",
              height: "15px",
              borderRadius: "3px",
              display: "inline-block",
            }}
          ></span>
          <span style={{ color: "#333", fontSize: "14px", fontWeight: "500" }}>
            {entry.dataKey === "selfAssessment"
              ? "Self Assessment Score"
              : "MSI Calibration Score"}
          </span>
        </li>
      ))}
    </ul>
  );
};

const ThirdLineDemo = ({ supplyData = [] }) => {

  const [activeMode, setActiveMode] = useState(true);
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const menuRef = useRef(null);
  const tableRef = useRef(null);
  const chartRef = useRef(null);
  const [visibleSeries, setVisibleSeries] = useState({
    selfAssessment: true,
    calibrationScore: true,
  });

  // State for chart data
  const [chartData, setChartData] = useState(defaultData);
  const [filteredChartData, setFilteredChartData] = useState(defaultData);
  const [selfAssessmentData, setSelfAssessmentData] = useState([]);

  // Date filter state
  const [dateFilter, setDateFilter] = useState({
    start: null,
    end: null
  });

  // Sorting state
  const [sortField, setSortField] = useState('none');

  // Memoize sorting options to prevent re-creation
  const sortOptions = useMemo(() => createSortOptions([
    { name: 'month', label: 'Month', type: 'string' },
    { name: 'selfAssessment', label: 'Self Assessment', type: 'number' },
    { name: 'calibrationScore', label: 'Calibration Score', type: 'number' },
  ]), []);

  // Handle sort change - memoized to prevent re-creation
  const handleSortChange = useCallback((e) => {
    const selectedSort = e.value;

    setSortField(selectedSort);

    // Apply sorting to the data
    setChartData(prevData => sortData(prevData, selectedSort));
  }, []);

  // Filter data based on date range
  const filterDataByDate = useCallback((data) => {
    if (!dateFilter.start && !dateFilter.end) {
      return data;
    }

    return data.filter(item => {
      if (!item.monthName || !item.year) return true;

      // Create a date object for the first day of the month
      const months = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"];
      const monthIndex = months.indexOf(item.monthName);
      if (monthIndex === -1) return true;

      const itemDate = new Date(item.year, monthIndex, 1);

      let isInRange = true;
      if (dateFilter.start) {
        const startDate = new Date(dateFilter.start.getFullYear(), dateFilter.start.getMonth(), 1);
        isInRange = isInRange && itemDate >= startDate;
      }
      if (dateFilter.end) {
        const endDate = new Date(dateFilter.end.getFullYear(), dateFilter.end.getMonth(), 1);
        isInRange = isInRange && itemDate <= endDate;
      }

      return isInRange;
    });
  }, [dateFilter]);

  // Memoize Line component props to prevent re-creation
  const lineProps = useMemo(() => ({
    selfAssessment: {
      dataKey: "selfAssessment",
      stroke: "#F59E0B",
      strokeWidth: 2,
      name: "Self Assessment Score",
      type: "monotone"
    },
    calibrationScore: {
      dataKey: "calibrationScore",
      stroke: "#6C480B",
      strokeWidth: 2,
      name: "MSI Calibration Score",
      type: "monotone"
    }
  }), []);

  // Function to process self-assessment data and get latest scores by vendor and month-year
  const processSelfAssessmentData = useCallback((assessmentData) => {
    const processedData = {};

    assessmentData.forEach(item => {
      if (item.created_on && item.vendor && item.vendor.code && item.supplierMSIScore) {
        // Parse the created_on date (format: "2025-01-30T03:26:22.125Z")
        const createdDate = new Date(item.created_on);
        const year = createdDate.getFullYear();
        const monthIndex = createdDate.getMonth();
        const months = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"];
        const monthName = months[monthIndex];
        const monthYearKey = `${monthName} ${year}`;

        const vendorCode = item.vendor.code;
        const score = parseFloat(item.supplierMSIScore);

        // Create unique key for vendor-month-year combination
        const key = `${vendorCode}-${monthYearKey}`;

        if (!processedData[key] || new Date(item.created_on) > new Date(processedData[key].created_on)) {
          processedData[key] = {
            vendorCode,
            monthYearKey,
            monthName,
            year,
            score,
            created_on: item.created_on
          };
        }
      }
    });

    return Object.values(processedData);
  }, []);

  // Fetch self-assessment data from API
  useEffect(() => {
    const fetchSelfAssessmentData = async () => {
      try {
        const uriString = {
          "include": [
            {
              "relation": "vendor"
            }
          ]
        };

        const response = await APIServices.get(
          API.SupplierAssessmentAss_Up_All + `?filter=${encodeURIComponent(JSON.stringify(uriString))}`
        );

        

        // Store the self-assessment data
        if (response.data && Array.isArray(response.data)) {
          setSelfAssessmentData(response.data);
        }

      } catch (error) {
        console.error('Error fetching self-assessment data:', error);
      }
    };

    fetchSelfAssessmentData();
  }, []); // Empty dependency array means this runs once on component mount

  // Process supplyData and self-assessment data to generate chart data
  useEffect(() => {
  
    if (supplyData && supplyData.length > 0) {

      
      // Process the supplyData to extract monthly data
      try {
        // Group data by month-year and calculate average scores
        const monthlyData = {};
        const months = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"];

        // Process self-assessment data first
        const processedSelfAssessment = processSelfAssessmentData(selfAssessmentData);
     

        // Create a map of vendor codes from supplyData for filtering
        const supplierVendorCodes = new Set(supplyData.map(supplier => supplier.vendor_code?.toString()));

        // Process each supplier data for calibration scores
        supplyData.forEach(supplier => {
          // Extract month from audit_start_date if available
          if (supplier.audit_start_date) {
            // Parse the date format "24.08.2024" (DD.MM.YYYY)
            const dateParts = supplier.audit_start_date.split('.');
            if (dateParts.length === 3) {
              const day = parseInt(dateParts[0]);
              const monthIndex = parseInt(dateParts[1]) - 1; // Month is 0-indexed in JavaScript
              const year = parseInt(dateParts[2]);

              const date = new Date(year, monthIndex, day);
              const monthName = months[date.getMonth()];
              const monthYearKey = `${monthName} ${year}`;

              // Initialize monthly data if not exists
              if (!monthlyData[monthYearKey]) {
                monthlyData[monthYearKey] = {
                  month: monthYearKey,
                  monthName: monthName,
                  year: year,
                  selfAssessment: null,
                  calibrationScore: null,
                  count: 0,
                  selfAssessmentCount: 0,
                  selfAssessmentTotal: 0,
                  calibrationSupplierCount: 0,
                  selfAssessmentSupplierCount: 0,
                  calibrationSuppliers: new Set(),
                  selfAssessmentSuppliers: new Set()
                };
              }

              // Add calibration score if available
              if (supplier.msi_score) {
                if (!monthlyData[monthYearKey].calibrationScore) {
                  monthlyData[monthYearKey].calibrationScore = 0;
                }
                monthlyData[monthYearKey].calibrationScore += parseFloat(supplier.msi_score);
                monthlyData[monthYearKey].count++;
                // Track unique suppliers for calibration
                if (supplier.vendor_code) {
                  monthlyData[monthYearKey].calibrationSuppliers.add(supplier.vendor_code);
                }
              }
            }
          }
        });

        // Process self-assessment data and add to monthly data
        processedSelfAssessment.forEach(assessment => {
          // Only include assessments for vendors that exist in supplyData
          if (supplierVendorCodes.has(assessment.vendorCode)) {
            const monthYearKey = assessment.monthYearKey;

            // Initialize monthly data if not exists
            if (!monthlyData[monthYearKey]) {
              monthlyData[monthYearKey] = {
                month: monthYearKey,
                monthName: assessment.monthName,
                year: assessment.year,
                selfAssessment: null,
                calibrationScore: null,
                count: 0,
                selfAssessmentCount: 0,
                selfAssessmentTotal: 0,
                calibrationSupplierCount: 0,
                selfAssessmentSupplierCount: 0,
                calibrationSuppliers: new Set(),
                selfAssessmentSuppliers: new Set()
              };
            }

            // Add self-assessment score
            monthlyData[monthYearKey].selfAssessmentTotal += assessment.score;
            monthlyData[monthYearKey].selfAssessmentCount++;
            // Track unique suppliers for self-assessment
            monthlyData[monthYearKey].selfAssessmentSuppliers.add(assessment.vendorCode);
          }
        });
        // Calculate averages and supplier counts
        const processedData = Object.values(monthlyData).map(item => {
          return {
            month: item.month,
            monthName: item.monthName,
            year: item.year,
            selfAssessment: item.selfAssessmentCount > 0 ?
              parseFloat(item.selfAssessmentTotal / item.selfAssessmentCount).toFixed(2) : null,
            calibrationScore: item.count > 0 ?
              parseFloat(item.calibrationScore / item.count).toFixed(2) : null,
            calibrationSupplierCount: item.calibrationSuppliers.size,
            selfAssessmentSupplierCount: item.selfAssessmentSuppliers.size,
            totalSupplierCount: new Set([...item.calibrationSuppliers, ...item.selfAssessmentSuppliers]).size
          };
        });

        // Sort by year and then by month order
        const sortedData = processedData.sort((a, b) => {
          if (a.year !== b.year) {
            return a.year - b.year;
          }
          return months.indexOf(a.monthName) - months.indexOf(b.monthName);
        });

        // Use hardcoded data for testing if no real data is available
        if (sortedData.every(item => item.calibrationScore === null && item.selfAssessment === null)) {
          // Create some sample data for testing
          const sampleData = [...defaultData];

          // Make sure at least some months have data for calibration scores
          sampleData[5].calibrationScore = "48.54"; // June
          sampleData[5].calibrationSupplierCount = 12;
          sampleData[6].calibrationScore = "49.04"; // July
          sampleData[6].calibrationSupplierCount = 15;
          sampleData[7].calibrationScore = "51.60"; // August
          sampleData[7].calibrationSupplierCount = 18;
          sampleData[8].calibrationScore = "52.90"; // September
          sampleData[8].calibrationSupplierCount = 20;

          // Add sample self-assessment scores
          sampleData[4].selfAssessment = "45.20"; // May
          sampleData[4].selfAssessmentSupplierCount = 8;
          sampleData[5].selfAssessment = "46.75"; // June
          sampleData[5].selfAssessmentSupplierCount = 10;
          sampleData[6].selfAssessment = "47.30"; // July
          sampleData[6].selfAssessmentSupplierCount = 13;
          sampleData[7].selfAssessment = "49.80"; // August
          sampleData[7].selfAssessmentSupplierCount = 16;
          sampleData[8].selfAssessment = "50.40"; // September
          sampleData[8].selfAssessmentSupplierCount = 19;

          // Update total supplier counts
          sampleData.forEach(item => {
            item.totalSupplierCount = item.calibrationSupplierCount + item.selfAssessmentSupplierCount;
          });

          setChartData(sampleData);
        } else {


          setChartData(sortedData);
        }
      } catch (error) {
        console.error("Error processing supply data for chart:", error);
        setChartData(defaultData);
      }
    } else {
      // Create some sample data for testing
      const sampleData = [...defaultData];

      // Make sure at least some months have data for calibration scores
      sampleData[5].calibrationScore = "48.54"; // June
      sampleData[5].calibrationSupplierCount = 12;
      sampleData[6].calibrationScore = "49.04"; // July
      sampleData[6].calibrationSupplierCount = 15;
      sampleData[7].calibrationScore = "51.60"; // August
      sampleData[7].calibrationSupplierCount = 18;
      sampleData[8].calibrationScore = "52.90"; // September
      sampleData[8].calibrationSupplierCount = 20;

      // Add sample self-assessment scores
      sampleData[4].selfAssessment = "45.20"; // May
      sampleData[4].selfAssessmentSupplierCount = 8;
      sampleData[5].selfAssessment = "46.75"; // June
      sampleData[5].selfAssessmentSupplierCount = 10;
      sampleData[6].selfAssessment = "47.30"; // July
      sampleData[6].selfAssessmentSupplierCount = 13;
      sampleData[7].selfAssessment = "49.80"; // August
      sampleData[7].selfAssessmentSupplierCount = 16;
      sampleData[8].selfAssessment = "50.40"; // September
      sampleData[8].selfAssessmentSupplierCount = 19;

      // Update total supplier counts
      sampleData.forEach(item => {
        item.totalSupplierCount = item.calibrationSupplierCount + item.selfAssessmentSupplierCount;
      });

      setChartData(sampleData);
    }
  }, [supplyData, selfAssessmentData, processSelfAssessmentData]);

  // Apply date filtering to chart data
  useEffect(() => {
    const filtered = filterDataByDate(chartData);
    setFilteredChartData(filtered);
  }, [chartData, filterDataByDate]);

  // Toggle visibility of series
  const toggleSeriesVisibility = (seriesName) => {
    setVisibleSeries(prev => ({
      ...prev,
      [seriesName]: !prev[seriesName]
    }));
  };

  const panelItems = [
    {
      items: [
        {
          label: "Export as Excel",
          icon: "pi pi-file-excel",
          command: () => {
            // Implement Excel export functionality
            if (activeMode) {
              alert("Exporting chart data to Excel...");
              // Actual implementation would go here
            } else {
              // Export table data
              alert("Exporting table data to Excel...");
              // Actual implementation would go here
            }
          },
        },
        {
          label: "Export as PDF",
          icon: "pi pi-file-pdf",
          command: () => {
            if (activeMode && chartRef.current) {
              alert("Exporting chart as PDF...");
              // Actual implementation would go here
            }
          },
        },
        {
          label: "Export as JPG",
          icon: "pi pi-image",
          command: () => {
            if (activeMode && chartRef.current) {
              alert("Exporting chart as JPG...");
              // Actual implementation would go here
            }
          },
        },
        activeMode && {
          label: "Print",
          icon: "pi pi-print",
          command: () => {
            if (chartRef.current) {
              alert("Printing chart...");
              // Actual implementation would go here
            }
          },
        },
        {
          label: visibleSeries.selfAssessment ? "Hide Self Assessment" : "Show Self Assessment",
          icon: visibleSeries.selfAssessment ? "pi pi-eye-slash" : "pi pi-eye",
          command: () => toggleSeriesVisibility("selfAssessment"),
        },
        {
          label: visibleSeries.calibrationScore ? "Hide Calibration Score" : "Show Calibration Score",
          icon: visibleSeries.calibrationScore ? "pi pi-eye-slash" : "pi pi-eye",
          command: () => toggleSeriesVisibility("calibrationScore"),
        },
      ],
    },
  ];

  return (
    <Card>
      <div style={{ display: "flex", justifyContent: "space-between" }}>
        <h3 style={{ fontSize: "18px", margin: "25px", fontWeight: "600" }}>
          Supplier Self - Assessment and MSI Calibration Scores Trend Chart

        </h3>
        <div
          style={{
            margin: "18px 10px 18px 10px",
            display: "flex",
            gap: "10px",
            alignItems: "center"
          }}
        >
          {/* Date Range Filter */}
          <div style={{ display: "flex", gap: "5px", alignItems: "center" }}>
            <div style={{ display: "flex", flexDirection: "column" }}>
              <label style={{ fontSize: "12px", marginBottom: "2px" }}>From</label>
              <Calendar
                value={dateFilter.start}
                onChange={(e) => setDateFilter({ ...dateFilter, start: e.value })}
                placeholder="Start Date"
                showIcon
                view="month"
                dateFormat="mm/yy"
                style={{ width: "120px" }}
              />
            </div>
            <div style={{ display: "flex", flexDirection: "column" }}>
              <label style={{ fontSize: "12px", marginBottom: "2px" }}>To</label>
              <Calendar
                value={dateFilter.end}
                onChange={(e) => setDateFilter({ ...dateFilter, end: e.value })}
                placeholder="End Date"
                showIcon
                view="month"
                dateFormat="mm/yy"
                minDate={dateFilter.start}
                disabled={!dateFilter.start}
                style={{ width: "120px" }}
              />
            </div>
          </div>

          {/* Sort Dropdown */}
          <ChartSortDropdown
            value={sortField}
            options={sortOptions}
            onChange={handleSortChange}
          />

          <div
            className="buttons"
            style={{
              background: "#F0F2F4",
              borderRadius: "3px",
              width: "4.5rem",
              marginLeft: "10px",
              height: "30px",
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
            }}
          >
            <Button
              style={{
                background: `${!activeMode ? "#FFFFFF" : "transparent"}`,
                padding: "6px",
                color: "black",
                border: "0px",
                marginRight: "4px",
              }}
              onClick={() => {
                setActiveMode(false);
              }}
            >
              <i className="pi pi-table fs-19" />
            </Button>
            <Button
              style={{
                background: `${activeMode ? "#FFFFFF" : "transparent"}`,
                padding: "6px",
                color: "black",
                border: "0px",
              }}
              onClick={() => {
                setActiveMode(true);
              }}
            >
              <i className="pi pi-chart-bar fs-19" />
            </Button>
          </div>
          <div ref={menuRef}>
            <Button
              style={{
                color: "black",
                height: "30px",
                marginLeft: "3px",
                background: "#F0F2F4",
                border: "0px",
                padding: "6px",
                position: "relative",
              }}
              onClick={() => {
                setDropdownOpen(!dropdownOpen);
              }}
            >
              <i className="pi pi-angle-down fs-19" />
            </Button>
            {dropdownOpen && (
              <Menu
                model={panelItems}
                style={{
                  position: "absolute",
                  right: 45,
                  zIndex: "1",
                  padding: 0,
                }}
              ></Menu>
            )}
          </div>
        </div>
      </div>
      <div
        style={{
          display: "flex",
          justifyContent: "space-evenly",
        }}
      >
        <div style={{ display: "flex", flexDirection: "column" }}>
          <p style={{ fontSize: "12px", fontWeight: "600" }}>Self Assessment Score</p>
          <p style={{ fontSize: "20px", fontWeight: "700", color: "#F59E0B" }}>
            {filteredChartData.some(item => item.selfAssessment)
              ? filteredChartData.filter(item => item.selfAssessment).reduce((max, item) => {
                const score = parseFloat(item.selfAssessment || 0);
                return score > max ? score : max;
              }, 0).toFixed(2)
              : 'N/A'}
          </p>
        </div>
        <div style={{ display: "flex", flexDirection: "column" }}>
          <p style={{ fontSize: "12px", fontWeight: "600" }}>MSI Calibration Score</p>
          <p style={{ fontSize: "20px", fontWeight: "700", color: "#6C480B" }}>
            {filteredChartData.some(item => item.calibrationScore)
              ? filteredChartData.filter(item => item.calibrationScore).reduce((max, item) => {
                const score = parseFloat(item.calibrationScore || 0);
                return score > max ? score : max;
              }, 0).toFixed(2)
              : 'N/A'}
          </p>
        </div>
        <div style={{ display: "flex", flexDirection: "column" }}>
          <p style={{ fontSize: "12px", fontWeight: "600" }}>Total Suppliers</p>
          <p style={{ fontSize: "20px", fontWeight: "700", color: "#2563EB" }}>
            {filteredChartData.reduce((total, item) => {
              return total + (item.totalSupplierCount || 0);
            }, 0)}
          </p>
        </div>{" "}
        {/* <div style={{ display: "flex", flexDirection: "column" }}>
          <p style={{ fontSize: "12px" }}>Rating</p>
          <p style={{ fontSize: "20px", color: "#D28B24" }}>BRONZE</p>
        </div> */}
      </div>

      {activeMode ? (
        <ResponsiveContainer width="100%" height={400}>
          <LineChart data={filteredChartData} ref={chartRef} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="month" />
            <YAxis />
            <Tooltip
              formatter={(value, name, props) => {
                const { payload } = props;
                if (name === "Self Assessment Score") {
                  return [
                    value ? `${value}` : 'N/A',
                    `${name} (${payload.selfAssessmentSupplierCount || 0} suppliers)`
                  ];
                } else if (name === "MSI Calibration Score") {
                  return [
                    value ? `${value}` : 'N/A',
                    `${name} (${payload.calibrationSupplierCount || 0} suppliers)`
                  ];
                }
                return [value, name];
              }}
              labelFormatter={(label) => `Month: ${label}`}
            />
            <Legend content={CustomLegend} />
            <Line
              {...lineProps.selfAssessment}
              hide={!visibleSeries.selfAssessment}
              connectNulls={false}
              dot={{ r: 4 }}
            />
            <Line
              {...lineProps.calibrationScore}
              hide={!visibleSeries.calibrationScore}
              connectNulls={false}
              dot={{ r: 4 }}
            />
          </LineChart>
        </ResponsiveContainer>
      ) : (
        <div style={{ padding: "20px" }}>
          <DataTable value={filteredChartData} paginator rows={10} ref={tableRef} sortMode="multiple">
            <Column field="month" header="Month" sortable />
            <Column
              field="selfAssessment"
              header="Self Assessment Score"
              sortable
              body={(rowData) => rowData.selfAssessment || 'N/A'}
            />
            <Column
              field="selfAssessmentSupplierCount"
              header="Self Assessment Suppliers"
              sortable
              body={(rowData) => rowData.selfAssessmentSupplierCount || 0}
            />
            <Column
              field="calibrationScore"
              header="MSI Calibration Score"
              sortable
              body={(rowData) => rowData.calibrationScore || 'N/A'}
            />
            <Column
              field="calibrationSupplierCount"
              header="Calibration Suppliers"
              sortable
              body={(rowData) => rowData.calibrationSupplierCount || 0}
            />
            <Column
              field="totalSupplierCount"
              header="Total Suppliers"
              sortable
              body={(rowData) => rowData.totalSupplierCount || 0}
            />
          </DataTable>
        </div>
      )}
    </Card>
  );
};

export default ThirdLineDemo;
