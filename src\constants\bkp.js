const data =  [
  {
      "SDP78_1Name": "<PERSON><PERSON><PERSON><PERSON>",
      "SDP78_1Contact Email Id": "<PERSON><PERSON><PERSON><PERSON>.<EMAIL>"
  },
  {
      "SDP78_1Fuel Name": "Diesel",
      "SDP78_1Total Consumption": 2.655,
      "SDP78_1Unit": "tonnes",
      "SDP78_1Data Source": ""
  },
  {
      "SDP78_1Fuel Name": "Petrol",
      "SDP78_1Total Consumption": 0.009,
      "SDP78_1Unit": "tonnes",
      "SDP78_1Data Source": ""
  },
  {
      "SDP78_1Fuel Name": "LPG",
      "SDP78_1Total Consumption": 27.72,
      "SDP78_1Unit": "tonnes",
      "SDP78_1Data Source": ""
  },
  {
      "SDP78_1Fuel Name": "CNG",
      "SDP78_1Total Consumption": 0,
      "SDP78_1Unit": "tonnes",
      "SDP78_1Data Source": ""
  },
  {
      "SDP78_1Fuel Name": "Propane",
      "SDP78_1Total Consumption": 0,
      "SDP78_1Unit": "tonnes",
      "SDP78_1Data Source": ""
  },
  {
      "SDP78_1Fuel Name": "Furnace Oil",
      "SDP78_1Total Consumption": 0,
      "SDP78_1Unit": "tonnes",
      "SDP78_1Data Source": ""
  },
  {
      "SDP78_2Type of Refrigerant": "R-32 (HFC-32)",
      "SDP78_2Quantity of Refrigerant Consumed": 1.5
  },
  {
      "SDP78_1Electricity Source": "Electricity from Renewables purchased from Third Party",
      "SDP78_1Electricity Consumption": 0,
      "SDP78_1Unit": "kWh"
  },
  {
      "SDP78_1Electricity Source": "Electricity from Grid",
      "SDP78_1Electricity Consumption": 475040,
      "SDP78_1Unit": "kWh"
  },
  {
      "SDP78_1Electricity Source": "Renewable power procured under \"green tariff\" program of the state",
      "SDP78_1Electricity Consumption": 0,
      "SDP78_1Unit": "kWh"
  },
  {
      "SDP78_1Electricity Source": "Non-renewable power procured from third party",
      "SDP78_1Electricity Consumption": 0,
      "SDP78_1Unit": "kWh"
  }
]
const headers = [{ "name": "SDP78_1Name", "header": "Name" }, { "name": "SDP78_1Contact Email Id", "header": "Contact Email Id" }, { "name": "SDP78_1Fuel Name", "header": "Fuel Name" }, { "name": "SDP78_1Total Consumption", "header": "Total Consumption" }, { "name": "SDP78_1Unit", "header": "Unit" }, { "name": "SDP78_1Data Source", "header": "Data Source" }, { "name": "SDP78_2Type of Biomass Fuel", "header": "Type of Biomass Fuel" }, { "name": "SDP78_2Quantity of Fuel Consumed", "header": "Quantity of Fuel Consumed" }, { "name": "SDP78_2Unit", "header": "Unit" }, { "name": "SDP78_2Type of Biofuel", "header": "Type of Biofuel" }, { "name": "SDP78_2Quantity of Fuel Consumption", "header": "Quantity of Fuel Consumption" }, { "name": "SDP78_2Unit", "header": "Unit" }, { "name": "SDP78_2Type of Refrigerant", "header": "Type of Refrigerant" }, { "name": "SDP78_2Quantity of Refrigerant Consumed", "header": "Quantity of Refrigerant Consumed" }, { "name": "SDP78_2Unit", "header": "Unit" }, { "name": "SDP78_1Electricity Source", "header": "Electricity Source" }, { "name": "SDP78_1Electricity Consumption", "header": "Electricity Consumption" }, { "name": "SDP78_1Unit", "header": "Unit" }, { "name": "SDP78_6Name of the Third-party", "header": "Name of the Third-party" }, { "name": "SDP78_6Emission Factor of the Power Procured from Third Party (tCO2e/kWh)", "header": "Emission Factor of the Power Procured from Third Party (tCO2e/kWh)" }].map(i=>i.name)
const mergeDataByPrefix = (data) => {
  const result = [];

  data.forEach(item => {
    Object.keys(item).forEach(key => {
      const match = key.match(/(SDP78_\d+)/); // Extract prefix like SDP78_1, SDP78_2
      if (match) {
        const prefix = match[1];
        let target = result.find(obj => !obj[key]); // Find object without existing key

        if (!target) {
          target = {}; // Create a new object if key already exists in all objects
          result.push(target);
        }

        target[key] = item[key]; // Add key-value to the object
      }
    });
  });

  return result;
};

const result = mergeDataByPrefix(data);
console.log(result);
const groupSRFIntoAttributes = (submissions) => {
    const combinedData = submissions

    const result = [];
    let currentAttribute = null;

    combinedData.forEach((item) => {
      if (item.type === "paragraph" && item.label.includes("ATTRIBUTE")) {
        if (currentAttribute) {
          result.push(currentAttribute);
        }

        currentAttribute = {
          title: item.label?.split(':')[0],
          data: []
        };
      } else if (currentAttribute) {
        if (item.type === 'table' || item.type === 'tableadd') {

          const result = item?.value?.map((x,y) => item.name +y+ x)
          if (Array.isArray(result) && result.length) {
            currentAttribute.data.push(...result);
          }
        }


      }
    });

    if (currentAttribute) {
      result.push(currentAttribute);
    }

    return result;
  };

  {
    "id": 849,
    "code": "141291",
    "clientId": 289,
    "emailSentCount": null,
    "plantLocation": null,
    "supplierCategory": null,
    "supplierContact": null,
    "supplierName": null,
    "supplierSPOC": null,
    "supplierLocation": null,
    "supplierSpentOn": null,
    "supplierContact2": null,
    "supplierContact3": null,
    "supplierEmail2": null,
    "supplierEmail3": null,
    "dealerName": "SUBAS MOTORS PRIVATE LIMITED",
    "dealerSPOC": "SUBAS MOTORS PRIVATE LIMITED",
    "dealerCategory": 1,
    "dealerZone": 2,
    "dealerLocation": "JAJPUR",
    "dealerCountry": "India",
    "dealerAO": "ODI",
    "service": {
      "areaManagerName": "Subhadeep Ganguly",
      "areaManagerMailId": "<EMAIL>",
      "zonalPlannerName": "Pronoy Sarkar",
      "zonalPlannerMailId": "<EMAIL>",
      "regionalManagerName": "Samir Kumar Singh",
      "regionalManagerMailId": "<EMAIL>"
    },
    "sales": {
      "areaManagerName": "Dipak Kumar Mahapatra",
      "areaManagerMailId": "<EMAIL>",
      "zonalPlannerName": "Chandra Shekhar Chaurasia",
      "zonalPlannerMailId": "<EMAIL>",
      "regionalManagerName": "Ajay Gupta",
      "regionalManagerMailId": "<EMAIL>"
    },
    "aps": {
      "areaManagerName": "",
      "areaManagerMailId": "",
      "regionalManagerName": "",
      "regionalManagerMailId": "",
      "hoPlannerName": "",
      "hoPlannerMailId": ""
    },
    "ao": {
      "areaCommercialManagerName": "",
      "areaCommercialManagerMailId": "",
      "regionalCommercialManagerName": "",
      "regionalCommercialManagerMailId": ""
    },
    "created_on": "2025-02-02T20:38:13.068Z",
    "userProfileId": 1097,
    "modified_on": null,
    "assessmentStartMonth": null,
    "created_by": 374,
    "modified_by": null
  }