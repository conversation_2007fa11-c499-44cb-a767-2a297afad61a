import React from "react";

const PrincipleTwo = () => {
  return (
    <div style={{ minHeight: "80vh" }}>
      <div
        style={{
          maxWidth: "90%",
          margin: "auto",
          marginBottom: "2rem",
        }}
      >
        <p style={{ fontWeight: "bold", color: "black", marginBottom: "1rem" }}>
          PRINCIPLE 2 -BUSINESSES SHOULD PROVIDE GOODS AND SERVICES IN A MANNER
          THAT IS SUSTAINABLE AND SAFE
        </p>
        <p
          style={{
            fontWeight: "bold",
            color: "blue",
            textAlign: "center",
            marginBottom: "1rem",
          }}
        >
          Essential Indicators
        </p>
        <p style={{ fontWeight: "bold", color: "black", marginBottom: "1rem" }}>
          1. Percentage of R&amp;D and capital expenditure (capex) investments
          in specific technologies to improve the environmental and social
          impacts of products and processes to total R&amp;D and capex
          investments made by the entity, respectively.
        </p>
        <table
          border={1}
          cellPadding={8}
          style={{
            borderCollapse: "collapse",
            width: "100%",
            textAlign: "left",
          }}
        >
          <thead>
            <tr>
              <th style={{ width: "20%" }}>&nbsp;</th>
              <th>FY 2023-24 (INR Millions)</th>
              <th>FY 2022-23 (INR Millions)</th>
              <th>
                Details of improvements in environmental and social impact
              </th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
            <tr>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
          </tbody>
        </table>
      </div>

      <div
        style={{
          maxWidth: "90%",
          margin: "auto",
          marginBottom: "2rem",
        }}
      >
        <p style={{ fontWeight: "bold", color: "black", marginBottom: "1rem" }}>
          2.
        </p>
        <p style={{ fontWeight: "bold", color: "black", marginBottom: "1rem" }}>
          a. Does the entity have procedures in place for sustainable sourcing?
          (Yes/No)
        </p>
        <p style={{ fontWeight: "bold", color: "black", marginBottom: "1rem" }}>
          b. If yes, what percentage of inputs were sourced sustainably?
        </p>
        <p style={{ fontWeight: "bold", color: "black", marginBottom: "1rem" }}>
          3. Describe the processes in place to safely reclaim your products for
          reusing, recycling and disposing at the end of life, for (a) Plastics
          (including packaging) (b) E-waste (c) Hazardous waste and (d) other
          waste
        </p>
        <table
          border={1}
          cellPadding={8}
          style={{
            borderCollapse: "collapse",
            width: "100%",
            textAlign: "left",
          }}
        >
          <thead>
            <tr>
              <th>S.No</th>
              <th>Product</th>
              <th>Product Process to safely reclaim the product</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td style={{ fontWeight: "bold" }}>1</td>
              <td></td>
              <td></td>
            </tr>
            <tr>
              <td style={{ fontWeight: "bold" }}>2</td>
              <td></td>
              <td></td>
            </tr>
            <tr>
              <td style={{ fontWeight: "bold" }}>3</td>
              <td></td>
              <td></td>
            </tr>
            <tr>
              <td style={{ fontWeight: "bold" }}>4</td>
              <td></td>
              <td></td>
            </tr>
          </tbody>
        </table>
      </div>

      <div
        style={{
          maxWidth: "90%",
          margin: "auto",
          marginBottom: "10rem",
        }}
      >
        <p style={{ fontWeight: "bold", color: "black", marginBottom: "1rem" }}>
          4. Whether Extended Producer Responsibility (EPR) is applicable to the
          entity’s activities (Yes / No). If yes, whether the waste collection
          plan is in line with the Extended Producer Responsibility (EPR) plan
          submitted to Pollution Control Boards? If not, provide steps taken to
          address the same.
        </p>
      </div>

      <div
        style={{
          maxWidth: "90%",
          margin: "auto",
          marginBottom: "2rem",
        }}
      >
        <p
          style={{
            fontWeight: "bold",
            color: "blue",
            textAlign: "center",
            marginBottom: "1rem",
          }}
        >
          Leadership Indicators
        </p>
        <p style={{ fontWeight: "bold", color: "black", marginBottom: "1rem" }}>
          1. Has the entity conducted Life Cycle Perspective / Assessments (LCA)
          for any of its products (for manufacturing industry) or for its
          services (for service industry)? If yes, provide details.
        </p>
        <table
          border={1}
          cellPadding={8}
          style={{
            borderCollapse: "collapse",
            width: "100%",
            textAlign: "left",
          }}
        >
          <thead>
            <tr>
              <th>NIC Code</th>
              <th>Name of Product /Service</th>
              <th>% of total Turnover contributed</th>
              <th>
                Boundary for which the Life Cycle Perspective / Assessment was
                conducted
              </th>
              <th>Whether conducted by independent external agency (Yes/No)</th>
              <th>
                Results communicate d in public domain (Yes/No) If yes, provide
                the web-link.
              </th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
          </tbody>
        </table>
      </div>

      <div
        style={{
          maxWidth: "90%",
          margin: "auto",
          marginBottom: "2rem",
        }}
      >
        <p style={{ fontWeight: "bold", color: "black", marginBottom: "1rem" }}>
          2. If there are any significant social or environmental concerns
          and/or risks arising from the production or disposal of your
          products/services, as identified in the Life Cycle Perspective /
          Assessments (LCA) or through any other means, briefly describe the
          same along-with action taken to mitigate the same.
        </p>
        <table
          border={1}
          cellPadding={8}
          style={{
            borderCollapse: "collapse",
            width: "100%",
            textAlign: "left",
          }}
        >
          <thead>
            <tr>
              <th>Name of Product / Service</th>
              <th>Description of the risk / concern</th>
              <th>Action Taken</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
          </tbody>
        </table>
      </div>

      <div
        style={{
          maxWidth: "90%",
          margin: "auto",
          marginBottom: "10rem",
        }}
      >
        <p style={{ fontWeight: "bold", color: "black", marginBottom: "1rem" }}>
          3. Percentage of recycled or reused input material to total material
          (by value) used in production (for manufacturing industry) or
          providing services (for service industry).
        </p>
        <table
          border={1}
          cellPadding={8}
          style={{
            borderCollapse: "collapse",
            width: "100%",
            textAlign: "left",
          }}
        >
          <thead>
            <tr>
              <th rowSpan={2} style={{ width: "30%" }}>
                Indicate input material
              </th>
              <th colSpan={2} style={{ textAlign: "center" }}>
                Recycled or re-used input material to total material
              </th>
            </tr>
            <tr>
              <th style={{ textAlign: "center" }}>FY 2024-25</th>
              <th style={{ textAlign: "center" }}>FY 2023-24</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
          </tbody>
        </table>

        <p style={{ fontSize: "0.9em", fontStyle: "italic", marginTop: "1em" }}>
          As a healthcare business, the safe usage and quality of our products
          are of the utmost priority. As per Good Manufacturing Practice (GMP)
          and as a responsible pharmaceutical manufacturer, we do not reuse any
          material/chemical for manufacturing.
        </p>
      </div>

      <div
        style={{
          maxWidth: "90%",
          margin: "auto",
          marginBottom: "10rem",
        }}
      >
        <p style={{ fontWeight: "bold", color: "black", marginBottom: "1rem" }}>
          4. Of the products and packaging reclaimed at end of life of products,
          amount (in metric tonnes) reused, recycled, and safely disposed:
        </p>
        <table
          border={1}
          cellPadding={8}
          style={{
            borderCollapse: "collapse",
            width: "100%",
            textAlign: "left",
          }}
        >
          <thead>
            <tr>
              <th rowSpan={2}>Type of Waste</th>
              <th colSpan={3} style={{ textAlign: "center" }}>
                FY 2024-25
              </th>
              <th colSpan={3} style={{ textAlign: "center" }}>
                FY 2023-24
              </th>
            </tr>
            <tr>
              <th>Re-Used</th>
              <th>Recycled</th>
              <th>Safely Disposed (Metric Tonnes)</th>
              <th>Re-Used</th>
              <th>Recycled</th>
              <th>Safely Disposed (Metric Tonnes)</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>Plastics including packaging</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
            <tr>
              <td>E-waste</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
            <tr>
              <td>Hazardous waste</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
            <tr>
              <td>Other waste - Paper waste</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
          </tbody>
        </table>
      </div>

      <div
        style={{
          maxWidth: "90%",
          margin: "auto",
          marginBottom: "10rem",
        }}
      >
        <p style={{ fontWeight: "bold", color: "black", marginBottom: "1rem" }}>
          5. Reclaimed products and their packaging materials (as a percentage
          of products sold) for each product category.
        </p>
        <table
          border={1}
          cellPadding={8}
          style={{
            borderCollapse: "collapse",
            width: "100%",
            textAlign: "left",
          }}
        >
          <thead>
            <tr>
              <th style={{ width: "50%" }}>Indicate product category</th>
              <th style={{ textAlign: "center" }}>
                Reclaimed products and their packaging materials as % of total
                products sold in the respective category
              </th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default PrincipleTwo;
