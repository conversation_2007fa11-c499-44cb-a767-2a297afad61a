import React, { useEffect, useState } from 'react'
import APIServices from '../../../service/APIService'
import { API } from '../../../constants/api_url'
import { red } from '@mui/material/colors';
import { MultiSelect } from 'primereact/multiselect';
import { Column } from 'primereact/column';
import { Button } from 'primereact/button';
import { DataTable } from 'primereact/datatable';
import { useSelector } from 'react-redux';

export const EnterpriseEFAssignment = ({ admin }) => {
  const [label1, label2, label3] = useSelector((state) => state.user.tierLabel);
    const [efassignment, setEfAssignment] = useState([]);
    const [load, setLoad] = useState(true)
    const emissionsColumns = [
        { header: label1, field: "tier1" },
        { header: label2, field: "tier2" },
        { header: label3, field: "tier3" },
        { header: "EF_ID", field: "uniqueEfId" },
        { header: "Standard", field: "standard" },
        { header: "Start Month", field: "startMonth" },
        { header: "End Month", field: "endMonth" },
        { header: "GHG Category", field: "ghgcategory" },
        { header: "GHG SubCategory", field: "ghgsubcategory" },
        { header: "Item", field: "item" },
        { header: "Item Category1", field: "subCategory1" },
        { header: "Item Category2", field: "subCategory2" },
        { header: "Item Category3", field: "subCategory3" },
        { header: "Item Category4", field: "subCategory4" },
        { header: "Co2e in kg", field: "co2e" },
    ];
    useEffect(() => {
       
        APIServices.get(API.EFAssignment_UP(admin?.id)).then((res) => {
            setEfAssignment(res.data)
            setLoad(false)
        }).catch(() => {

        })
    }, [])
    const RowFilterTemplate = (options, obj) => {
        return (
            <MultiSelect
                value={options.value}
                options={Array.from(new Set(efassignment.map((i) => i[obj])))}
                onChange={(e) => options.filterCallback(e.value)}
                placeholder="Any"
                className="p-column-filter"
                maxSelectedLabels={1}
                style={{ minWidth: "14rem" }}
            />
        );
    };
    const exportReport = (data, columns, fileName = "report.csv") => {
        if (!data || data.length === 0) {
            alert("No data available to export!");
            return;
        }

        // Extract headers from columns
        const headers = columns.map((col) => col.header);

        // Generate CSV rows
        const csvRows = [];
        csvRows.push(headers.join(",")); // Add header row

        // Add data rows
        data.forEach((row) => {
            const rowData = columns.map((col) => {
                const value = row[col.field] || ""; // Safely access field values
                return `"${value.toString().replace(/"/g, '""')}"`; // Escape double quotes
            });
            csvRows.push(rowData.join(","));
        });

        // Create CSV string
        const csvString = csvRows.join("\n");

        // Create a Blob and download the file
        const blob = new Blob([csvString], { type: "text/csv" });
        const url = window.URL.createObjectURL(blob);

        const link = document.createElement("a");
        link.href = url;
        link.download = fileName;
        document.body.appendChild(link);
        link.click();

        // Clean up
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
    };
    return (
        <div>
            <p>
                This screen provides an overview of the emission factors used to
                calculate CO2e values for various reports and disclosures. Using
                the filters, you can select a specific reporting entity within the
                enterprise to view the corresponding emission factor source,
                category, and sub-categories. Emission values are presented for
                each item within the selected category, based on the unit of
                measurement used to collect data on the Navigos platform. Each
                emission factor in this list has a unique ID that is used in
                computations and can be traced back to its source.
            </p>
            <div className="col-12 align-items-end flex justify-content-end">
                <Button
                    onClick={() => {
                        exportReport(
                            efassignment,
                            emissionsColumns,
                            "emissions_data.csv"
                        );
                    }}
                    label="Export Report"
                    icon="pi pi-download"
                    className="p-button-primary mr-3"
                />
            </div>
            <DataTable
                scrollable
                showGridlines
                className="custom-datatable"
                loading={load}
                filters={{
                    startMonth: { value: null, matchMode: "in" },
                    endMonth: { value: null, matchMode: "in" },
                    tier1: { value: null, matchMode: "in" },
                    tier2: { value: null, matchMode: "in" },
                    tier3: { value: null, matchMode: "in" },
                    standard: { value: null, matchMode: "in" },
                    ghgcategory: { value: null, matchMode: "in" },
                    ghgsubcategory: { value: null, matchMode: "in" },
                    item: { value: null, matchMode: "in" },
                    subCategory1: { value: null, matchMode: "in" },
                    subCategory2: { value: null, matchMode: "in" },
                    subCategory3: { value: null, matchMode: "in" },
                    subCategory4: { value: null, matchMode: "in" }
                }}
                value={efassignment}

            >
                <Column
                    header={label1}
                    field={"tier1"}
                    showFilterMatchModes={false}
                    filter
                    body={(rowData) => rowData.tier1}
                    filterElement={(options) => RowFilterTemplate(options, "tier1")}
                />

                <Column
                    header={label3}
                    field={"tier2"}
                    showFilterMatchModes={false}
                    filter
                    body={(rowData) => rowData.tier2}
                    filterElement={(options) => RowFilterTemplate(options, "tier2")}
                />
                <Column
                    header={label3}
                    field={"tier3"}
                    showFilterMatchModes={false}
                    filter
                    body={(rowData) => rowData.tier3}
                    filterElement={(options) => RowFilterTemplate(options, "tier3")}
                />

                <Column
                    header={"EF_ID"}
                    bodyStyle={{ width: "150px", minWidth: "150px" }}
                    field={"uniqueEfId"}
                />

                <Column
                    header={"Standard"}
                    field={"standard"}
                    showFilterMatchModes={false}
                    filter
                    filterElement={(options) =>
                        RowFilterTemplate(options, "standard")
                    }
                />
                <Column
                    header={"Start Month"}
                    field={"startMonth"}
                    showFilterMatchModes={false}
                    filter
                    body={(rowData) => rowData.startMonth}
                    filterElement={(options) =>
                        RowFilterTemplate(options, "startMonth")
                    }
                />
                <Column
                    header={"End Month"}
                    field={"endMonth"}
                    showFilterMatchModes={false}
                    filter
                    body={(rowData) => rowData.endMonth}
                    filterElement={(options) =>
                        RowFilterTemplate(options, "endMonth")
                    }
                />
                <Column
                    header={"GHG Category"}
                    field={"ghgcategory"}
                    showFilterMatchModes={false}
                    filter
                    filterElement={(options) =>
                        RowFilterTemplate(options, "ghgcategory")
                    }
                />
                <Column
                    header={"GHG SubCategory"}
                    field={"ghgsubcategory"}
                    showFilterMatchModes={false}
                    filter
                    filterElement={(options) =>
                        RowFilterTemplate(options, "ghgsubcategory")
                    }
                />
                <Column
                    header={"Item"}
                    field={"item"}
                    showFilterMatchModes={false}
                    filter
                    filterElement={(options) => RowFilterTemplate(options, "item")}
                />
                <Column
                    header={"Item Category1"}
                    field={"subCategory1"}
                    showFilterMatchModes={false}
                    filter
                    filterElement={(options) =>
                        RowFilterTemplate(options, "subCategory1")
                    }
                />
                <Column
                    header={"Item Category2"}
                    field={"subCategory2"}
                    showFilterMatchModes={false}
                    filter
                    filterElement={(options) =>
                        RowFilterTemplate(options, "subCategory2")
                    }
                />
                <Column
                    header={"Item Category3"}
                    field={"subCategory3"}
                    showFilterMatchModes={false}
                    filter
                    filterElement={(options) =>
                        RowFilterTemplate(options, "subCategory3")
                    }
                />
                <Column
                    header={"Item Category4"}
                    field={"subCategory4"}
                    showFilterMatchModes={false}
                    filter
                    filterElement={(options) =>
                        RowFilterTemplate(options, "subCategory4")
                    }
                />
                <Column header={"Co2e in kg"} field={"co2e"} />
            </DataTable>
        </div>
    )

}