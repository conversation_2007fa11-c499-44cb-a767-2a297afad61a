import React from "react";

const PrincipleSeven = () => {
  return (
    <div style={{ minHeight: "80vh" }}>
      <div
        style={{
          maxWidth: "90%",
          margin: "auto",
          marginBottom: "2rem",
        }}
      >
        <p
          style={{
            fontWeight: "bold",
            color: "black",
            textAlign: "center",
            marginBottom: "1rem",
          }}
        >
          PRINCIPLE 7- BUSINEESS, WHEN ENGAGING IN INFLUENCING PUBLIC AND
          REGULATORY POLICY, SHOULD DO SO IN A MANNER THAT IS RESPONSIBLE AND
          TRANSPARENT
        </p>
        <p
          style={{
            fontWeight: "bold",
            color: "blue",
            textAlign: "center",
            marginBottom: "1rem",
          }}
        >
          Essential Indicators
        </p>
        <p style={{ fontWeight: "bold", color: "black", marginBottom: "2rem" }}>
          1. a. Number of affiliations with trade and industry chambers/
          associations. Not applicable
        </p>
        <p style={{ fontWeight: "bold", color: "black", marginBottom: "1rem" }}>
          b. List the top 10 trade and industry chambers/ associations
          (determined based on the total members of such body) the entity is a
          member of/ affiliated to.
        </p>
        <table
          border={1}
          cellPadding={8}
          style={{
            borderCollapse: "collapse",
            width: "100%",
            textAlign: "left",
          }}
        >
          <thead>
            <tr>
              <th style={{ width: "10%" }}>S. No.</th>
              <th>Name of the trade and industry chambers / associations</th>
              <th>
                Reach of trade and industry chambers/ associations
                (State/National)
              </th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>1</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
            <tr>
              <td>2</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
            <tr>
              <td>3</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
          </tbody>
        </table>
      </div>

      <div
        style={{
          maxWidth: "90%",
          margin: "auto",
          marginBottom: "2rem",
        }}
      >
        <p style={{ fontWeight: "bold", color: "black", marginBottom: "1rem" }}>
          2. Provide details of corrective action taken or underway on any
          issues related to anti- competitive conduct by the entity, based on
          adverse orders from regulatory authorities.
        </p>
        <table
          border={1}
          cellPadding={8}
          style={{
            borderCollapse: "collapse",
            width: "100%",
            textAlign: "left",
          }}
        >
          <thead>
            <tr>
              <th style={{ width: "10%" }}>Name of the Authority</th>
              <th>Brief of the case</th>
              <th>Corrective Action Taken</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
          </tbody>
        </table>
      </div>

      <div
        style={{
          maxWidth: "90%",
          margin: "auto",
          marginBottom: "2rem",
        }}
      >
        <p
          style={{
            fontWeight: "bold",
            color: "blue",
            textAlign: "center",
            marginBottom: "1rem",
          }}
        >
          Leadership Indicators
        </p>
        <p style={{ fontWeight: "bold", color: "black", marginBottom: "2rem" }}>
          1. Details of public policy positions advocated by the entity:
        </p>
        <table
          border={1}
          cellPadding={8}
          style={{
            borderCollapse: "collapse",
            width: "100%",
            textAlign: "left",
          }}
        >
          <thead>
            <tr>
              <th style={{ width: "10%" }}>S. No.</th>
              <th>Public policy advocated</th>
              <th>Method resorted for such advocacy</th>
              <th>Whether information available in public domain? (Yes/No)</th>
              <th>
                Frequency of Review by Board (Annually/ Half yearly/ Quarterly /
                Others – please specify)
              </th>
              <th>Web Link, if available</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
              <td>&nbsp;</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default PrincipleSeven;
