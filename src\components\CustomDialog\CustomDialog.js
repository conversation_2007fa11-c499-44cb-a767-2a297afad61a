import { Dialog } from 'primereact/dialog';
import { InputText } from 'primereact/inputtext';
import { InputNumber } from 'primereact/inputnumber';
import { Dropdown } from 'primereact/dropdown';
import ReactDOM from 'react-dom';
import React, { useState, useEffect } from 'react';

let container = null;
let isDialogOpen = false;

export const CustomDialog = {
  Set: function (props) {
    if (!container) {
      container = document.createElement('div');
      document.body.appendChild(container);
    }

    const renderDialog = () => {
      isDialogOpen = true;
      ReactDOM.render(
        <DialogWrapper
          {...props}
          onClose={() => {
            isDialogOpen = false;
            ReactDOM.unmountComponentAtNode(container);
          }}
        />,
        container
      );
    };

    // Wait if a dialog is already open
    const waitAndRender = () => {
      if (!isDialogOpen) {
        renderDialog();
      } else {
        const interval = setInterval(() => {
          if (!isDialogOpen) {
            clearInterval(interval);
            renderDialog();
          }
        }, 100);
      }
    };

    waitAndRender();
  },
};

function DialogWrapper({
  title = 'Dialog',
  message = '',
  inputType = null,
  dropdownOptions = [],
  validation = null,
  validateValue = '',
  buttons = [{ label: 'OK', className: '', style: {}, onClick: () => {} }],
  autoClose = null,
  onClose = () => {},
}) {
  const [visible, setVisible] = useState(true);
  const [inputValue, setInputValue] = useState('');
  const [error, setError] = useState('');

  useEffect(() => {
    if (autoClose) {
      const timer = setTimeout(() => {
        closeDialog();
      }, autoClose);
      return () => clearTimeout(timer);
    }
  }, []);

  const closeDialog = () => {
    setVisible(false);
    setTimeout(() => onClose(), 300); // wait for animation
  };

  const validateInput = (val) => {
    if (!validation) return true;

    switch (validation) {
      case 'email':
        return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(val);
      case 'positive':
        return !isNaN(val) && Number(val) > 0;
      case 'alpha':
        return /^[A-Za-z]+$/.test(val);
      case 'alphanumeric':
        return /^[A-Za-z0-9]+$/.test(val);
      case 'includes':
        return Array.isArray(validateValue)
          ? validateValue.some((v) => val.includes(v))
          : val.includes(validateValue);
      case 'equals':
        return val === validateValue;
      default:
        return true;
    }
  };

  const handleButtonClick = async (btn) => {
    if (inputType && !validateInput(inputValue)) {
      setError('Invalid input');
      return;
    }

    await btn.onClick?.(inputValue);
    closeDialog();
  };

  const renderInput = () => {
    if (inputType === 'text') {
      return (
        <InputText
          value={inputValue}
          onChange={(e) => {
            setInputValue(e.target.value);
            setError('');
          }}
          className="w-full"
        />
      );
    }
    if (inputType === 'number') {
      return (
        <InputNumber
          value={inputValue}
          onValueChange={(e) => {
            setInputValue(e.value);
            setError('');
          }}
          className="w-full"
        />
      );
    }
    if (inputType === 'dropdown') {
      return (
        <Dropdown
          value={inputValue}
          options={dropdownOptions}
          onChange={(e) => {
            setInputValue(e.value);
            setError('');
          }}
          placeholder="Select"
          className="w-full"
        />
      );
    }
    return null;
  };

  return (
    <Dialog header={title} visible={visible} dismissableMask={false} closeOnEscape={false} onHide={closeDialog} closable={!autoClose}>
      <div>
      <div dangerouslySetInnerHTML={{ __html: message }} />
        {renderInput()}
        {error && <small className="p-error">{error}</small>}
        <div className="flex justify-end gap-2 mt-4">
          {buttons.map((btn, idx) => (
            <button
              key={idx}
              className={`p-button ${btn.className}`}
              style={btn.style}
              onClick={() => handleButtonClick(btn)}
            >
              {btn.label}
            </button>
          ))}
        </div>
      </div>
    </Dialog>
  );
}
