.container {
    max-width: 1200px;
    margin: 0 auto;
  }
  
  /* DIV-Based Table Container */
  .table-container {
    display: flex;
    flex-direction: column;
    border: 1px solid #dee2e6;
    margin-bottom: 2rem;
  }
  
  /* Header Row */
  .header-row {
    display: flex;
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
  }
  
  .header-cell {
    flex: 1;
    text-align: center;
    font-weight: bold;
    padding: 12px;
    border-right: 1px solid #dee2e6;
  }
  
  .header-cell:last-child {
    border-right: none;
  }
  
  /* Data Row */
  .data-row {
    display: flex;
    border-bottom: 1px solid #dee2e6;
  }
  
  .data-row:last-child {
    border-bottom: none;
  }
  
  .user-cell {
    flex: 1;
    display: flex;
    align-items: center;
    padding: 12px;
    border-right: 1px solid #dee2e6;
    font-weight: 600;
    gap: 8px;
  }
  
  /* Additional styling for the user avatar circle */
  .avatar-circle {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background-color: #007bff;
    color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
  }
  
  /* Each cell for data */
  .data-cell {
    flex: 1;
    text-align: center;
    padding: 12px;
    border-right: 1px solid #dee2e6;
  }
  
  .data-cell:last-child {
    border-right: none;
  }
  
  /* Consolidated link styling (within the last cell) */
  .consolidated-link {
    color: #007bff;
    text-decoration: none;
  }
  .consolidated-link:hover {
    text-decoration: underline;
  }
  
  /* -------------------- Document Repository -------------------- */
  .document-repository {
    margin-top: 1rem;
    margin-bottom: 2rem;
    border: 1px solid #dee2e6;
  }
  
  .doc-repo-title {
    margin: 0;
    padding: 12px;
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
  }
  
  .doc-repo-header-row,
  .doc-repo-row {
    display: flex;
    border-bottom: 1px solid #dee2e6;
  }
  
  .doc-repo-header-row:last-child,
  .doc-repo-row:last-child {
    border-bottom: none;
  }
  
  .doc-repo-header-cell,
  .doc-repo-cell {
    flex: 1;
    padding: 10px 12px;
    border-right: 1px solid #dee2e6;
    text-align: left;
  }
  
  .doc-repo-header-cell:last-child,
  .doc-repo-cell:last-child {
    border-right: none;
    text-align: center;
  }
  
  .download-icon {
    cursor: pointer;
  }
  