import React from 'react';

const cellColors = [
  "#f3f5f9",
  "#f3f5f9",
  "#fff",
  "#FEF5E5",
  "#E5F5EA",
  "#ffffff",
  "#E5F5EA",
  "#FEF5E5",
  "#E5F5EA"
];

const TableRow = ({ user, avatar, cells }) => {
  return (
    <tr>
      <td className="user-cell">
        <img src={avatar} alt={user} className="avatar-img" />
        {user}
      </td>
      {cells.map((cell, idx) => (
        <td
          key={idx}
          className="status-cell"
          style={{ backgroundColor: cells[idx] ? cellColors[idx] : "#ffffff" }}
        >
          {cells[idx] ? '✔️' : ''}
        </td>
      ))}
    </tr>
  );
};

export default TableRow;
