import React from 'react';
import { Card } from 'primereact/card';
import ActionHistoryItem from './ActionHistoryItem';

const ActionHistoryCard = ({ histories, getUserName }) => {
    if (!histories || histories.length === 0) {
        return null;
    }

    return (
        <Card className="shadow-2 mt-3"
            title={
                <div className="flex align-items-center">
                    <i className="pi pi-history mr-2" style={{ color: '#315975' }}></i>
                    <span className="font-bold">Action History</span>
                </div>
            }
        >
            <div className="grid">
                {histories.map((history, index) => (
                    <div key={index} className="col-12 md:col-6 mb-3">
                        <ActionHistoryItem 
                            history={history} 
                            index={index} 
                            getUserName={getUserName} 
                        />
                    </div>
                ))}
            </div>
        </Card>
    );
};

export default ActionHistoryCard;
