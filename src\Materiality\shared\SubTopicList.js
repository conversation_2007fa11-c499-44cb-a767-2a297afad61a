import React, { useState } from "react";
import Editable from "react-bootstrap-editable";
import AddSubTopicPopup from "./AddSubTopicPopup";
// const SubTopicList = (props) => {
//     return (
//         <>
//         {
//             props.subTopics.map((subTopic) => {
//                 return (
//                     <div key={subTopic.id} onClick={(e) => {props.onClick(e, subTopic.id)}} className={"mail-list " +  (props.subTopicId === subTopic.id ? "active" : "")}>
           
//                             <div className="content">
//                                 <p className="sender-name"><Editable
//                                     onSubmit={(value) => props.onSubTopicChange(value, subTopic.id)}
//                                     initialValue={subTopic.name}
//                                     isValueClickable
//                                     mode="inline"
//                                 />
//                         </p>
                              
//                             </div>
//                             <div className="details">
//                                 <i className="mdi mdi-chevron-right"></i>
//                             </div>
//                     </div>
//                 )
//             })
//         }
       

       
//         </>
        
//     );
// }

// export default SubTopicList;


const SubTopicList = (props) => {
    const [showPopup, setShowPopup] = useState(false);
  
    const togglePopup = () => {
      setShowPopup(!showPopup);
    };
  
    return (
      <>
        {showPopup && (
          <AddSubTopicPopup
            onSubmit={props.onAddSubTopic}
            companies={props.companies}
          />
        )}
        {/* Existing code for rendering subtopics */}
        {props.subTopics.map((subTopic) => {
          return (
            <div key={subTopic.id} onClick={(e) => { props.onClick(e, subTopic.id) }} className={"mail-list " + (props.subTopicId === subTopic.id ? "active" : "")}>
              <div className="content">
                <p className="sender-name">
                  
                  <Editable
                    onSubmit={(value) => props.onSubTopicChange(value, subTopic.id)}
                    initialValue={subTopic.name}
                    isValueClickable
                    mode="inline"
                  />
                </p>
              </div>
              <div className="details">
                <i className="mdi mdi-chevron-right"></i>
              </div>
            </div>
          );
        })}
      </>
    );
  };
  
  export default SubTopicList;