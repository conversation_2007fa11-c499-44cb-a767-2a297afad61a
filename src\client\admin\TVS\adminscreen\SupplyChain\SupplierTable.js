import React, { useState, useEffect, useCallback, useMemo } from "react";
import { Dropdown } from "primereact/dropdown";
import { DataTable } from "primereact/datatable";
import { Column } from "primereact/column";
import { MultiSelect } from "primereact/multiselect";
import { InputText } from "primereact/inputtext"; // Import InputText for search
import APIServices from "../../../../../service/APIService";
import { API } from "../../../../../constants/api_url";

const SupplierTable = ({ supplyData }) => {
  const [filteredData, setFilteredData] = useState([]);
  const [filterOption, setFilterOption] = useState("All");
  const [selectedCategory, setSelectedCategory] = useState("All");
  const [categories, setCategories] = useState([]);
  const [globalFilter, setGlobalFilter] = useState(""); // State for search input
  const [vendorCodeData, setVendorCodeData] = useState([]);

  // Fetch VendorCodeAll data
  useEffect(() => {
    const fetchVendorCodeData = async () => {
      try {

        const response = await APIServices.get(API.VendorCodeAll);


        if (response.data) {
          setVendorCodeData(response.data);
        }
      } catch (error) {
        console.error('Error fetching VendorCodeAll data:', error);
      }
    };

    fetchVendorCodeData();
  }, []);

  useEffect(() => {
    if (supplyData?.length > 0) {

      // Extract unique categories dynamically
      const uniqueCategories = [
        "All",
        ...new Set(supplyData.map((item) => item.category)),
      ];
      setCategories(uniqueCategories);
    }
  }, [supplyData]);

  // Function to get supplier spent amount by vendor code
  const getSupplierSpentOn = useCallback((vendorCode) => {
    const vendor = vendorCodeData.find(v => v.code === vendorCode?.toString());
    return vendor?.supplierSpentOn || 0;
  }, [vendorCodeData]);

  // Function to format spent amount with Cr. suffix
  const formatSpentAmount = useCallback((amount) => {
    return `${parseFloat(amount || 0).toFixed(2)} Cr.`;
  }, []);

  // Memoize the filtered data calculation to prevent unnecessary re-renders
  const memoizedFilteredData = useMemo(() => {
    if (!supplyData?.length) return [];

    let sortedData = [...supplyData];

    // Map vendor codes with spent amounts from vendorCodeData
    sortedData = sortedData.map(item => ({
      ...item,
      supplierSpentOn: getSupplierSpentOn(item.vendor_code),
      formattedSpentAmount: formatSpentAmount(getSupplierSpentOn(item.vendor_code))
    }));

    // Apply Category Filter First
    if (selectedCategory !== "All") {
      sortedData = sortedData.filter(
        (item) => item.category === selectedCategory
      );
    }

    // Sort by MSI Score (Descending)
    sortedData.sort((a, b) => b.msi_score - a.msi_score);

    // Apply Spending Range Filters
    switch (filterOption) {
      case "5-10 Cr":
        return sortedData.filter(item => item.supplierSpentOn >= 5 && item.supplierSpentOn < 10);
      case "10-20 Cr":
        return sortedData.filter(item => item.supplierSpentOn >= 10 && item.supplierSpentOn < 20);
      case "20-50 Cr":
        return sortedData.filter(item => item.supplierSpentOn >= 20 && item.supplierSpentOn < 50);
      case "50-100 Cr":
        return sortedData.filter(item => item.supplierSpentOn >= 50 && item.supplierSpentOn < 100);
      case "100 Cr and more":
        return sortedData.filter(item => item.supplierSpentOn >= 100);
      case "Top 5":
        return sortedData.slice(0, 5);
      case "Top 10":
        return sortedData.slice(0, 10);
      case "Top 15":
        return sortedData.slice(0, 15);
      case "Bottom 5":
        return sortedData.slice(-5);
      case "Bottom 10":
        return sortedData.slice(-10);
      case "Bottom 15":
        return sortedData.slice(-15);
      default:
        return sortedData;
    }
  }, [supplyData, filterOption, selectedCategory, vendorCodeData, getSupplierSpentOn, formatSpentAmount]);

  useEffect(() => {
    setFilteredData(memoizedFilteredData);
  }, [memoizedFilteredData]);

  const RowFilterTemplate_ = (options, obj) => {
    console.log('Filter data:', filteredData);
    return (
      <MultiSelect
        value={options.value}
        options={Array.from(new Set(filteredData?.map((i) => i[obj]))).filter(x => x)}
        onChange={(e) => options.filterCallback(e.value)}
        placeholder="Any"
        filter
        panelClassName='hidefilter'
        className="p-column-filter"
        maxSelectedLabels={1}
        style={{ minWidth: "14rem" }}
      />
    );
  };

  return (
    <div className="container mt-4 ms-0">
      <h5 className="text-dark">Suppliers</h5>



      <div className="mb-3 d-flex gap-3">
        {/* 🔍 Search Box for Supplier Name & Vendor Code */}
        <span className="p-input-icon-left">
          <i className="pi pi-search" />
          <InputText
            value={globalFilter}
            onChange={(e) => setGlobalFilter(e.target.value)}
            placeholder="Search by Supplier Name or Vendor Code"
            style={{ width: "300px" }}
          />
        </span>

        {/* Spending Range Filter Dropdown */}
        <Dropdown
          value={filterOption}
          options={[
            "All",
            "5-10 Cr",
            "10-20 Cr",
            "20-50 Cr",
            "50-100 Cr",
            "100 Cr and more",
            "Top 5",
            "Top 10",
            "Top 15",
            "Bottom 5",
            "Bottom 10",
            "Bottom 15",
          ]}
          onChange={(e) => setFilterOption(e.value)}
          placeholder="Select Filter"
          className="w-25"
        />
      </div>

      <DataTable
        value={filteredData}
        paginator
        rows={10}
        globalFilter={globalFilter} // 🔍 Enable Global Filtering
        responsiveLayout="scroll"
      >
        <Column
          field="company_name"
          header="Supplier Name"
          sortable
          filter
          showFilterMatchModes={false}
          filterElement={(options) => RowFilterTemplate_(options, "company_name")}
        />
        <Column
          field="vendor_code"
          header="Vendor Code"
          sortable
          filter
          showFilterMatchModes={false}
          filterElement={(options) => RowFilterTemplate_(options, "vendor_code")}
        />
        <Column
          field="category"
          header="Category"
          filter
          showFilterMatchModes={false}
          filterElement={(options) => RowFilterTemplate_(options, "category")}
        />
        <Column
          field="formattedSpentAmount"
          header="Supplier Spent"
          sortable
          filter
          showFilterMatchModes={false}
          sortField="supplierSpentOn"
          body={(rowData) => rowData.formattedSpentAmount}
          filterElement={(options) => RowFilterTemplate_(options, "formattedSpentAmount")}
        />
        <Column
          field="msi_score"
          header="MSI Score"
          sortable
          filter
          showFilterMatchModes={false}
          filterElement={(options) => RowFilterTemplate_(options, "msi_score")}
        />
        <Column
          field="rank"
          header="Overall Rank"
          sortable
          filter
          showFilterMatchModes={false}
          filterElement={(options) => RowFilterTemplate_(options, "rank")}
        />
      </DataTable>
    </div>
  );
};

export default SupplierTable;
