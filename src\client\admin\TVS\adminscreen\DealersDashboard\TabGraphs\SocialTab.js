import React from "react";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Legend,
  ResponsiveContainer,
  CartesianGrid,
} from "recharts";
import { useState, useEffect } from "react";

const SocialTab = ({ data }) => {
  const [selectedData, setSelectedData] = useState([]);

  const transformData = (data) => {
    // Define the categories and their max values
    let categories = [
      { key: "road_safety", label: "Road Safety", maxValue: 10 },
      { key: "electrical_safety", label: "Electrical Safety", maxValue: 10 },
      { key: "fire_safety", label: "Fire Safety", maxValue: 10 },
      {
        key: "5s",
        label: "5S & Housekeeping ",
        maxValue: 10,
      },
      { key: "personnel_safety", label: "Personal Safety", maxValue: 10 },
    ];

    // Check if 'personnel_safety' exists in any data entry
    const hasPersonalSafety = data.some(
      (item) => item.personnel_safety !== undefined
    );

    // Filter out 'Personal Safety' if no relevant data is found
    if (!hasPersonalSafety) {
      categories = categories.filter(
        (category) => category.key !== "personnel_safety"
      );
    }

    // Helper function to clamp values between 0 and maxValue for graph display
    const clampForGraph = (value) => Math.max(0, value);
    const clampRemaining = (avgValue, maxValue) =>
      Math.max(0, Math.min(maxValue, maxValue - avgValue));

    return categories.map(({ key, label, maxValue }) => {
      const avgValue =
        data.reduce((sum, item) => sum + (item[key] || 0), 0) / data.length;

      return {
        category: label,
        avgValue: parseFloat(avgValue.toFixed(2)),
        avgValueForGraph: clampForGraph(parseFloat(avgValue.toFixed(2))),
        maxValue,
        remainingToMax: clampRemaining(parseFloat(avgValue.toFixed(2)), maxValue),
      };
    });
  };

  useEffect(() => {
    setSelectedData(transformData(data));
  }, [data]);

  const getYAxisDomain = () => {
    return [0, 12];
  };

  const salesYAxisDomain = getYAxisDomain();

  const CustomLegend = (props) => {
    const { payload } = props;
    return (
      <ul
        style={{
          display: "flex",
          listStyleType: "none",
          justifyContent: "center",
          padding: 0,
        }}
      >
        {payload.map((entry, index) => (
          <li
            key={`item-${index}`}
            style={{
              color: entry.color,
              marginRight: "5px",
            }}
          >
            <span
              style={{
                color: entry.color,
                backgroundColor: entry.color,
                marginRight: 4,
                fontSize: "20px",
                width: "10px",
                height: "10px",
                borderRadius: "50%",
                display: "inline-block",
              }}
            ></span>
            <span style={{ color: "#555", fontSize: "14px" }}>
              {entry.value}
            </span>
          </li>
        ))}
      </ul>
    );
  };

  //   let words = text.split(" ");
  //   let lines = [];
  //   let currentLine = "";

  //   words.forEach((word) => {
  //     if ((currentLine + " " + word).length > width) {
  //       lines.push(currentLine);
  //       currentLine = word;
  //     } else {
  //       currentLine += (currentLine ? " " : "") + word;
  //     }
  //   });

  //   lines.push(currentLine);
  //   return lines.map((line, index) => (
  //     <tspan key={index} x="0" dy={index === 0 ? "0" : "12"}>
  //       {line}
  //     </tspan>
  //   ));
  // };

  // const CustomizedTick = ({ x, y, payload }) => {
  //   return (
  //     <g transform={`translate(${x},${y})`}>
  //       <text
  //         x={0}
  //         y={0}
  //         dy={15} // Adjust spacing for better visibility
  //         textAnchor="middle"
  //         fontSize={10}
  //         fill="#666"
  //       >
  //         {wrapText(payload.value, 12)}
  //       </text>
  //     </g>
  //   );
  // };

  return (
    <div
      style={{
        display: "flex",
        justifyContent: "space-between",
        width: "100%",
        padding: "20px",
      }}
    >
      <div style={{ width: "100%", height: 400 }}>
        <h3 style={{ textAlign: "center", marginBottom: 20, color: "#555" }}>
          Social Management
        </h3>
        <ResponsiveContainer width="100%" height="100%">
          <BarChart
            data={selectedData}
            barSize={60}
            margin={{ top: 20, right: 30, left: 20, bottom: 15 }}
          >
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="category" tick={{ fontSize: 7, width: "25px" }} />
            <YAxis domain={salesYAxisDomain} />
            <Tooltip
              formatter={(value, name, props) => {
                const { payload } = props;
                if (name === "Achieved") {
                  return [
                    `${payload.avgValue.toFixed(2)} (Max: ${payload.maxValue})`,
                    name,
                  ];
                }
                if (name === "Maximum") {
                  return [
                    `Remaining: ${payload.remainingToMax.toFixed(2)}`,
                    name,
                  ];
                }
                return [value, name];
              }}
            />

            <Legend content={CustomLegend} />
            <Bar
              dataKey="avgValueForGraph"
              stackId="score"
              fill="#FC6E51"
              name="Achieved"
              label={{
                position: "insideTop",
                fill: "#fff",
                formatter: (value) => value.toFixed(1),
              }}
            />
            <Bar
              dataKey="remainingToMax"
              stackId="score"
              fill="#FEB2A8"
              name="Maximum"
            />
          </BarChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
};

export default SocialTab;
