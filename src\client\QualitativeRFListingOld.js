import Axios from "axios";
import React, { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import useForceUpdate from "use-force-update";
import { Dropdown } from "primereact/dropdown";
import { EditText } from "react-edit-text";
import { Button } from "primereact/button";
import { Calendar } from "primereact/calendar";
import { InputTextarea } from "primereact/inputtextarea";
import { Dialog } from "primereact/dialog";
import { InputText } from "primereact/inputtext";
import { CascadeSelect } from "primereact/cascadeselect";
import Swal from "sweetalert2";
import { Checkbox } from 'primereact/checkbox';
import $, { data } from "jquery";
import { API } from "../constants/api_url";
import { RadioButton } from "primereact/radiobutton";
import LazyView from "../components/LazyView";
import { MultiSelect } from 'primereact/multiselect';
import moment from "moment";
import { DataTable } from "primereact/datatable";
import { Column } from "primereact/column";
import { useHistory } from "react-router-dom/cjs/react-router-dom.min";
import { Tooltip } from 'primereact/tooltip';
import APIServices from "../service/APIService";
import { TabMenu } from "primereact/tabmenu";

window.jQuery = $;
window.$ = $;
let activeRowData = {}
const QualitativeRFListing = () => {
    const [rfass, setRFAss] = useState([])
   
    const [activeindex, setActiveIndex] = useState(0)
    const [requiredList, setRequiredList] = useState([])
    const [requiredListBK, setRequiredListBK] = useState([])
    const [requiredList2, setRequiredList2] = useState([])
    const [requiredList2BK, setRequiredList2BK] = useState([])
    const [selectedFramework, setSelectedFramework] = useState('All')
    const [assFramework, setAssFramework] = useState([])
    const [selectedFramework2, setSelectedFramework2] = useState('All')
    const [assFramework2, setAssFramework2] = useState([])
    const selector = useSelector((state) => state.user.userdetail);
    const userList = useSelector(state => state.userlist.userList)
    const [user, setUser] = useState(null);
    const [list, setList] = useState({ category: null, topic: null, metric: null, framework: null })
    const [selected, setSelected] = useState({ category: [], topic: [], framework: [], metric: [] })
    const navigate = useHistory()
    const [selDataPoint, setSelDataPoint] = useState([])
    const configtype = [{ name: 'Location' }, { name: 'Data Point' }]
    const forceUpdate = useForceUpdate();
    const [editmode, setEditMode] = useState(false)
    const [old, setOld] = useState([]);
    const [datapoint, setDataPoint] = useState([]);
    const [metriclist, setMetricList] = useState([]);
    const [metricbk, setMetricBk] = useState([])
    const [raw, setRaw] = useState([])
    const [usermodal, setUserModal] = useState(false)
    const [selectedrf, setSelectedRF] = useState({})
    const [response, setResponse] = useState([])
    const [rfresponse, setRFResponse] = useState([])
    const [historydata, setHistoryData] = useState([])
    const [historydialog, setHistoryDialog] = useState(false)
    const [usermetric, setUserMetric] = useState([])
    const [rawrf, setRawRF] = useState([])
    const [dupdpiddialog, setDupDPIDDialog] = useState(false)
    const [dupdpid, setDupId] = useState([])
    const [selecteddcf, setSelectedDCF] = useState([]);
    const [selecteddcfbk, setSelectedDCFBK] = useState([]);
    const [selectedlist, setSelectedList] = useState({ title: '', data: [] })
    const [prevdialog, setPrevDialog] = useState(false);
    const [search, setSearch] = useState({ metric: '', dcf: '' })
    const [location, setLocation] = useState([]);
    const [overallmetric, setOverallMetric] = useState([]);
    const [userConfig, setUserConfig] = useState({
        name: "", type: "",
        location: ''
    });
    const items = [
        { label: 'Required Disclosure' },
        { label: 'Optional Disclosure' }

    ];
    const [module, setModule] = useState({
        tier1: "",
        tier2: "",
        tier3: "",
    });
    const [cascade, setCascade] = useState("");
    const [showSave, setShowSave] = useState(0);

    const [tier2, setTier2] = useState([]);
    const [tier3, setTier3] = useState([]);
    const [moduleList, setModuleList] = useState({
        mod: [],
        title: [],
        topic: [],
    });

    useEffect(async () => {
        setOld(selector.information);
        APIServices.get(API.RF_User_UP(selector.id)).then((k) => {
            setRFAss(k.data)
        })

        let uriString = {
            "include": [{ "relation": "newTopics", "scope": { "include": [{ "relation": "newMetrics", "scope": { "include": [{ "relation": "newDataPoints" }] } }] } }]

        }
        let uriString2 = {
            "include": [{ "relation": "newTopics", "scope": { "include": [{ "relation": "newMetrics", "scope": { "include": [{ "relation": "newDataPoints" }] } }] } }]


        }

        let promise1 = APIServices.get(API.RF)


        let promise2 = APIServices.get(API.RF_Submit_UP(selector.id))
        let promise3 = APIServices.get(API.Report_Name_Twos)
        let promise5 = APIServices.get(API.QL_Listing_Filter_UP(selector.id))
        let promise6 = APIServices.get(API.AssignDCFClient_UP(selector.id))
        let Overall = API.Categories + `?filter=${encodeURIComponent(JSON.stringify(uriString2))}`;

        let url = API.Categories + `?filter=${encodeURIComponent(JSON.stringify(uriString))}`;
        let promise4 = APIServices.get(Overall)
        let promise7 = APIServices.get(url)
        Promise.all([promise1, promise2, promise3, promise4, promise5, promise6, promise7]).then(function (values) {
            let topic_ids = values[5].data[0].topic_ids
            let required_rf = [], optional_rf = []
            setRawRF(values[0].data)
            setRFResponse(values[1].data)
            values[6].data.forEach((cat) => {
                if (cat.newTopics) {
                    cat.newTopics.forEach((topic) => {
                        if (topic_ids.includes(topic.id) && topic.newMetrics) {
                            topic.newMetrics.forEach((metric) => {
                                if (Array.isArray(metric.data1) && metric.data1[0] !== undefined && metric.data1[0].type === 1 && values[0].data.map(i => i.id).includes(metric.data1[0].rf) && (metric.tag === null || parseFloat(metric.tag) === selector.id ) ) {
                                    console.log(values[0].data.find(i => i.id === metric.data1[0].rf))
                                    required_rf.push({ cat_id: cat.id, top_id: topic.id, overallTags: [metric.data1[0].tags1, metric.data1[0].tags2, metric.data1[0].tags3], cat_title: cat.title, top_title: topic.title, title: metric.title, ...metric })

                                }
                            })
                        } else if (!topic_ids.includes(topic.id) && topic.newMetrics) {
                            topic.newMetrics.forEach((metric) => {
                                if (Array.isArray(metric.data1) && metric.data1[0] !== undefined && metric.data1[0].type === 1 && values[0].data.map(i => i.id).includes(metric.data1[0].rf) && (metric.tag === null || parseFloat(metric.tag) === selector.id ) ) {
                                    console.log(values[0].data.find(i => i.id === metric.data1[0].rf))
                                    optional_rf.push({ cat_id: cat.id, top_id: topic.id, overallTags: [metric.data1[0].tags1, metric.data1[0].tags2, metric.data1[0].tags3], cat_title: cat.title, top_title: topic.title, title: metric.title, ...metric })

                                }
                            })
                        }
                    }
                    )
                }
            })
            console.log(values[2].data, selector.information.report, values[2].data.filter((i) => { return selector.information.report.includes(i.id) }))
            setAssFramework(values[2].data.filter((i) => { return selector.information.report.includes(i.id) }))
            setAssFramework2(values[2].data)
            setRequiredList2(optional_rf.filter((i) => { return !required_rf.map(i => i.id).includes(i.id) }))
            setRequiredList2BK(optional_rf.filter((i) => { return !required_rf.map(i => i.id).includes(i.id) }))
            setRequiredList(required_rf)
            setRequiredListBK(required_rf)
        })

    }, [selector]);

    const renderResponseTable = (id, category, topic, frameworks, resdata, listdata, frameList) => {
        let loc = selected, topicList = [], framework = [], categoryList = []
        let loclist = list
        loc['id'] = id;
        loc.category = category
        loc.topic = topic
        loc.framework = frameworks
        resdata.forEach((cat) => {
            if (cat.newTopics !== undefined) {
                categoryList.push(cat)
                if (loc.category.includes(cat.id)) {
                    cat.newTopics.forEach((topic) => {
                        topic.newMetrics !== undefined && topicList.push({ name: topic.title, title: topic.title + "(" + cat.title + ")", id: topic.id })
                        if (topic.newMetrics !== undefined && loc.topic.includes(topic.id)) {

                            topic.newMetrics.forEach((metric) => {
                                if (Array.isArray(metric.data1) && metric.data1.length !== 0) {
                                    if (Object.keys(metric.data1[0]).length > 2) {
                                        if (metric.data1[0].type === 1 && metric.data1[0].rf !== null) {

                                            // let index = framework.findIndex((k) => { return k.cat_id === cat.id && k.top_id === topic.id })
                                            // if (index !== -1) {
                                            //     framework[index].metric.push(metric)
                                            // } else {
                                            let tag = []
                                            console.log(loc.framework, listdata.framework)
                                            let tags = JSON.parse(JSON.stringify(loc.framework)).map((fw) => { return listdata.framework.filter((k) => { return k.id === fw })[0].title.trim().toLowerCase() })
                                            let frame = JSON.parse(JSON.stringify(loc.framework)).map((fw) => { return listdata.framework.filter((k) => { return k.id === fw })[0] })


                                            let one = metric.data1[0].tags1.some((str) => tags.some((fruit) => str.toLowerCase().includes(fruit.toLowerCase())))
                                            let two = metric.data1[0].tags2.some((str) => tags.some((fruit) => str.toLowerCase().includes(fruit.toLowerCase())))
                                            let three = metric.data1[0].tags3.some((str) => tags.some((fruit) => str.toLowerCase().includes(fruit.toLowerCase())))
                                            const matchedTag1 = metric.data1[0].tags1.filter((str) => tags.some((fruit) => str.toLowerCase().includes(fruit.toLowerCase())));
                                            const matchedTag2 = metric.data1[0].tags2.filter((str) => tags.some((fruit) => str.toLowerCase().includes(fruit.toLowerCase())));
                                            const matchedTag3 = metric.data1[0].tags3.filter((str) => tags.some((fruit) => str.toLowerCase().includes(fruit.toLowerCase())));

                                            frame.forEach((t) => {
                                                matchedTag1.forEach((i) => {

                                                    if (i.trim().toLowerCase().includes(t.title.trim().toLowerCase())) {
                                                        !tag.includes(t.id) && tag.push(t.id)
                                                    }


                                                })
                                                matchedTag2.forEach((i) => {

                                                    if (i.trim().toLowerCase().includes(t.title.trim().toLowerCase())) {
                                                        !tag.includes(t.id) && tag.push(t.id)
                                                    }


                                                })
                                                matchedTag3.forEach((i) => {

                                                    if (i.trim().toLowerCase().includes(t.title.trim().toLowerCase())) {
                                                        !tag.includes(t.id) && tag.push(t.id)
                                                    }


                                                })
                                            })
                                            console.log(matchedTag1, matchedTag2, matchedTag3, tag)
                                            if (one || two || three) {
                                                framework.push({ tags: tag, overallTags: [metric.data1[0].tags1, metric.data1[0].tags2, metric.data1[0].tags3], cat_id: cat.id, top_id: topic.id, cat_title: cat.title, top_title: topic.title, ...metric })
                                            }
                                            // }

                                        }
                                    }
                                }

                            })
                        }
                    })
                }
            }


        })
        loclist.metric = framework
        loclist.category = categoryList
        loclist.topic = topicList
        loc.category = categoryList.filter((i) => { return loc.category.includes(i.id) }).map((k) => { return k.id })
        loc['topic'] = topicList.filter((i) => { return loc.topic.includes(i.id) }).map((k) => { return k.id })
        loc.framework = frameList.filter((i) => { return loc.framework.includes(i.id) }).map((k) => { return k.id })
        console.log(categoryList)
        setSelected(loc)

        setList(loclist)
        forceUpdate()
    }

    const updateSelected = (obj, val) => {
        let loc = selected;
        loc[obj] = val;
        console.log(val)
        let ser = search
        ser.dcf = ''
        ser.metric = ''
        let categoryList = [], metricList = [], topicList = [], userSelectedMetric = []
        let loclist = list


        setSearch(ser)
        if (obj === 'user') {
            APIServices.get(API.AssignDCFClient_UP(val.id)).then((res) => {
                setEditMode(res.data.length === 0 ? false : true)

                if (res.data.length !== 0) {



                    response.forEach((cat) => {
                        if (cat.newTopics !== undefined) {
                            categoryList.push({ id: cat.id, title: cat.title })
                            cat.newTopics.forEach((topic) => {
                                topic.newMetrics !== undefined && topicList.push({ name: topic.title, title: topic.title + "(" + cat.title + ")", id: topic.id })
                                if (topic.newMetrics !== undefined) {

                                    topic.newMetrics.forEach((metric) => {
                                        metricList.push(metric)
                                    })
                                }
                            })
                        }


                    })
                    let loc = JSON.parse(JSON.stringify(metricList)).map(k => { return { title: k.title, id: k.id, selected: false } })

                    res.data.forEach((item) => {
                        userSelectedMetric = item.metric_ids
                    })
                    setUserMetric(userSelectedMetric)
                    loclist.category = categoryList
                    loclist.metric = metricList
                    loclist.topic = topicList

                    setList(loclist)

                    forceUpdate()
                }
            })
        } else if (obj === 'category') {
            let framework = []
            response.forEach((cat) => {
                if (cat.newTopics !== undefined && val.includes(cat.id)) {

                    cat.newTopics.forEach((topic) => {
                        topic.newMetrics !== undefined && topicList.push({ name: topic.title, title: topic.title + "(" + cat.title + ")", id: topic.id })
                        if (topic.newMetrics !== undefined && loc.topic.includes(topic.id)) {

                            topic.newMetrics.forEach((metric) => {
                                if (Array.isArray(metric.data1) && metric.data1.length !== 0) {
                                    if (Object.keys(metric.data1[0]).length > 2) {
                                        if (metric.data1[0].type === 1 && metric.data1[0].rf !== null) {

                                            // let index = framework.findIndex((k) => { return k.cat_id === cat.id && k.top_id === topic.id })
                                            // if (index !== -1) {
                                            //     framework[index].metric.push(metric)
                                            // } else {
                                            let tag = []
                                            let tags = JSON.parse(JSON.stringify(loc.framework)).map((fw) => { return list.framework.filter((k) => { return k.id === fw })[0].title.trim().toLowerCase() })
                                            let frame = JSON.parse(JSON.stringify(loc.framework)).map((fw) => { return list.framework.filter((k) => { return k.id === fw })[0] })


                                            let one = metric.data1[0].tags1.some((str) => tags.some((fruit) => str.toLowerCase().includes(fruit.toLowerCase())))
                                            let two = metric.data1[0].tags2.some((str) => tags.some((fruit) => str.toLowerCase().includes(fruit.toLowerCase())))
                                            let three = metric.data1[0].tags3.some((str) => tags.some((fruit) => str.toLowerCase().includes(fruit.toLowerCase())))
                                            const matchedTag1 = metric.data1[0].tags1.filter((str) => tags.some((fruit) => str.toLowerCase().includes(fruit.toLowerCase())));
                                            const matchedTag2 = metric.data1[0].tags2.filter((str) => tags.some((fruit) => str.toLowerCase().includes(fruit.toLowerCase())));
                                            const matchedTag3 = metric.data1[0].tags3.filter((str) => tags.some((fruit) => str.toLowerCase().includes(fruit.toLowerCase())));

                                            frame.forEach((t) => {
                                                matchedTag1.forEach((i) => {

                                                    if (i.trim().toLowerCase().includes(t.title.trim().toLowerCase())) {
                                                        !tag.includes(t.id) && tag.push(t.id)
                                                    }


                                                })
                                                matchedTag2.forEach((i) => {

                                                    if (i.trim().toLowerCase().includes(t.title.trim().toLowerCase())) {
                                                        !tag.includes(t.id) && tag.push(t.id)
                                                    }


                                                })
                                                matchedTag3.forEach((i) => {

                                                    if (i.trim().toLowerCase().includes(t.title.trim().toLowerCase())) {
                                                        !tag.includes(t.id) && tag.push(t.id)
                                                    }


                                                })
                                            })
                                            console.log(matchedTag1, matchedTag2, matchedTag3, tag)
                                            if (one || two || three) {
                                                framework.push({ tags: tag, overallTags: [metric.data1[0].tags1, metric.data1[0].tags2, metric.data1[0].tags3], cat_id: cat.id, top_id: topic.id, cat_title: cat.title, top_title: topic.title, ...metric })
                                            }
                                            // }

                                        }
                                    }
                                }
                            })
                        }
                    })
                }


            })

            loclist.metric = framework
            loclist.topic = topicList
            loc['topic'] = topicList.filter((i) => { return loc.topic.includes(i.id) }).map((k) => { return k.id })



            setList(loclist)
        } else if (obj === 'topic') {
            let framework = []
            response.forEach((cat) => {
                if (cat.newTopics !== undefined) {

                    cat.newTopics.forEach((topic) => {
                        topic.newMetrics !== undefined && topicList.push({ name: topic.title, title: topic.title + "(" + cat.title + ")", id: topic.id })
                        if (topic.newMetrics !== undefined && val.includes(topic.id)) {

                            topic.newMetrics.forEach((metric) => {
                                if (Array.isArray(metric.data1) && metric.data1.length !== 0) {
                                    if (Object.keys(metric.data1[0]).length > 2) {
                                        if (metric.data1[0].type === 1 && metric.data1[0].rf !== null) {

                                            // let index = framework.findIndex((k) => { return k.cat_id === cat.id && k.top_id === topic.id })
                                            // if (index !== -1) {
                                            //     framework[index].metric.push(metric)
                                            // } else {
                                            let tag = []
                                            let tags = JSON.parse(JSON.stringify(loc.framework)).map((fw) => { return list.framework.filter((k) => { return k.id === fw })[0].title.trim().toLowerCase() })
                                            let frame = JSON.parse(JSON.stringify(loc.framework)).map((fw) => { return list.framework.filter((k) => { return k.id === fw })[0] })


                                            let one = metric.data1[0].tags1.some((str) => tags.some((fruit) => str.toLowerCase().includes(fruit.toLowerCase())))
                                            let two = metric.data1[0].tags2.some((str) => tags.some((fruit) => str.toLowerCase().includes(fruit.toLowerCase())))
                                            let three = metric.data1[0].tags3.some((str) => tags.some((fruit) => str.toLowerCase().includes(fruit.toLowerCase())))
                                            const matchedTag1 = metric.data1[0].tags1.filter((str) => tags.some((fruit) => str.toLowerCase().includes(fruit.toLowerCase())));
                                            const matchedTag2 = metric.data1[0].tags2.filter((str) => tags.some((fruit) => str.toLowerCase().includes(fruit.toLowerCase())));
                                            const matchedTag3 = metric.data1[0].tags3.filter((str) => tags.some((fruit) => str.toLowerCase().includes(fruit.toLowerCase())));

                                            frame.forEach((t) => {
                                                matchedTag1.forEach((i) => {

                                                    if (i.trim().toLowerCase().includes(t.title.trim().toLowerCase())) {
                                                        !tag.includes(t.id) && tag.push(t.id)
                                                    }


                                                })
                                                matchedTag2.forEach((i) => {

                                                    if (i.trim().toLowerCase().includes(t.title.trim().toLowerCase())) {
                                                        !tag.includes(t.id) && tag.push(t.id)
                                                    }


                                                })
                                                matchedTag3.forEach((i) => {

                                                    if (i.trim().toLowerCase().includes(t.title.trim().toLowerCase())) {
                                                        !tag.includes(t.id) && tag.push(t.id)
                                                    }


                                                })
                                            })
                                            console.log(matchedTag1, matchedTag2, matchedTag3, tag)
                                            if (one || two || three) {
                                                framework.push({ tags: tag, overallTags: [metric.data1[0].tags1, metric.data1[0].tags2, metric.data1[0].tags3], cat_id: cat.id, top_id: topic.id, cat_title: cat.title, top_title: topic.title, ...metric })
                                            }
                                            // }

                                        }
                                    }
                                }
                            })
                        }
                    })
                }


            })
            console.log(loc)
            loclist.metric = framework


            setList(loclist)
        } else if (obj === 'framework') {
            let framework = []
            response.forEach((cat) => {
                if (cat.newTopics !== undefined) {

                    cat.newTopics.forEach((topic) => {
                        topic.newMetrics !== undefined && topicList.push({ name: topic.title, title: topic.title + "(" + cat.title + ")", id: topic.id })
                        if (topic.newMetrics !== undefined && selected.topic.includes(topic.id)) {

                            topic.newMetrics.forEach((metric) => {
                                if (Array.isArray(metric.data1) && metric.data1.length !== 0) {
                                    if (Object.keys(metric.data1[0]).length > 2) {
                                        if (metric.data1[0].type === 1 && metric.data1[0].rf !== null) {

                                            // let index = framework.findIndex((k) => { return k.cat_id === cat.id && k.top_id === topic.id })
                                            // if (index !== -1) {
                                            //     framework[index].metric.push(metric)
                                            // } else {
                                            let tag = []
                                            let tags = JSON.parse(JSON.stringify(val)).map((fw) => { return list.framework.filter((k) => { return k.id === fw })[0].title.trim().toLowerCase() })
                                            let frame = JSON.parse(JSON.stringify(val)).map((fw) => { return list.framework.filter((k) => { return k.id === fw })[0] })


                                            let one = metric.data1[0].tags1.some((str) => tags.some((fruit) => str.toLowerCase().includes(fruit.toLowerCase())))
                                            let two = metric.data1[0].tags2.some((str) => tags.some((fruit) => str.toLowerCase().includes(fruit.toLowerCase())))
                                            let three = metric.data1[0].tags3.some((str) => tags.some((fruit) => str.toLowerCase().includes(fruit.toLowerCase())))
                                            const matchedTag1 = metric.data1[0].tags1.filter((str) => tags.some((fruit) => str.toLowerCase().includes(fruit.toLowerCase())));
                                            const matchedTag2 = metric.data1[0].tags2.filter((str) => tags.some((fruit) => str.toLowerCase().includes(fruit.toLowerCase())));
                                            const matchedTag3 = metric.data1[0].tags3.filter((str) => tags.some((fruit) => str.toLowerCase().includes(fruit.toLowerCase())));

                                            frame.forEach((t) => {
                                                matchedTag1.forEach((i) => {

                                                    if (i.trim().toLowerCase().includes(t.title.trim().toLowerCase())) {
                                                        !tag.includes(t.id) && tag.push(t.id)
                                                    }


                                                })
                                                matchedTag2.forEach((i) => {

                                                    if (i.trim().toLowerCase().includes(t.title.trim().toLowerCase())) {
                                                        !tag.includes(t.id) && tag.push(t.id)
                                                    }


                                                })
                                                matchedTag3.forEach((i) => {

                                                    if (i.trim().toLowerCase().includes(t.title.trim().toLowerCase())) {
                                                        !tag.includes(t.id) && tag.push(t.id)
                                                    }


                                                })
                                            })
                                            console.log(matchedTag1, matchedTag2, matchedTag3, tag)
                                            if (one || two || three) {
                                                framework.push({ tags: tag, overallTags: [metric.data1[0].tags1, metric.data1[0].tags2, metric.data1[0].tags3], cat_id: cat.id, top_id: topic.id, cat_title: cat.title, top_title: topic.title, ...metric })
                                            }
                                            // }

                                        }
                                    }
                                }

                            })
                        }
                    })
                }


            })

            loclist.metric = framework
            console.log(framework)

            setList(loclist)
        }
        setSelected(loc)


        forceUpdate();
    };
    const prevDialogFooter = (
        <>
            <Button label="Cancel" icon="pi pi-times" className="p-button-text" onClick={() => { setPrevDialog(false) }} />
        </>
    );
    const dupdpidDialogFooter = (
        <>
            <Button label="Cancel" icon="pi pi-times" className="p-button-text" onClick={() => { setDupDPIDDialog(false) }} />
        </>
    );


    const removeHTMLTag = (html) => {
        return html.replace(/(<([^>]+)>)/gi, "")
            .replace(/\n/g, " ")
            .replace(/&nbsp;/g, " ")
    }
    const renderPreview = () => {


        return (
            <div className="col-12 grid" >
                {selectedlist.data.map((i, j) => {
                    return (
                        <label className="col-12">{j + 1}. &nbsp; {i.title}</label>
                    )
                })

                }
            </div>
        )
    }
    const checkDCFForDPDuplication = () => {
        let metrics = JSON.parse(JSON.stringify(metricbk));
        let dcf = JSON.parse(JSON.stringify(selecteddcfbk));
        let selectedMetric = metrics.filter((i) => { return i.selected }).map((j) => { return j.id })
        let selectedDataPoints = []
        raw.forEach((k) => {
            if (selectedMetric.findIndex((ind) => { return ind === k.id }) !== -1) {
                if (k.newDataPoints !== undefined) {
                    k.newDataPoints.forEach((k) => {
                        selectedDataPoints.push(k.suffix)
                    })

                }
            }
        })
        let dps = [], duplicatedids = []
        selectedDataPoints.forEach((i) => {
            if (!dps.includes(i)) {
                dps.push(i)
            } else {
                if (!duplicatedids.includes(i)) {
                    duplicatedids.push(i)
                }
            }
        })

        if (dps.length === selectedDataPoints.length) {
            Swal.fire({
                position: "center",
                icon: "success",
                title: `No Duplicates Found`,
                showConfirmButton: false,
                timer: 1500,
            });
        } else {
            console.log(duplicatedids, 'ids')
            setDupId(duplicatedids)
            setDupDPIDDialog(true)

        }

    }
    const checkDCFForDPDuplication_ = () => {
        let metrics = JSON.parse(JSON.stringify(metricbk));
        let dcf = JSON.parse(JSON.stringify(selecteddcfbk));
        let selectedMetric = metrics.filter((i) => { return i.selected }).map((j) => { return j.id })
        let selectedDataPoints = []
        raw.forEach((k) => {
            if (selectedMetric.findIndex((ind) => { return ind === k.id }) !== -1) {
                if (k.newDataPoints !== undefined) {
                    k.newDataPoints.forEach((k) => {
                        selectedDataPoints.push(k.suffix)
                    })

                }
            }
        })
        let dps = [], duplicatedids = []
        selectedDataPoints.forEach((i) => {
            if (!dps.includes(i)) {
                dps.push(i)
            } else {
                if (!duplicatedids.includes(i)) {
                    duplicatedids.push(i)
                }
            }
        })

        if (dps.length === selectedDataPoints.length) {
            return true
        } else {
            return false
        }

    }
    const saveAssignedDCF = () => {
        let metrics = JSON.parse(JSON.stringify(metricbk));
        let dcf = JSON.parse(JSON.stringify(selecteddcfbk));
        let selectedMetric = metrics.filter((i) => { return i.selected }).map((j) => { return j.id })
        let selectedDataPoints = []
        raw.forEach((k) => {
            if (selectedMetric.findIndex((ind) => { return ind === k.id }) !== -1) {
                if (k.newDataPoints !== undefined) {
                    k.newDataPoints.forEach((k) => {
                        selectedDataPoints.push(k.id)
                    })

                }
            }
        })
        let selectedDCF = dcf.map((k) => { return k.id })
        if (selectedMetric.length !== 0 && selectedDCF.length !== 0 && checkDCFForDPDuplication_()) {
            if (editmode) {
                APIServices.patch(API.AssignDCFClient_UP(userConfig.name.id), { dcf_ids: selectedDCF, dp_ids: selectedDataPoints, metric_ids: selectedMetric, user_id: selector.id }).then((a) => {
                    console.log(a)
                    Swal.fire({
                        position: "center",
                        icon: "success",
                        title: `Data updated successfully`,
                        showConfirmButton: false,
                        timer: 1500,
                    });
                })
            } else {
                APIServices.post(API.AssignDCFClient_UP(userConfig.name.id), { dcf_ids: selectedDCF, dp_ids: selectedDataPoints, metric_ids: selectedMetric, user_id: selector.id, created: moment.utc() }).then((a) => {
                    Swal.fire({
                        position: "center",
                        icon: "success",
                        title: `Data saved successfully`,
                        showConfirmButton: false,
                        timer: 1500,
                    });
                })
            }
        } else {
            if (!checkDCFForDPDuplication_()) {
                checkDCFForDPDuplication()
            } else {
                Swal.fire({
                    position: "center",
                    icon: "error",
                    title: `Unable to save, DCF not assigned / not empty `,
                    showConfirmButton: false,
                    timer: 1500,
                })
            }
        }


    }
    const listingMulitSelectTemplate = (option) => {
        if (option) {
            return (
                <div >
                    <span class="p-multiselect-token-label">{option.name}</span>
                    {/* <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg" class="p-icon p-multiselect-token-icon" aria-hidden="true"><g clip-path="url(#pr_icon_clip_2)"><path fill-rule="evenodd" clip-rule="evenodd" d="M7 14C5.61553 14 4.26215 13.5895 3.11101 12.8203C1.95987 12.0511 1.06266 10.9579 0.532846 9.67879C0.00303296 8.3997 -0.13559 6.99224 0.134506 5.63437C0.404603 4.2765 1.07129 3.02922 2.05026 2.05026C3.02922 1.07129 4.2765 0.404603 5.63437 0.134506C6.99224 -0.13559 8.3997 0.00303296 9.67879 0.532846C10.9579 1.06266 12.0511 1.95987 12.8203 3.11101C13.5895 4.26215 14 5.61553 14 7C14 8.85652 13.2625 10.637 11.9497 11.9497C10.637 13.2625 8.85652 14 7 14ZM7 1.16667C5.84628 1.16667 4.71846 1.50879 3.75918 2.14976C2.79989 2.79074 2.05222 3.70178 1.61071 4.76768C1.16919 5.83358 1.05367 7.00647 1.27876 8.13803C1.50384 9.26958 2.05941 10.309 2.87521 11.1248C3.69102 11.9406 4.73042 12.4962 5.86198 12.7212C6.99353 12.9463 8.16642 12.8308 9.23232 12.3893C10.2982 11.9478 11.2093 11.2001 11.8502 10.2408C12.4912 9.28154 12.8333 8.15373 12.8333 7C12.8333 5.45291 12.2188 3.96918 11.1248 2.87521C10.0308 1.78125 8.5471 1.16667 7 1.16667ZM4.66662 9.91668C4.58998 9.91704 4.51404 9.90209 4.44325 9.87271C4.37246 9.84333 4.30826 9.8001 4.2544 9.74557C4.14516 9.6362 4.0838 9.48793 4.0838 9.33335C4.0838 9.17876 4.14516 9.0305 4.2544 8.92113L6.17553 7L4.25443 5.07891C4.15139 4.96832 4.09529 4.82207 4.09796 4.67094C4.10063 4.51982 4.16185 4.37563 4.26872 4.26876C4.3756 4.16188 4.51979 4.10066 4.67091 4.09799C4.82204 4.09532 4.96829 4.15142 5.07887 4.25446L6.99997 6.17556L8.92106 4.25446C9.03164 4.15142 9.1779 4.09532 9.32903 4.09799C9.48015 4.10066 9.62434 4.16188 9.73121 4.26876C9.83809 4.37563 9.89931 4.51982 9.90198 4.67094C9.90464 4.82207 9.84855 4.96832 9.74551 5.07891L7.82441 7L9.74554 8.92113C9.85478 9.0305 9.91614 9.17876 9.91614 9.33335C9.91614 9.48793 9.85478 9.6362 9.74554 9.74557C9.69168 9.8001 9.62748 9.84333 9.55669 9.87271C9.4859 9.90209 9.40996 9.91704 9.33332 9.91668C9.25668 9.91704 9.18073 9.90209 9.10995 9.87271C9.03916 9.84333 8.97495 9.8001 8.9211 9.74557L6.99997 7.82444L5.07884 9.74557C5.02499 9.8001 4.96078 9.84333 4.88999 9.87271C4.81921 9.90209 4.74326 9.91704 4.66662 9.91668Z" fill="currentColor"></path></g><defs><clipPath id="pr_icon_clip_2"><rect width="14" height="14" fill="white"></rect></clipPath></defs></svg> */}
                </div>
            );
        }

        return 'Select SDGs';
    };
    const checkStandlone = (metric) => {
        if (Array.isArray(metric.data1) && metric.data1.length !== 0) {
            if (metric.data1[0].source === undefined) {
                return false
            } else if (metric.data1[0].source === 1) {
                return true
            }
        }
    }
    const rrTemplate = (rowData) => {
        let data = rowData.data1[0], oldData = [], id = 0, show = true

        let index = rfresponse.findLastIndex((i) => { return i.rfid === data.rf && rowData.top_id === i.topicId && rowData.id === i.indicatorId && rowData.cat_id === i.categoryId })
        if (index !== -1) {
            oldData = rfresponse[index]

        }

        return (
            < >
                <a style={{ cursor: 'pointer', textDecoration: 'underline' }} onClick={() => { navigate.push({ pathname: '/rf_input_entry/' + rowData.data1[0].rf, state: { data: rowData, oldData } }) }}>       {rowData.data1[0].title} </a>

            </>
        )
    }
    const historyTemplate = (rowData) => {
        let text = true
        let data = rowData.data1[0]
        let mergeData = []
        console.log(rowData)
        let index = rfresponse.findLastIndex((i) => { return i.rfid === data.rf && rowData.top_id === i.topicId && rowData.id === i.indicatorId && rowData.cat_id === i.categoryId })
        if (index !== -1) {
            text = false
            mergeData = rfresponse.filter((i) => { return i.rfid === data.rf && rowData.top_id === i.topicId && rowData.id === i.indicatorId && rowData.cat_id === i.categoryId })
            mergeData.forEach((i) => {
                i.top_title = rowData.top_title
                i.cat_title = rowData.cat_title
            })
        }
        return (
            <>
                {text ?
                    <span>NA</span> :
                    <a onClick={() => { setHistoryData(mergeData); activeRowData = rowData; setHistoryDialog(true) }}>View History</a>
                }

            </>
        )
    }
    const deleteRFAssignment = async (rassid, rid) => {
        const { value: accept } = await Swal.fire({
            title: `<div style="overflow:visible;font-size:20px;font-weight:600;margin-top:0px">Warning</div>`,
            html: `<div style="overflow:auto;max-height:200px" >Are you sure want to remove RF Assignment
          </div>`,

            confirmButtonColor: 'red',
            showCancelButton: true,
            confirmButtonText:
                'Remove',

        })
        if (accept) {
            console.log(rassid)
            APIServices.delete(API.RF_User_Edit(rassid)).then((l) => {
                let data_ = JSON.parse(JSON.stringify(rfass))
                data_.splice(rid, 1)
                setRFAss(data_)

            })
        }
    }
    const responsibilityTemplate = (rowData) => {
        // setUserModal(true); setSelectedRF(rowData);
        let index = 0
        let rf_index = rfass.findIndex((i) => { return i.rfid === rowData.data1[0].rf && i.reporter_ids !== null })
        if (rf_index !== -1) {
            index = userList.filter((i) => { return rfass[rf_index].reporter_ids.includes(i.id)  }).length
            console.log(index)
        }
        return (
            <div className="flex justify-content-center">
                {index !== 0 ?
                    <div style={{ display: 'flex', alignItems: 'center' }}>
                        {/* {userList[index].information.empname} */}
                        <i className="pi pi-pencil" onClick={() => { setUserModal(true); setUser(rfass[rf_index].reporter_ids); console.log(user); setSelectedRF(rowData) }} />
                        {/* <i className="material-icons" onClick={() => { deleteRFAssignment(rfass[rf_index].id, rf_index) }} style={{ color: 'red', cursor: 'pointer', fontSize: 14, marginLeft: 5 }}>close</i> */}
                    </div> :
                    <div style={{ display: 'flex', alignItems: 'center' }}>
                        <i className="material-icons" onClick={() => { setUserModal(true); setUser(null); setSelectedRF(rowData) }}>add</i>
                    </div>

                }
            </div>
        )
    }
    const titleTemplate = (rowData) => {

        return (
            < >
                <Tooltip className="tag-tooltip" target={".tags" + rowData.id} position={'top'} autoHide={true}> {rowData.overallTags.map((i, j) => {
                    if (i.length !== 0) {
                        return (
                            <>
                                <label style={{ color: 'black', display: 'flex' }}> {
                                    j === 0 ? 'Must Have' : j === 1 ? 'Progressive' : 'Advanced'

                                }
                                </label>
                                {
                                    i.map((tag, k) => {

                                        return (
                                            <label style={{ color: 'green' }}>{tag}{k !== i.length - 1 && ','}</label>
                                        )

                                    })
                                }
                                <div style={{ marginBottom: 10 }} />
                            </>
                        )
                    }
                })} </Tooltip>
                <div style={{ alignItems: 'center' }} >{rowData.title} <i className={"material-icons " + "tags" + rowData.id} style={{ fontSize: 14, cursor: 'pointer' }}>info</i>  </div>

            </>
        )
    }
    const lastResponse = (rowData) => {

        let text = 'Not Responded'
        let data = rowData.data1[0]

        let index = rfresponse.findLastIndex((i) => { return i.rfid === data.rf && rowData.top_id === i.topicId && rowData.id === i.indicatorId && rowData.cat_id === i.categoryId })
        if (index !== -1) {
            text = moment.utc(rfresponse[index].created_on).format('DD MMMM YYYY')

        }
        return (
            <>
                {text}
            </>
        )
    }
    const checkChildrenSelected = (metric) => {
        console.log(metric)

        if (Array.isArray(metric.data1) && metric.data1.length !== 0) {
            let children = metric.data1[0].indicator.filter((i) => { return usermetric.includes(i) })
            if (children.length !== 0) {
                if (!usermetric.includes(metric.id)) {
                    usermetric.push(metric.id)
                }
                return true
            } else {
                if (usermetric.includes(metric.id)) {
                    let index = usermetric.findIndex((i) => { return i === metric.id })
                    usermetric.splice(index, 1)
                }
            }
        }

        return false

    }
    const checkChildrenSelected_ = (metric) => {


        if (Array.isArray(metric.data1) && metric.data1.length !== 0) {
            let children = metric.data1[0].indicator.filter((i) => { return usermetric.includes(i) })
            if (children.length !== 0) {

                return true
            }
        }

        return false

    }
    const showList = (id) => {
        let loclist = selectedlist
        if (id === 1) {
            loclist.title = 'Selected Categories'
            loclist.data = list.category.filter((k) => { return selected.category.includes(k.id) })
        } else if (id === 2) {
            loclist.title = 'Selected Topics'
            loclist.data = list.topic.filter((k) => { return selected.topic.includes(k.id) })
        } else if (id === 3) {
            loclist.title = 'Selected Standards/Frameworks'
            loclist.data = list.framework.filter((k) => { return selected.framework.includes(k.id) })
        }
        setSelectedList(loclist)
        setPrevDialog(true)

    }
    const renderTable = (val) => {
        setSelectedFramework(val)
        let filtered = requiredListBK.filter((i) => { return (i.overallTags.some(array => array.some(item => item.includes(val))) || val === 'All') })
        setRequiredList(filtered)
    }
    const renderTable2 = (val) => {
        setSelectedFramework2(val)
        let filtered = requiredList2BK.filter((i) => { return (i.overallTags.some(array => array.some(item => item.includes(val))) || val === 'All') })
        setRequiredList2(filtered)
    }
    const AssignRFUser = () => {
        let loc = JSON.parse(JSON.stringify(rfass))
        let rf_index = loc.findIndex((k) => { return k.rfid === selectedrf.data1[0].rf })
        if (rf_index === -1) {
            let data = { reporter_ids: user, rfid: selectedrf.data1[0].rf, submitted_by: selector.id, created_on: moment.utc() }
            APIServices.post(API.RF_User_UP(selector.id), data).then((res) => {
                let rfass_ = JSON.parse(JSON.stringify(rfass))
                rfass_.push(res.data)
                loc.push(res.data)
                setRFAss(loc)
                setRFAss(rfass_)
                setUserModal(false)
                setUser(null)
                setList(loc)
            })
        } else {
            let data = { reporter_ids: user, submitted_by: selector.id, created_on: moment.utc() }
            APIServices.patch(API.RF_User_Edit(loc[rf_index].id), data).then((res) => {

                loc[rf_index].reporter_ids = user
                setRFAss(loc)

                setUserModal(false)
                setUser(null)
                setList(loc)
            })
        }

    }
    const updateFilter = () => {
        if (selected.id === undefined) {
            let newObj = selected
            newObj.created = moment.utc()

            APIServices.post(API.QL_Listing_Filter_UP(selector.id), newObj).then((a) => {
                delete newObj.created
                newObj.id = a.data.id
                setSelected(newObj)
                Swal.fire({
                    title: "Filter Saved Successfully",

                    confirmButtonText: 'Exit',
                    allowOutsideClick: false,
                })

            })
        } else {
            let newObj = selected


            newObj.created = moment.utc()
            APIServices.patch(API.QL_Listing_Filter_Edit(selected.id), newObj).then((a) => {
                Swal.fire({
                    title: "Filter Saved Successfully",

                    confirmButtonText: 'Exit',
                    allowOutsideClick: false,
                })

            })
        }
    }
    const selectedTab = (val) => {
        setActiveIndex(val)

    }
    return (
        <div className="grid">
            <div className="col-12">
                <div className="card" >
                    <div style={{
                        fontSize: '16px',
                        display: 'flex',
                        justifyContent: 'flex-start',
                        fontWeight: '600', marginBottom: 30
                    }}> </div>
                    {selector.role === "clientadmin" ?
                        <>
                            <TabMenu model={items} activeIndex={activeindex} onTabChange={(e) => { selectedTab(e.index) }} />
                            {activeindex === 0 &&
                                <div className="mt-4">
                                    <div>
                                        <div style={{ marginBottom: 10 }}>
                                            <label
                                                style={{
                                                    marginRight: 10,
                                                }}
                                            >
                                                Filter by selected Standards / Frameworks / Disclosures
                                            </label>
                                            <Dropdown style={{ width: 200 }} options={[{ title: 'All', id: 0 }, ...assFramework]} value={selectedFramework} optionValue="title" optionLabel="title" onChange={(e) => { renderTable(e.value) }} />
                                        </div>
                                    </div>
                                    {requiredList.length ? <div>
                                        <div style={{
                                            fontSize: '16px',
                                            display: 'flex',
                                            justifyContent: 'flex-start',
                                            fontWeight: '600', marginBottom: 30
                                        }}>Applicable Disclosures for Selected Topics ({requiredList.length})</div>
                                        <DataTable value={requiredList} scrollable >
                                            <Column field='cat_title' header='Category' />
                                            <Column field='top_title' header='Topic' />
                                            <Column field='title' body={titleTemplate} header='Aspect' />
                                            <Column body={rrTemplate} header='Requirement' />
                                            <Column header='Responsibility' body={responsibilityTemplate} />
                                            <Column body={historyTemplate} header='history' />
                                            <Column body={lastResponse} header='Last Response Date' />

                                        </DataTable>
                                    </div>
                                        :
                                        <div>No Data Found</div>

                                    }
                                </div>

                            }
                            {activeindex === 1 &&
                                <div className="mt-4">
                                    <div>
                                        <div style={{ marginBottom: 10 }}>
                                            <label
                                                style={{
                                                    marginRight: 10,
                                                }}
                                            >
                                                Filter by  Standards / Frameworks / Disclosures
                                            </label>
                                            <Dropdown style={{ width: 200 }} options={[{ title: 'All', id: 0 }, ...assFramework2]} value={selectedFramework2} optionValue="title" optionLabel="title" onChange={(e) => { renderTable2(e.value) }} />
                                        </div>
                                    </div>
                                    {requiredList2.length ? <div>
                                        <div style={{
                                            fontSize: '16px',
                                            display: 'flex',
                                            justifyContent: 'flex-start',
                                            fontWeight: '600', marginBottom: 30
                                        }}>Other Disclosures ({requiredList2.length})</div>
                                        <DataTable value={requiredList2} scrollable >
                                            <Column field='cat_title' header='Category' />
                                            <Column field='top_title' header='Topic' />
                                            <Column field='title' body={titleTemplate} header='Aspect' />
                                            <Column body={rrTemplate} header='Requirement' />
                                            <Column header='Responsibility' body={responsibilityTemplate} />
                                            <Column body={historyTemplate} header='history' />
                                            <Column body={lastResponse} header='Last Response Date' />

                                        </DataTable>
                                    </div>
                                        :
                                        <div>No Data Found</div>

                                    }
                                </div>

                            }

                        </>


                        :
                        <div className=" col-12">You have no rights to access this page</div>

                    }
                </div>
            </div>
            <Dialog
                visible={prevdialog}
                style={{
                    width: "60%",
                }}
                header={selectedlist.title}
                modal
                className="p-fluid"

                onHide={() => { setPrevDialog(false) }}
            >
                {renderPreview()}
            </Dialog>
            <Dialog
                visible={historydialog}
                style={{
                    width: "30%",
                }}
                header={"Response History"}
                modal
                className="p-fluid"

                onHide={() => { setHistoryDialog(false) }}
            >
                <div>
                    {historydata.sort((a, b) => { return moment(b.created_on).toDate() - moment(a.created_on).toDate() }).map((i) => {
                        return (
                            <div style={{ flexDirection: 'column', alignItems: 'center', display: 'flex', padding: 5, borderRadius: 10, margin: 5, boxShadow: 'rgb(0 0 0 / 24%) 0px 3px 8px' }}>
                                <text style={{ color: 'black', borderRadius: 10, padding: 5, fontStyle: 'italic' }} onClick={() => { navigate.push({ pathname: '/rf_submission_preview/' + i.rfid, state: { data: activeRowData, oldData: i } }) }}>{moment(i.created_on).local().format('DD MMM YYYY, hh:mm A')} by <span style={{ color: 'green' }}>{i.submitted_by === selector.id ? 'Admin' : !userList.findIndex((j) => { return j.id === i.submitted_by }) === -1 ? 'User Deleted' : userList.find((j) => { return j.id === i.submitted_by }).information.empname}</span> </text>

                            </div>
                        )
                    })

                    }
                </div>
            </Dialog>
            <Dialog
                visible={usermodal}
                style={{
                    width: "50%",
                }}
                header={"Select User"}
                modal
                className="p-fluid"

                onHide={() => { setUserModal(false) }}
            >
                <div>
                    <MultiSelect filter options={userList.filter((i)=>{ return i.information.role.reporter})} value={user} optionLabel="information.empname" optionValue="id" onChange={(e) => { setUser(e.value) }} />

                    {user !== null && <Button style={{ marginTop: 20 }} onClick={() => { AssignRFUser() }}>Assign User</Button>}
                </div>
            </Dialog>
        </div>
    );
};

const comparisonFn = function (prevProps, nextProps) {
    return prevProps.location.pathname === nextProps.location.pathname;
};

export default React.memo(QualitativeRFListing, comparisonFn);
