import React, { useEffect, useRef } from "react";
import * as d3 from "d3";

const complaintData = [
  { stage: "Filing", count: 100 },
  { stage: "Acknowledged", count: 80 },
  { stage: "In Review", count: 60 },
  { stage: "Resolved", count: 40 },
  { stage: "Closed", count: 30 },
];

const PoshComplaintFunnel = () => {
  const chartRef = useRef(null);

  useEffect(() => {
    renderFunnelChart();
  }, []);

  const renderFunnelChart = () => {
    const width = 600;
    const height = 400;
    const margin = { top: 20, right: 30, bottom: 40, left: 50 };

    // Clear any existing chart
    d3.select(chartRef.current).select("svg").remove();

    const svg = d3
      .select(chartRef.current)
      .append("svg")
      .attr("width", width)
      .attr("height", height);

    // Calculate the width for each stage
    const maxCount = d3.max(complaintData, (d) => d.count);

    const funnelScale = d3
      .scaleLinear()
      .domain([0, maxCount])
      .range([0, width - margin.left - margin.right]);

    const stageHeight =
      (height - margin.top - margin.bottom) / complaintData.length;

    // Draw the funnel
    complaintData.forEach((d, i) => {
      const stageWidth = funnelScale(d.count);

      // Draw the funnel bar
      svg
        .append("rect")
        .attr("x", (width - stageWidth) / 2)
        .attr("y", margin.top + i * stageHeight)
        .attr("width", stageWidth)
        .attr("height", stageHeight - 10)
        .attr("fill", d3.interpolateBlues(i / complaintData.length));

      // Add the text labels (centered inside the bar)
      svg
        .append("text")
        .attr("x", width / 2) // Center text horizontally
        .attr("y", margin.top + i * stageHeight + (stageHeight - 10) / 2) // Center text vertically within the bar
        .attr("text-anchor", "middle")
        .attr("dy", ".35em") // Adjust vertical alignment
        .style("font-size", "12px")
        .style("fill", "#fff") // White text color for readability
        .text(`${d.stage}: ${d.count}`);
    });
  };

  return (
    <>
      <div
        style={{
          fontSize: "14px",
          fontWeight: 700,
          lineHeight: "19.2px",
          textAlign: "left",
          marginBottom: "5px",
        }}
      >
        {" "}
        POSH Complaint Funnel
      </div>
      <div>Track complaints from filing to resolution.</div>
      <div style={{ display: "flex", justifyContent: "center" }}>
        <div ref={chartRef} />
      </div>
    </>
  );
};

export default PoshComplaintFunnel;
