import React from 'react';

/**
 * Error Boundary component to catch and handle errors in React components
 */
class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { 
      hasError: false,
      error: null,
      errorInfo: null
    };
  }

  static getDerivedStateFromError(error) {
    // Update state so the next render will show the fallback UI
    return { hasError: true, error };
  }

  componentDidCatch(error, errorInfo) {
    // Log the error to console
    console.warn('Error caught by ErrorBoundary:', error, errorInfo);
    
    // Check if it's the "destroy is not a function" error
    if (error && error.message && error.message.includes('destroy is not a function')) {
      console.warn('Caught "destroy is not a function" error. This is a known issue that is being addressed.');
      // Set state to show a specific message for this error
      this.setState({ 
        hasError: true,
        error,
        errorInfo,
        isDestroyError: true
      });
    } else {
      // Set state for other errors
      this.setState({
        errorInfo,
        isDestroyError: false
      });
    }
  }

  render() {
    if (this.state.hasError) {
      // If it's the specific "destroy is not a function" error, just render children
      // This effectively suppresses the error and allows the app to continue
      if (this.state.isDestroyError) {
        // Reset the error state so the component can recover
        setTimeout(() => {
          this.setState({ 
            hasError: false,
            error: null,
            errorInfo: null,
            isDestroyError: false
          });
        }, 0);
        
        // Return children to continue rendering
        return this.props.children;
      }
      
      // For other errors, show an error UI
      return (
        <div className="error-boundary">
          <h2>Something went wrong.</h2>
          <p>The application encountered an error. Please try refreshing the page.</p>
          <button 
            onClick={() => {
              this.setState({ 
                hasError: false,
                error: null,
                errorInfo: null
              });
            }}
            style={{
              padding: '8px 16px',
              backgroundColor: '#007bff',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer'
            }}
          >
            Try Again
          </button>
        </div>
      );
    }

    // If there's no error, render children normally
    return this.props.children;
  }
}

export default ErrorBoundary;
