import React from 'react';
import { Dialog } from 'primereact/dialog';
import { Card } from 'primereact/card';
import { Divider } from 'primereact/divider';
import { format } from 'date-fns';

export default function DealerActionHistoryDialog({ visible, onHide, data }) {
    const { maskId, actionToBeTaken, created, history = [], description } = data || {};

    const renderMetaData = () => (
        <div className="p-3">
            <div className="text-sm mb-2">
                <strong>Mask ID:</strong> {maskId || '-'}
            </div>
            <div className="text-sm mb-2">
                <strong>Description:</strong> {description || '-'}
            </div>
            <div className="text-sm mb-2">
                <strong>Action To Be Taken:</strong> {actionToBeTaken || '-'}
            </div>
            <div className="text-sm">
                <strong>Last Updated On:</strong> {created ? format(new Date(created), 'PPPpp') : '-'}
            </div>
        </div>
    );

    const renderHistoryCards = () => (
        <div className="space-y-4 mt-4">
            {history.map((entry, idx) => (
                <Card
                    key={entry.id || idx}
                    subTitle={`Submitted on ${format(new Date(entry.created), 'PPPpp')}`}
                    
                    className="shadow-md border border-gray-200 mb-2" 
                >
                    <div>
                    <div className="text-sm mb-2">
                        <strong> {entry.actionType.includes('Approver ') ? 'Return ' : 'Dealer '} Comments:</strong> {entry?.comments || entry?.remarks}
                    </div>

                    <div className="text-sm">
                        <strong>Action Taken:</strong>
                        <div
                            className="p-2 bg-gray-50 border border-gray-200 rounded mt-1"
                            dangerouslySetInnerHTML={{ __html: entry.actionTaken }}
                        />
                    </div>
                    </div>
                </Card>
            ))}
        </div>
    );

    return (
        <Dialog
            header={actionToBeTaken+' - '+maskId}
            visible={visible}
            style={{ width: '50vw', maxHeight: '90vh' }}
            modal
            className="p-fluid"
            onHide={onHide}
        >
            <div className="p-2">
                {renderMetaData()}
                <Divider />
                {renderHistoryCards()}
            </div>
        </Dialog>
    );
}
