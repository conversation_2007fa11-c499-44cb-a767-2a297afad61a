import React, { useEffect, useRef, useState } from "react";
import * as d3 from "d3";
import { interpolateYlOrRd } from "d3-scale-chromatic"; // Import the color scale

const dummyData = {
  months: [
    "January",
    "February",
    "March",
    "April",
    "May",
    "June",
    "July",
    "August",
    "September",
    "October",
    "November",
    "December",
  ],
  years: ["2022", "2023", "2024"],
  emissions: {
    2022: [50, 60, 70, 65, 80, 90, 100, 110, 120, 115, 130, 140], // Emissions for 2022
    2023: [55, 65, 75, 80, 85, 95, 105, 115, 125, 120, 135, 145], // Emissions for 2023
    2024: [60, 70, 80, 85, 90, 100, 110, 120, 130, 125, 140, 150], // Emissions for 2024
  },
};

const OverviewHeatMap = () => {
  const chartRef = useRef(null);

  useEffect(() => {
    renderHeatMap();
  }, []);

  const renderHeatMap = () => {
    const margin = { top: 50, right: 50, bottom: 50, left: 50 };
    const width = 600 - margin.left - margin.right;
    const height = 400 - margin.top - margin.bottom;

    // Clear any existing SVG
    d3.select(chartRef.current).select("svg").remove();

    const svg = d3
      .select(chartRef.current)
      .append("svg")
      .attr("width", width + margin.left + margin.right)
      .attr("height", height + margin.top + margin.bottom)
      .append("g")
      .attr("transform", `translate(${margin.left},${margin.top})`);

    // Define the color scale for the heatmap
    const colorScale = d3
      .scaleSequential(interpolateYlOrRd)
      .domain([
        d3.min(Object.values(dummyData.emissions).flat()),
        d3.max(Object.values(dummyData.emissions).flat()),
      ]);

    const months = dummyData.months;
    const years = dummyData.years;

    // Define the X and Y scales
    const xScale = d3.scaleBand().domain(years).range([0, width]).padding(0.05);

    const yScale = d3
      .scaleBand()
      .domain(months)
      .range([0, height])
      .padding(0.05);

    // Add X axis
    svg
      .append("g")
      .selectAll(".x-axis")
      .data(years)
      .enter()
      .append("text")
      .attr("x", (d, i) => xScale(d) + xScale.bandwidth() / 2)
      .attr("y", -10)
      .attr("text-anchor", "middle")
      .text((d) => d)
      .style("font-size", "12px");

    // Add Y axis
    svg
      .append("g")
      .selectAll(".y-axis")
      .data(months)
      .enter()
      .append("text")
      .attr("x", -23)
      .attr("y", (d, i) => yScale(d) + yScale.bandwidth() / 2)
      .attr("text-anchor", "middle")
      .text((d) => d)
      .style("font-size", "12px")
      .style("dominant-baseline", "middle");

    // Create the heatmap cells
    svg
      .selectAll("rect")
      .data(
        months.flatMap((month, i) =>
          years.map((year, j) => ({
            month,
            year,
            value: dummyData.emissions[year][i],
          }))
        )
      )
      .enter()
      .append("rect")
      .attr("x", (d) => xScale(d.year))
      .attr("y", (d) => yScale(d.month))
      .attr("width", xScale.bandwidth())
      .attr("height", yScale.bandwidth())
      .style("fill", (d) => colorScale(d.value))
      .on("mouseover", (event, d) => {
        d3.select(event.currentTarget).style("opacity", 0.7);
        svg
          .append("text")
          .attr("x", xScale(d.year) + xScale.bandwidth() / 2)
          .attr("y", yScale(d.month) + yScale.bandwidth() / 2)
          .attr("text-anchor", "middle")
          .attr("dy", ".35em")
          .style("font-size", "12px")
          .style("fill", "black")
          .text(d.value);
      })
      .on("mouseout", (event, d) => {
        d3.select(event.currentTarget).style("opacity", 1);
        svg.selectAll("text").remove();
      });
  };

  return (
    <div>
      <div
        style={{
          fontFamily: "Lato",
          fontSize: "16px",
          fontWeight: 700,
          lineHeight: "19.2px",
          textAlign: "left",
          marginBottom: "10px",
        }}
      >
        Heat Map for Emissions
        <div style={{ fontWeight: 200, fontSize: "14px" }}>
          View emissions data across months and years with color gradients.
        </div>
      </div>
      <div ref={chartRef} style={{ textAlign: "center" }} />

      {/* Legends */}
      <div style={{ textAlign: "center", marginTop: "20px" }}>
        <span style={{ fontSize: "14px" }}>
          Color scale represents emissions intensity: higher emissions have
          darker shades.
        </span>
      </div>
    </div>
  );
};

export default OverviewHeatMap;
