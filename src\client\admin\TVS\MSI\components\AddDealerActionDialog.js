import React, { useState, useEffect } from 'react';
import { Dialog } from 'primereact/dialog';
import { But<PERSON> } from 'primereact/button';
import { InputText } from 'primereact/inputtext';
import { InputTextarea } from 'primereact/inputtextarea';
import { Dropdown } from 'primereact/dropdown';
import { Calendar } from 'primereact/calendar';
import { Card } from 'primereact/card';
import { DateTime } from 'luxon';
import moment from 'moment';
import APIServices from '../../../../../service/APIService';
import { API } from '../../../../../constants/api_url';
import Swal from 'sweetalert2';

const AddDealerActionDialog = ({ visible, onHide, dealer, onActionAdded }) => {
    const [actions, setActions] = useState([{
        finding: '',
        description: '',
        categoryOfFinding: null,
        nonComplianceType: null,
        dueDate: null,
        status: 'initiated'
    }]);

    const categoryOptions = [
        { label: 'Good Practices', value: 1 },
        { label: 'Opportunity of Improvement', value: 2 },
        { label: 'Non-compliance', value: 3 }
    ];

    const nonComplianceOptions = [
        { label: 'Regulatory (Major)', value: 1 },
        { label: 'Regulatory (Minor)', value: 2 },
        { label: 'Minor', value: 3 }
    ];

    const addNewAction = () => {
        setActions([...actions, {
            finding: '',
            description: '',
            categoryOfFinding: null,
            nonComplianceType: null,
            dueDate: null,
            status: 'initiated'
        }]);
    };

    const removeAction = (index) => {
        const updatedActions = [...actions];
        updatedActions.splice(index, 1);
        setActions(updatedActions);
    };

    const updateAction = (index, field, value) => {
        const updatedActions = [...actions];
        updatedActions[index][field] = value;
        setActions(updatedActions);
    };

    const handleSubmit = async () => {
        // Validate actions
        const isValid = actions.every(action => 
            action.finding && 
            action.description && 
            action.categoryOfFinding !== null
        );

        if (!isValid) {
            Swal.fire({
                icon: 'error',
                title: 'Validation Error',
                text: 'Please fill all required fields for all actions'
            });
            return;
        }

        try {
            // Prepare actions with dealer information
            const actionsToSubmit = actions.map(action => ({
                ...action,
                dealerId: dealer.dealerId,
                dealerName: dealer.vendor?.dealerName,
                dealerLocation: dealer.vendor?.dealerLocation,
                msiId: `MSI-${dealer.vendor?.code}-${DateTime.fromISO(dealer.created_on, { zone: 'utc' }).toLocal().toFormat('ddMMyyyy')}`,
                auditStartDate: dealer.auditStartDate,
                auditEndDate: dealer.auditEndDate,
                created_on: new Date().toISOString()
            }));

            // Add actions to dealer
            const response = await APIServices.patch(API.DealerChecklistSubmission_Edit(dealer.id), {
                actions: [...(dealer.actions || []), ...actionsToSubmit]
            });

            if (response.status === 200) {
                Swal.fire({
                    icon: 'success',
                    title: 'Success',
                    text: 'Actions added successfully'
                });
                onActionAdded(response.data);
                onHide();
            }
        } catch (error) {
            console.error('Error adding actions:', error);
            Swal.fire({
                icon: 'error',
                title: 'Error',
                text: 'Failed to add actions. Please try again.'
            });
        }
    };

    const renderActionCard = (action, index) => {
        return (
            <Card 
                key={index} 
                className="mb-3 shadow-1"
                title={
                    <div className="flex justify-content-between align-items-center">
                        <span>Action {index + 1}</span>
                        {actions.length > 1 && (
                            <Button 
                                icon="pi pi-trash" 
                                className="p-button-rounded p-button-danger p-button-text" 
                                onClick={() => removeAction(index)}
                            />
                        )}
                    </div>
                }
            >
                <div className="grid">
                    <div className="col-12">
                        <div className="field">
                            <label htmlFor={`finding-${index}`} className="block font-medium mb-2">Finding*</label>
                            <InputText
                                id={`finding-${index}`}
                                value={action.finding}
                                onChange={(e) => updateAction(index, 'finding', e.target.value)}
                                className="w-full"
                            />
                        </div>
                    </div>

                    <div className="col-12">
                        <div className="field">
                            <label htmlFor={`description-${index}`} className="block font-medium mb-2">Description*</label>
                            <InputTextarea
                                id={`description-${index}`}
                                value={action.description}
                                onChange={(e) => updateAction(index, 'description', e.target.value)}
                                rows={3}
                                className="w-full"
                            />
                        </div>
                    </div>

                    <div className="col-12 md:col-6">
                        <div className="field">
                            <label htmlFor={`category-${index}`} className="block font-medium mb-2">Category*</label>
                            <Dropdown
                                id={`category-${index}`}
                                value={action.categoryOfFinding}
                                options={categoryOptions}
                                onChange={(e) => updateAction(index, 'categoryOfFinding', e.value)}
                                placeholder="Select Category"
                                className="w-full"
                            />
                        </div>
                    </div>

                    {action.categoryOfFinding === 3 && (
                        <div className="col-12 md:col-6">
                            <div className="field">
                                <label htmlFor={`nonCompliance-${index}`} className="block font-medium mb-2">Non-compliance Type</label>
                                <Dropdown
                                    id={`nonCompliance-${index}`}
                                    value={action.nonComplianceType}
                                    options={nonComplianceOptions}
                                    onChange={(e) => updateAction(index, 'nonComplianceType', e.value)}
                                    placeholder="Select Type"
                                    className="w-full"
                                />
                            </div>
                        </div>
                    )}

                    <div className="col-12 md:col-6">
                        <div className="field">
                            <label htmlFor={`dueDate-${index}`} className="block font-medium mb-2">Due Date</label>
                            <Calendar
                                id={`dueDate-${index}`}
                                value={action.dueDate}
                                onChange={(e) => updateAction(index, 'dueDate', e.value)}
                                showIcon
                                dateFormat="dd-mm-yy"
                                className="w-full"
                            />
                        </div>
                    </div>
                </div>
            </Card>
        );
    };

    const renderDealerInfo = () => {
        if (!dealer) return null;

        return (
            <Card className="mb-3 shadow-1">
                <div className="grid">
                    <div className="col-12 md:col-6">
                        <div className="field">
                            <label className="block text-sm font-medium text-gray-700">Dealer</label>
                            <div className="text-lg font-semibold">{dealer.vendor?.dealerName || 'NA'}</div>
                        </div>
                    </div>
                    
                    <div className="col-12 md:col-6">
                        <div className="field">
                            <label className="block text-sm font-medium text-gray-700">Location</label>
                            <div className="text-lg font-semibold">{dealer.vendor?.dealerLocation || 'NA'}</div>
                        </div>
                    </div>
                    
                    <div className="col-12 md:col-6">
                        <div className="field">
                            <label className="block text-sm font-medium text-gray-700">MSI ID</label>
                            <div>{`MSI-${dealer.vendor?.code}-${DateTime.fromISO(dealer.created_on, { zone: 'utc' }).toLocal().toFormat('ddMMyyyy')}` || 'NA'}</div>
                        </div>
                    </div>
                    
                    <div className="col-12 md:col-6">
                        <div className="field">
                            <label className="block text-sm font-medium text-gray-700">MSI Rating</label>
                            <div>{dealer.grade || 'NA'}</div>
                        </div>
                    </div>
                </div>
            </Card>
        );
    };

    const dialogFooter = (
        <div>
            <Button label="Add More Action" icon="pi pi-plus" className="p-button-secondary mr-2" onClick={addNewAction} />
            <Button label="Submit" icon="pi pi-check" className="p-button-success" onClick={handleSubmit} />
            <Button label="Cancel" icon="pi pi-times" className="p-button-danger" onClick={onHide} />
        </div>
    );

    return (
        <Dialog
            header="Add Actions"
            visible={visible}
            style={{ width: '80vw', maxWidth: '1200px' }}
            onHide={onHide}
            footer={dialogFooter}
            modal
            className="p-fluid"
        >
            {renderDealerInfo()}
            
            <div className="action-cards">
                {actions.map((action, index) => renderActionCard(action, index))}
            </div>
        </Dialog>
    );
};

export default AddDealerActionDialog;
